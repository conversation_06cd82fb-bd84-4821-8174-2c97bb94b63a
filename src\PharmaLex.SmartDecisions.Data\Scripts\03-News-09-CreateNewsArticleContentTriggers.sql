﻿create trigger [dbo].[NewsArticleContent_Insert] on [dbo].[NewsArticleContent]
for insert as
insert into [Audit].[NewsArticleContent_Audit]
select 'I'
      ,[Id]
      ,[NewsArticleId]
      ,[LocaleId]
      ,[Title]
      ,[AuthorId]
      ,[ReviewerId]
      ,[SourceUrl]
      ,[PublishingStateId]
      ,[PublishingStateDateUtc]
      ,[AzureBlobId]
      ,[ImpactAssessmentSummary]
      ,[FriendlyUrl]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsArticleContent_Update] on [dbo].[NewsArticleContent]
for update as
insert into [Audit].[NewsArticleContent_Audit]
select 'U'
      ,[Id]
      ,[NewsArticleId]
      ,[LocaleId]
      ,[Title]
      ,[AuthorId]
      ,[ReviewerId]
      ,[SourceUrl]
      ,[PublishingStateId]
      ,[PublishingStateDateUtc] 
      ,[AzureBlobId]
      ,[ImpactAssessmentSummary]
      ,[FriendlyUrl]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsArticleContent_Delete] on [dbo].[NewsArticleContent]
for delete as
insert into [Audit].[NewsArticleContent_Audit]
select 'D'
      ,[Id]
      ,[NewsArticleId]
      ,[LocaleId]
      ,[Title]
      ,[AuthorId]
      ,[ReviewerId]
      ,[SourceUrl]
      ,[PublishingStateId]
      ,[PublishingStateDateUtc] 
      ,[AzureBlobId]
      ,[ImpactAssessmentSummary]
      ,[FriendlyUrl]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
