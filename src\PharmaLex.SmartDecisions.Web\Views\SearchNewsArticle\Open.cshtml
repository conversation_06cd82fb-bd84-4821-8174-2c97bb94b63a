@model NewsArticleFullReadModel
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.SmartDecisions.Entities
@inject IAuthorizationService AuthorizationService
@{
    ViewData["Title"] = Model?.NewsArticle.Title;

    string categories = "";
    var productCategories = (await ls.LocaliseList<NewsCategory>())
                            .Where(x => Model.NewsArticle.ProductIds.Contains(x.Id))
                            .OrderBy(x => x.Name)
                            .Select(x => x.Name).ToList();

    var themeCategories = (await ls.LocaliseList<NewsCategory>())
                            .Where(x => Model.NewsArticle.ThemeIds.Contains(x.Id))
                            .OrderBy(x => x.Name)
                            .Select(x => x.Name).ToList();

    var typeOfTextCategories = (await ls.LocaliseList<NewsCategory>())
                            .Where(x => Model.NewsArticle.TypeOfTextIds.Contains(x.Id))
                            .OrderBy(x => x.Name)
                            .Select(x => x.Name).ToList();

    var newsSource = (await ls.LocaliseList<NewsSource>())
                            .FirstOrDefault(x => Model.NewsArticle.NewsSourceId == x.Id)?.Name;

    if (Model != null)
    {
        var articleCategories = (await ls.LocaliseList<NewsCategory>())
            .Where(x => Model.NewsArticle.CategoryIds.Contains(x.Id))
            .OrderBy(x => x.Name)
            .Select(x => x.Name).ToList();

        categories = string.Join(" / ", articleCategories);
    }
}

<div id="search-impact-assessment">
    <div class="loader" v-if="searching"><img src="/images/loaders/spinner-75.svg" alt="loading" /></div>
    <div v-if="model != null">
        <div class="sub-header">
            <div>&nbsp;</div>
            <div class="controls">
                @if (User.Identity?.IsAuthenticated == true)
                {
                    <a class="button" v-on:click.stop="download">@ls.Localise("(news).open.download")</a>
                    <div class="vertical-line"></div>
                }
                @if ((await AuthorizationService.AuthorizeAsync(User, "NewsAuthor")).Succeeded)
                {
                    <a class="button" :href="edit" v-on:click.stop>@ls.Localise("edit-news-article")</a>
                    <div class="vertical-line"></div>
                    <a class="button secondary" href="javascript:window.close();">@ls.Localise("close-window")</a>
                }
            </div>
        </div>
        <div class="flex flex-row justify-space-between newsletter-article-page-container">
            <div class="flex-item flex-x1 overflow-hidden">
                <section id="news-wall">
                    <article>
                        <h1>{{ model.newsArticle.title }}</h1>
                        <section class="meta">
                            <div>
                                <strong>@ls.Localise("newsletter-publication-date")</strong>
                                <span>{{model.newsArticle.goLiveDate}}</span>
                            </div>
                            <div>
                                <strong>@ls.Localise("source-publication-date")</strong>
                                <span>{{model.newsArticle.sourcePublicationDate}}</span>
                            </div>
                            <div>
                                <strong>@ls.Localise("published-by")</strong>
                                <span>{{model.newsArticle.publisherFullName}}</span>
                            </div>
                        </section>
                        <section class="meta">
                            <div class="source">
                                <strong>@ls.Localise("source-url")</strong>
                                <span>
                                    <a :href="model.newsArticle.sourceUrl"
                                       target="_blank">
                                        {{model.newsArticle.sourceUrl}}
                                    </a>
                                </span>
                            </div>
                        </section>
                        <section id="pills">
                            <div class="pills-items">
                                <span class="purple" v-for="product in productCategories">{{product}}</span>
                                <span class="gray" v-for="theme in themeCategories">{{theme}}</span>
                                <span class="orange" v-for="typeOfText in typeOfTextCategories">{{typeOfText}}</span>
                                <span class="red">{{newsSource}}</span>
                            </div>
                            <div class="legend">
                                <div class="legend-help"><i class="m-icon pr-2">help</i></div>
                                <div class="legend-container">
                                    <div class="legend-container-items">
                                        <div><span class="purple"></span>@ls.Localise("(news).subscribe.products")</div>
                                        <div><span class="gray"></span>@ls.Localise("(news).subscribe.themes")</div>
                                        <div><span class="orange"></span>@ls.Localise("[NewsCategory].type-of-text")</div>
                                        <div><span class="red"></span>@ls.Localise("news-source")</div>
                                    </div>
                                </div>

                            </div>
                        </section>
                        <section class="content">
                            <template v-if="isPdf">
                                <!-- Migrated document -->
                                <iframe id="iframeContent" ref="pdfJsIframe" style="width: 100%; min-height: 600px;" :src="viewerUrl"></iframe>
                            </template>
                            <template v-else>
                                <iframe style="width: 100%; min-height: 600px;" :srcdoc="content"></iframe>
                            </template>
                        </section>
                    </article>
                </section>
            </div>

            <div class="flex-item flex-x4 flex columns ml-2 overflow-auto white-background pl-3 pt-2" v-if="model.newsletterKey">
                <div class="flex pb-2 flex-align-center">
                    <h3>@ls.Localise("newsletter-summary")</h3>
                </div>
                <div class="flex pb-1 newsletter-article-item" v-for="n in newsArticles">
                    <a v-bind:href="`/article/${model.newsletterKey}/${n.friendlyUrl}`"
                       :class="n.friendlyUrl === model.newsArticle.friendlyUrl ? 'highlight-title flex justify-space-between' : 'flex justify-space-between '">{{ n.title }}</a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#search-impact-assessment',
            data() {
                return {
                    searching: false,
                    model: @Html.Raw(Model.ToJson()),
                    categories: '@Html.Raw(categories)',
                    content: @Html.Raw(Json.Serialize(Model.NewsArticle.Content)),
                    productCategories: @Html.Raw(Json.Serialize(productCategories)),
                    themeCategories: @Html.Raw(Json.Serialize(themeCategories)),
                    typeOfTextCategories: @Html.Raw(Json.Serialize(typeOfTextCategories)),
                    newsSource: '@Html.Raw(newsSource)',
                    newsArticles: @Html.Raw(Json.Serialize(Model.NewsArticles)),
                    viewerUrl: '/js/lib/pdfjs/web/viewer.html'
                };
            },
            computed: {
                edit() {
                    return `/article/edit/${this.model.newsArticle.newsArticleId}?contentId=${this.model.newsArticle.id}`;
                },
                isPdf() {
                    return this.model.newsArticle.contentUrl?.indexOf('.pdf') > 0;
                }
            },
            mounted() {
                loadPdfContent(this.model.newsArticle.contentBase64);
            },
            methods: {
                download() {
                    this.searching = true;

                    fetch(`/article/download/${this.model.newsArticle.friendlyUrl}`, {
                        method: 'GET'
                    })
                        .then(response => response.blob())
                        .then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.style.display = 'none';
                            a.href = url;
                            const fileTitle = this.model.newsArticle.title.replace(/[<>:"\/\\|?*]+/g, '');
                            a.download = `${fileTitle}.pdf`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            this.searching = false;
                        });
                },
            }
        };
    </script>
}
