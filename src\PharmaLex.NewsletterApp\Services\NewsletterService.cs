﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.NewsletterApp.Helpers;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public interface INewsletterService
    {
        Task<bool> TryInit(int subscriptionId, ILogger logger);
        Task<bool> TryBuildNewsletterModel();
        Task SendNewsletter();
    }
    public class NewsletterService : INewsletterService
    {
        private readonly IRepositoryFactory repoFactory;
        private readonly IEmailService emailService;
        private readonly ILocalisationService localisationService;
        private readonly INewsCategoryLicenseHelper licenseHelper;
        private readonly IAzureBlobHelper blobHelper;
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly IMapper mapper;
        private readonly string articleUrlTemplate;
        private readonly string editPreferencesLink;
        private readonly string searchLink;
        private readonly string unsubscribeLink;
        private readonly bool emailsSendingEnabled;
        private readonly string environment;

        public NewsletterService(IRepositoryFactory repoFactory,
            IEmailService emailService,
            INewsCategoryLicenseHelper licenseHelper,
            ILocalisationService localisationService,
            IAzureBlobHelper blobHelper,
            IDistributedCacheServiceFactory cache,
            IMapper mapper,
            IConfiguration configuration)
        {
            this.repoFactory = repoFactory;
            this.emailService = emailService;
            this.localisationService = localisationService;
            this.licenseHelper = licenseHelper;
            this.blobHelper = blobHelper;
            this.cacheFactory = cache;
            this.mapper = mapper;
            this.articleUrlTemplate = configuration.GetValue<string>("articleUrlTemplate");
            this.editPreferencesLink = configuration.GetValue<string>("editPreferencesLink");
            this.searchLink = configuration.GetValue<string>("searchLink");
            this.unsubscribeLink = configuration.GetValue<string>("unsubscribeLink");
            this.emailsSendingEnabled = configuration.GetValue<bool>("emailsSendingEnabled");
            this.environment = configuration.GetValue<string>("Static:Env");
        }

        public ILogger Logger { get; set; }
        public SubscriptionModel Subscription { get; set; }
        public UserModel User => this.Subscription?.User;
        public LocaleModel Locale { get; set; }

        public Dictionary<NewsCategoryGroup, List<NewsCategory>> NewsCategoryGroups { get; set; }
        public List<NewsArticleContentModel> NewsArticleContents { get; set; }
        public Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>> SubscriberPreferenceGroups { get; set; }
        public List<NewsSource> NewsSources { get; set; }
        public NewsletterModel Model { get; set; }

        public async Task<bool> TryBuildNewsletterModel()
        {
            if (this.NewsArticleContents.Count > 0)
            {
                var builder = new NewsletterProductGroupBuilder(
                    this.blobHelper,
                    this.NewsArticleContents,
                    this.SubscriberPreferenceGroups,
                    this.NewsCategoryGroups,
                    this.NewsSources,
                    this.User.Locale,
                    this.articleUrlTemplate,
                    this.localisationService.LocaliseText(this.Locale.Id, "(news).headline"),
                    this.Subscription.IsInfoflash
                    );
                await builder.Build();

                this.Model = new NewsletterModel
                {
                    Date = DateTime.Now.ToString("dd MMMM yyyy", new CultureInfo(this.User.Locale.IsoLanguageCode)),
                    Key = builder.NewsletterKey,
                    HeadlineArticles = builder.Headline,
                    ProductGroupArticles = builder.ByProductGroup,
                    Recepient = this.User.DisplayFullName,
                    EditPreferencesLink = this.editPreferencesLink,
                    IsoLanguageCode = this.Locale.IsoLanguageCode,
                    LocaleId = this.Locale.Id,
                    IsMonthly = this.Subscription.IsMonthly,
                    IsInfoflash = this.Subscription.IsInfoflash,
                    SearchLink = this.searchLink,
                    UnsubscribeLink = this.unsubscribeLink,
                    Environment = this.environment
                };
            }

            return this.Model != null;
        }

        public async Task SendNewsletter()
        {
            if (Model == null)
            {
                Logger.LogInformation("No news articles found. Newsletter email will not be sent.");
                return;
            }

            var nr = cacheFactory.CreateTrackedEntity<Newsletter>();

            Logger.LogInformation("Starting to process newsletter with unique key {UniqueKey} for user {UserId}", Model.Key, User.Id);
            var newsletter = new Newsletter
            {
                UserId = User.Id,
                CreatedDateUtc = DateTime.UtcNow,
                LocaleId = Locale.Id,
                UniqueKey = Model.Key,
                NewsletterSubscriptionId = Subscription.Id,
                IsMonthly = Subscription.IsMonthly,
                NewsArticleContentIds = JsonConvert.SerializeObject(NewsArticleContents.Select(x => x.Id).ToArray())
            };
            nr.Add(newsletter);

            try
            {
                if (emailsSendingEnabled)
                {
                    var (success, responseCode) = await this.emailService.Send(this.User, this.Model);
                    if (success)
                    {
                        Logger.LogInformation("Email sent to '{Email}'.", User.Email);
                    }
                    else
                    {
                        Logger.LogWarning("Failed to send email. Response code was {ResponseCode}", responseCode);
                    }
                }
                else
                {
                    Logger.LogInformation("Sending emails is DISABLED.");
                }
                await nr.SaveChangesAsync();
            }
            catch (Exception e)
            {
                Logger.LogError(
                    "Newsletter generation failed for {UserId}, with {UniqueKey}. Exception Message: {Message}",
                    User.Id, Model.Key, e.Message);
            }
        }

        public async Task<bool> TryInit(int subscriptionId, ILogger logger)
        {
            Logger = logger;

            if (!await this.TryInitSubscription(subscriptionId))
            {
                Logger.LogWarning("Subscription with id:{SubscriptionId} was not found in database and will not be processed.", subscriptionId);
                return false;
            }

            if (!await this.TryInitLocales())
            {
                Logger.LogWarning("Subscription locale could not be found in the database. Subscription with id:{SubscriptionId} will not be processed.", subscriptionId);
                return false;
            }

            await InitNewsCategoryGroups();
            await InitSubscriberPreferenceGroups();
            await InitNewsSources();
            await LoadArticleContent();

            return true;
        }

        private async Task InitNewsSources()
        {
            NewsSources = await localisationService.LocaliseLookupList<NewsSource>(Locale.Id);
        }

        private async Task InitSubscriberPreferenceGroups()
        {
            var preferencesRepo = repoFactory.Create<UserNewsCategory>().Configure(o => o.Include(x => x.NewsCategory));
            var preferences = await mapper.ProjectTo<UserNewsCategoryModel>(preferencesRepo.Where(x => x.UserId == this.User.Id)).OrderBy(x => x.Order).ToListAsync();

            this.SubscriberPreferenceGroups = new Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>>();
            foreach (NewsCategoryGroup group in Enum.GetValues(typeof(NewsCategoryGroup)))
            {
                var license = await this.licenseHelper.GetUserNewsCategoryGroupLicense(this.User.Id, group, true);
                this.SubscriberPreferenceGroups.Add(group, preferences
                    .Where(x => license.Contains(x.NewsCategoryId)).ToList());
            }
        }

        private async Task InitNewsCategoryGroups()
        {
            var newsCategoryGroups = (await this.localisationService.LocaliseNewsCategories(this.Locale.Id)).GroupBy(x => x.GroupId).ToArray();

            this.NewsCategoryGroups = new Dictionary<NewsCategoryGroup, List<NewsCategory>>();
            foreach (NewsCategoryGroup group in Enum.GetValues(typeof(NewsCategoryGroup)))
            {
                this.NewsCategoryGroups.Add(group, newsCategoryGroups.First(x => x.Key == (int)group).OrderBy(x => x.Name).ToList());
            }
        }

        private async Task LoadArticleContent()
        {
            var isMonthly = this.Subscription.IsMonthly;
            var isInfoflash = this.Subscription.IsInfoflash;
            var daysAdjustment = isMonthly ? -40 : -7;
            var maxAge = this.Subscription.CreatedDateUtc > DateTime.UtcNow.AddDays(daysAdjustment)
                ? this.Subscription.CreatedDateUtc.Date : DateTime.UtcNow.AddDays(daysAdjustment).Date;
            var today = DateTime.UtcNow.Date;

            var articleContentCache = cacheFactory.CreateEntity<NewsArticleContent>().Configure(o => o
                    .Include(x => x.AzureBlob)
                    .Include(x => x.Locale)
                    .Include(x => x.NewsArticle)
                        .ThenInclude(x => x.NewsArticleCategory));

            var articleContentList = (await articleContentCache.WhereProjectedAsync<NewsArticleContentModel>(x => x.PublishingStateId == (int)NewsArticlePublishingState.Approved
                        && x.GoLiveDate.HasValue && x.GoLiveDate.Value <= today
                        && (
                            (!isMonthly && !isInfoflash && x.NewsArticle.ImportanceId != (int)NewsArticleImportance.Miscellaneous && x.NewsArticle.ImportanceId != (int)NewsArticleImportance.INFOFLASH)
                            || (isMonthly && x.NewsArticle.ImportanceId == (int)NewsArticleImportance.Miscellaneous)
                            || (isInfoflash && x.NewsArticle.ImportanceId == (int)NewsArticleImportance.INFOFLASH)
                        )
                        && x.PublishingStateDateUtc.Date >= maxAge
                )).ToList();

            var newslettersRepo = repoFactory.Create<Newsletter>().Configure(o => o );
            var newsletters = await mapper.ProjectTo<NewsletterNewsArticleModel>(newslettersRepo.Where(x => x.UserId == this.User.Id)).ToListAsync();
            var newsletterNewsArticleContentIds = newsletters.SelectMany(n => n.NewsArticleContentIds).ToArray();

            var geographicalScopePreferenceIds = this.SubscriberPreferenceGroups[NewsCategoryGroup.GeographicalScope].Select(x => x.NewsCategoryId).ToList();
            var productsPreferenceIds = this.SubscriberPreferenceGroups[NewsCategoryGroup.Products].Select(x => x.NewsCategoryId).ToList();
            var themesPreferenceIds = this.SubscriberPreferenceGroups[NewsCategoryGroup.Themes].Select(x => x.NewsCategoryId).ToList();
            var articleLanguagePreferenceIds = this.SubscriberPreferenceGroups[NewsCategoryGroup.NewsletterArticleLanguage].Select(x => x.NewsCategoryId).ToList();
            var newsCategoriesFilteredForUser = (await cacheFactory.CreateEntity<NewsCategory>().WhereAsync(x => articleLanguagePreferenceIds.Contains(x.Id))).ToList();
            var languagePreferenceLocalizationKeysMap = newsCategoriesFilteredForUser.ToDictionary(x => x.Id, x => x.LocalisationKey);

            //filter by preference
            NewsArticleContents = articleContentList
                .GroupBy(x => x.NewsArticleId,
                    (_, g) => NewsArticleContentHelper.FilterArticlesUsingLanguagePreference(g.ToArray(), articleLanguagePreferenceIds, languagePreferenceLocalizationKeysMap))
                .Where(a => a != null && !newsletterNewsArticleContentIds.Contains(a.Id))
                .Where(x => Array.Exists(x.NewsArticleCategoryIds, y => geographicalScopePreferenceIds.Contains(y)) &&
                            Array.Exists(x.NewsArticleCategoryIds, y => productsPreferenceIds.Contains(y)) &&
                            (themesPreferenceIds.Count == 0 || Array.Exists(x.NewsArticleCategoryIds, y => themesPreferenceIds.Contains(y)))).ToList();

            Logger.LogInformation("Number of articles to be sent: {Count}", NewsArticleContents.Count);
        }

        private async Task<bool> TryInitLocales()
        {
            var defaultLocaleCache = cacheFactory.CreateEntity<Locale>();
            var defaultLocale = await defaultLocaleCache.FirstOrDefaultAsync(x => x.Default);

            Locale = User.Locale ?? (defaultLocale != null ? new LocaleModel() {  Id = defaultLocale.Id,  IsoLanguageCode = defaultLocale.IsoLanguageCode } : new LocaleModel());

            Logger.LogInformation("LocaleId:{LocaleId}", Locale.Id);

            return this.Locale.Id > 0;
        }

        private async Task<bool> TryInitSubscription(int subscriptionId)
        {
            var repo = repoFactory.Create<NewsletterSubscription>().Configure(o => o
                   .Include(x => x.User)
                       .ThenInclude(x => x.Locale));

            Subscription = await mapper.ProjectTo<SubscriptionModel>(repo.Where(x => x.Id == subscriptionId && x.Active)).FirstOrDefaultAsync();

            return Subscription != null;
        }
    }
}
