﻿@model EditContentItemViewModel
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.Caching.Data
@inject IDistributedCacheServiceFactory cache
@{
    ViewData["Title"] = "Edit " + Model.ContentType.Name;
    string referer = this.Context.Request.Headers["Referer"].ToString();
}

<div id="content-item">

    <div class="sub-header">
        <h2>@Model.Item.Name @Model.ContentType.Name</h2>
        <div class="controls">
            @if (referer.Contains("/data/"))
            {
                <a class="button secondary" href="/data/@Model.ContentType.Id">@Model.ContentType.PluralName</a>
            }
            else
            {
                <a class="button secondary" href="/records">My Records</a>
            }
            @if (Model.Item.Id > 0)
            {
                <a href="/data/delete/@Model.Item.Id" class="button">Delete</a>
            }
        </div>
    </div>

    <section>
        <form method="post">
            <h5>Details</h5>

            <div class="flex flex-nowrap">
                <div class="flex-item flex-x3" v-if="!contentType.autoManageName">
                    <div class="form-group pr-3">
                        <label for="name">Name*</label>
                        <input type="text" id="name" title="Enter a valid Name without forbidden symbols <>." name="name" value="@Model.Item.Name" required pattern="^[^<>]*$" autofocus />
                        @if (@TempData.ContainsKey("ErrorMessage"))
                        {
                            <span validation-for="name" id="error" name="error" class="error-color">@TempData["ErrorMessage"]</span>
                        }
                    </div>
                </div>
                <div class="flex-item flex-x3 pr-3">
                    <div class="form-group">
                        <label for="owner">Owner*</label>
                        <input type="text" id="owner" name="owner" value="@Model.Item.Owner" required />
                    </div>
                </div>
                <div class="flex-item flex-x3">
                    <div class="form-group">
                        <label for="verifieddate">Verified on*</label>
                        <input type="date" id="verifieddate" name="verifieddate" value="@Model.Item.VerifiedDate?.ToString("yyyy-MM-dd")" required />

                    </div>
                </div>
            </div>

            <div class="mt-3">
                <edit-field v-for="f in contentType.field" :field="f" :value="item[f.name]" :picklists="picklists" :relationships="relationships"></edit-field>
            </div>



            <div class="buttons mt-2">
                @if (referer.Contains("/data/"))
                {
                    <a class="button secondary" href="/data/@Model.ContentType.Id">Cancel</a>
                }
                else
                {
                    <a class="button secondary" href="/records">Cancel</a>
                }
                <button name="button" value="save" type="submit">Save</button>
                <button v-if="contentType.contentTypeCategoryId === 3" type="submit" name="button" value="verify" title="Sets Verified Date to today and saves record">Verify</button>
            </div>
            <input type="hidden" name="id" value="@Model.Item.Id" />
        </form>
    </section>
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#content-item',
            data: function () {
                return {
                    link: '/data/edit/',
                    item: @Html.Raw(Model.Item.ToDynamicJson()),
                    contentType: @Html.Raw(Model.ContentType.ToJson()),
                    picklists: @Html.Raw(Model.ContentType.Field.Where(x => x.Type == "picklist").Select(x => new PicklistModel(x.RelatedContentTypeId.Value, cache.CreateMappedEntity<ContentItem, PicklistItemModel>().Where(y => y.ContentTypeId == x.RelatedContentTypeId))).ToJson()),
                    relationships: @Html.Raw(Model.ContentType.Field.Where(x => x.Type == "relationship").Select(x => new PicklistModel(x.RelatedContentTypeId.Value, cache.CreateMappedEntity<ContentItem, PicklistItemModel>().Where(y => y.ContentTypeId == x.RelatedContentTypeId).OrderBy(y => y.Name))).ToJson())
                };
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/EditContentItemForm" />
}
