@font-face {
    font-family: 'interstate-bold';
    src: url('../fonts/interstate-bold-webfont.eot');
    src: url('../fonts/interstate-bold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/interstate-bold-webfont.woff') format('woff'), url('/fonts/interstate-bold.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'interstate-light';
    src: url('../fonts/interstate-light-webfont.eot');
    src: url('../fonts/interstate-light-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/interstate-light-webfont.woff') format('woff'), url('../fonts/interstate-light.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'interstate-regular';
    src: url('../fonts/interstate-regular-webfont.eot');
    src: url('../fonts/interstate-regular-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/interstate-regular-webfont.woff2') format('woff2'), url('../fonts/interstate-regular-webfont.woff') format('woff'), url('../fonts/interstate-regular-webfont.ttf') format('truetype'), url('../fonts/interstate-regular-webfont.svg#interstateregular') format('svg');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'smartphlex';
    src: url('../fonts/smartphlex-20201119.eot?11755948');
    src: url('../fonts/smartphlex-20201119.eot?11755948#iefix') format('embedded-opentype'), url('../fonts/smartphlex-20201119.woff?11755948') format('woff'), url('../fonts/smartphlex-20201119.ttf?11755948') format('truetype'), url('../fonts/smartphlex-20201119.svg?11755948#smartphlex') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'MaterialIcons';
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/MaterialIcons-Regular.ttf);
    font-display:block;
}

@font-face {
    font-family: 'MaterialIconsOutlined';
    font-style: normal;
    font-weight: 400;
    src: url(../fonts/MaterialIconsOutlined-Regular.otf);
    font-display: block;
}
[class^="icon-"]:before, [class*=" icon-"]:before {
    font-family: "smartphlex";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-filter:before {
    content: '\f0b0';
}

.icon-zoom-out:before {
    content: '\e800';
}
/* '' */
.icon-cancel-circled:before {
    content: '\e801';
}
/* '' */
.icon-info-circled:before {
    content: '\e802';
}
/* '' */
.icon-link:before {
    content: '\e803';
}
/* '' */
.icon-trash:before {
    content: '\e804';
}
/* '' */
.icon-floppy:before {
    content: '\e805';
}
/* '' */
.icon-pencil:before {
    content: '\e806';
}
/* '' */
.icon-login:before {
    content: '\e807';
}
/* '' */
.icon-upload:before {
    content: '\e808';
}
/* '' */
.icon-download:before {
    content: '\e809';
}
/* '' */
.icon-task-list:before {
    content: '\f009';
}
/* '' */
.icon-pin:before {
    content: '\f031';
}
/* '' */
.icon-export:before {
    content: '\f081';
}
/* '' */
.icon-spin:before {
    content: '\e832';
}
/* '' */
.icon-globe:before {
    content: '\e80a';
}
/* '' */
.icon-phone:before {
    content: '\e80b';
}
/* '' */
.icon-mail:before {
    content: '\e80c';
}
/* '' */
.icon-file:before {
    content: '\f15b';
}
/* '' */
.icon-star:before {
    content: '\e80d';
}
/* '' */
.icon-group:before {
    content: '\e80e';
}
/* '' */
.icon-user:before {
    content: '\e80f';
}
/* '' */
.icon-eye:before {
    content: '\e813';
}
/* '' */
.icon-lock:before {
    content: '\e812';
}
/* '@' */
.icon-unlock:before {
    content: '\f13e';
}
/* '@' */
.icon-search:before {
    content: '\1f50d';
}
/* '🔍' */
.icon-copy:before {
    content: '\f0c5';
}

.icon-dollar:before {
    content: '\f155';
}
/* '@' */
.icon-attention:before {
    content: '\e810';
    color: #f18d39;
}

.icon-attention-large:before {
    font-size: 4rem;
    position: relative;
    top: 2rem;
}

.icon-warning:before {
    content: '\e810';
    color: #6C0000;
}

.icon-email:before {
    content: '@';
}
/* '@' */
.icon-tick:before {
    content: '\2714';
    color: #009aa8;
}

.icon-cross:before {
    content: '\2716';
    color: #af1e22 !important;
}

.icon-folder:before {
    content: '\e814';
}
/* '' */
.icon-folder-open:before {
    content: '\e816';
}
/* '' */
.icon-right-dir:before {
    content: '\e817';
}
/* '' */
.icon-down-dir:before {
    content: '\25be';
}
/* '▾' */
.icon-history:before {
    content: '\f1da';
}
/* '' */
.icon-cog:before {
    content: '\2699';
}
/* '⚙' */
.icon-level-up:before {
    content: '\f148';
    transform: scaleX(-1);
}
/* '' */
.icon-down:before {
    content: '\f175';
}
/* '' */
.icon-up:before {
    content: '\f175';
    transform: scaleY(-1);
}