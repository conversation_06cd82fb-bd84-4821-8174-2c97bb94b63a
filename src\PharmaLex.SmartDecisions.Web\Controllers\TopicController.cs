﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class TopicController : Controller
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly ITopicService topicService;
        private readonly IContentService contentService;

        public TopicController(IDistributedCacheServiceFactory cacheFactory, ITopicService topicService, IContentService dataService)
        {
            this.cacheFactory = cacheFactory;
            this.topicService = topicService;
            this.contentService = dataService;
        }

        [HttpGet("/topics")]
        public async Task<IActionResult> ListTopics()
        {
            if (!this.User.IsPharmaLexUser())
            {
                var cache = this.cacheFactory.CreateEntity<CompanyContentType>();
                var cct = await cache.WhereAsync(x => x.CompanyId == this.User.GetClaimValue<int>("plx:companyid"));
                var licensedTopics = cct.Select(x => x.ContentTypeId).ToArray();
                return Json(await topicService.ListTopics(licensedTopics));
            }
            return Json(await topicService.ListTopics());
        }

        [HttpGet("/topic/{id}")]
        public async Task<IActionResult> View(int id)
        {
            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == id);

            var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue));
            var cim = (await cic.WhereAsync(x => x.ContentTypeId == ctm.Id)).Select(x =>
            {
                x.ContentType = ctm;
                return x;
            });

            return View("ViewTopicDialog", new ViewContentItemsViewModel
            {
                ContentType = ctm,
                Items = cim
            });
        }

        [HttpGet("/topic/content/{id}")]
        public async Task<IActionResult> ListContent(int id)
        {
            return Json(await topicService.ListItemsByTopic(id, null));
        }

        [HttpGet("/topic/data/{contentId}/{topicId}/{id}")]
        public async Task<IActionResult> TopicData(int contentId, int topicId, int id, bool ownedRecordsOnly = false)
        {
            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
            var ctdc = cacheFactory.CreateMappedEntity<ContentTypeDisplay, ContentTypeDisplayModel>().Configure(x => x.Include(y => y.ContentType));
            var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == contentId);
            var fields = ctm.Field.Select(f => new { id = f.Id, name = f.Name });

            var item = (await topicService.ListItemsByTopic(contentId)).FirstOrDefault(t => t.Id == topicId);

            var picklists = new List<PicklistModel>();
            foreach (FieldModel fm in ctm.PicklistFields)
            {
                var listItems = await contentService.ListItemsByContentType<PicklistItemModel>(fm.RelatedContentTypeId.Value);
                picklists.Add(new PicklistModel(fm.RelatedContentTypeId.Value, listItems));
            }

            var ctd = ctdc.Where(x => x.ContentTypeId == contentId).FirstOrDefault(x => x.Id == id && x.ContentTypeDisplayTypeId == (int)ContentTypeDisplayType.View);
            var display = ctd?.Json != null ? JsonConvert.DeserializeObject<ViewContentTypeDisplayModel>(ctd.Json) : new ViewContentTypeDisplayModel();

            var contentItemsModel = (await cic.WhereAsync(x => x.ContentTypeId == ctm.Id && (!ownedRecordsOnly || x.Owner.ToLower() == this.User.Identity.Name.ToLower()))).Select(x =>
            {
                x.ContentType = ctm;
                x.Values = x.Values.Where(y => fields.Select(f => f.id).Contains(y.FieldId));
                return x;
            });

            var contentTypeRecord = contentItemsModel.Where(c => c.Id == topicId).Select(x =>
            {
                var pl = x.ContentType.Field.FirstOrDefault(y => y.Name == "Regulatory Authority");
                var fvm = x.Values.FirstOrDefault(y => y.FieldId == pl.Id);
                var plc = cacheFactory.CreateMappedEntity<ContentItem, PicklistItemModel>();
                fvm.Value = plc.FirstOrDefault(y => y.ContentTypeId == pl.RelatedContentTypeId && y.Id == Int32.Parse(fvm.Value))?.Name;

                return new { Value = fvm.Value };
            }).FirstOrDefault();

            var twoLetterCountryCode = contentTypeRecord.Value.Substring(0, 2);

            var country = (await contentService.ListItemsByContentType<CountryModel>(x => x.TwoLetterCode == twoLetterCountryCode)).FirstOrDefault();
            var authorities = await contentService.ListItemsByContentType<RegulatoryAuthorityModel>(x => x.Countries.Contains(country.Id));
            var marketIds = authorities.Select(x => x.Market).ToArray();
            var markets = await contentService.ListItemsByContentType<CountryModel>(x => marketIds.Contains(x.Id));

            var dtm = new DisplayTopicViewModel()
            {
                ContentType = ctm,
                Display = display,
                SelectedItem = item,
                Picklists = picklists,
                Items = new List<TopicItemModel> { item },
                RegulatoryAuthorities = authorities,
                Markets = markets,
                Country = country
            };

            dtm.Display.MergeData(dtm.ContentType, dtm.SelectedItem, dtm.Picklists);

            return View("DisplayTopicDialog", dtm);
        }

        [HttpGet("topic/{id}/{mapId}")]
        public async Task<IActionResult> ViewByCountry(int id, int mapId, int itemId = -1)
        {
            var cache = cacheFactory.Create<DisplayTopicViewModel>("ContentItem", "FieldValue", "ContentTypeDisplay");

            DisplayTopicViewModel dtm = await cache.GetOrCreateAsync($"DTVM-{id}-{mapId}", async () =>
            {
                var country = (await contentService.ListItemsByContentType<CountryModel>(x => x.MapId == mapId)).FirstOrDefault();
                var authorities = await contentService.ListItemsByContentType<RegulatoryAuthorityModel>(x => x.Countries.Contains(country.Id));
                var marketIds = authorities.Select(x => x.Market).ToArray();
                var markets = await contentService.ListItemsByContentType<CountryModel>(x => marketIds.Contains(x.Id));
                var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
                var ct = await ctc.FirstOrDefaultAsync(x => x.Id == id);
                var authorityIds = authorities.Select(x => x.Id).ToArray();
                var items = (await topicService.ListItemsByTopic(id, x => authorityIds.Contains(x.AuthorityId))).OrderBy(x => x.VerifiedDate);

                List<PicklistModel> picklists = new List<PicklistModel>();
                foreach (FieldModel fm in ct.PicklistFields)
                {
                    var listItems = await contentService.ListItemsByContentType<PicklistItemModel>(fm.RelatedContentTypeId.Value);
                    picklists.Add(new PicklistModel(fm.RelatedContentTypeId.Value, listItems));
                }



                var ctdc = cacheFactory.CreateEntity<ContentTypeDisplay>();
                var ctd = ctdc.Where(x => x.ContentTypeId == id).FirstOrDefault(x => x.ContentTypeDisplayTypeId == (int)ContentTypeDisplayType.View);
                var display = ctd?.Json != null ? JsonConvert.DeserializeObject<ViewContentTypeDisplayModel>(ctd.Json) : new ViewContentTypeDisplayModel();

                return new DisplayTopicViewModel
                {
                    ContentType = ct,
                    Display = display,
                    Items = [.. items],
                    RegulatoryAuthorities = authorities,
                    Markets = markets,
                    Country = country,
                    Picklists = picklists,
                    SelectedItem = items.FirstOrDefault()
                };
            });

            if (itemId > 0)
            {
                dtm.SelectedItem = dtm.Items.FirstOrDefault(x => x.Id == itemId);
            }
            dtm.Display.MergeData(dtm.ContentType, dtm.SelectedItem, dtm.Picklists);

            return View("DisplayTopicDialog", dtm);
        }
    }
}
