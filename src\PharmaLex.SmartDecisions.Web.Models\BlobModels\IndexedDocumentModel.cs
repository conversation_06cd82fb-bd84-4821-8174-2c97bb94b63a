﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class IndexedDocumentModel
    {
        [SearchableField(AnalyzerName = LexicalAnalyzerName.Values.EnMicrosoft), JsonPropertyName("content")]
        public string Content { get; set; }

        [Key]
        [JsonPropertyName("key")]
        public string Key { get; set; }

        [SearchableField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("id")]
        public string Id { get; set; }

        [SimpleField(IsSortable = true, IsFilterable = true, IsFacetable = true), JsonPropertyName("locale")]
        public string Locale { get; set; }

        [SimpleField(IsSortable = true, IsFilterable = true, IsFacetable = true), JsonPropertyName("localeId")]
        public string LocaleId { get; set; }

        [JsonPropertyName("metadata_storage_content_type")]
        public string StorageContentType { get; set; }

        [JsonPropertyName("metadata_storage_path")]
        public string StoragePath { get; set; }

        [SearchableField(SearchAnalyzerName = LexicalAnalyzerName.Values.StandardAsciiFoldingLucene, IndexAnalyzerName = "standard"), SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("base64Title")]
        public string Base64Title { get; set; }

        [SearchableField(AnalyzerName = "custom-analyzer"), SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("searchableTitle")]
        public string SearchableTitle { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("newsArticleId")]
        public string NewsArticleId { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("publishingStateId")]
        public string PublishingStateId { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("friendlyUrl")]
        public string FriendlyUrl { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("newsSourceId")]
        public string NewsSourceId { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("sourceUrl")]
        public string SourceUrl { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("sourcePublicationDate")]
        public DateTime? SourcePublicationDate { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("categories")]
        public string Categories { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("importanceId")]
        public string ImportanceId { get; set; }

        [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true), JsonPropertyName("golivedate")]
        public DateTime? GoLiveDate { get; set; }
    }
}
