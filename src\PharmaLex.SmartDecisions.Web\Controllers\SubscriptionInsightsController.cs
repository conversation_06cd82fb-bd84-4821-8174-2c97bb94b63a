﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "InsightsManager")]
    public class SubscriptionInsightsController : BaseController
    {
        private readonly IRepositoryFactory repoFactory;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;

        public SubscriptionInsightsController(
            IRepositoryFactory repoFactory,
            IDistributedCacheServiceFactory cache,
            IMapper mapper)
        {
            this.repoFactory = repoFactory;
            this.cache = cache;
            this.mapper = mapper;
        }

        [HttpGet("/manage/subscription-insights")]
        public async Task<IActionResult> SubscriptionInsightsAsync()
        {
            var usersRepo = this.repoFactory.Create<User>().Configure(o => o
                .Include(x => x.UserClaim)
                .ThenInclude(x => x.Claim)
                .Include(x => x.CompanyUser)
                .ThenInclude(x => x.Company));

            var users = mapper.ProjectTo<UserModel>(usersRepo).ToList();

            var subscriptions = await this.cache
                   .CreateMappedEntity<NewsletterSubscription, NewsletterSubscriptionModel>()
                   .Configure(x => x
                        .Include(y => y.User))
                   .AllAsync();

            var categories = await this.cache
                    .CreateMappedEntity<UserNewsCategory, UserNewsCategoryModel>()
                    .Configure(x => x
                        .Include(y => y.NewsCategory)
                        .Include(y => y.User))
                    .AllAsync();

            var model = new List<SubscriptionInsightModel>();

            foreach (var user in users)
            {
                var products = categories.Where(x => x.User.Id == user.Id && x.NewsCategory.GroupId == ((int)NewsCategoryGroup.Products)).OrderBy(uc => uc.Order).Select(x => x.NewsCategory.Name).ToArray();
                var themes = categories.Where(x => x.User.Id == user.Id && x.NewsCategory.GroupId == ((int)NewsCategoryGroup.Themes)).OrderBy(uc => uc.Order).Select(x => x.NewsCategory.Name).ToArray();
                var languages = categories.Where(x => x.User.Id == user.Id && x.NewsCategory.GroupId == ((int)NewsCategoryGroup.NewsletterArticleLanguage)).OrderBy(uc => uc.Order).Select(x => x.NewsCategory.Name).ToArray();
                var geographicalScope = categories.Where(x => x.User.Id == user.Id && x.NewsCategory.GroupId == ((int)NewsCategoryGroup.GeographicalScope)).OrderBy(uc => uc.NewsCategory.Name).Select(x => x.NewsCategory.Name).ToArray();
                var daysToReceive = subscriptions.Where(x => x.User.Id == user.Id && !x.IsInfoflash && !x.IsMonthly && x.Active).OrderBy(x => x.DeliveryLocalDay).Select(x => ((DayOfWeek)x.DeliveryLocalDay).GetDescription()).Distinct().ToArray();

                var subscriptionInsight = new SubscriptionInsightModel()
                {
                    Id = user.Id,
                    Company = user.CompanyName,
                    User = user.DisplayFullName,
                    Email = user.Email,
                    Products = products.Length > 0 ? products : ["None"],
                    Themes = themes.Any() ? themes : ["None"],
                    Languages = languages.Any() ? languages : ["None"],
                    GeographicalScope = geographicalScope.Any() ? geographicalScope : ["None"],
                    DaysToReceive = daysToReceive.Any() ? daysToReceive : ["None"],
                    IsInfoflash = subscriptions.FirstOrDefault(x => x.User?.Id == user.Id && x.IsInfoflash && x.Active)?.IsInfoflash ?? false,
                    IsMonthlyMiscNewsletter = subscriptions.FirstOrDefault(x => x.User?.Id == user.Id && x.IsMonthly && x.Active)?.IsMonthly ?? false,
                    LastLoginDate = user.LastLoginDate,
                };
                model.Add(subscriptionInsight);
            }

            return this.View(model);
        }
    }
}
