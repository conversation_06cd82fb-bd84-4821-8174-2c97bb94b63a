﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class ContentTypeAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public string PluralName { get; set; }
        public string ShortName { get; set; }
        public int? ContentTypeCategoryId { get; set; }
        public string Owner { get; set; }
        public bool? System { get; set; }
        public bool? AutoManageName { get; set; }
    }
}
