﻿using PharmaLex.DataAccess;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class ContentTypeDisplay : IEntity
    {
        [NotMapped]
        public ContentTypeDisplayType DisplayType
        {
            get { return (ContentTypeDisplayType)this.ContentTypeDisplayTypeId; }
            set { this.ContentTypeDisplayTypeId = (int)value; }
        }
    }
}
