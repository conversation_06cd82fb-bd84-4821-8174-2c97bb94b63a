﻿@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Localization
@using Microsoft.AspNetCore.Builder
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.Authentication.B2C
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Options
@using PharmaLex.SmartDecisions.Web.Helpers.Extensions

@inject IAuthorizationService AuthorizationService
@inject AppSettingsHelper AppSettings
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject IOptions<RequestLocalizationOptions> LocOptions
@{
    var requestCulture = this.Context.Features.Get<IRequestCultureFeature>();
    var cultureItems = LocOptions.Value.SupportedUICultures
        .Select(c =>
        {
            string lang = c.NativeName.Split('(', StringSplitOptions.TrimEntries)[0];
            return new SelectListItem
                    {
                        Value = c.Name,
                        Text = $"{lang.First().ToString().ToUpper()}{lang.Substring(1)}"
                    };
        }).ToList();

    var returnUrl = string.IsNullOrEmpty(Context.Request.Path) ? "~/" : $"~{Context.Request.Path.Value}";
    var isLoginView = ViewBag.ShowBackground ?? false;
    var vueAppClass = string.Empty;
    if (!isLoginView)
    {
        vueAppClass = "core-content";
    }
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="SmartDECISIONS - Regulatory Intelligence">
    <meta name="apple-mobile-web-app-title" content="PharmaLex">
    <meta name="application-name" content="PharmaLex">
    <meta name="theme-color" content="#233c4c">
    <link rel="shortcut icon" type="image/png" href="@VersionCdn.GetUrl("images/favicon/cencora-favicon.png")">
    <title>SmartDECISIONS :: @ViewData["Title"]</title>
    <link rel="stylesheet" href="@VersionCdn.Host/css/fonts.css" asp-append-version="true" />
    <link rel="stylesheet" href="@VersionCdn.GetUrl("css/shared.css")" asp-append-version="true" />
    <link rel="stylesheet" href="@VersionCdn.GetUrl("css/login.css")" asp-append-version="true" />
    <link rel="stylesheet" href="/dist/css/smartDecisionsCss.css" asp-append-version="true" />
    <link rel="stylesheet" href="/dist/css/filteredTablesCss.css" asp-append-version="true" />
    <link rel="stylesheet" href="/dist/css/mapsCss.css" asp-append-version="true" />
 
    <script src="@VersionCdn.GetUrl("js/plx.js")"></script>
    <script src="@VersionCdn.GetUrl("js/toast.js")"></script>
    <script src="@VersionCdn.GetUrl("js/navbar.js")"></script>
    <script src="@VersionCdn.GetUrl("lib/vue/3.2.6/vue.prod.js")"></script>
    <script src="@VersionCdn.GetUrl("lib/tippy/popper/1.15/popper.min.js")"></script>
    <script src="@VersionCdn.GetUrl("lib/tippy/4.3.5/tippy.min.js")"></script>
    <script src="/js/nav.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <div class="core-container">
        @if (!isLoginView)
        {
            <header id="page-header" class="page-header">
                <div class="app-title-wrapper">
                    <a class="app-title" href="/?mp=true">
                        <h1 class="mb-0"><span>Knowledge.</span><span class="app-title-bold">Accelerated.</span></h1>
                    </a>
                </div>

                @if (User.Identity.IsAuthenticated)
                {
                    <nav class="main-navigation">
                        <ul class="nav-menu main-nav">
                            @if ((await AuthorizationService.AuthorizeAsync(User, "Admin")).Succeeded ||
                                 (await AuthorizationService.AuthorizeAsync(User, "CompanyManager")).Succeeded ||
                                 (await AuthorizationService.AuthorizeAsync(User, "InsightsManager")).Succeeded )
                            {
                            <li>
                                <a>Manage <i class="m-icon">expand_more</i></a>
                                <div class="flyout">
                                    <ul>
                                        @if ((await AuthorizationService.AuthorizeAsync(User, "SystemAdmin")).Succeeded)
                                        {
                                            <li><a href="/manage/content-type">Content Types</a></li>
                                        }
                                        @if ((await AuthorizationService.AuthorizeAsync(User, "CompanyAdmin")).Succeeded)
                                        {
                                            <li><a href="/manage/companies">Companies</a></li>
                                        }
                                        @if ((await AuthorizationService.AuthorizeAsync(User, "UserAdmin")).Succeeded)
                                        {
                                            <li><a href="/manage/users">Admins</a></li>
                                        }
                                        @if ((await AuthorizationService.AuthorizeAsync(User, "CompanyManager")).Succeeded)
                                        {
                                            <li><a href="/users">@ls.Localise("(news).users.manage")</a></li>
                                        }
                                        @if ((await AuthorizationService.AuthorizeAsync(User, "InsightsManager")).Succeeded)
                                        {
                                            <li><a href="/manage/newsletter-insights">Newsletter Insights</a></li>
                                        }
                                        @if ((await AuthorizationService.AuthorizeAsync(User, "InsightsManager")).Succeeded)
                                        {
                                            <li><a href="/manage/subscription-insights">Subscription Insights</a></li>
                                        }
                                    </ul>
                                </div>
                            </li>
                            }
                            @if ((await AuthorizationService.AuthorizeAsync(User, "SystemAdmin")).Succeeded)
                            {
                                <li class="full-menu">
                                    <a>Data<i class="m-icon">expand_more</i></a>
                                    <div class="flyout">
                                        @{
                                            int i = 0;
                                            var contentTypes = (await cache.CreateEntity<ContentType>
                                            ().AllAsync()).OrderBy(x => x.Name);

                                            var groupCount = Enum.GetNames(typeof(ContentTypeCategory)).Length;
                                            var flexItemClass = "flex-x" + groupCount;

                                            foreach (ContentTypeCategory t in Enum.GetValues(typeof(ContentTypeCategory)))
                                            {
                                                <ul class="flex-item @flexItemClass @(i++ == 0 ? "selected" : "")">

                                                    <li><h3 class="sub-brand-color underline-block mb-2 pb-1">@t.GetDescription()</h3></li>
                                                    <li>
                                                        <ul>
                                                            @foreach (var ct in contentTypes.Where(x => x.Category == t))
                                                            {
                                                                <li><a href="/data/@ct.Id">@ct.Name</a></li>
                                                            }
                                                        </ul>
                                                    </li>

                                                </ul>
                                            }
                                        }
                                    </div>
                                </li>
                            }

                            @if ((await AuthorizationService.AuthorizeAsync(User, "NewsSubscriber")).Succeeded)
                            {
                                <li>
                                    <a>@ls.LocaliseSafe("(news).(menu).news-list")<i class="m-icon">expand_more</i></a>
                                    <div class="flyout">
                                        <ul>
                                            @if ((await AuthorizationService.AuthorizeAsync(User, "NewsAuthor")).Succeeded)
                                            {
                                                <li><a href="/articles">@ls.LocaliseSafe("(news).(menu).news-list")</a></li>
                                            }
                                            <li><a href="/newsletter/subscribe">@ls.LocaliseSafe("(news).(menu).subscription")</a></li>
                                            <li><a href="/articles/search">@ls.LocaliseSafe("(news).(menu).search-articles")</a></li>

                                        </ul>
                                    </div>
                                </li>
                            }

                            @if ((await AuthorizationService.AuthorizeAsync(User, "RecordOwner")).Succeeded)
                            {
                                <li class="no-menu">
                                    <a href="/records">My Records</a>
                                </li>
                            }

                            <li>
                                <a>@ls.LocaliseSafe("help")<i class="m-icon">expand_more</i></a>
                                <div class="flyout">
                                    <ul>
                                        <li class="data-nav-item">
                                            <a target="_blank" href="https://cencoragcs.zendesk.com/hc/en-gb">Contact</a>
                                        </li>
                                        <li><a href="/help">@ls.Localise("about")</a></li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </nav>
                    <nav class="account-navigation" aria-label="account navigation">
                        <ul class="nav-menu">
                            <li>
                                <a>
                                    <span class="profile-picture">account_circle</span>
                                    @User.GetName()
                                </a>
                                <div class="flyout">
                                    <ul>
                                        @if (User.IsPharmaLexUser())
                                        {
                                            <li><a href="https://myaccount.microsoft.com" target="_blank">@ls.Localise("(news).(account).view-account")</a></li>
                                        }
                                        else
                                        {
                                            <li><a href="/MicrosoftIdentity/Account/ResetPassword">@ls.Localise("(news).(account).reset-password")</a></li>
                                        }
                                        <li><a href="/signout">@ls.Localise("(news).(account).sign-out")</a></li>
                                        <form id="selectLanguage"
                                              asp-controller="Home"
                                              asp-action="SetLanguage"
                                              asp-route-returnUrl="@returnUrl"
                                              method="post"
                                              class="flex flex-align-center">
                                            <div class="form-group">
                                                <div class="custom-select">
                                                    <select name="culture"
                                                            onchange="this.form.submit();"
                                                            asp-for="@requestCulture.RequestCulture.UICulture.Name" asp-items="cultureItems">
                                                    </select>
                                                </div>
                                            </div>
                                        </form>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </nav>
                }
                else
                {
                    @if (this.ViewData["unauthorised"] == null && this.ViewData["landing"] == null)
                    {
                        <div class="no-account">
                            <span class="mr-10">@ls.Localise("preferences-login")</span>
                            <a id="signIn" href="/Login" class="button ml-4">@ls.Localise("(news).(account).sign-in")</a>
                        </div>
                    }
                }
            </header>
        }

        <div id="vue-app" class="@vueAppClass">
            @RenderBody()
        </div>
    </div>

    <script type="text/javascript">
        function setImportanceImage(article) {
            article.importanceType = { title: '', icon: '' };
            if (article.importanceId == @((int)NewsArticleImportance.Headline)) {
                article.importanceType = {
                    title: '@ls.Localise("(news).headline")',
                    icon: 'campaign'
                };
            }
            else if (article.importanceId == @((int)NewsArticleImportance.Miscellaneous)) {
                article.importanceType = {
                    title: '@ls.Localise("[NewsArticleImportance].miscellaneous")',
                    icon: 'pending'
                };
            }
            else if (article.importanceId == @((int)NewsArticleImportance.INFOFLASH)) {
                article.importanceType = {
                    title: '@ls.Localise("[NewsArticleImportance].infoflash")',
                    icon: 'info'
                };
            }
            else {
                article.importanceType = {
                    title: '@ls.Localise("[NewsArticleImportance].newsletter")',
                    icon: 'feed'
                };
            }
        }

        function loadPdfContent(contentBase64) {
            const pdfjsframe = document.getElementById('iframeContent');

            if (pdfjsframe) {
                pdfjsframe.onload = function () {
                    const newsArticleContent = atob(contentBase64);
                    const uint8Array = new Uint8Array(newsArticleContent.length);
                    for (let i = 0; i < newsArticleContent.length; i++) {
                        uint8Array[i] = newsArticleContent.charCodeAt(i);
                    }

                    pdfjsframe.contentWindow.PDFViewerApplicationOptions.set("defaultUrl", "");
                    const pdfJsApp = pdfjsframe.contentWindow.PDFViewerApplication;
                    if (pdfJsApp && pdfJsApp.initialized) {
                        pdfJsApp.open({ data: uint8Array });
                    }
                }
            }
        }
    </script>
    
    <script type="text/javascript">
        const signInLink = document.getElementById('signIn');
        if (signInLink) {
            signInLink.href = `${signInLink}?path=${window.location.pathname}`;

        }

        const languages = [...document.getElementsByClassName('language-selector')];
        languages.forEach(x => x.addEventListener('click', (e) => {
            e.preventDefault();
            fetch(`/setlanguage/${e.currentTarget.dataset.locale}`, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(_ => {
                window.location.assign(window.location.href);
            });
        }));
    </script>
    @await RenderSectionAsync("Scripts", required: false)
    
    <script>
        if (typeof pageConfig !== 'undefined' && pageConfig.appElement) {
            var vueApp = Vue.createApp({ mixins: [pageConfig] });
        }
    </script>
    
    @await RenderSectionAsync("VueComponentScripts", required: false)
    
    <script>
        //mount vue instance here with components now attached(if they exist)
        if (typeof pageConfig !== 'undefined' && pageConfig.appElement) {
            var vueRootComponent = vueApp.mount(pageConfig.appElement);
        }
    </script>
    
    @if (this.TempData.TryGet<UserNotificationModel>("UserNotification", out UserNotificationModel un))
    {
        <script type="text/javascript">
        plx.toast.show('@Html.Raw(un.Html)', @un.Position, '@un.Type.ToLower()', @(un.Number.HasValue ? un.Number.ToString() : "null"), @un.Duration, @Html.Raw(Json.Serialize(un.Options)));
    </script>
    }
    </body>
</html>
