﻿@model ContentTypeModel
@{
    ViewData["Title"] = "Delete ContentType";
}
<div class="sub-header">
    <h2>Delete <em>@Model.Name</em> Content Type</h2>
    <div class="controls">
        <a class="button secondary" href="/manage/content-type/edit/@Model.Id">@Model.Name Content Type</a>
    </div>
</div>

<section>
    <form method="post">
        <div class="flex mb-4">
            <i class="m-icon warning-color">warning</i>
            <p class="lead m-0 p-0 pl-1"><strong>Warning:</strong> Deleting is permanent and once deleted the Content Type cannot be restored. All fields belonging to this Content Type will also be deleted.</p>
        </div>

        <p class="lead">Are you sure you want to delete the ContentType '<strong>@Model.Name</strong>'?</p>
        <div class="buttons">
            <a class="button secondary" href="/manage/content-type/edit/@Model.Id">Cancel</a>
            <button type="submit">Delete</button>
        </div>
    </form>
</section>
    
