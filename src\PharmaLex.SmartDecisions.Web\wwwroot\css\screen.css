:root {
    --main-theme-color: #009aa8;
    --background-image: url(../images/splash.png);
}

/* Elements */
html, body {
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    text-align: center;
    margin: auto;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.5rem;
    color: #233c4c;
}

h1 {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 1rem;
}

h2 {
    font-size: 1.8rem;
}

h3 {
    font-size: 1.6rem;
}

h4 {
    font-size: 1rem;
    color: #515c65;
}

h5 {
    font-size: 1.2rem;
}

h6 {
    font-size: 1.2rem;
    margin-bottom: 0;
    margin-top: 0.5rem;
}

p, address {
    margin: 0.5rem 0;
    line-height: 1.2rem;
}

em + address {
    margin-top: 0;
}

a {
    color: var(--main-theme-color);
    text-decoration: none;
}

    a:hover {
        text-decoration: underline;
    }

    a.action {
        cursor: pointer;
    }

button, .button {
    background-color: var(--main-theme-color);
    color: #fff;
    text-align: center;
    border: 1px solid var(--main-theme-color);
    padding: 5px 10px;
    font-weight: bold;
    cursor: pointer;
    font-size: 1rem;
    margin-right: 0.5rem;
    line-height: 16px;
    display: inline-block;
}

button.secondary, a.secondary {
    background-color: #7a8b98;
    border-color: #7a8b98;
    color: #fff;
}

.icon-only-button {
    color: var(--main-theme-color);
    cursor: pointer;
}

.icon-only-button.disabled {
    color: lightgray;
    cursor: default;
}

a.button:hover {
    text-decoration: none;
}

.button-large {
    font-size: 2rem;
    padding: 2rem;
}

.data-table-actions {
    width: 60px;
    text-align: center;
    cursor: pointer;
}
.data-table-add-button {
    margin-left: 8px;
}

[class^="icon-button"]:before, [class*=" icon-button"]:before, [class^="icon-button"]:after, [class*=" icon-button"]:after {
    position: relative;
    display: inline-block;
    left: -0.3rem;
    font-weight: normal;
}

[class^="icon-button"]:after, [class*=" icon-button"]:after {
    left: 0.3rem;
}

.icon-button-search:before {
    content: '\1f50d';
}

.icon-button-login:before {
    content: '\e807';
}

.icon-button-back:before {
    content: '\27A4';
    transform: rotate(180deg);
    top: 0.05rem;
}

.icon-button-next:after {
    content: '\27A4';
}

.icon-button-add:before {
    content: '\271A';
    top: 0.05rem;
}

.icon-button-cancel:before {
    content: '\2716';
    left: -0.2rem;
    top: -0.05rem;
}

.icon-button-delete:before {
    font-family: 'smartphlex';
    content: '\e804';
    font-size: 0.8rem;
    top: -0.05rem;
}

.icon-button-save:before {
    font-family: 'smartphlex';
    content: '\e805';
    font-size: 0.8rem;
    top: -0.1rem;
}

.icon-button-edit:before {
    font-family: 'smartphlex';
    content: '\e806';
    font-size: 0.8rem;
    top: -0.1rem;
}

.icon-button-copy:before {
    font-family: 'smartphlex';
    content: '\f0c5';
    font-size: 0.8rem;
    top: -0.05rem;
}

.icon-button-upload:before {
    font-family: 'smartphlex';
    content: '\e808';
    font-size: 0.8rem;
    top: -0.1rem;
}

.icon-button-download:before {
    font-family: 'smartphlex';
    content: '\e809';
    font-size: 0.8rem;
    top: -0.1rem;
}

.icon-button-export:before {
    font-family: 'smartphlex';
    content: '\f801';
    font-size: 0.8rem;
    top: -0.1rem;
}

.icon-button-info:before {
    font-family: 'smartphlex';
    content: '\e802';
    font-size: 0.8rem;
}

.icon-button-spin:before {
    font-family: 'smartphlex';
    content: '\e832';
    font-size: 0.8rem;
}

.icon-button-list:before {
    font-family: 'smartphlex';
    content: '\f009';
    font-size: 0.9rem;
}

.icon-button-tick:before {
    content: '\2714';
}

.icon-button-tack:before {
    content: '\e811';
}

.icon-button-file:before {
    content: '\f15b';
}

.icon-button-lock:before {
    content: '\e812';
}
/* '@' */
.icon-button-unlock:before {
    content: '\f13e';
}
/* '@' */

.block-buttons {
    text-align: center;
    margin: 2rem 0;
}

ul {
    list-style-image: url('../images/bullet.png');
    margin-left: 2rem;
}

ol {
    margin-left: 2rem;
}

li {
    margin-bottom: 0.5rem;
}

li.more {
    list-style-image: url('../images/bullet_plus.png');
}

li.less {
    list-style-image: url('../images/bullet_minus.png');
}

.icon-list {
    list-style-image: none;
    list-style-type: none;
    margin-left: 0.5rem;
    text-align: left;
    display: inline-block;
}

.icon-list li {
    margin-bottom: 1.5rem;
}

.icon-list li:last-child {
    margin-bottom: 0;
}

.icon-list a[href^=http], .icon-list a[href^=mailto] {
    margin-left: 3px;
}

.list-block {
    display: flex;
    justify-content: space-around;
}

address {
    font-style: normal;
}

hr {
    border-color: #c7d8e5;
}

dt {
    font-weight: bold;
    margin: 1.2rem 0.2rem 0.2rem 0;
}

/* Header */
.page-header {
    height: 60px;
    background-color: #fff;
    border-bottom: 4px solid var(--main-theme-color);
    position: relative;
    z-index: 1000;
}

.page-header-content {
    max-width: 1950px;
    height: 100%;
    text-align: left;
    margin: auto;
    display: flex;
    flex: 1 1 auto;
    flex-flow: row nowrap;
    justify-content: space-between;
}

.page-header-content .logo {
    display: flex;
    align-items: center;
    margin-left: auto;
}
.page-header-content .logo:last-child {
    margin-left: 12%;
}

.page-header-title {
    background-color: var(--main-theme-color);
    color: #fff;
    min-width: 150px;
    justify-content: center;
    display: flex;
    align-content: center;
    align-items: center;
    margin-left: 2rem;
    padding: 0 1rem;
    font-weight: bold;
    font-size: 1.5rem;
}
.account {
    margin-right: 13%;
}
.no-account {
    position: absolute;
    right: 12.5%;
    margin-top: 1.3%;
    text-transform: uppercase;
}
.page-header .account, .page-header {
    display: flex;
    margin-left: auto;
    align-items: center;
}
.page-header-content nav:last-child {
    margin-right: auto;
}
.profile-display {
    display: flex;
    align-items: center;
    margin-top: 3px;
}
.page-header .account img, .profile-picture {
    border-radius: 50%;
    height: 40px;
    width: 40px;
    margin: 5px 10px;
}
.account-nav {
    padding-top: 0px;
    align-items: center;
}
.account-nav li.has-children {
    padding-top: 0px;
}


.main-nav {
    display: flex;
    height: 100%;
    text-transform: uppercase;
    white-space: nowrap;
}


.main-nav li {
    padding-top: 21px;
    position: relative;
    margin-bottom: 0;
}

.main-nav > li.has-children > span:after, .main-nav > li.has-children > a:after, .account-nav span:after {
    content: '^';
    color: #ccc;
    display: inline-block;
    transform: rotate(180deg);
    position: relative;
    left: 2px;
    top: -3px;
}

.main-nav a, .main-nav span {
    padding: 0;
    margin: 0 1rem;
    transition: all 0.3s ease 0s;
    font-family: 'interstate-regular', Helvetica, Arial, Verdana, sans-serif;
    color: #5f5f5d;
    font-size: 17px;
}

.main-nav > li:hover > a, .main-nav > li:hover > span {
    text-decoration: none;
    border-bottom: 2px solid var(--main-theme-color);
}

.main-sub-nav-container, .main-mega-menu-container {
    background-color: transparent;
    padding-top: 1.5rem;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease 0s;
    height: 0;
    max-width: 110px;
}
.account-nav .main-sub-nav-container {
    padding-top: 7px;
    padding-left: 40px;
    max-width: 150px;
}
.main-sub-nav, .main-tertiary-nav {
    display: block;
    background-color: #333;
    color: #fff;
    margin: 0 1rem;
    min-width: 150%;
    padding: 0;
    text-transform: none;
}
.main-mega-menu-container {
    width: 900px;
    min-height: 350px;
    left: -350px;
    position: relative;
    max-width: unset;
}
.main-mega-menu-container .main-sub-nav {
    background: linear-gradient(#414141, #414141) no-repeat 200px/1px 100%;
    background-color: #333;
    min-height: 350px;
    min-width: unset;
    width: 900px;
}
.main-mega-menu-container .main-sub-nav > li, .main-mega-menu-container .main-sub-nav > li:last-child {
    padding: 1rem;
    width: 200px;
    height: 3rem;
    border-bottom: 1px solid #414141;
    text-transform: uppercase;
    position: relative;
    cursor: pointer;
}
.main-mega-menu-container .main-sub-nav > li:focus, .main-mega-menu-container .main-sub-nav > li:focus:last-child {
    background-color: #414141;
}
.main-mega-menu-container .main-sub-nav > li ul {
    display: none;
    position: absolute;
    left: 201px;
    top: 0;
    margin: 0 0 0 2rem;
    list-style-image: url('../images/bullet.png');
    column-count: 2;
    width: 700px;
    padding-top: 1rem;
}
.main-mega-menu-container .main-sub-nav > li ul li {
    padding: 0 0 0.5rem 0;
    display: list-item;
    white-space: normal;
    column-break-inside: avoid;
}
.main-mega-menu-container .main-sub-nav > li ul li a{
    display: inline-block;
}
.main-mega-menu-container .main-sub-nav > li.selected {
    background-color: #6d6d6d;
}
.main-mega-menu-container .main-sub-nav > li.selected ul {
    display: block;
}

.main-mega-menu-container a {
    margin-left: 0;
}

.account-sub-nav {
    background-color: #333;
    color: #fff;
    border-top: solid 0.5rem #fff;
    display: block;
    visibility: hidden;
    margin: 0;
    padding-bottom: 0.5rem;
    transition: opacity 0.3s ease 0s;
    text-transform: none;
    opacity: 0;
}

.main-nav a:hover + .main-sub-nav-container, 
.main-nav a:hover + .main-mega-menu-container, 
.main-nav span:hover + .main-sub-nav, 
.main-sub-nav-container:hover, 
.main-mega-menu-container:hover, 
.main-sub-nav:hover, 
span:hover + .account-sub-nav, 
.account-sub-nav:hover, 
.profile-display:hover + .main-sub-nav-container{
    visibility: visible;
    opacity: 1;
    height: auto;
}

.main-sub-nav li, .account-sub-nav li {
    padding: 0.5rem 0.5rem 0 0.5rem;
    position: relative;
    white-space: nowrap;
    display: block;
}

.main-sub-nav li:last-child {
    padding: 0.5rem;
}

.main-sub-nav > li.has-children > a:after, .main-sub-nav > li.has-children > span:after {
    content: '>';
    position: absolute;
    right: 12px;
}

.main-sub-nav a, .main-sub-nav span, .account-sub-nav a {
    text-align: left;
    padding: 0.5rem;
    color: #fff;
    font-size: 0.9rem;
    display: block;
}

.main-sub-nav a:hover, .account-sub-nav a:hover {
    background-color: #414141;
    color: #b7b7b7;
    border-bottom-color: var(--main-theme-color);
    text-decoration: none;
}

.main-tertiary-nav {
    position: absolute;
    padding-top: 0;
    border-top: none;
    min-width: 100%;
    left: 20%;
    top: 0;
    margin: 0 0 0 calc(80% + 1px);
}

.main-sub-nav li:hover > .main-tertiary-nav, .main-tertiary-nav:hover {
    visibility: visible;
    opacity: 1;
}

.app-info {
    margin-right: 10%;
}

/* Footer */
.page-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 40px;
    background-color: #fff;
    flex: 0 1 auto;
    border-top: 1px solid var(--main-theme-color);
    display: flex;
    justify-content: space-between;
}

.page-footer p {
    line-height: 1.7rem;
}

@media screen and (max-width: 1950px) {
    .page-footer .logo, .page-footer .p {
        margin-left: 13%;
    }
}

/* Map */
.map-surface {
    background-color: #f9f9f9;
}

.map-container {
    max-width: 1540px;
    width: 100%;
    display: inline-block;
    position: relative;
    min-height: 100px;
    z-index: 500;
    background-color: #f9f9f9;
}

.map-button {
    position: absolute;
    width: 60px;
    height: 55px;
    padding-top: 10px;
    background-color: var(--main-theme-color);
    color: #fff;
    border: 2px solid #fff;
    border-radius: 5px;
    cursor: pointer;
    z-index: 600;
}

.map-button i {
    font-size: 1.2rem;
    display: block;
}

.map-button span {
    font-size: 0.8rem;
}

.reset-zoom-button {
    top: -5px;
    left: 50%;
    margin-left: -25px;
    border-top: none;
    text-align: center;
}

.svg-map {
    width: 100%;
    height: 100%;
}

.country-path {
    fill: rgb(122, 139, 152);
    stroke: #fff;
    stroke-width: 0.02em;
    cursor: pointer;
}

    .country-path:hover {
        fill: rgb(81, 92, 101);
    }

.country-path-zoomed {
    stroke-width: 0.01em;
}

.country-path-max-zoomed {
    stroke-width: 0.005em;
}

.country-centroid {
    fill: transparent;
    pointer-events: none;
}

.map-tooltip {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 5px;
    padding: 10px;
    z-index: 700;
    pointer-events: none;
    text-align: left;
}

.map-tooltip:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 40%;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 7px solid rgba(255, 255, 255, 0.9);
    z-index: 700;
}

.map-tooltip-below:after {
    top: -7px;
    border-top: none;
    border-bottom: 7px solid rgba(255, 255, 255, 0.9);
}

/* Dialog */
.dialog-surface, .vue-dialog-surface {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10000;
}

.dialog-container, .vue-dialog-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    min-width: 240px;
    min-height: 135px;
    max-width: 90%;
    max-height: 90%;
    z-index: 10001;
    padding: 15px;
    text-align: left;
    overflow-y: auto;
    overflow-x: hidden;
    border: none;
}

.dialog-closer {
    position: absolute;
    top: 5px;
    right: 5px;
    color: #515c65;
    font-size: 1.1rem;
    cursor: pointer;
}

.dialog-content footer {
    margin-top: 1rem;
    border-top: 1px solid #233c4c;
    color: #aaa;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    font-size: 0.8em;
}

.dialog-last-updated {
    text-align: right;
    flex-grow: 1;
}

.dialog-content h4 {
    margin-top: 2rem;
}

.dialog-content h5 {
    border-bottom: 1px solid #233c4c;
}

.dialog-content h6 {
    color: #7a8b98;
    font-size: 0.9rem;
    font-weight: normal;
    margin: 1rem 0 0.5rem 0;
}

.dialog-content p, .dialog-content address {
    font-size: 0.9rem;
}

.dialog-content p, .dialog-content ul {
    margin-bottom: 1.5rem;
}

.dialog-table {
    table-layout: fixed;
}

.dialog-table tr:nth-child(2n+2) {
    background-color: #ebeeee;
}

.dialog-table td {
    border: none;
    overflow: hidden;
}

.dialog-table .title-row td {
    background-color: #233c4c;
    color: #fff;
    text-align: center;
}

.dialog-buttons {
    text-align: right;
}

/* Toast */
.toast {
    background-color: #fff;
    border: 1px solid #ddd;
    box-shadow: #888 2px 2px 2px;
    position: fixed;
    z-index: 11000;
    padding: 1rem 1rem 1rem 4rem;
    min-height: 3rem;
}

.toast-1 {
    left: 50px;
    top: 70px;
}

.toast-2 {
    left: 50%;
    top: 70px;
    transform: translateX(-50%);
}

.toast-3 {
    right: 50px;
    top: 70px;
}

.toast-4 {
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
}

.toast-5 {
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
}

.toast-6 {
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
}

.toast-7 {
    left: 50px;
    bottom: 50px;
}

.toast-8 {
    left: 50%;
    bottom: 50px;
    transform: translateX(-50%);
}

.toast-9 {
    right: 50px;
    bottom: 50px;
}

.toast:before {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 2rem;
    color: #fff;
    width: 3rem;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-info:before {
    content: '\e802';
    font-family: 'smartphlex';
    background-color: #ddd;
}

.toast-confirm:before {
    content: '\2714';
    background-color: #499d3d;
}

.toast-failed:before {
    content: '\2716';
    background-color: #c60000;
}

.toast-warning:before {
    content: '\e810';
    font-family: 'smartphlex';
    background-color: #e67717;
}

.toast-number:before {
    content: attr(data-number);
    background-color: #233c4c;
}

/* Login */
.login-container {
    width: 100%;
    min-height: 100%;
    padding-top: 80px;
    background-image: var(--background-image);
    background-repeat: no-repeat;
    background-size: cover;
    background-position-y: center
}

.login-content {
    width: 75%;
    margin-top: 2px;
    padding: 2rem;
    display: inline-block;
    position: relative;
    background-color: #fff;
    text-align: justify;
    opacity: .9;
}

.login-button {
    margin-left: auto;
    padding: 7px 14px;
}

.login-text {
    font-weight: normal;
    margin-right: 3%;
}

.login-info {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.logo-info {
    display: flex;
    align-items: center;
}
.logo-info span {
    margin-left: 0.5rem;
    font-size: 2rem;
    color: var(--main-theme-color);
}

.logo-content {
    width: 75%;
    margin-top: 2px;
    padding: 1rem;
    display: inline-block;
    position: relative;
    background-color: #fff;
    text-align: justify;
}

.logo-image {
    content: url("https://plxstaticcontent.blob.core.windows.net/$web/$images/logos/SmartRisk_logo.png");
}

.app-name {
    content: url("https://plxstaticcontent.blob.core.windows.net/$web/$images/logos/SmartRisk_name.png");
}

.contact-email {
    color: var(--main-theme-color);
}

.copyright {
    margin-right: 12%;
}

.browser-warning-icon {
    height: 50px;
    display: inline-block;
    position: relative;
    margin: 50px 0 20px 0;
    color: red;
}

.browser-warning-icon:after {
    position: absolute;
    content: '\26a0';
    left: -40px;
    font-size: 4rem;
}

/* Forms */
form, .flex-col {
    position: relative;
    display: flex;
    flex: 1 0 auto;
    flex-wrap: wrap;
    margin: 1rem 0;
}

.form-col {
    flex-grow: 1;
    padding: 0.5rem;
    position: relative;
}

.form-col-quarter {
    width: 25%;
    flex-grow: 0;
}

.form-col-half {
    width: 50%;
    flex-grow: 0;
}

.form-col-third {
    width: 33%;
    flex-grow: 0;
}

.form-col-forty {
    width: 40%;
    flex-grow: 0;
}

.form-col-full {
    width: 100%;
    flex-grow: 0;
}

.form-col-two-third {
    width: 67%;
    flex-grow: 0;
}

.form-col-three-quarter {
    width: 75%;
    flex-grow: 0;
}

.form-col-no-header {
    padding-top: 0;
}

.form-col-header-only {
    padding-bottom: 0;
}

.form-col h5 {
    border-bottom: 1px solid #233c4c;
    margin-bottom: 0;
}

.form-col h5:nth-child(n+2) {
    margin-top: 1.5rem;
}

.form-col-disabled:after {
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #f9f9f9;
    opacity: 0.7;
}

label {
    display: inline-block;
    margin: 1.2rem 0 0.2rem 0;
}

.label-readonly {
    color: #aaa;
}

input[type=text], input[type=url], input[type=email], input[type=number], input[type=tel], input[type=date], input[type=password], textarea {
    display: block;
    width: 100%;
    background-color: #c7d8e5;
    font-size: 0.9rem;
    border: 0;
    padding: 5px;
    text-indent: 4px;
}

textarea {
    height: 6.35rem;
}

select {
    width: 100%;
    color: #233c4c;
    border: none;
    background-color: transparent;
    position: relative;
    font-size: 0.9rem;
}

select:focus {
    outline: 0;
}

.select-wrapper, .multi-select-wrapper {
    background-color: #c7d8e5;
    padding: 5px;
    position: relative;
    height: 28px;
}

.multi-select-wrapper {
    height: auto;
}
.multi-select-wrapper select {
    height: 120px;
}

.select-wrapper-inline {
    display: inline-block;
}

.select-wrapper-inline option {
    padding-right: 13px;
}

.select-wrapper:after {
    content: '\25bc';
    font-size: 0.6rem;
    position: absolute;
    right: 0px;
    top: 0px;
    width: 30px;
    height: 28px;
    color: #233c4c;
    background-color: #c7d8e5;
    border-left: 1px solid #515c65;
    padding: 12px 9px;
    pointer-events: none;
    cursor: pointer;
}


h5 + .select-wrapper, h5 + .select-validator + .select-wrapper, h5 + .checkbox-list-item, h5 + p, h5 + .checkbox-list-grid {
    margin-top: 1.2rem;
}

.checkbox-list-item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    -webkit-column-break-inside: avoid;
}

.checkbox-list-item input {
    margin-left: 1rem;
}

.checkbox-list-item label {
    margin: 0.3rem 0 0.3rem 0.3rem;
}

.checkbox-list-grid {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: 50% 50%;
    grid-gap: 0 0.5rem;
}

.switch-container {
    display: flex;
    margin-top: 0.2rem;
    align-items: center;
    pointer-events: none;
}

input.switch {
    height: 0;
    width: 0;
    visibility: hidden;
}

label.switch {
    cursor: pointer;
    text-indent: -9999px;
    width: 40px;
    height: 20px;
    display: inline-block;
    background-color: #7a8b98;
    border-radius: 20px;
    position: relative;
    margin: 0 0.5rem 0 0;
    pointer-events: all;
}

label.switch:after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-radius: 18px;
    transition: 0.3s;
}

input.switch:checked + label {
    background: var(--main-theme-color);
}

input.switch:checked + label:after {
    left: 3px;
    transform: translateX(100%);
}

.buttons {
    width: 100%;
    border-top: 1px solid #233c4c;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
}

.buttons a, .buttons button {
    height: 2rem;
}

.uppy-DragDrop-container {
    border: 1px dashed #aaa;
    min-height: 50px;
    margin: 0.5em 0;
    text-align: center;
    color: #aaa;
}

.uppy-DragDrop-arrow {
    fill: #aaa;
    position: relative;
    top: 4px;
}

.uppy-DragDrop-dragText {
    color: var(--main-theme-color);
    cursor: pointer;
}

.drag {
    background-color: #c7d8e5;
    color: #fff;
}

.drag .uppy-DragDrop-arrow {
    fill: #fff;
}

.uppy-ProgressBar-inner {
    background-color: var(--main-theme-color);
    height: 10px;
}

.uppy-ProgressBar-percentage {
    display: none;
}

form [class^="icon-"]:before, form [class*=" icon-"]:before {
    font-size: 0.9rem;
}

input.input-validation-error, textarea.input-validation-error, .field-validation-error + .select-wrapper, .select-wrapper.field-validation-error {
    border: 1px solid #f00;
}

.field-validation-error + label .icon-info-circled {
    color: #f00;
}

.ui-menu-item {
    font-family: 'ProximaNovaRegular',Verdana,Sans-Serif;
    font-size: 0.8rem;
    text-align: left;
    padding: 3px;
}


/* Badges */
.badge {
    border-radius: 50%;
    background-color: #233c4c;
    color: #fff;
    font-size: 9px;
    line-height: 9px;
    width: 25px;
    width: 25px;
    height: 25px;
    display: inline-block;
    padding-top: 9px;
    font-weight: bold;
    text-align: center;
    margin-right: 6px;
}

.badge.inactive {
    background-color: #ddd;
    color: #aaa;
}

.badges {
    margin: 0.5rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.large-badges .badge {
    width: 50px;
    height: 50px;
    font-size: 18px;
    padding-top: 20px;
    margin-right: 18px;
}

/* Manage */
.manage-container {
    width: 100%;
    max-width: 1260px;
    max-height: calc(100% - 100px);
    margin: 0 auto;
    padding: 20px 20px 0 20px;
    overflow-x: hidden;
    overflow-y: auto;
    text-align: left;
    height: inherit;
}

.manage-container-fullscreen {
    max-width: 100%;
}

.manage-nav {
    display: flex;
    flex-wrap: wrap;
}

.manage-nav ul {
    margin: 2rem;
    padding: 0.5rem 1rem 1rem 1rem;
}

.manage-nav li {
    margin: 0.5rem 0 0 0.5rem;
}

.manage-nav li:first-child {
    font-size: 1.2rem;
    text-decoration: underline;
    font-weight: bold;
    margin-left: 0;
}

.manage-nav li:nth-child(2) {
    margin-top: 1rem;
}

.manage-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.manage-header h2 {
    flex-grow: 1;
}

.manage-header a {
    height: 2rem;
    white-space: nowrap;
}

.manage-header a:first-of-type {
    margin-left: auto;
}

.manage-table-header {
    display: flex;
    flex: 1 0 auto;
    margin: 0.5rem 0 0 0;
}

.manage-table-header a:last-child {
    margin: 0 0 0 auto;
}

.manage-table-header + .dataTables_wrapper {
    margin-top: 0;
}


/* Grids */
.two-col-grid {
    display: grid;
    grid-template-columns: 49% 49%;
    grid-auto-rows: auto;
    grid-gap: 2%;
}

.edit-entity-grid {
    margin: 0.5em 0;
}

.description-list-grid, .description-list-column-grid {
    display: grid;
    margin: 2rem 0;
}

.description-list-grid dt {
    grid-row: 1 / 2;
    margin: 0;
    text-align: center;
}

.description-list-grid dd {
    grid-row: 2 / 3;
    margin-top: 0.5rem;
    text-align: center;
}

.description-list-grid-inline {
    display: inline-grid;
    grid-gap: 2px 30px;
}

.description-list-column-grid dt {
    grid-row: auto;
    grid-column: 1 / 2;
    margin: 0.5rem 0;
}

.description-list-column-grid dd {
    grid-column: 2/3;
    margin: 0.5rem;
}

/* Tables */
table {
    width: 100%;
    margin: 10px 0;
}

tr.parent-row:hover {
    background-color: #ddd;
    cursor: pointer;
}

tr.child-row {
    display: none;
}

tr.child-row td:first-child {
    padding-left: 1rem;
}

th, td {
    padding: 8px;
    white-space: nowrap;
    vertical-align: middle;
}

th {
    text-transform: uppercase;
    background-color: #233c4c;
    color: #fff;
}

th:after {
    position: relative;
    top: -3px;
    font-size: 0.7rem;
    margin: 0 0 0 5px;
    cursor: pointer;
    font-weight: bold;
}

th.sorting:after, th .sorting:after {
    content: '\2191\2193';
}

th.sorting_asc:after, th .sorting_asc:after {
    content: '\2191';
}

th.sorting_desc:after, th .sorting_desc:after {
    content: '\2193';
}

td {
    border-top: 1px solid #999;
    border-bottom: 1px solid #999;
}

.dataTable.filtered {
    margin: 0;
}

.select-table tbody tr:hover {
    cursor: pointer;
    background-color: #72b2e5;
    color: #fff;
}

.select-table tr:hover a.action-link {
    color: #fff;
}

.dataTables_wrapper {
    display: flex;
    flex-wrap: wrap;
    flex: 1 0 auto;
    margin: 20px 0;
}

.dataTables_wrapper select, .dataTables_wrapper input {
    border: 1px solid #000;
    background-color: #fff;
}

.dataTables_length, .dataTables_info {
    display: flex;
}

.dataTables_filter {
    display: flex;
    margin-left: auto;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.dataTables_paginate{
    display: flex;
}

.dataTables_filter label {
    margin: 5px 0 0;
}

.dataTables_length *, .dataTables_filter * {
    display: flex;
    align-items: center;
}

.dataTables_paginate a {
    margin: 0 3px;
    cursor: pointer;
}
    
.dataTables_paginate a.current {
    color: #999;
    cursor: default;
    border-bottom: 1px solid #999;
}

.dataTables_paginate a.disabled {
    color: #999;
    cursor: default;
}

.dataTable {
    table-layout: fixed;
}

.dataTable td {
    overflow: hidden;
}

tr.dtrg-group {
    background-color: #515c65;
    color: #fff;
}

.select-table tbody tr.dtrg-group:hover {
    background-color: #515c65;
    color: #fff;
    cursor: default;
}

table.dt-rowReorder-float {
    position: absolute !important;
    opacity: 0.8;
    table-layout: fixed;
    z-index: 2001
}

tr.dt-rowReorder-moving {
    outline: 2px solid var(--main-theme-color);
    outline-offset: -2px
}

body.dt-rowReorder-noOverflow {
    overflow-x: hidden
}

table.dataTable td.reorder {
    text-align: center;
    cursor: move
}

.lookup-table th:nth-child(1) {
    width: 30px;
}

.lookup-boolean, .lookup-table th:last-child, .lookup-table td:last-child {
    text-align: center;
}

.lookup-table .edit-mode, .lookup-edit-table .view-mode {
    display: none;
}

.lookup-edit-table .edit-mode {
    display: table-cell;
}

.no-records-container {
    display: flex;
    width: 100%;
    margin: 10px 0;
}

.pager-top{
    padding-bottom: 10px;
    border-bottom: 1px solid #999;
}

.pager-bottom {
    padding-top: 10px;
    border-top: 1px solid #999;
}

/* Tabs */
.tab-container {
    display: flex;
    font-size: 1.8rem;
    justify-content: stretch;
}
.tab-container > div {
    color: #aaa;
    border-bottom: 3px;
    padding: 5px 10px 5px 0;
    margin: 25px 10px;
    cursor: pointer;
    white-space: nowrap;
    text-wrap: none;
    text-overflow: ellipsis;
}
.tab-container > div.active {
    color: #000;
    border-bottom: 3px solid var(--main-theme-color);
    cursor: default;
}

/* Icons */
.tick:after {
    content: '\2714';
    color: var(--main-theme-color);
    font-style: normal;
}

.cross:after {
    content: '\2716';
    color: #af1e22;
    font-style: normal;
}

/* Classes */
.hidden, a.hidden {
    display: none !important;
}

.fade-out {
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.warning {
    background-color: rgba(255, 0, 0, 0.5);
    color: #900;
}

.attention {
    color: #f18d39;
    text-align: center;
    margin: 3rem 25%;
}

.attention .icon-attention {
    display: block;
}

.attention .icon-attention:before {
    font-size: 4rem;
}

[v-cloak] {
    display: none;
}

/*Cache*/
.cache-warning {
    border: 1px solid red;
    padding: 5px;
    margin-top: 10px;
    color: red;
}

.cache-warning div {
    line-height: 1.5em;
}

.table-header {
    display: flex;
    width: 100%;
}
.table-header :first-child {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table-header.bool {
    place-content: center;
}
.table-header .sorter {
    width: 15px;
    margin-top: -1px;
    margin-left: 2px;
    cursor: ns-resize;
}

.table-filter {
    display: flex;
    flex-flow: row-reverse;
    cursor: pointer;
}
.table-filter.search {
    flex-flow: row;
}
.table-filter.active, .sorter.active {
    color: #f18c3a;
}
.table-filter.open {
    background-color: #fff;
    color: #233c4c;
}
.table-filter.active.open {
    background-color: #fff;
    color: #f18c3a;
}

.table-filter.clear:after {
    content: 'x';
    font-size: .5rem;
    font-style: normal;
    font-weight: 900;
    position: relative;
    width: 10px;
    height: 10px;
    top: 6px;
    left: 20px;
}

.table-filter-items {
    position: absolute;
    padding: 0;
    margin: 15px 0;
    border: 1px solid #233c4c;
    overflow-y: auto !important;
    background-color: white;
    max-height: 250px;
    max-width: 290px;
    overflow: auto;
    z-index: 100000;
}

.table-filter-items input{
    font-size: 0.9rem;
    margin: 20px;
    border: 1px solid #ebeeee;
    background-color: #ebeeee;
    -webkit-appearance: none;
}

.table-filter-items input:focus{
    outline-offset: 0;
    outline: none;
}

.table-filter-item {
    list-style: none;
    text-align: left;
    text-transform: capitalize;
    cursor: pointer;
    font-weight: initial;
    padding: 5px;
    margin: 0;
    color: #000;
    z-index: 10000;
}

.table-filter-item.is-active,
.table-filter-item:hover {
    background-color: #ebeeee;
}

.table-row.editing {
    background-color: #ebeeee;
    cursor: default;
}
.table-row.selectable:hover {
    cursor: pointer;
    background-color: #ebeeee;
}
.table-row input,
.table-row select {
    padding: 2px;
    background-color: #c7d8e5;
    border-color: #c7d8e5;
}

.table-row input.invalid,
.table-row select.invalid {
    border-color: red;
}

/* AutoComplete List */
.autocomplete-list {
    list-style: none;
    margin: 0.8rem 0 0 0;
    padding: 0;
    display: block;
    max-height: 400px;
    overflow-y: auto;
}

.autocomplete-list > li {
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    padding: .3rem .5rem;
    margin: 0;
}
.autocomplete-list.selectable > li {
    border-bottom: 2px solid #fff;
}
.autocomplete-list.selectable > li:hover {
    border-bottom: 2px solid #fff;
}

.autocomplete-list.selectable > li.selected {
    border-bottom: 2px solid var(--main-theme-color);
    background: #ebeeee;
    font-weight: bold;
}

.autocomplete-list.selectable > li > i {
    opacity: 0;
    color: var(--main-theme-color);
}

.autocomplete-list.selectable > li:hover > i {
    animation: autocomplete-icon-fade-in .5s;
    opacity: 1;
}
@keyframes autocomplete-icon-fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.autocomplete-list .icon-trash {
    color: var(--main-theme-color);
    cursor: pointer;
}

.autocomplete-list.empty-msg {
    display: flex;
    justify-content: center;
    color: #7a8b98;
    padding: 0.3rem 0;
}

.autocomplete-items {
    position: absolute;
    padding: 0;
    margin: 0;
    border: 1px solid #eeeeee;
    overflow: hidden;
    background-color: white;
    max-height: 250px;
    width: 98%;
    overflow: auto;
    z-index: 1;
}

.autocomplete-item {
    list-style: none;
    text-align: left;
    cursor: pointer;
    font-weight: initial;
    padding: 5px;
    margin: 0;
}

.autocomplete-item.is-active, .autocomplete-item:hover {
    background-color: #ebeeee;
}



/* ESignature Dialog */
.esignature-dialog {
    width: 400px;
}
.esignature-form {
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-items: flex-start;
}
.esignature-form label {
    margin: 0 0.3rem 0 0;
}
.esignature-form input[type=password] {
    display: inline;
}