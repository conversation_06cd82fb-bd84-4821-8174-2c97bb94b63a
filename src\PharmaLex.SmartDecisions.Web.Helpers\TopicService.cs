﻿using AutoMapper;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface ITopicService
    {
        Task EnsureSystemFields(ContentType ct);
        Task<IEnumerable<TopicItemModel>> ListItemsByTopic(int id, Func<TopicItemModel, bool> filter = null, bool includeValues = true);
        Task<IEnumerable<TopicModel>> ListTopics(int[] licensedTopics = null);
    }

    public class TopicService : ITopicService
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;
        private readonly IContentService contentService;

        public TopicService(IDistributedCacheServiceFactory cache, IMapper mapper, IContentService contentService)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.contentService = contentService;
        }

        public async Task EnsureSystemFields(ContentType ct)
        {
            var ctc = cache.CreateEntity<ContentType>();
            var fc = cache.CreateTrackedEntity<Field>();

            var fields = await fc.WhereAsync(x => x.ContentTypeId == ct.Id);
            if(!fields.Any(x => x.Name == "Regulatory Authority"))
            {
                fc.Add(new Field
                {
                    ContentTypeId = ct.Id,
                    FieldType = FieldType.Relationship,
                    RelatedContentTypeId = (await ctc.FirstOrDefaultAsync(x => x.Name == "Regulatory Authority")).Id,
                    Name = "Regulatory Authority",
                    Description = "The Regulatory Authority whose guidelines/requirements are captured by the content",
                    System = true,
                    Required = true,
                    Unique = false,
                    Length = 1
                });
            }

            await EnsureSystemPicklistField("Medicinal Product Domain");
            await EnsureSystemPicklistField("Medicinal Product Type");
            await EnsureSystemPicklistField("Procedure Type");
            await EnsureSystemPicklistField("Product Category");
            async Task EnsureSystemPicklistField(string name)
            {
                if (!fields.Any(x => x.Name == name))
                {
                    fc.Add(new Field
                    {
                        ContentTypeId = ct.Id,
                        FieldType = FieldType.Picklist,
                        RelatedContentTypeId = (await ctc.FirstOrDefaultAsync(x => x.Name == name)).Id,
                        Name = name,
                        System = true,
                        Required = true,
                        Unique = false,
                        Length = 1
                    });
                }
            }

            await fc.SaveChangesAsync();
        }

        public async Task<IEnumerable<TopicModel>> ListTopics(int[] licensedTopics = null)
        {
            var ctc = cache.CreateMappedEntity<ContentType, TopicModel>().Configure(x => x.Include(y => y.Field));
            if (licensedTopics == null)
            {
                return await ctc.WhereAsync(x => x.ContentTypeCategoryId == (int)ContentTypeCategory.Topic);
            }
            return await ctc.WhereAsync(x => x.ContentTypeCategoryId == (int)ContentTypeCategory.Topic && licensedTopics.Contains(x.Id));
        }

        public async Task<IEnumerable<TopicItemModel>> ListItemsByTopic(int id, Func<TopicItemModel, bool> filter = null, bool includeValues = true)
        {
            return await contentService.ListItemsByContentType<TopicItemModel>(id, filter, includeValues);
        }
    }
}
