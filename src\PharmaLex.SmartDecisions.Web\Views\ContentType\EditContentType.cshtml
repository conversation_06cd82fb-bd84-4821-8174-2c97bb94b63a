﻿@model ContentTypeModel
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.SmartDecisions.Web.Models
@using Microsoft.AspNetCore.Authorization
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject IAuthorizationService AuthorizationService
@inject AutoMapper.IMapper mapper
@{
    ViewData["Title"] = "Edit ContentType";

    var categories = mapper.Map<IEnumerable<PicklistItemModel>>(Enum.GetValues(typeof(ContentTypeCategory))).ToSelectList();
}

<div id="content-type">

    <div class="sub-header">
        <h2>@Model.Name Content Type</h2>
        <div class="controls">
            <a class="button secondary" href="/manage/content-type">Content Types</a>
            @if (Model.Id > 0)
            {
                <a class="button secondary" href="/data/@Model.Id">Content Items</a>
                <a href="/manage/content-type/delete/@Model.Id" class="button" v-if="system !== true">Delete</a>
            }
        </div>
    </div>

    <form method="post" v-cloak>
        <h5>Details</h5>
        <div class="flex flex-nowrap gapped-2 mt-2">
            <div class="flex-item flex-x2 tile">
                <div class="form-group">
                    <label asp-for="Name">Name*</label>
                    <input type="text" asp-for="Name" required autofocus />
                    <span asp-validation-for="Name" class="error-color"></span>
                </div>
                <div class="form-group">
                    <label asp-for="PluralName">Plural name*</label>
                    <input type="text" asp-for="PluralName" required />
                    <span asp-validation-for="PluralName" class="error-color"></span>
                </div>
                <div class="form-group">
                    <label asp-for="ShortName">Short name* <i class="icon-info-circled" title="Maximum 31 characters, cannot include \ / * ? : [ ] <>"></i></label>
                    <input type="text" asp-for="ShortName" required pattern="^[^\\/*?:\[\]<>]+$" maxlength="31" />
                    <span asp-validation-for="ShortName" class="error-color"></span>
                </div>
                <div class="form-group">
                    <template v-if="showAutoManage">
                        <label>Auto-manage name field? <i class="icon-info-circled" title="When selected the Name field is hidden and auto-generated in the form [ContentTypeName]-[Id]"></i></label>
                        <label class="switch-container">
                            No
                            <input type="checkbox" asp-for="AutoManageName" class="switch" />
                            <label asp-for="AutoManageName" class="switch">Is system</label>
                            Yes
                        </label>
                    </template>
                </div>
            </div>

            <div class="flex-item flex-x2 tile">
                <div class="form-group">
                    <label>Is system type?</label>
                    <div class="switch-display-only"><i class="icon-@(Model.System ? "check" : "cross")"></i> @(Model.System ? "Yes" : "No")</div>
                </div>
                <div class="form-group">
                    <label asp-for="ContentTypeCategoryId">Category*</label>
                    <div class="select-wrapper custom-select"><select asp-for="ContentTypeCategoryId" asp-items="categories" required v-model="categoryId"><option value="">Select Category</option></select></div>
                </div>
                <div class="form-group">

                    <label asp-for="Owner">Owner*</label>
                    <input type="text" asp-for="Owner" required pattern=".*\S+.*" />
                </div>
            </div>

        </div>

        <section v-if="showRelations()">
            <h5>Fields</h5>
            <filtered-table id="fields" styling="add-table" :items="fields" :columns="fieldColumns" :link="fieldLink" addurl="/manage/field/new/@Model.Id" :page-size="10"></filtered-table>

            <div v-if="showDisplay()">
                <div class="flex justify-space-between pb-1 flex-align-center">
                    <h5 class="flex-item justify-center mb-1 flex justify-content flex-align-center">
                        <span class="pr-1">Display</span>
                        <span class="hero-banner-tooltip">
                            <i class="m-icon pr-2">help</i>
                            <span class="hero-banner-tooltiptext add-view-display">Only one View Display can be created per display type</span>
                        </span>
                    </h5>
                </div>
                <filtered-table id="displays" styling="add-table" :items="displays" :columns="displayColumns" :link="displayLink" addurl="/manage/content-type-display/new/@Model.Id" :page-size="10"></filtered-table>
            </div>
        </section>
        <div class="buttons no-border mt-2 p-2">
            <a class="button secondary" href="/manage/content-type">Cancel</a>
            <button type="submit" v-if="system !== true">Save</button>
        </div>
    </form>

</div>

@section Scripts {
    <script type="text/javascript">
    var pageConfig = {
        appElement: '#content-type',
        data: function () {

            let contentTypeDisplays =  @Model.Displays.Count() > 0 ? (@Html.Raw(Model.Displays.ToJson())).filter(d => d.displayType === 'View') : [];

            return {
                contentTypeDisplays: contentTypeDisplays,
                fieldLink: '/manage/field/edit/',
                displayLink: '/manage/content-type-display/edit/',
                fields: @Html.Raw(Model.Field.ToJson()),
                displays: @Html.Raw(Model.Displays.ToJson()),
                filters: [],
                system: @Model.System.ToString().ToLower(),
                categoryId: @Model.ContentTypeCategoryId > 0 ? '@Model.ContentTypeCategoryId' : '',
                fieldColumns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'id',
                            type: 'number',
                            header: 'ID',
                        },
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            sortDirection: 1,
                            type: 'text'
                        },
                        {
                            dataKey: 'fieldType',
                            sortKey: 'fieldType',
                            header: 'Field Type',
                            type: 'text'
                        },
                        {
                            dataKey: 'required',
                            sortKey: 'required',
                            header: 'Required',
                            type: 'bool',
                            style: 'width: 50px;'
                        },
                        {
                            dataKey: 'system',
                            sortKey: 'system',
                            header: 'System',
                            type: 'bool',
                            style: 'width: 50px;'
                        }
                    ]
                },
                displayColumns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            sortDirection: 1,
                            type: 'text'
                        },
                        {
                            dataKey: 'displayType',
                            sortKey: 'displayType',
                            header: 'Display Type',
                            type: 'text'
                        }
                    ]
                }
            };
        },
        computed: {
            showAutoManage: function () {
                return this.categoryId !== '@((int)ContentTypeCategory.Picklist)';
            }
        },
        methods: {
            showRelations: function() {
                return @Model.Id > 0 && @Model.ContentTypeCategoryId != @((int)ContentTypeCategory.Picklist);
            },
            showDisplay: function() {
                return !(@Model.ContentTypeCategoryId == @((int)ContentTypeCategory.Data));
            }
        },
        mounted() {

            if(this.contentTypeDisplays.length) {
                //Disable add button on table, as only one View can be created.
                let addDisplayButtonContainer = document.getElementsByClassName("actions")[1].firstChild;
                let addDisplayButton = addDisplayButtonContainer.getElementsByClassName("m-icon")[0].style.pointerEvents = "none";
            }
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}
