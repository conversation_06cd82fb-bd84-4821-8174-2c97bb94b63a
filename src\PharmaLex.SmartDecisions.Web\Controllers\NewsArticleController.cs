using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Pharmalex.AzureCloudStorage;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Helpers.AzureSearch;
using PharmaLex.SmartDecisions.Web.Helpers.Exceptions;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize("NewsAuthor")]
    public class NewsArticleController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly IRepositoryFactory repoFactory;
        private readonly ILocalisationService localisationService;
        private readonly IDecisionsBlobContainer blobContainer;
        private readonly INewsArticleService newsArticleService;
        private readonly IAzureSearchHelper azureSearchHelper;
        private readonly SearchSettings searchSettings;

        public NewsArticleController(IDistributedCacheServiceFactory cacheFactory,
            IRepositoryFactory repoFactory,
            ILocalisationService localisationService,
            IDecisionsBlobContainer blobContainer,
            INewsArticleService newsArticleService,
            IAzureSearchHelper azureSearchHelper,
            IOptionsMonitor<SearchSettings> searchOptions
            )
        {
            this.searchSettings = searchOptions.CurrentValue;
            this.cacheFactory = cacheFactory;
            this.repoFactory = repoFactory;
            this.localisationService = localisationService;
            this.blobContainer = blobContainer;
            this.newsArticleService = newsArticleService;
            this.azureSearchHelper = azureSearchHelper ?? throw new ArgumentNullException(nameof(azureSearchHelper));
        }

        [HttpGet("/articles")]
        public async Task<IActionResult> Articles()
        {
            DateTime today = DateTime.UtcNow.Date;
            var importanceEnum = await this.localisationService.LocaliseEnum<NewsArticleImportance>();

            var result = await this.cacheFactory.Create<List<NewsArticleContentListModel>>(typeof(NewsArticleContent).Name, typeof(NewsArticle).Name)
                    .GetOrCreateAsync("news-article-content-list", async () =>
                    {
                        var users = await this.cacheFactory.CreateEntity<User>().AllAsync();
                        var contentList = await this.repoFactory.Create<NewsArticleContent>().Configure(o => o
                            .Include(x => x.NewsArticle))
                        .Where(x => x.PublishingStateId != (int)NewsArticlePublishingState.Approved
                            || !x.GoLiveDate.HasValue
                            || x.GoLiveDate > today)
                        .Select(x => new NewsArticleContentListModel
                        {
                            Id = x.Id,
                            Title = x.Title,
                            SourceUrl = x.SourceUrl,
                            SourcePublicationDate = x.NewsArticle.SourcePublicationDate.ToString("yyyy-MM-dd"),
                            GoLiveDate = x.GoLiveDate.HasValue ? x.GoLiveDate.Value.ToString("yyyy-MM-dd") : null,
                            LocaleId = x.LocaleId,
                            AuthorId = x.AuthorId,
                            ReviewerId = x.ReviewerId,
                            NewsSourceId = x.NewsArticle.NewsSourceId,
                            PublishingStateId = x.PublishingStateId,
                            Path = $"{x.NewsArticleId}?contentId={x.Id}",
                            ImportanceId = x.NewsArticle.ImportanceId
                        })
                        .ToListAsync();

                        foreach (var c in contentList)
                        {
                            c.Author = users.FirstOrDefault(x => x.Id == c.AuthorId)?.FullName ?? string.Empty;
                            c.Reviewer = users.FirstOrDefault(x => x.Id == c.ReviewerId)?.FullName ?? string.Empty;
                            c.ImportanceName = importanceEnum.First(i => i.Id == c.ImportanceId).Name;
                        }

                        return contentList;
                    });

            var newsSources = await this.localisationService.LocaliseList<NewsSource>();
            var locales = await this.localisationService.LocaliseList<Locale>();
            var publishingStates = await this.localisationService.LocaliseEnum<NewsArticlePublishingState>();
            foreach (var c in result)
            {
                c.NewsSource = newsSources.First(x => x.Id == c.NewsSourceId).Name;
                c.Locale = locales.First(x => x.Id == c.LocaleId).Name;
                c.PublishingState = publishingStates.First(x => x.Id == c.PublishingStateId).Name;
            }

            return this.View(result);
        }

        [HttpGet("/article/new")]
        public IActionResult New()
        {
            return View("Edit", new NewsArticleModel());
        }

        [HttpPost("/article/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New(NewsArticleModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }

            try
            {
                (NewsArticle article, NewsArticleContent updatedContent) = await this.newsArticleService.CreateOrUpdateNewsArticle(model);

                return Redirect($"/article/edit/{article.Id}?contentId={updatedContent.Id}");
            }
            catch (MismatchingContentLocaleException ex)
            {
                return this.BadRequest(ex.Message);

            }
        }

        [HttpGet("/article/edit/{id}")]
        public async Task<IActionResult> Edit(int id, int? contentId)
        {
            var result = await this.cacheFactory.Create<NewsArticleModel>(
                nameof(NewsArticle),
                nameof(NewsSource),
                nameof(NewsArticleContent),
                nameof(AzureBlob),
                nameof(NewsArticleCategory)
                ).GetOrCreateAsync($"news-article-content-{id}", async () =>
                {
                    var articlesRepo = this.repoFactory.Create<NewsArticle>().Configure(o => o
                        .Include(x => x.NewsSource)
                        .Include(x => x.NewsArticleContent)
                            .ThenInclude(x => x.AzureBlob)
                        .Include(x => x.NewsArticleCategory));

                    var article = await articlesRepo.FirstOrDefaultAsync(x => x.Id == id &&
                        (!contentId.HasValue || x.NewsArticleContent.Any(y => y.Id == contentId)));

                    if (article == null) return null;

                    var model = new NewsArticleModel
                    {
                        Id = article.Id,
                        SourcePublicationDate = article.SourcePublicationDate.ToString("yyyy-MM-dd"), // yyyy-MM-dd
                        NewsSourceId = article.NewsSourceId,
                        NewsCategoryIds = article.NewsArticleCategory.Select(x => x.NewsCategoryId).ToList(),
                        CreatedBy = article.CreatedBy,
                        ImportanceId = article.ImportanceId,
                        NewsArticleContent = article.NewsArticleContent.Select(x => new NewsArticleContentModel
                        {
                            Id = x.Id,
                            Title = x.Title,
                            AuthorId = x.AuthorId,
                            Body = string.Empty,
                            LocaleId = x.LocaleId,
                            PublishingStateId = x.PublishingStateId,
                            ReviewerId = x.ReviewerId,
                            SourceUrl = x.SourceUrl,
                            GoLiveDate = x.GoLiveDate?.ToString("yyyy-MM-dd"), // yyyy-MM-dd
                            Path = $"/article/edit/{article.Id}?contentId={x.Id}"
                        }).ToList()
                    };

                    foreach (var content in model.NewsArticleContent)
                    {
                        content.BlobName = article.NewsArticleContent.First(x => x.Id == content.Id).AzureBlob?.Name;
                    }

                    return model;
                });

            foreach (var content in result.NewsArticleContent)
            {
                if (!string.IsNullOrEmpty(content.BlobName))
                {
                    var blobClient = await this.blobContainer.GetBlobClientAsync(content.BlobName);
                    BlobDownloadResult downloadResult = await blobClient.DownloadContentAsync();
                    content.Body = downloadResult.Content.ToString();
                    content.BodyBase64 = Convert.ToBase64String(downloadResult.Content);
                    content.BodyUrl = blobClient.GetDownloadLink();
                }
            }

            // Business rule: don't allow user to navigate to reviewed infoflash by pasting URL into browser
            var referer = Request.Headers.Where(x => x.Key == "Referer");
            if (!referer.Any() && result.ImportanceId == (int)NewsArticleImportance.INFOFLASH && result.NewsArticleContent.First(x => x.Id == contentId).PublishingStateId == (int)NewsArticlePublishingState.Approved)
            {
                throw new Exception($"Infoflash article unavailable id:{id}, content:{contentId}");
            }

            result.SelectedLocaleId = contentId.HasValue ? result.NewsArticleContent.First(x => x.Id == contentId).LocaleId : null;
            return this.View(result);
        }

        [HttpPost("/article/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(NewsArticleModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.View(model);
            }

            (NewsArticle article, NewsArticleContent updatedContent) = await this.newsArticleService.CreateOrUpdateNewsArticle(model);

            this.AddConfirmationNotification(this.localisationService.Localise("(news).(articles).updated").Value, new NotificationOptions() { UseIcons = true });

            var approved = model.NewsArticleContent[0].PublishingStateId == (int)NewsArticlePublishingState.Approved;

            if (approved && searchSettings.IsEnabled)
            {
                await azureSearchHelper.RunIndexerOnSuccess().ConfigureAwait(false);
            }

            return Redirect($"/article/edit/{article.Id}?contentId={updatedContent.Id}");
        }

        [HttpDelete("/article/edit/content/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteContent(int id)
        {
            var contentCache = this.cacheFactory.CreateTrackedEntity<NewsArticleContent>();
            var content = await contentCache.FirstOrDefaultAsync(x => x.Id == id);

            if (content == null)
            {
                return this.NotFound();
            }

            var articleCache = this.cacheFactory.CreateTrackedEntity<NewsArticle>()
                .Configure(o => o.Include(x => x.NewsArticleContent));

            var article = await articleCache.FirstOrDefaultAsync(x => x.Id == content.NewsArticleId);
            if (article.NewsArticleContent.Count == 1)
            {
                articleCache.Remove(article);
                await articleCache.SaveChangesAsync();
            }
            else
            {
                contentCache.Remove(content);
                await contentCache.SaveChangesAsync();
            }

            return this.NoContent();
        }
    }
}