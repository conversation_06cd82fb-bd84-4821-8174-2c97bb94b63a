﻿CREATE TRIGGER [dbo].[Company_Insert] ON [dbo].[Company]
FOR INSERT AS
INSERT INTO [Audit].[Company_Audit] ([AuditAction], [Id], [Name], [PrimaryContactName], [PrimaryContactEmail], [PrimaryContactAddress], [PrimaryContactPhone], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'I', [Id], [Name], [PrimaryContactName], [PrimaryContactEmail], [PrimaryContactAddress], [PrimaryContactPhone], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Company_Update] ON [dbo].[Company]
FOR UPDATE AS
INSERT INTO [Audit].[Company_Audit] ([AuditAction], [Id], [Name], [PrimaryContactName], [PrimaryContactEmail], [PrimaryContactAddress], [PrimaryContactPhone], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'U', [Id], [Name], [PrimaryContactName], [PrimaryContactEmail], [PrimaryContactAddress], [PrimaryContactPhone], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Company_Delete] ON [dbo].[Company]
FOR DELETE AS
INSERT INTO [Audit].[Company_Audit] ([AuditAction], [Id], [Name], [PrimaryContactName], [PrimaryContactEmail], [PrimaryContactAddress], [PrimaryContactPhone], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'D', [Id], [Name], [PrimaryContactName], [PrimaryContactEmail], [PrimaryContactAddress], [PrimaryContactPhone], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO