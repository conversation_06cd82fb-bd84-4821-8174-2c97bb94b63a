﻿--alter table [dbo].[ContentType]
--add constraint [UC_ContentType_Name] unique ([Name])

create trigger [dbo].[ContentType_Insert] on [dbo].[ContentType]
for insert as
insert into [Audit].[ContentType_Audit]
select 'I', [Id], [Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[ContentType_Update] on [dbo].[ContentType]
for update as
insert into [Audit].[ContentType_Audit]
select 'U', [Id], [Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[ContentType_Delete] on [dbo].[ContentType]
for delete as
insert into [Audit].[ContentType_Audit]
select 'D', [Id], [Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
