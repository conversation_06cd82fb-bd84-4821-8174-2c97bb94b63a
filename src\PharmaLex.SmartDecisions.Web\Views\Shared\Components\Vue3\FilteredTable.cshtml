﻿<link rel="stylesheet" href="/dist/css/filteredTablesCss.css" asp-append-version="true" />

<script type="text/x-template" id="collection-cell-template">
    <td>
        <span>{{val[0]}}</span>
        <span class="tipified" :data-tippy-content="tipContent" v-on:click.stop
              v-if="val.length>1">+ {{val.length-1}}</span>
    </td>
</script>
<script type="text/x-template" id="data-table-pager-template">
    <div class="pager">
        <div class="summary">
            {{interpolate(strings.showingFormat, itemCount === 0 ? 0 : startIndex + 1, endIndex, itemCount)}}
            <span v-if="totalItems!==itemCount">&nbsp;{{interpolate(strings.showingFilteredFormat, totalItems)}}</span>
        </div>
        <div class="page-size">
            <span class="nowrap">{{strings.pageSize}}&nbsp;</span>
            <select v-on:change="pageSizeChange($event.target.value)">
                <option v-for="s in [10, 25, 50, 100]" :selected="pageSize===s" :value="s">{{s}}</option>
            </select>
        </div>
        <div class="pagination">
            <a v-on:click="$emit('page-index-change', 0)" :disabled="pageCount===0||pageIndex===0" v-bind:class="[{'disabled': pageCount===0||pageIndex===0}, 'first']">{{strings.first}}</a>
            <a v-on:click="$emit('page-index-change', Math.max(0, pageIndex - 1))" :disabled="pageCount===0||pageIndex===0" v-bind:class="[{'disabled': pageCount===0||pageIndex===0}, 'previous']">{{strings.previous}}</a>
            <div>
                <a v-for="i in pageCount" v-on:click="$emit('page-index-change', i-1)" :disabled="pageIndex===i-1" v-bind:class="[{'current': pageIndex===i-1}]">{{i}}</a>
            </div>
            <a v-on:click="$emit('page-index-change', Math.min(pageCount - 1, pageIndex + 1))" :disabled="pageCount===0||pageIndex===pageCount-1" v-bind:class="[{'disabled': pageCount===0||pageIndex===pageCount-1}, 'next']">{{strings.next}}</a>
            <a v-on:click="$emit('page-index-change', pageCount - 1)" :disabled="pageCount===0||pageIndex===pageCount-1" v-bind:class="[{'disabled': pageCount===0||pageIndex===pageCount-1}, 'last']">{{strings.last}}</a>
        </div>
    </div>
</script>
<script type="text/x-template" id="select-filter-template">
    <div :class="['table-filter', 'search', {'active': !!value, 'open': isOpen }]"
         v-on:click="open()">
        <ul v-show="isOpen" class="table-filter-items"
            @@keydown.enter.prevent="change"
            @@keydown.down="onArrowDown"
            @@keydown.up="onArrowUp"
            ref="select">
            <li v-for="(fi, i) in availableOptions" :title="fi.value"
                :key="i"
                @@click.stop="change(fi.key)"
                :class="[{ 'is-active': i === selectedIndex }, 'table-filter-item']">
                <div class="flex-centred-spaced-nowrapped">
                    <span>{{ fi.value }}</span>
                    <i class="m-icon small pl-1" v-show="i === selectedIndex"  @@click.stop="change()">close</i>
                </div>

            </li>
        </ul>
    </div>
</script>
<script type="text/x-template" id="text-cell-template">
    <td>{{val}}</td>
</script>
<script type="text/x-template" id="text-edit-select-cell-template">
    <td v-on:click>
        <select v-model="data.selected"
                :class="{'invalid': invalid}"
                :required="data.required"
                @@change="onChange"
                @@input="onInput">
            <option v-for="item in data.options" :value="item.id">{{item.name}}</option>
        </select>
    </td>
</script>
<script type="text/x-template" id="text-edit-plain-cell-template">
    <td v-on:click>
        <input :type="inputType"
               :class="{'invalid': invalid}"
               v-model="data.selected"
               :required="data.required"
               :placeholder="data.placeholder"
               :pattern="data.pattern"
               :title="data.customErrorMessage"
               @@change="onChange"
               @@input="onInput" />
    </td>
</script>
<script type="text/x-template" id="bool-cell-template">
    <td>
            <span v-if="val === true">Yes</span>
            <span v-else>No</span>
    </td>
</script>


<script type="text/x-template" id="search-filter-template">
    <div :class="['table-filter', 'search', {'active': !!value, 'open': isOpen }]"
         v-on:click="open()">
        <div v-show="isOpen" class="table-filter-items" v-on:click.stop @@keydown.enter.prevent="close">
            <input ref="search" type="search" :placeholder="config.header" :value="value" v-on:input="change($event.target.value)" />
            <i class="m-icon small close" v-on:click="clear">close</i>
        </div>
    </div>
</script>


<script type="text/x-template" id="filtered-table-template">
    <div>
        <data-table-pager v-if="pagerLocation.includes('top')"
                          :item-count="itemCount"
                          :page-index="pageIndex"
                          :page-size="size"
                          :location="'top'"
                          :resources="strings.pager"
                          :total-items="data.length"
                          v-on:page-index-change="changePage"
                          v-on:page-size-change="changePageSize">
        </data-table-pager>
        <table class="filtered" v-bind:class="styling">
            <thead>
                <tr>
                    <th v-for="column in columns.config"
                        v-bind:title="column.header||column.dataKey"
                        v-bind:style="getColumnStyle(column)">
                        <div :class="['table-header', {'bool':column.type==='bool'}, getColumnClass(column)]">
                            <div v-on:click="sort(column)"
                                 v-if="column.sortKey"
                                 :title="interpolate(strings.sortByFormat, column.header || column.dataKey)"
                                 :class="['sorter', {'active': sortModel[column.sortKey], 'sorting': column.sortKey&&!sortModel[column.sortKey], 'sorting_asc': sortModel[column.sortKey] > 0},{'sorting_desc': sortModel[column.sortKey] < 0}]"></div>
                            <component v-if="getFilterByKey(column.dataKey)"
                                 v-bind:is="getFilterByKey(column.dataKey).type + '-filter'"
                                 v-model="filterModel[column.dataKey]"
                                 v-on:filter="filter"
                                 v-on:clear="clear"
                                 :resources="{'noValue': strings.noValue}"
                                 :filtered="filterItems"
                                 :config="getFilterByKey(column.dataKey)"
                                 :title="getFilterByKey(column.dataKey).header"
                                 :value ="filterModel[column.dataKey]" >
                            </component>
                            <div class="header-text">{{column.header||column.dataKey}}</div>
                        </div>
                    </th>
                    <th class="actions">
                        <div>
                                <i v-if="internalFilters.length" v-on:click.stop="clear" :class="['m-icon clear',{'active': isFiltered}]" :title="strings.clearFilters">filter_list_off</i>

                                <i v-if="(addurl || columns.add?.enabled) && !editItem" v-on:click.stop="addItemClicked" class="m-icon" :title="strings.addItem">add</i>

                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(row, index) in pageItemsAdd" v-on:click="rowClicked(row)" :key="row[columns.idKey]" :class="['table-row', {'selectable': !editItem && link?.length > 0, 'editing': editItem && editItem.id === row[columns.idKey]}]">
                    <component v-for="column in columns.config"
                        v-bind:is="getCellType(row, column)"
                        :href="getHref(row, column)"
                        :convert="column.edit?.convert"
                        :val="editItem && editItem.id === row[columns.idKey] ? editItem[column.dataKey].selectedText : row[column.dataKey]"
                        :title="editItem && editItem.id === row[columns.idKey] ? '' : column.edit?.convert ? column.edit.convert(row[column.dataKey]) : row[column.dataKey]"
                        :key="column.dataKey"
                        :data="editItem ? editItem[column.dataKey] : {}"
                        v-on:clicked="$emit('cell-clicked', $event)"
                        v-bind:style="getColumnStyle(column)"
                        v-bind:class="getColumnClass(column)">
                    </component>
                    <td>

                        <template v-if="columns.config.find(x=> x.edit) && (!editItem || editItem.id === row[columns.idKey])">
                            <div class="flex flex-nowrap">
                                <i v-if="!editItem && columns.edit?.enabled" class="m-icon" :title="strings.edit" v-on:click="editItemClicked(row)">edit</i>
                                <i v-if="!editItem && columns.remove?.enabled" :class="['m-icon', {'disabled': columns.remove.canRemoveProp && !row[columns.remove.canRemoveProp]}]" :title="strings.remove" v-on:click.stop="removeItemClicked(row)">delete</i>
                                <i v-if="editItem && editItem.id == row[columns.idKey]" :class="['m-icon', {disabled: columns.config.find(x => editItem && editItem[x.dataKey].invalid)}]"
                                   :title="strings.save" v-on:click="saveItemClicked(row)">save</i>
                                <i v-if="editItem && editItem.id == row[columns.idKey]" class="m-icon" :title="strings.cancel" v-on:click.stop="editItem=null">close</i>
                            </div>
                        </template>
                    </td>
                </tr>
            </tbody>
        </table>
        <div v-if="!filterItems.length && !editItem" class="no-records-container">
            <i class="m-icon warning-color pr-1">warning</i>{{strings.noRecordsMessage}}
        </div>
        <data-table-pager v-if="pagerLocation.includes('bottom')"
                          :item-count="itemCount"
                          :page-index="pageIndex"
                          :page-size="size"
                          :location="'bottom'"
                          :resources="strings.pager"
                          :total-items="data.length"
                          v-on:page-index-change="changePage"
                          v-on:page-size-change="changePageSize">
        </data-table-pager>
    </div>
</script>

