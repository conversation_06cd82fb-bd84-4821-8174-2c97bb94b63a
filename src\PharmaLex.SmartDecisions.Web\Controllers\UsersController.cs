﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class UsersController : BaseController
    {
        private readonly IAzureAdGraphService graphService;
        private readonly IMapper mapper;
        private readonly IDistributedCacheServiceFactory cache;
        public UsersController(IDistributedCacheServiceFactory cache, IAzureAdGraphService graphService, IMapper mapper)
        {
            this.cache = cache;
            this.graphService = graphService;
            this.mapper = mapper;
        }

        [AllowAnonymous]
        [HttpGet, Route("/user/picture")]
        public async Task<IActionResult> Picture()
        {
            if (this.User.Identity.IsAuthenticated)
            {
                return new FileContentResult(await this.graphService.GetUserPicture(this.User.GetEmail()), "image/jpeg");
            }
            else
            {
                return new FileContentResult(Convert.FromBase64String("/9j/4AAQSkZJRgABAQEAYABgAAD/4QCMRXhpZgAATU0AKgAAAAgABwEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAARAAAAclEQAAEAAAABAQAAAFERAAQAAAABAAAAAFESAAQAAAABAAAAAAAAAAAAAABgAAAAAQAAAGAAAAABcGFpbnQubmV0IDQuMC4yMQAA/9sAQwACAQECAQECAgICAgICAgMFAwMDAwMGBAQDBQcGBwcHBgcHCAkLCQgICggHBwoNCgoLDAwMDAcJDg8NDA4LDAwM/9sAQwECAgIDAwMGAwMGDAgHCAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgAPAA8AwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A/aCiiitDMKK4P4t/F/8A4QlvsNiscupOu5mblbcHpkd2PYfjXkd/8Qdc1O4Mk2rX5YnOFmZFH0C4A/CnygfTFNd9orwXwd8ctY8N3KLeTSalZ5w6THdIB6q3XP1yP517Tp2uW+vaZDeWsnmW9wodG9v/AK3SkBPPNVN7jDUXM9U3uPmoA6SiiigD5f8AFepyax4mv7qUkvNO7c9hk4H4DA/Cs8nFdN8WfCMvhLxldKVP2a6dp4HxwVY5I/AnH5etcrLJWgDZZM163+zvrEk3hm+tWYsttOGTPYMOR+Yz+NeOySYr2v4O+G5PC3g4NcKUuL5/PZSOUXGFB/Dn8aUgOturjFZ0t189LeXWM1mTXWJKgD0iiivNfj/8SJvD9tHpNjI0dxdJvnkU/NHGeAB6E8/gPegC78UfH3hOS2bTdUY37K3KW67nhb1DZABHpn6ivNrjwV4X1A+ZaeKlt4zz5dzbHev45XP4CuLkkqrPLV2A9A0S78E+CtUikkurrWJ1ORL5GIYT67Tyf/Hq9IsfE9n4isRcWNzHcQtxlD0PoR1B9jXzTcT1N4Y8bXXg3WEurZzt4EsWfllXuD/Q9qTQH0Je3WO9Zc95iQ/NUUeuR6tp8N1C26K4QSIfYjNVGmy1SB7WzbRXzb8ZL9734k6qzH7sojA9AqgD+VfRtwxBr5n+K7f8XC1f/r4b+lVEDm5pcCqdxNUly55rPuHOKoCO6nxms66uKluXPNZ125z+FAHsvwf1BrrwFbqxz5Mjov0zn+tdA0+D2rkfgs3/ABQi/wDXd/6V1mKlgf/Z"), "image/jpeg");
            }
        }

        [HttpGet, Route("/manage/users/find"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Find(string term)
        {
            var allUsers = await this.cache.CreateMappedEntity<User, UserFindResultModel>()
                .Configure(x => x.Include(y => y.UserClaim).ThenInclude(y => y.Claim)).AllAsync();
            
            List<UserFindResultModel> dbUsers = allUsers.Where(x =>
                x.Email.StartsWith(term, StringComparison.InvariantCultureIgnoreCase) ||
                x.GivenName?.StartsWith(term, StringComparison.InvariantCultureIgnoreCase) == true ||
                x.FamilyName?.StartsWith(term, StringComparison.InvariantCultureIgnoreCase) == true
            ).ToList();

            List<Microsoft.Graph.Models.User> graphUsers = await this.graphService.FindUsers(term);

            var graphUsersNotInDb = mapper.Map<List<UserFindResultModel>>(graphUsers.Where(x => !dbUsers.Any(y => y.Value.ToLower() == x.GetEmail().ToLower())));
            var graphUsersAlreadyInDb = dbUsers.Where(x => graphUsers.Any(y => y.GetEmail().ToLower() == x.Value.ToLower()));
            var users = new List<UserFindResultModel>(graphUsersNotInDb);
            users.AddRange(graphUsersAlreadyInDb);

            return this.Json(users.OrderBy(x => x.Name));
        }

        [HttpGet, Route("/manage/users"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Index()
        {
            var users = cache.CreateMappedEntity<User, UserModel>()
                .Configure(x => x
                    .Include(y => y.UserClaim)
                    .ThenInclude(y => y.Claim));
            var usersWithClaims = await users.WhereAsync(x =>
                x.UserClaim.Any(x => x.Claim.ClaimType == "admin" || x.Claim.ClaimType == "news"));

            return View(usersWithClaims);
        }

        [HttpGet, Route("/manage/users/new"), Authorize(Policy = "UserAdmin")]
        public IActionResult New()
        {
            return View("Edit", new UserModel());
        }

        [HttpPost, Route("/manage/users/new"), ValidateAntiForgeryToken, Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> New([Bind("Email,GivenName,FamilyName,Claims,InvitationEmailLink,CompanyName")] UserModel user)
        {
            if (this.ModelState.IsValid)
            {
                var users = cache.CreateTrackedEntity<User>();
                User u = await users.FirstOrDefaultAsync(x => x.Email.ToLower() == user.Email.ToLower());
                if (u == null)
                {
                    users.Add(mapper.Map<UserModel, User>(user));
                    await users.SaveChangesAsync();
                }
                return Redirect("/manage/users");
            }
            return View("Edit", user);
        }

        [HttpGet, Route("/manage/users/edit/{id}"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Edit(int id)
        {
            var user = await cache.CreateMappedEntity<User, UserModel>()
                .Configure(x => x
                    .Include(y => y.CompanyUser)
                    .Include(y => y.UserClaim)
                    .ThenInclude(y => y.Claim))
                .FirstOrDefaultAsync(x => x.Id == id && x.CompanyUser == null);

            if(user == null)
            {
                return this.Redirect("/manage/users");
            }

            return this.View(user);
        }

        [HttpPost, Route("/manage/users/edit/{id}"), ValidateAntiForgeryToken, Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Edit(int id, UserModel user)
        {
            if (this.ModelState.IsValid && await this.ValidateClaims(user))
            {
                var users = cache.CreateTrackedEntity<User>()
                    .Configure(x => x
                        .Include(y => y.CompanyUser)
                        .Include(y => y.UserClaim)
                        .ThenInclude(y => y.Claim));
                User u = await users.FirstOrDefaultAsync(x => x.Id == id && x.CompanyUser == null);
                if(u == null)
                {
                    return this.BadRequest();
                }

                user.LastLoginDate = u.LastLoginDate;

                if (this.User.IsSuperAdmin())
                {
                    mapper.Map(user, u);
                }
                else
                {
                    int[] newsClaims = (await this.cache.CreateEntity<Claim>().WhereAsync(x => x.ClaimType == "news")).Select(x => x.Id).ToArray();
                    int[] claims = this.User.Claims.Where(x => x.Type.StartsWith("admin:")).Select(x => Int32.Parse(x.Value)).Union(newsClaims).ToArray();
                    List<UserClaim> claimsToKeep = u.UserClaim.Where(x => !claims.Contains(x.ClaimId)).ToList();
                    mapper.Map(user, u);
                    foreach (UserClaim uc in claimsToKeep)
                    {
                        u.UserClaim.Add(uc);
                    }
                }

                await users.SaveChangesAsync();
                return Redirect("/manage/users");
            }
            return View(user);
        }

        private async Task<bool> ValidateClaims(UserModel user)
        {
            if (this.User.IsSuperAdmin())
            {
                return true;
            }
            int[] newsClaims = (await this.cache.CreateEntity<Claim>().WhereAsync(x => x.ClaimType == "news")).Select(x => x.Id).ToArray();
            int[] claims = this.User.Claims.Where(x => x.Type.StartsWith("admin:")).Select(x => int.Parse(x.Value)).Union(newsClaims).ToArray();
            if (user.Claims.Any(x => !claims.Contains(x)))
            {
                throw new UnauthorizedAccessException("The logged in user does not have permission to set the attempted claim(s)");
            }
            return true;
        }
    }
}