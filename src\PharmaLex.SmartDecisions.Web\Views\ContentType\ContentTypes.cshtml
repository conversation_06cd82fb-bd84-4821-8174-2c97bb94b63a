﻿@model List<ContentTypeModel>
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.SmartDecisions.Web.Models
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@{
    ViewData["Title"] = "Content Types";
    var categories = mapper.Map<IEnumerable<PicklistItemModel>>(Enum.GetValues(typeof(ContentTypeCategory)));
}

<div id="content-types">
    <div class="sub-header">
        <h2>Content Types</h2>
        <div class="controls">
            <a class="button" href="/manage/content-type/new">Add</a>
        </div>
    </div>
    <form id="exportForm" style="display: none;" method="post" action="/manage/content-type/export"></form>

    <filtered-table :items="contentTypes" :columns="columns" :filters="filters" :link="link"></filtered-table>

    
</div>

@section Scripts {
<script type="text/javascript">
        var pageConfig = {
            appElement: '#content-types',
            data: function () {
                return {
                    link: '/manage/content-type/edit/',
                    contentTypes: @Html.Raw(Model.ToJson()),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'name',
                                sortKey: 'name',
                                sortDirection: 1,
                                type: 'text',
                                header: 'Name',
                                style: 'width: 45%;'
                            },
                            {
                                dataKey: 'category',
                                sortKey: 'category',
                                header: 'Category',
                                type: 'text',
                                style: 'width: 45%;'
                            },
                            {
                                dataKey: 'system',
                                sortKey: 'system',
                                header: 'System',
                                type: 'bool',
                                style: 'width: 50px;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'name',
                            options: [],
                            type: 'search',
                            header: 'Search Name',
                            fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'category',
                            options: @Html.Raw(categories.ToJson()),
                            filterCollection: 'contentTypeCategoryId',
                            display: 'name',
                            type: 'select',
                            header: 'Filter by Category',
                            fn: v => p => p.contentTypeCategoryId === v,
                            convert: v => parseInt(v)
                        }
                    ]
                };
            }
        };
</script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}