﻿using Azure.Identity;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Pharmalex.SmartDecisions.Web.Authorization;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartDecisions.Data.Persistance.Repository;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Authentication;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Helpers.Authorization;
using PharmaLex.SmartDecisions.Web.Helpers.AzureSearch;
using PharmaLex.SmartDecisions.Web.Helpers.HealthChecks;
using PharmaLex.SmartDecisions.Web.Helpers.HealthChecks.KeyVault;
using PharmaLex.SmartDecisions.Web.Helpers.HealthChecks.SendGrid;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Globalization;
using System.Linq;
using System.Reflection;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.HtmlToPdfConverter;
using PharmaLex.HtmlToPdfConverter.Interfaces;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;

namespace PharmaLex.SmartDecisions.Web
{
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Configuration = configuration;
            this.Environment = environment;
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddScoped<IUserContext, PlxUserContext>();           
            services.RegisterDbContext<SmartDecisionsContext>(); 
            services.AddScoped<PlxDbContext, SmartDecisionsContext>();

            services.AddScoped<IDistributedCacheServiceFactory, DistributedCacheServiceFactory>();
            services.AddSingleton<IDistributedCacheService, DistributedCacheService>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton<ITicketReceivedCallback, AddClaimsCallback>();

            services.AddHsts(options =>
            {
                options.Preload = true;
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(365);
            });

            services.AddAuthorization(options =>
            {
                options.FallbackPolicy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .Build();
            }).AddPolicies();

            services
                .AddOptions()
                .Configure<AzureAdGraphOptions>(this.Configuration.GetSection("AzureAdGraph"))
                .Configure<AzureAdB2CGraphOptions>(this.Configuration.GetSection("AzureAdB2CGraph"))
                .Configure<CookiePolicyOptions>(options =>
                {
                    options.CheckConsentNeeded = _ => true;
                    options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
                    options.HandleSameSiteCookieCompatibility();
                    options.HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always;
                    options.Secure = CookieSecurePolicy.Always;
                })
                .ConfigureMicrosoftItentityWebAuthentication(Configuration)
                .Configure<CookieAuthenticationOptions>(IdentityConstants.ApplicationScheme, options =>
                {
                    options.Cookie.HttpOnly = true;
                    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                })
                .ConfigureCustomAuthentication(Configuration);

            services
                .AddSession(options =>
                 {
                     options.Cookie.IsEssential = true;
                     options.Cookie.HttpOnly = true;
                     options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                 })
                .AddControllersWithViews(options =>
                {
                    options.Filters.Add(new AutoValidateAntiforgeryTokenAttribute());
                })
                .AddMicrosoftIdentityUI()
                .AddSessionStateTempDataProvider()
                .AddAzureAdB2CAuthenticationUI()
                .AddRazorRuntimeCompilation();

            services.AddAntiforgery(options =>
            {
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            });

            services.AddAutoMapper(typeof(NewsArticleModel).Assembly);

            services.Configure<StorageSettings>(Configuration.GetSection("AzureStorage"));
            services.AddTransient<IGraphClientProvider<AzureAdB2CGraphOptions>, AzureAdB2CGraphClientProvider>();
            services.AddTransient<IGraphClientProvider<AzureAdGraphOptions>, AzureAdGraphClientProvider>();
            services.AddScoped<IAzureAdB2CGraphService, AzureAdB2CGraphService>();
            services.AddScoped<IAzureAdGraphService, AzureAdGraphService>();
            services.AddScoped<ICompanyUserManagementService, CompanyUserManagementService>();
            services.AddSingleton<AppSettingsHelper>();
            services.AddSingleton<VersionCdnHelper>();
            services.AddScoped<IContentItemExportWriter, ContentItemExportWriter>();
            services.AddScoped<IContentItemImportReader, ContentItemImportReader>();
            services.AddScoped<ITopicService, TopicService>();
            services.AddScoped<IContentService, ContentService>();
            services.AddScoped<ISearchService, SearchService>();
            services.AddScoped<INewsArticleService, NewsArticleService>();
            services.AddScoped<INewsCategoryLicenseHelper, NewsCategoryLicenseHelper>();
            services.AddScoped<INewsCategoryHelper, NewsCategoryHelper>();
            services.AddScoped<IDbAccessProvider, DbAccessProvider>();
            services.AddScoped<IHttpHelper, HttpHelper>();
            services.AddScoped<INewsletterActivityRepository, NewsletterActivityRepository>();
            services.AddScoped<INewsletterActivityService, NewsletterActivityService>();
            services.AddScoped<IContentItemRepository, ContentItemRepository>();

            services.AddScoped<IDecisionsBlobStorage, DecisionsBlobStorage>(x => new DecisionsBlobStorage(x.GetRequiredService<IOptions<StorageSettings>>(), Configuration["VisualStudioTenantId"]));
            services.AddScoped<IDecisionsBlobContainer, DecisionsBlobContainer>();
            services.AddScoped<ILocaleService, LocaleService>();
            services.AddScoped<ILocalisationService, LocalisationService>();
            services.AddSingleton<SmartDecisionsAppSettingsHelper>();

            var azureCredentialOptions = new DefaultAzureCredentialOptions() { VisualStudioTenantId = Configuration["VisualStudioTenantId"] };
            var azureSearchSection = Configuration.GetSection("AzureSearch");
            services.Configure<SearchSettings>(azureSearchSection);
            var searchSettings = azureSearchSection.Get<SearchSettings>();
            services.AddScoped<IAzureSearchHelper, AzureSearchHelper>();
            services.AddScoped<IHtmlToPdfConverterService, HtmlToPdfConverterService>();
            var searchEndpointUri = new Uri($"https://{searchSettings.ServiceName}.search.windows.net/");

            services.AddScoped(_ =>
            {
                var searchClient = new SearchClient(searchEndpointUri, searchSettings.IndexName, new DefaultAzureCredential(azureCredentialOptions));
                return searchClient;
            })
                .AddScoped(_ =>
                {
                    var searchIndexClient = new SearchIndexClient(searchEndpointUri, new DefaultAzureCredential(azureCredentialOptions));
                    return searchIndexClient;
                })
                .AddScoped(_ =>
                {
                    var searchIndexerClient = new SearchIndexerClient(searchEndpointUri, new DefaultAzureCredential(azureCredentialOptions));
                    return searchIndexerClient;
                });

            JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };

            services.AddOptions<RequestLocalizationOptions>().Configure<IServiceProvider>(async (options, provider) =>
            {
                using var scope = provider.CreateScope();
                var cache = scope.ServiceProvider.GetRequiredService<IDistributedCacheServiceFactory>();
                var locales = (await cache.CreateEntity<Locale>().WhereAsync(x => x.Active)).ToList();
                var defaultLocale = locales.First(x => x.Default);
                var defaultCulture = new CultureInfo(
                    string.IsNullOrWhiteSpace(defaultLocale.IsoCountryCode) ?
                        defaultLocale.IsoLanguageCode :
                        $"{defaultLocale.IsoLanguageCode}-{defaultLocale.IsoCountryCode}");

                options.DefaultRequestCulture = new RequestCulture(defaultCulture, defaultCulture);
                options.SupportedUICultures = options.SupportedCultures =
                    locales.Select(x => new CultureInfo(
                        string.IsNullOrWhiteSpace(x.IsoCountryCode) ?
                            x.IsoLanguageCode :
                            $"{x.IsoLanguageCode}-{x.IsoCountryCode}")).ToList();

                options.FallBackToParentUICultures = true;
            });

            var keyVaultEndpointUri = new Uri($"https://{Configuration["KeyVaultName"]}.vault.azure.net/");
            var azureKeyVaultOptions = new AzureKeyVaultOptions();
            azureKeyVaultOptions.AddSecret("ConnectionStrings--default");
            const string Ready = "ready";
            
            services.AddHealthChecks()
                     .AddDbContextCheck<SmartDecisionsContext>()
                     .AddCheck("Azure-Key-Vault-Check",
                             new AzureKeyVaultHealthCheck(keyVaultEndpointUri, new DefaultAzureCredential(azureCredentialOptions), azureKeyVaultOptions),
                             HealthStatus.Unhealthy,
                             new string[] { Ready })
                     .AddCheck("Azure-Blob-Storage-Check",
                             new AzureBlobStorageHealthCheck(new BlobServiceClient(Configuration["AzureStorage:ConnectionString"]), Configuration.GetSection("AzureStorage:Container").Value),
                             HealthStatus.Unhealthy,
                             new string[] { Ready })
                      .AddCheck("Azure-Search-Check",
                             new AzureSearchHealthCheck(searchEndpointUri, searchSettings.IndexName, searchSettings.ApiKey),
                             HealthStatus.Unhealthy,
                             new string[] { Ready })
                      .AddSendGrid(Configuration["ConnectionStrings:SendGrid"], "SendGrid-Check", HealthStatus.Unhealthy, new[] { Ready });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment environment, IServiceProvider serviceProvider)
        {
            var locOptions = app.ApplicationServices.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(locOptions.Value);

            if (environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();

            //Add MIME type to avoid pdfjs viewer.ftl error 
            var provider = new FileExtensionContentTypeProvider
            {
                Mappings =
                {
                    [".ftl"] = "text/plain"
                }
            };

            app.UseCookiePolicy(
                new CookiePolicyOptions
                {
                    Secure = CookieSecurePolicy.Always,
                    HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always
                }
            );

            app.Use(async (context, next) =>
            {
                if (!context.Response.Headers.ContainsKey("Content-Security-Policy"))
                {
                    var cdn = Configuration.GetValue<string>("Static:Cdn");
                    context.Response.Headers.Append("Content-Security-Policy", "default-src 'self'; "
                        + "connect-src 'self' https://cdn.tiny.cloud/1/d1bebbpjpekufn4wupmtojcyuuwhwz45h5jjcaya1ddatlbe/tinymce/5.10.9-138/cdn-init https://cdn.tiny.cloud/1/d1bebbpjpekufn4wupmtojcyuuwhwz45h5jjcaya1ddatlbe/account-message" + (environment.IsDevelopment() ? "wss: ws: http:" : "") + "; "
                        + $"script-src 'self' 'unsafe-inline' 'unsafe-eval' {cdn}; "
                        + $"script-src-elem 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tiny.cloud/1/d1bebbpjpekufn4wupmtojcyuuwhwz45h5jjcaya1ddatlbe/tinymce/ {cdn}; "
                        + $"style-src 'self' 'unsafe-inline' {cdn}; "
                        + $"style-src-elem 'self' 'unsafe-inline' https://cdn.tiny.cloud/1/d1bebbpjpekufn4wupmtojcyuuwhwz45h5jjcaya1ddatlbe/tinymce/ {cdn}; "
                        + $"img-src 'self' https://sp.tinymce.com/ data: {cdn}; "
                        + $"font-src 'self' {cdn}; "
                        + $"manifest-src 'self' {cdn}; "
                        + "frame-ancestors 'self'; "
                        + "object-src 'none';");
                }
                await next();
            });

            app.UseStaticFiles(new StaticFileOptions
            {
                ContentTypeProvider = provider
            });
            app.UseStaticFiles();

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseSession();

            var responseWriter = new UIResponseWriter(new AppSettingsHelper(Configuration));

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapDefaultControllerRoute();
                endpoints.MapRazorPages();
                endpoints.MapHealthChecks("/Health", new HealthCheckOptions
                {
                    ResponseWriter = responseWriter.WriteResponse
                }).AllowAnonymous();
            });
        }
    }
}
