<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter Template</title>
    <style>
        @media all {

            html, body, section#news-wall {
                break-before: avoid;
                break-inside: avoid;
                break-after: avoid;
            }

            div, section, h1, span, header, i {
                break-before: avoid;
                break-inside: avoid;
                break-after: avoid;
            }

                div#news-wall {
                    break-before: avoid;
                    break-inside: avoid;
                    break-after: auto;
                }

            article {
                break-before: avoid;
                break-inside: avoid;
                break-after: avoid;
            }

            section.content {
                break-before: avoid;
                break-inside: auto;
                break-after: avoid;
            }

            .footer {
                break-before: avoid;
                break-inside: avoid;
                break-after: avoid;
            }
        }

        #news-wall {
            font-family: Helvetica, Arial, Verdana, sans-serif;
            font-size: 13px;
            padding: 50px;
        }

            #news-wall a {
                color: #333333;
            }
/*
            #news-wall article {
                padding: 0 0 10px 0;
            }*/

                #news-wall article h1 {
                    font-weight: 400;
                    font-size: 24px;
                    line-height: 29px;
                    border-top: 1px solid #E5E5E5;
                    margin: 0;
                    padding: 10px 0;
                }

                #news-wall article section {
                    padding: 15px 0;
                    border-top: 1px solid #E5E5E5;
                }
            #news-wall article section.footer {
                padding: 0;
                padding-top: 15px;
                border-top: 1px solid #E5E5E5;
            }
                    #news-wall article section.meta {
                        display: flex;
                    }

                        #news-wall article section.meta div {
                            display: flex;
                            padding: 0 30px;
                            overflow: hidden;
                            box-sizing: border-box;
                        }

                            #news-wall article section.meta div strong {
                                padding: 0 5px 0 0;
                            }

                            #news-wall article section.meta div span {
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }

                            #news-wall article section.meta div:first-of-type {
                                padding-left: 0;
                            }

                            #news-wall article section.meta div:last-of-type {
                                padding-right: 0;
                            }

                    #news-wall article section#pills {
                        padding-bottom: 5px;
                    }

            #news-wall #pills span {
                display: inline-block;
                margin: 0 5px 10px 0;
                padding: 5px 12px;
                border-radius: 12px;
                color: #FFFFFF;
            }

            #news-wall span.purple {
                background-color: #4A1671;
            }

            #news-wall span.gray {
                background-color: #557595;
            }

            #news-wall span.orange {
                background-color: #DF8043;
            }

            #news-wall span.red {
                background-color: #B10000;
            }


        .footer {
            display: flex;
            font-size: 12px;
        }

            .footer div {
                display: flex;
            }

            .footer span {
                display: block;
                width: 20px;
                height: 20px;
            }

            .footer i {
                display: block;
                line-height: 20px;
                padding: 0 13px 0 8px;
                font-style: normal;
            }

        @media screen and (max-width: 1200px) {

            #news-wall {
                padding: 30px;
            }

                #news-wall article section.meta {
                    flex-wrap: wrap;
                    padding-bottom: 5px !important;
                }

                    #news-wall article section.meta div {
                        margin-bottom: 10px;
                        padding: 0;
                        width: 50%;
                    }

                        #news-wall article section.meta div:nth-child(odd) {
                            padding-right: 5px;
                        }

                        #news-wall article section.meta div:nth-child(even) {
                            padding-left: 5px;
                        }

                        #news-wall article section.meta div.source {
                            width: 100%;
                        }
        }

        @media screen and (max-width: 768px) {

            #news-wall {
                font-size: 12px;
                padding: 20px;
            }

                #news-wall article section.meta div {
                    flex-direction: column;
                }
        }

        @media screen and (max-width: 480px) {

            .footer {
                flex-wrap: wrap;
            }

                .footer div {
                    width: 50%;
                    margin-bottom: 10px;
                }
        }
    </style>

</head>
<body>

    <section id="news-wall">
        <article>
            <h1>{{title}}</h1>
            <section class="meta">
                <div>
                    <strong>Newsletter Publication Date</strong>
                    <span>{{newsletterPublicationDate}}</span>
                </div>
                <div>
                    <strong>Source Publication Date</strong>
                    <span>{{sourcePublicationDate}}</span>
                </div>
                <div>
                    <strong>Publisher</strong>
                    <span>{{publisher}}</span>
                </div>
            </section>
            <section class="meta">
                <div class="source">
                    <strong>Source URL </strong>
                    <span>
                        <a href="{{sourceUrl}}"
                           target="_blank">
                            {{sourceUrl}}
                        </a>
                    </span>
                </div>
            </section>
            <section id="pills">
                {{products}}
                {{themes}}
                {{typeOfText}}
                {{newsSources}}
            </section>
            <section class="content">
                {{body}}
            </section>
            <section class="footer">
                <div><span class="purple"></span><i>Products</i></div>
                <div><span class="gray"></span><i>Themes</i></div>
                <div><span class="orange"></span><i>Type of Text</i></div>
                <div><span class="red"></span><i>News Source</i></div>
            </section>
        </article>
    </section>

</body>
</html>