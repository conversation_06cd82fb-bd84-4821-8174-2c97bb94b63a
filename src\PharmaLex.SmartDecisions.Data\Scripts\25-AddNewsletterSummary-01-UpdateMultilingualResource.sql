﻿insert into [dbo].[MultilingualResource] select 1, 'newsletter-summary', 'Newsletter Summary', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, N'newsletter-summary', 'Sommaire de la Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

update [dbo].[MultilingualResource]
set Content = 'First Monday'
where [Key] = '(news).subscribe.first-monday' and LocaleId = 1

update [dbo].[MultilingualResource]
set Content = 'Envoi le premier lundi du mois'
where [Key] = '(news).subscribe.first-monday' and LocaleId = 2

insert into [dbo].[MultilingualResource] select 1, 'preferences-login', 'To see articles in the order of your preferences, please login', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'preferences-login', N'Pour voir les articles dans l''ordre de vos préférences, merci de vous connecter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'