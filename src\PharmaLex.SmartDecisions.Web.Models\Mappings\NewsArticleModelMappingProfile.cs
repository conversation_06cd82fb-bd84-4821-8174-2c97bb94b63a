﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class NewsArticleModelMappingProfile : Profile
    {
        public NewsArticleModelMappingProfile()
        {
            this.CreateMap<NewsArticle, NewsArticleModel>()
                .ForMember(x => x.SourcePublicationDate, s => s.MapFrom(x => x.SourcePublicationDate.ToString("yyyy-MM-dd")))
                .ForMember(x => x.NewsCategoryIds, s => s.MapFrom(x => x.NewsArticleCategory.Select(y => y.NewsCategoryId)))
                .ForMember(d => d.SelectedLocaleId, o => o.Ignore())
                .ForMember(d => d.IsMigrated, o => o.Ignore());

            this.CreateMap<NewsArticleModel, NewsArticle>()
                .ForMember(d => d.Id, s => s.Ignore())
                .ForMember(d => d.CreatedDate, o => o.Ignore())
                .ForMember(d => d.CreatedBy, o => o.Ignore())
                .ForMember(d => d.LastUpdatedDate, o => o.Ignore())
                .ForMember(d => d.LastUpdatedBy, o => o.Ignore())
                .ForMember(d => d.NewsArticleContent, s => s.Ignore())
                .ForMember(d => d.NewsSource, s => s.Ignore())
                .ForMember(d => d.NewsArticleCategory, s => s.Ignore())
                .ForMember(d => d.ImportanceId, s => s.MapFrom(x => x.ImportanceId ?? 0))
                .AfterMap((m, a) =>
                {
                    foreach (var toAddId in m.NewsCategoryIds.Where(x => !a.NewsArticleCategory.Any(y => y.NewsCategoryId == x)))
                    {
                        a.NewsArticleCategory.Add(new NewsArticleCategory
                        {
                            NewsCategoryId = toAddId
                        });
                    }
                    foreach (var toRemoveCategory in a.NewsArticleCategory.Where(x => !m.NewsCategoryIds.Contains(x.NewsCategoryId)))
                    {
                        a.NewsArticleCategory.Remove(toRemoveCategory);
                    }
                });

            this.CreateMap<NewsArticleContent, NewsArticleContentModel>()
                .ForMember(d => d.Body, s => s.Ignore())
                .ForMember(d => d.BodyBase64, s => s.Ignore())
                .ForMember(d => d.BodyUrl, s => s.Ignore())
                .ForMember(d => d.BlobName, s => s.Ignore())
                .ForMember(d => d.Path, s => s.Ignore())
                .ForMember(d => d.ImpactAssessmentText, s => s.Ignore());

            this.CreateMap<NewsArticleContentModel, NewsArticleContent>()
                .BeforeMap((m, c) =>
                {
                    if (c.PublishingStateId != m.PublishingStateId)
                    {
                        c.PublishingStateDateUtc = System.DateTime.UtcNow;
                    }
                })
                .ForMember(d => d.Id, s => s.Ignore())
                .ForMember(d => d.NewsArticleId, s => s.Ignore())
                .ForMember(d => d.PublishingStateDateUtc, s => s.Ignore())
                .ForMember(d => d.AzureBlobId, s => s.Ignore())
                .ForMember(d => d.FriendlyUrl, s => s.Ignore())
                .ForMember(d => d.Locale, s => s.Ignore())
                .ForMember(d => d.NewsArticle, s => s.Ignore())
                .ForMember(d => d.Author, s => s.Ignore())
                .ForMember(d => d.Reviewer, s => s.Ignore())
                .ForMember(d => d.AzureBlob, s => s.Ignore())
                .ForMember(d => d.CreatedDate, s => s.Ignore())
                .ForMember(d => d.CreatedBy, s => s.Ignore())
                .ForMember(d => d.LastUpdatedDate, s => s.Ignore())
                .ForMember(d => d.LastUpdatedBy, s => s.Ignore());
        }
    }
}
