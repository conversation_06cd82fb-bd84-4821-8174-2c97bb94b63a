﻿create trigger [dbo].[NewsArticle_Insert] on [dbo].[NewsArticle]
for insert as
insert into [Audit].[NewsArticle_Audit]
select 'I'
      ,[Id]
      ,[NewsSourceId]
      ,[ImportanceId]
      ,[SourcePublicationDate]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsArticle_Update] on [dbo].[NewsArticle]
for update as
insert into [Audit].[NewsArticle_Audit]
select 'U'
      ,[Id]
      ,[NewsSourceId]
      ,[ImportanceId]
      ,[SourcePublicationDate]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsArticle_Delete] on [dbo].[NewsArticle]
for delete as
insert into [Audit].[NewsArticle_Audit]
select 'D'
      ,[Id]
      ,[NewsSourceId]
      ,[ImportanceId] 
      ,[SourcePublicationDate]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
