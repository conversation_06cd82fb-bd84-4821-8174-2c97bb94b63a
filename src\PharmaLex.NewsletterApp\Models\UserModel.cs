﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.NewsletterApp
{
    public class UserModel
    {
        public int Id { get; set; }
        public string Email { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public string DisplayFullName { get; set; }// => $"{this.GivenName} {this.FamilyName}";

        public string DisplayNameAndEmail
        {
            get { return this.Id > 0 ? $"{this.GivenName} {this.FamilyName} ({this.Email})" : ""; }
        }

        public LocaleModel Locale { get; set; }
    }
  
    public class UserMappingProfile : Profile
    {
        public UserMappingProfile()
        {
            this.CreateMap<User, UserModel>()
                .ForMember(d => d.DisplayFullName, s => s.MapFrom(x => $"{x.GivenName} {x.FamilyName}"));                                  
        }
    }
}
