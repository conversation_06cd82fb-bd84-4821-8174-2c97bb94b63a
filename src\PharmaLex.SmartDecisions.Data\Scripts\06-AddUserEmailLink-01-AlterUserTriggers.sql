﻿ALTER TRIGGER [dbo].[user_Insert] ON [dbo].[user]
FOR INSERT AS
INSERT INTO [Audit].[user_Audit]
(AuditAction,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId], [InvitationEmailLink])
SELECT 'I' ,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId], [InvitationEmailLink] FROM [Inserted]
GO
 
ALTER TRIGGER [dbo].[user_Update] ON [dbo].[user]
FOR UPDATE AS
INSERT INTO [Audit].[user_Audit]
(AuditAction,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId], [InvitationEmailLink])
SELECT 'U' ,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId], [InvitationEmailLink] FROM [Inserted]
GO
 
ALTER TRIGGER [dbo].[user_Delete] ON [dbo].[user]
FOR DELETE AS
INSERT INTO [Audit].[user_Audit]
(AuditAction,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId], [InvitationEmailLink])
SELECT 'D' ,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [LocaleId], [InvitationEmailLink] FROM [Deleted]
GO
 
