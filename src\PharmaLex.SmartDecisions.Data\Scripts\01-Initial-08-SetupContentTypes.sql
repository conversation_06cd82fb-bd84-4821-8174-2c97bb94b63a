﻿declare @ctid int
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Medicinal Product Domain',  'Medicinal Product Domains',  'Medicinal Product Domain',  1,  'system',  1,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Procedure Type',  'Procedure Types',  'Procedure Type',  1,  'system',  1,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Medicinal Product Type',  'Medicinal Product Type',  'Medicinal Product Type',  1,  'system',  1,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Product Category',  'Product Categories',  'Product Category',  1,  'system',  1,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Company Responsible for Submitting the MAH Transfer',  'Company Responsible for Submitting the MAH Transfers',  'Company Responsible for Submitt',  1,  '<EMAIL>',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission Type',  'Submission Types',  'Submission Type',  1,  'system',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Yes or No',  'Yes or No',  'Yes or No',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Timeline Definition',  'Timeline Definitions',  'Timeline Definition',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Dossier Format',  'Dossier Formats',  'Dossier Format',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Sample Type',  'Sample Types',  'Sample Type',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Labelling Document Details',  'Labelling Document Details',  'Labelling Document Details',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Legalisation Requirements',  'Legalisation Requirements',  'Legalisation Requirements',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Consequential Variation(s)',  'Consequential Variations',  'Consequential Variations',  1,  'System',  0,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Country',  'Countries',  'Country',  2,  'system',  1,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Short name', @ctid,  1,  128,  1,  1,  'A shortened display name. Useful for countries with long official names.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Two letter code', @ctid,  1,  2,  1,  1,  'The two letter ISO-3166 country code',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Three letter code', @ctid,  1,  3,  1,  1,  'The three letter ISO-3166 country code',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'M49 numeric code', @ctid,  1,  3,  1,  1,  'The United Nations M49 numeric code',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Map ID', @ctid,  4,  null,  0,  1,  null,  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Regulatory Authority',  'Regulatory Authorities',  'Regulatory Authority',  2,  'system',  1,  0,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Market', @ctid,  9,  null,  1,  0,  'The market the authority covers',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Country'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Url', @ctid,  6,  256,  1,  1,  'The url of the authority''s website',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'National authority?', @ctid,  3,  null,  1,  0,  'Is the authority a national body?',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Countries', @ctid,  9,  2,  1,  0,  null,  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Country'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Acronym', @ctid,  1,  16,  0,  1,  'An accepted acronym or abbreviation for the authority''s name.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Marketing Authorisation Holder Transfer',  'Marketing Authorisation Holder Transfers',  'Marketing Authorisation Holder ',  3,  '<EMAIL>',  0,  1,  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = scope_identity()
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Market', @ctid,  9,  null,  1,  0,  null,  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Country'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Regulatory Authority', @ctid,  9,  1,  1,  0,  null,  1,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Regulatory Authority'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Medicinal Product Domain', @ctid,  8,  1,  1,  0,  null,  1,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Medicinal Product Domain'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Medicinal Product Type', @ctid,  8,  1,  1,  0,  null,  1,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Medicinal Product Type'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Procedure Type', @ctid,  8,  1,  1,  0,  null,  1,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Procedure Type'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Product Category', @ctid,  8,  1,  1,  0,  null,  1,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Product Category'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Company Responsible for Submitting the MAH Transfer', @ctid,  8,  null,  1,  0,  'Select company responsible for submitting the MAH Transfer.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Company Responsible for Submitting the MAH Transfer'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission Type', @ctid,  8,  null,  1,  0,  'Select the correct submission type to indicate how the MAH Transfer is regulated.  If an MAH Transfer is not regulated, and does not require any regulatory action, select Not Applicable.   ',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Submission Type'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Link to MAH Transfer Fee Information', @ctid,  6,  750,  0,  0,  'Paste the URL to the specific MAH Transfer fee information published by the Health Authority',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Link to Official MAH Transfer Guidance Document', @ctid,  6,  null,  0,  0,  'Paste the URL to the specific MAH Transfer guidance document published by the Health Authority',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Agency Approval - Timeline', @ctid,  1,  100,  0,  0,  null,  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Agency Approval - Definition', @ctid,  8,  null,  0,  0,  null,  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Timeline Definition'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Agency Approval - Further Information', @ctid,  1,  500,  0,  0,  'Specify further information relating to the timeline for obtaining approval of the MAH Transfer.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission Format', @ctid,  8,  null,  1,  0,  null,  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Dossier Format'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission Delivery', @ctid,  1,  100,  1,  0,  null,  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Parallel Submissions Allowed?', @ctid,  8,  null,  1,  0,  null,  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Parallel Submission of MAH Transfer and Variations Possible?', @ctid,  8,  null,  1,  0,  'Select Yes if if it is possible to submit variations in parallel with the MAH Transfer. ',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission of MAH Transfer during Ongoing Variation?', @ctid,  8,  null,  1,  0,  'Select Yes if it is possible to submit an MAH Transfer while variations are ongoing.  ',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission of MAH Transfer during Ongoing Renewal?', @ctid,  8,  null,  1,  0,  'Select Yes if it is possible to submit an MAH Transfer while a renewal is ongoing.  ',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission of Variations during Ongoing MAH Transfer?', @ctid,  8,  null,  1,  0,  'Select Yes if it is possible to submit variations while an MAH Transfer is ongoing.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Submission of Renewal during Ongoing MAH Transfer?', @ctid,  8,  null,  1,  0,  'Select Yes if it is possible to submit a renewal while an MAH Transfer is ongoing.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Parallel Submissions - Further Information', @ctid,  1,  500,  0,  0,  'Specify further information relating to parallel submissions.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Mock-ups Required?', @ctid,  8,  null,  1,  0,  'Select Yes if mockups have to be included in the MAH Transfer Submission Package.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Mock-ups - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to mock-up requirements.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Samples Required?', @ctid,  8,  null,  1,  0,  'Select Yes if samples are required by the Health Authority.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Type of Samples Required', @ctid,  8,  null,  1,  0,  'Select the type of samples required by the Health Authority.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Sample Type'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'CPP Required?', @ctid,  8,  null,  1,  0,  'Select Yes if a CPP is required by the Health Authority.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Type of CPP Required', @ctid,  8,  null,  1,  0,  'Select the relevant legalisation requirements.  If legalisation is not required, select Not Applicable.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Legalisation Requirements'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Timeline for Obtaining a CPP', @ctid,  1,  100,  0,  0,  'Specify the amount of time needed to obtain the CPP.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'CPP - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to CPP requirements.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'GMP Certificate Required?', @ctid,  8,  null,  1,  0,  'Select Yes if a GMP Certificate is required by the Health Authority.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Type of GMP Certificate Required', @ctid,  8,  null,  1,  0,  'Select the relevant legalisation requirements.  If legalisation is not required, select Not Applicable.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Legalisation Requirements'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Timeline for Obtaining a GMP Certificate', @ctid,  1,  100,  0,  0,  'Specify the amount of time needed to obtain the GMP Certificate.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'GMP Certificate - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to GMP Certificate requirements.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Labelling Update Required?', @ctid,  8,  null,  1,  0,  'Select Yes if labelling has to be updated.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Labelling Document Details', @ctid,  8,  null,  1,  0,  'Select the labelling document(s) that have to be updated because of the MAH Transfer.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Labelling Document Details'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Labelling Update - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to labelling update requirements.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Grace Period Possible?', @ctid,  8,  null,  1,  0,  'Select Yes if a grace period is possible.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Grace Period - Release (Duration)', @ctid,  1,  100,  0,  0,  'Specify the length of the grace period for releasing  products in the old livery after the MAH Transfer approval.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Grace Period - Import (Duration)', @ctid,  1,  100,  0,  0,  'Specify the length of the grace period for importing  products in the old livery after the MAH Transfer approval.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Grace Period - Sale (Duration)', @ctid,  1,  100,  0,  0,  'Specify the length of the grace period for the sale of  products in the old livery after the MAH Transfer approval.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Grace Period - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to grace period.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local MAH Required?', @ctid,  8,  null,  1,  0,  'Select Yes if the MAH must be located in the market.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Can a Legal Entity (not located in the country) act as MAH?', @ctid,  8,  null,  1,  0,  'Select Yes if a Legal Entity (not located in the market) can act as the MAH.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local Representative Required?', @ctid,  8,  null,  1,  0,  'Select Yes if a local representative in the market is required.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local Distributor Required?', @ctid,  8,  null,  1,  0,  'Select Yes if a local distributor in the market is required.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local Representative/Distributor - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to local representative/distributor information.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local Contact Person for Pharmacovigilance (LPPV) Required?', @ctid,  8,  null,  1,  0,  'Select Yes if a LPPV is required in the market.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'LPPV''s Name Required?', @ctid,  8,  null,  1,  0,  'Select Yes if the name of the LPPV has to be provided to the Health Authority.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'LPPV Resident within the Market?', @ctid,  8,  null,  1,  0,  'Select Yes if the LPPV has to be resident within the country.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'LPPV - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to the LPPV.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local Information Officer Required?', @ctid,  8,  null,  1,  0,  'Select Yes if a local Information Office is required in the market.',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Yes or No'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Local Information Officer - Further Information', @ctid,  1,  500,  0,  0,  'Provide further information relating to local Information Officer.',  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Mandatory Consequential Variations', @ctid,  8,  null,  1,  0,  'Select the consequential variations that must be submitted as a result of the MAH Transfe',  0,  [Id], getdate(), '<EMAIL>', getdate(), '<EMAIL>' from [ContentType] where [Name] = 'Consequential Variation(s)'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Mandatory Consequential Variation - Further Information', @ctid,  1,  500,  0,  0,  null,  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  'Market Dependency', @ctid,  1,  100,  1,  0,  null,  0,  null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'