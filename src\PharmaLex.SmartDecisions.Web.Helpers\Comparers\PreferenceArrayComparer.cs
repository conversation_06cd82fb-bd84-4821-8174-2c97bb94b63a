﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers.Comparers
{
    public class PreferenceArrayComparer : IComparer<int[]>
    {
        public int Compare(int[] x, int[] y)
        {
            int minLength = Math.Min(x.Length, y.Length);
            int i = 0;

            while (i < minLength)
            {
                if (x[i] < y[i]) return -1; //order x before y
                if (x[i] > y[i]) return 1;//order y before x
                i++;
            }

            if (i == x.Length && x.Length != y.Length) return 1; //y is longer. order y before x
            if (i == y.Length && x.Length != y.Length) return -1;  //x is longer. order x before y

            return 0; // x == y
        }
    }
}
