﻿plx = window.plx || {};

plx.dirty = {
    init: () => {
        plx.dirty.forms = [...document.querySelectorAll("form")];

        if (plx.dirty.forms.length) {
            console.log("%cInitialising dirty form events.", "font-size:12px;font-weight:bold;color:#0c0");

            plx.dirty.forms.forEach((form) => {
                const buttonElement = form.querySelector("button[type=submit]");
                if (buttonElement) {
                    plx.dirty.submitButtons.push(buttonElement);
                }

                plx.dirty.fields = [
                    ...plx.dirty.fields,
                    ...form.querySelectorAll("input, select, textarea")
                ];
            });

            plx.dirty.setListeners();
        }
    },
    setDirty: () => {
        plx.dirty.isDirty = true;
        console.log("%cForm is dirty.", "font-size:12px;font-weight:bold;color:#c00");
    },
    setListeners: () => {
        plx.dirty.fields.forEach((field) => {
            field.addEventListener("input", plx.dirty.setDirty);
            field.addEventListener("change", plx.dirty.setDirty);
        });

        plx.dirty.forms.forEach((form) => {
            form.addEventListener("submit", (e) => {
                plx.dirty.isDirty = false;
                plx.dirty.setListeners();
            });
        });

        window.addEventListener("beforeunload", (e) => {
            if (plx.dirty.isDirty) {
                e.returnValue = "Unsaved form detected. Are you sure you want to leave?"
            }
        });
    },
    isDirty: false,
    forms: [],
    fields: [],
    submitButtons: []
};