﻿using Microsoft.EntityFrameworkCore.Design;
using PharmaLex.DataAccess.Design;
using System.IO;

namespace PharmaLex.SmartDecisions.Entities
{
    public class DesignTimeContextFactory : IDesignTimeDbContextFactory<SmartDecisionsContext>
    {
        public SmartDecisionsContext CreateDbContext(string[] args)
        {
            return new DesignPlxDbContextResolver<SmartDecisionsContext>
                (Path.Combine(Directory.GetCurrentDirectory().Replace(".Entities", ".Web"), "appSettings.json"))
                .ServiceProvider.GetService(typeof(SmartDecisionsContext)) as SmartDecisionsContext;
        }
    }
}
