﻿DECLARE @parentId int
SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Geographical Scope')

INSERT INTO [dbo].[NewsCategory] SELECT N'United States (FDA)', @parentId, 1, 78, N'unitedstates', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].unitedstates', 'United States (FDA)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].unitedstates', 'États-Unis (FDA)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'