﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
		<GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="wwwroot\js\lib\pdfjs\web\locale\ak\**" />
		<Compile Remove="wwwroot\js\lib\pdfjs\web\locale\as\**" />
		<Compile Remove="wwwroot\js\lib\pdfjs\web\locale\bn-BD\**" />
		<Compile Remove="wwwroot\js\lib\pdfjs\web\locale\bn-IN\**" />
		<Content Remove="wwwroot\js\lib\pdfjs\web\locale\ak\**" />
		<Content Remove="wwwroot\js\lib\pdfjs\web\locale\as\**" />
		<Content Remove="wwwroot\js\lib\pdfjs\web\locale\bn-BD\**" />
		<Content Remove="wwwroot\js\lib\pdfjs\web\locale\bn-IN\**" />
		<EmbeddedResource Remove="wwwroot\js\lib\pdfjs\web\locale\ak\**" />
		<EmbeddedResource Remove="wwwroot\js\lib\pdfjs\web\locale\as\**" />
		<EmbeddedResource Remove="wwwroot\js\lib\pdfjs\web\locale\bn-BD\**" />
		<EmbeddedResource Remove="wwwroot\js\lib\pdfjs\web\locale\bn-IN\**" />
		<None Remove="wwwroot\js\lib\pdfjs\web\locale\ak\**" />
		<None Remove="wwwroot\js\lib\pdfjs\web\locale\as\**" />
		<None Remove="wwwroot\js\lib\pdfjs\web\locale\bn-BD\**" />
		<None Remove="wwwroot\js\lib\pdfjs\web\locale\bn-IN\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Remove="wwwroot\css\fonts.css" />
		<Content Remove="wwwroot\css\materialdesignicons.css" />
		<Content Remove="wwwroot\css\reset.css" />
		<Content Remove="wwwroot\css\screen.css" />
		<Content Remove="wwwroot\css\site.min.css" />
		<Content Remove="wwwroot\css\vuetify\2.0.16\vuetify.css" />
		<Content Remove="wwwroot\data\countries.json" />
		<Content Remove="wwwroot\data\countries.topo.js" />
		<Content Remove="wwwroot\fonts\interstate-bold-webfont.eot" />
		<Content Remove="wwwroot\fonts\interstate-bold-webfont.woff" />
		<Content Remove="wwwroot\fonts\interstate-bold.ttf" />
		<Content Remove="wwwroot\fonts\interstate-light-webfont.eot" />
		<Content Remove="wwwroot\fonts\interstate-light-webfont.woff" />
		<Content Remove="wwwroot\fonts\interstate-light.ttf" />
		<Content Remove="wwwroot\fonts\interstate-regular-webfont.eot" />
		<Content Remove="wwwroot\fonts\interstate-regular-webfont.svg" />
		<Content Remove="wwwroot\fonts\interstate-regular-webfont.ttf" />
		<Content Remove="wwwroot\fonts\interstate-regular-webfont.woff" />
		<Content Remove="wwwroot\fonts\interstate-regular-webfont.woff2" />
		<Content Remove="wwwroot\fonts\materialdesignicons-webfont.eot" />
		<Content Remove="wwwroot\fonts\materialdesignicons-webfont.ttf" />
		<Content Remove="wwwroot\fonts\materialdesignicons-webfont.woff" />
		<Content Remove="wwwroot\fonts\materialdesignicons-webfont.woff2" />
		<Content Remove="wwwroot\fonts\MaterialIcons-Regular.ttf" />
		<Content Remove="wwwroot\fonts\MaterialIconsOutlined-Regular.otf" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.eot" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.svg" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.ttf" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.woff" />
		<Content Remove="wwwroot\fonts\smartphlex-20201119.woff2" />
		<Content Remove="wwwroot\js\dialog.js" />
		<Content Remove="wwwroot\js\dialogV2.js" />
		<Content Remove="wwwroot\js\dirty-form-warning.js" />
		<Content Remove="wwwroot\js\form-submit-handler.js" />
		<Content Remove="wwwroot\js\map-bundle.min.js" />
		<Content Remove="wwwroot\js\map.js" />
		<Content Remove="wwwroot\js\NewsArticles.cshtml" />
		<Content Remove="wwwroot\js\templates.js" />
		<Content Remove="wwwroot\js\toast.js" />
		<Content Remove="wwwroot\js\vue\filtered-table.js" />
		<Content Remove="wwwroot\js\vue\plx-map.js" />
		<Content Remove="wwwroot\lib\d3\d3.v4.js" />
		<Content Remove="wwwroot\lib\d3\d3.v4.min.js" />
		<Content Remove="wwwroot\lib\d3\topojson.js" />
		<Content Remove="wwwroot\lib\d3\topojson.min.js" />
		<Content Remove="wwwroot\lib\datatables\datatables-moment.js" />
		<Content Remove="wwwroot\lib\datatables\datatables.js" />
		<Content Remove="wwwroot\lib\datatables\datatables.min.js" />
		<Content Remove="wwwroot\lib\jquery-validation-unobtrusive\.bower.json" />
		<Content Remove="wwwroot\lib\jquery-validation-unobtrusive\LICENSE.txt" />
		<Content Remove="wwwroot\lib\jquery-validation\.bower.json" />
		<Content Remove="wwwroot\lib\jquery-validation\dist\additional-methods.js" />
		<Content Remove="wwwroot\lib\jquery-validation\dist\additional-methods.min.js" />
		<Content Remove="wwwroot\lib\jquery-validation\LICENSE.md" />
		<Content Remove="wwwroot\lib\jquery\.bower.json" />
		<Content Remove="wwwroot\lib\jquery\LICENSE.txt" />
		<Content Remove="wwwroot\lib\moment\luxon.js" />
		<Content Remove="wwwroot\lib\moment\luxon.min.js" />
		<Content Remove="wwwroot\lib\mustache\mustache.js" />
		<Content Remove="wwwroot\lib\mustache\mustache.min.js" />
		<Content Remove="wwwroot\lib\tippy\4.3.5\tippy.min.js" />
		<Content Remove="wwwroot\lib\tippy\popper\1.15\popper.min.js" />
		<Content Remove="wwwroot\lib\uppy\uppy.min.css" />
		<Content Remove="wwwroot\lib\uppy\uppy.min.js" />
		<Content Remove="wwwroot\lib\vuetify\2.0.16\vuetify.js" />
		<Content Remove="wwwroot\lib\vuetify\2.0.16\vuetify.min.js" />
		<Content Remove="wwwroot\lib\vuex\3.1.1\vuex.js" />
		<Content Remove="wwwroot\lib\vuex\3.1.1\vuex.min.js" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="src\images\splash.jpg" />
		<None Remove="src\templates\newsletter.html" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="src\images\splash.jpg" />
		<EmbeddedResource Include="src\templates\newsletter.html">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="wwwroot\css\fonts.css" />
		<EmbeddedResource Include="wwwroot\css\materialdesignicons.css" />
		<EmbeddedResource Include="wwwroot\css\reset.css" />
		<EmbeddedResource Include="wwwroot\css\screen.css" />
		<EmbeddedResource Include="wwwroot\css\site.min.css" />
		<EmbeddedResource Include="wwwroot\css\vuetify\2.0.16\vuetify.css" />
		<EmbeddedResource Include="wwwroot\data\countries.json" />
		<EmbeddedResource Include="wwwroot\data\countries.topo.js" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-bold-webfont.eot" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-bold-webfont.woff" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-bold.ttf" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-light-webfont.eot" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-light-webfont.woff" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-light.ttf" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-regular-webfont.eot" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-regular-webfont.svg" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-regular-webfont.ttf" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-regular-webfont.woff" />
		<EmbeddedResource Include="wwwroot\fonts\interstate-regular-webfont.woff2" />
		<EmbeddedResource Include="wwwroot\fonts\materialdesignicons-webfont.eot" />
		<EmbeddedResource Include="wwwroot\fonts\materialdesignicons-webfont.ttf" />
		<EmbeddedResource Include="wwwroot\fonts\materialdesignicons-webfont.woff" />
		<EmbeddedResource Include="wwwroot\fonts\materialdesignicons-webfont.woff2" />
		<EmbeddedResource Include="wwwroot\fonts\MaterialIcons-Regular.ttf" />
		<EmbeddedResource Include="wwwroot\fonts\MaterialIconsOutlined-Regular.otf" />
		<EmbeddedResource Include="wwwroot\fonts\smartphlex-20201119.eot" />
		<EmbeddedResource Include="wwwroot\fonts\smartphlex-20201119.svg" />
		<EmbeddedResource Include="wwwroot\fonts\smartphlex-20201119.ttf" />
		<EmbeddedResource Include="wwwroot\fonts\smartphlex-20201119.woff" />
		<EmbeddedResource Include="wwwroot\fonts\smartphlex-20201119.woff2" />
		<EmbeddedResource Include="wwwroot\js\dialog.js" />
		<EmbeddedResource Include="wwwroot\js\dialogV2.js" />
		<EmbeddedResource Include="wwwroot\js\dirty-form-warning.js" />
		<EmbeddedResource Include="wwwroot\js\form-submit-handler.js" />
		<EmbeddedResource Include="wwwroot\js\map-bundle.min.js" />
		<EmbeddedResource Include="wwwroot\js\map.js" />
		<EmbeddedResource Include="wwwroot\js\templates.js" />
		<EmbeddedResource Include="wwwroot\js\toast.js" />
		<EmbeddedResource Include="wwwroot\js\vue\filtered-table.js" />
		<EmbeddedResource Include="wwwroot\js\vue\plx-map.js" />
		<EmbeddedResource Include="wwwroot\lib\d3\d3.v4.js" />
		<EmbeddedResource Include="wwwroot\lib\d3\d3.v4.min.js" />
		<EmbeddedResource Include="wwwroot\lib\d3\topojson.js" />
		<EmbeddedResource Include="wwwroot\lib\d3\topojson.min.js" />
		<EmbeddedResource Include="wwwroot\lib\datatables\datatables-moment.js" />
		<EmbeddedResource Include="wwwroot\lib\datatables\datatables.js" />
		<EmbeddedResource Include="wwwroot\lib\datatables\datatables.min.js" />
		<EmbeddedResource Include="wwwroot\lib\jquery-validation-unobtrusive\.bower.json" />
		<EmbeddedResource Include="wwwroot\lib\jquery-validation-unobtrusive\LICENSE.txt" />
		<EmbeddedResource Include="wwwroot\lib\jquery-validation\.bower.json" />
		<EmbeddedResource Include="wwwroot\lib\jquery-validation\dist\additional-methods.js" />
		<EmbeddedResource Include="wwwroot\lib\jquery-validation\dist\additional-methods.min.js" />
		<EmbeddedResource Include="wwwroot\lib\jquery-validation\LICENSE.md" />
		<EmbeddedResource Include="wwwroot\lib\jquery\.bower.json" />
		<EmbeddedResource Include="wwwroot\lib\jquery\LICENSE.txt" />
		<EmbeddedResource Include="wwwroot\lib\moment\luxon.js" />
		<EmbeddedResource Include="wwwroot\lib\moment\luxon.min.js" />
		<EmbeddedResource Include="wwwroot\lib\mustache\mustache.js" />
		<EmbeddedResource Include="wwwroot\lib\mustache\mustache.min.js" />
		<EmbeddedResource Include="wwwroot\lib\tippy\4.3.5\tippy.min.js" />
		<EmbeddedResource Include="wwwroot\lib\tippy\popper\1.15\popper.min.js" />
		<EmbeddedResource Include="wwwroot\lib\uppy\uppy.min.css" />
		<EmbeddedResource Include="wwwroot\lib\uppy\uppy.min.js" />
		<EmbeddedResource Include="wwwroot\lib\vuetify\2.0.16\vuetify.js" />
		<EmbeddedResource Include="wwwroot\lib\vuetify\2.0.16\vuetify.min.js" />
		<EmbeddedResource Include="wwwroot\lib\vuex\3.1.1\vuex.js" />
		<EmbeddedResource Include="wwwroot\lib\vuex\3.1.1\vuex.min.js" />
	</ItemGroup>

	<ItemGroup>
		<None Include="..\..\.editorconfig" Link=".editorconfig" />
		<None Include="wwwroot\js\lib\pdfjs\LICENSE" />
		<None Include="wwwroot\js\lib\pdfjs\web\cmaps\*" />
		<None Include="wwwroot\js\lib\pdfjs\web\compressed.tracemonkey-pldi-09.pdf" />
		<None Include="wwwroot\js\lib\pdfjs\web\images\*" />
		<None Include="wwwroot\js\lib\pdfjs\web\locale\*" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Identity" Version="1.13.2" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions" Version="8.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.11" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.3" />
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.5" />
		<PackageReference Include="NuGet.CommandLine" Version="6.10.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.8.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.8.0" />
		<PackageReference Include="NuGet.Commands" Version="6.10.0" />
		<PackageReference Include="NuGet.Common" Version="6.10.0" />
		<PackageReference Include="NuGet.PackageManagement" Version="6.10.0" />
		<PackageReference Include="NuGet.Protocol" Version="6.10.0" />
		<PackageReference Include="NewRelic.Agent" Version="10.34.0" />
		<PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.SmartDecisions.Data\PharmaLex.SmartDecisions.Data.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartDecisions.Entities\PharmaLex.SmartDecisions.Entities.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartDecisions.Web.Helpers\PharmaLex.SmartDecisions.Web.Helpers.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartDecisions.Web.Models\PharmaLex.SmartDecisions.Web.Models.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="wwwroot\democontent\" />
		<Folder Include="newrelic\" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="package-lock.json">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</Content>
		<Content Update="package.json">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</Content>
		<Content Update="Views\Shared\Components\AutoCompleteList.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\DigitalSignature.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\EditorTable.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\EntityAuditDialog.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\FilteredTable.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\FilteredTableV2.cshtml">
			<Pack>false</Pack>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Shared\Components\FilteredTableV3.cshtml">
			<Pack>false</Pack>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Shared\Components\PlxMap.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\UserNotification.cshtml">
			<Pack>false</Pack>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Shared\Components\Vue3\AutoCompleteList.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\Vue3\Contact.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\Vue3\DigitalSignature.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\Vue3\FilteredTable.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Components\Vue3\PlxMap.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Shared\Error.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="wwwroot\images\ImportanceHeadline.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\images\ImportanceMiscellaneous.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\images\ImportanceNewsFlash.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<PropertyGroup>
		<AddRazorSupportForMvc>true</AddRazorSupportForMvc>
		<EnableNETAnalyzers>false</EnableNETAnalyzers>
		<AnalysisLevel>none</AnalysisLevel>
		<RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
	</PropertyGroup>

	<ItemGroup>
		<UpToDateCheckInput Remove="Views\Shared\Components\AutoCompleteList.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<_ContentIncludedByDefault Remove="Views\Shared\Components\AutoCompleteList.cshtml" />
	</ItemGroup>
</Project>
