﻿using PharmaLex.SmartDecisions.Web.Models;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.Caching.Data;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using PharmaLex.DataAccess;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface IContentService
    {
        Task<IEnumerable<ContentItemModel>> ListItemsByContentType(int id);
        Task<IEnumerable<ContentItemModel>> ListItemsByContentType(string name);
        Task<IEnumerable<T>> ListItemsByContentType<T>(int id, Func<T, bool> filter = null, bool includeValues = true) where T : class, IModel;
        Task<IEnumerable<T>> ListItemsByContentType<T>(Func<T, bool> filter = null, Func<T, object> sorter = null) where T : NamedContentTypeModel, new();
    }

    public class ContentService : IContentService
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ContentService(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public async Task<IEnumerable<ContentItemModel>> ListItemsByContentType(int id)
        {
            var ctc = cache.CreateEntity<ContentType>();
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == id);
            var cic = cache.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue).Include(y => y.ContentType));
            return (await cic.WhereAsync(x => x.ContentTypeId == ctm.Id));
        }

        public async Task<IEnumerable<ContentItemModel>> ListItemsByContentType(string name)
        {
            var ctc = cache.CreateEntity<ContentType>();
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Name == name);
            var cic = cache.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue).Include(y => y.ContentType));
            return (await cic.WhereAsync(x => x.ContentTypeId == ctm.Id));
        }

        public async Task<IEnumerable<T>> ListItemsByContentType<T>(int id, Func<T, bool> filter = null, bool includeValues = true) where T : class, IModel
        {
            var ctc = cache.CreateEntity<ContentType>();
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == id);
            var cic = includeValues ? cache.CreateMappedEntity<ContentItem, T>().Configure(x => x.Include(y => y.FieldValue)) : cache.CreateMappedEntity<ContentItem, T>();
            var items = await cic.WhereAsync(x => x.ContentTypeId == ctm.Id);
            return filter == null ? items : items.Where(filter);
        }

        public async Task<IEnumerable<T>> ListItemsByContentType<T>(Func<T, bool> filter = null, Func<T, object> sorter = null) where T : NamedContentTypeModel, new()
        {
            string name = new T().ContentTypeName;
            var ctc = cache.CreateEntity<ContentType>();
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Name == name);
            var cic = cache.CreateMappedEntity<ContentItem, T>().Configure(x => x.Include(y => y.FieldValue));
            IEnumerable<T> items = await cic.WhereAsync(x => x.ContentTypeId == ctm.Id);
            if(filter != null)
            {
                items = items.Where(filter);
            }
            if (sorter != null)
            {
                items = items.OrderBy(sorter);
            }
            return items;
        }
    }
}
