﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Entities
{
    // TODO: Remove after migration
    public class Logging : EntityBase
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Status { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime CreationDate { get; set; }
    }
}
