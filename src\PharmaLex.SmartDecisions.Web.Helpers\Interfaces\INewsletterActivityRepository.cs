﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities.Entities;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers.Interfaces
{
    public interface INewsletterActivityRepository : ITrackingRepository<NewsletterActivity>, IRepository<NewsletterActivity>
    {
        IQueryable<NewsletterActivity> GetQueryableItems(Func<IQueryable<NewsletterActivity>, IIncludableQueryable<NewsletterActivity, object>>? include = null);
        public Task AddItemsAsync(NewsletterActivity[] items);
        public Task<int> DeleteOldDataAsync(DateTime datetime);
    }
}
