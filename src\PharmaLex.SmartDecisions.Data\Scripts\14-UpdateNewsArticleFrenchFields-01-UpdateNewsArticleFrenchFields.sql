﻿insert into [dbo].[MultilingualResource] select 1, 'published-by', 'Published by', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'published-by', 'Publié par', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

update [dbo].[MultilingualResource]
set Content = 'Date de publication de la source',
CreatedDate = getdate(),
CreatedBy = '<EMAIL>',
LastUpdatedDate = getdate(),
LastUpdatedBy = '<EMAIL>'
where [Key] = 'source-publication-date' and [LocaleId] = 2
