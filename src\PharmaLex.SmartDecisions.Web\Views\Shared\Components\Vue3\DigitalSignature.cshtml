﻿<script type="text/x-template" id="esignature-dialog-template">
        <div id="esignature-dialog" class="vue-dialog-surface hidden">
            <div class="vue-dialog-container esignature-dialog">

                <div class="dialog-content">
                    <div class="flex justify-space-between">
                    <h3 class="brand-color">{{title}}</h3>
                    <i class="m-icon" @@click="close">close</i>
                    </div>
                    <p class="lead" v-html="message"></p>
                    <div id="esignature-error-message" autocomplete="off" class="hidden m-0 p-0" ref="password">
                        <div class="m-icon-group width-100 error">
                            <i class="m-icon error-color">error</i>
                            <div>
                                <p>Your digital signature could not be verified.</p>
                                <p>Please try again.</p>
                            </div>
                        </div>
                    </div>
                    <div v-if="submitting" class="dialog-loader dialog-body">
                        <img src="/images/loaders/spinner-75.svg" alt="loading"/>
                    </div>
                    <div v-else class="dialog-body">
                        <p class="esignature-form">
                            <label for="esignature-password">Password</label>
                            <input type="password" id="esignature-password" v-model="password" @@keyup.prevent.stop.enter="persist" />
                        </p>
                        <div class="buttons">
                        <button type="button" class="secondary" @@click.prevent.stop="close">Cancel</button>
                        <button type="button" @@click.stop.prevent="persist" :disabled="!this.password" id="digital-signature-submit">Continue</button>
                        </div>
                    </div>
                    <footer></footer>
                </div>
            </div>
        </div>
</script>

