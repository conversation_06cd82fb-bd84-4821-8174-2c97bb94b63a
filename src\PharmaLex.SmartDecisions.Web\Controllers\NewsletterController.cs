﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class NewsletterController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly IMapper mapper;
        private readonly INewsCategoryLicenseHelper licenseHelper;

        public NewsletterController(IDistributedCacheServiceFactory cacheFactory, IMapper mapper,
            INewsCategoryLicenseHelper licenseHelper)
        {
            this.cacheFactory = cacheFactory;
            this.mapper = mapper;
            this.licenseHelper = licenseHelper;
        }

        [HttpGet("/newsletter/subscribe")]
        public async Task<IActionResult> Subscribe()
        {
            var subscriptions = await this.cacheFactory
                    .CreateMappedEntity<NewsletterSubscription, NewsletterSubscriptionModel>()
                    .WhereAsync(x => x.UserId == this.CurrentUserId);

            var userCategories = (await this.cacheFactory
                    .CreateMappedEntity<UserNewsCategory, UserNewsCategoryModel>()
                    .WhereAsync(x => x.UserId == this.CurrentUserId)).OrderBy(uc => uc.Order).ToList();

            return this.View(new NewsletterSubscriptionViewModel
            {
                Subscriptions = [.. subscriptions],
                NewsletterUserNewsCategories = userCategories
            });
        }

        [HttpPost("/newsletter/subscribe")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Subscribe([FromBody] NewsletterSubscriptionViewModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.View(model);
            }

            var subscriptionsCache = this.cacheFactory.CreateTrackedEntity<NewsletterSubscription>()
                .Configure(o => o
                    .Include(x => x.User)
                        .ThenInclude(x => x.UserNewsCategory)
                    .Include(x => x.Newsletter));
            var userSubscriptions = (await subscriptionsCache.WhereAsync(x => x.UserId == this.CurrentUserId)).ToList();

            foreach (var us in userSubscriptions)
            {
                var ms = model.Subscriptions.FirstOrDefault(x => x.DeliveryLocalDay == us.DeliveryLocalDay && x.IsMonthly == us.IsMonthly && x.IsInfoflash == us.IsInfoflash);

                if (ms != null)
                {
                    this.mapper.Map(ms, us);
                    us.Active = true;
                }
                else
                {
                    if (us.Active)
                    {
                        us.Active = false;
                    }
                }
            }

            if (!model.Subscriptions.Any(x => x.IsInfoflash) && !userSubscriptions.Any(x => x.IsInfoflash))
            {
                var zoneData = model.Subscriptions.First();
                model.Subscriptions.Add(new NewsletterSubscriptionModel { IsInfoflash = true, Active = false, IsMonthly = false, DeliveryLocalHour = 1, DeliveryLocalDay = 1, Timezone = zoneData.Timezone, TimezoneOffset = zoneData.TimezoneOffset });
            }
            foreach (var ms in model.Subscriptions.Where(x => !userSubscriptions.Any(y => y.DeliveryLocalDay == x.DeliveryLocalDay && y.IsMonthly == x.IsMonthly && y.IsInfoflash == x.IsInfoflash)))
            {
                var newSub = this.mapper.Map<NewsletterSubscription>(ms);
                newSub.UserId = this.CurrentUserId;
                subscriptionsCache.Add(newSub);
            }

            await subscriptionsCache.SaveChangesAsync();
            userSubscriptions = [.. (await subscriptionsCache.WhereAsync(x => x.UserId == this.CurrentUserId))];
            var subscriptionCategoriesCache = this.cacheFactory.CreateTrackedEntity<UserNewsCategory>();
            var userCategories = (await subscriptionCategoriesCache.WhereAsync(x => x.UserId == this.CurrentUserId)).ToList();

            var licensedCategories = await this.licenseHelper.GetUserNewsCategoryLicense(this.CurrentUserId, true);

            userCategories.ForEach(x => subscriptionCategoriesCache.Remove(x));
            var filteredUserPreferences = model.PreferenceOrder.Where(x => licensedCategories.Contains(x)).ToList();
            for (var i = 0; i < filteredUserPreferences.Count; i++)
            {
                subscriptionCategoriesCache.Add(new UserNewsCategory
                {
                    UserId = this.CurrentUserId,
                    NewsCategoryId = filteredUserPreferences[i],
                    CreatedDateUtc = DateTime.UtcNow,
                    Order = i + 1
                });
            }

            await subscriptionCategoriesCache.SaveChangesAsync();

            var subscriptions = await this.cacheFactory.CreateMappedEntity<NewsletterSubscription, NewsletterSubscriptionModel>()
                .WhereAsync(x => x.UserId == this.CurrentUserId);

            return Json(JsonConvert.SerializeObject(subscriptions));
        }
    }
}