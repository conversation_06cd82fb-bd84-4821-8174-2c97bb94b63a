﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class ContentTypeDisplayModelMappingProfile : Profile
    {
        public ContentTypeDisplayModelMappingProfile()
        {
            this.CreateMap<ContentTypeDisplay, ContentTypeDisplayModel>()
                .ForMember(d => d.ContentTypeViewFieldModels, o => o.Ignore())
                .ForMember(d => d.ContentTypeViewRecordModels, o => o.Ignore())
                .ForMember(d => d.IsNew, o => o.Ignore());

            this.CreateMap<ContentTypeDisplayModel, ContentTypeDisplay>()
                .ForMember(d => d.ContentType, s => s.Ignore())
                .ForMember(d => d.DisplayType, s => s.Ignore())
                .ForMember(d => d.CreatedDate, o => o.Ignore())
                .ForMember(d => d.CreatedBy, o => o.Ignore())
                .ForMember(d => d.LastUpdatedDate, o => o.Ignore())
                .ForMember(d => d.LastUpdatedBy, o => o.Ignore());
        }
    }
}
