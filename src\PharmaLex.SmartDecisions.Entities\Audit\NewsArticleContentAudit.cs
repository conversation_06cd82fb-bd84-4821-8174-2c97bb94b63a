﻿using PharmaLex.DataAccess;
using System;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class NewsArticleContentAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public int? NewsArticleId { get; set; }
        public int? LocaleId { get; set; }
        public string Title { get; set; }
        public int? AuthorId { get; set; }
        public int? ReviewerId { get; set; }
        public string SourceUrl { get; set; }
        public int? PublishingStateId { get; set; }
        public DateTime? PublishingStateDateUtc { get; set; }
        public int? AzureBlobId { get; set; }
        public string ImpactAssessmentSummary { get; set; }
        public string FriendlyUrl { get; set; }
        public DateTime? GoLiveDate { get; set; }
    }
}
