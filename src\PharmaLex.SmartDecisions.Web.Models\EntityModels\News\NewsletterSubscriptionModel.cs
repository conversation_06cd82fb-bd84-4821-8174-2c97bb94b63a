﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsletterSubscriptionViewModel : IModel
    {
        public List<NewsletterSubscriptionModel> Subscriptions { get; set; } = new List<NewsletterSubscriptionModel>();

        public List<UserNewsCategoryModel> NewsletterUserNewsCategories { get; set; } = new List<UserNewsCategoryModel>();

        public List<int> PreferenceOrder { get; set; }
    }
    public class NewsletterSubscriptionModel : IModel
    {
        public int? Id { get; set; }
        [Required, MaxLength(256)]
        public string Timezone { get; set; }
        public int TimezoneOffset { get; set; }
        [Required]
        public int? DeliveryLocalDay { get; set; }
        [Required]
        public int? DeliveryLocalHour { get; set; }
        public bool IsMonthly { get; set; }
        public bool IsInfoflash { get; set; }
        public bool Active { get; set; }
        public UserModel User { get; set; }
    }

    public class NewsletterSubscriptionModelMappingProfile : Profile
    {
        public NewsletterSubscriptionModelMappingProfile()
        {
            this.CreateMap<NewsletterSubscription, NewsletterSubscriptionModel>().ReverseMap()
                .ForMember(d => d.Id, s => s.Ignore())
                .ForMember(d => d.UserId, s => s.Ignore())
                .ForMember(d => d.User, s => s.Ignore())
                .AfterMap((m, s) =>
                {
                    int utcHour = s.DeliveryLocalHour + s.TimezoneOffset / 60;
                    if (utcHour > 23)
                    {
                        s.DeliveryUtcDay = s.DeliveryLocalDay + 1 > 6 ? 0 : s.DeliveryLocalDay + 1;
                        s.DeliveryUtcHour = utcHour - 24;
                    }
                    else if (utcHour < 0)
                    {
                        s.DeliveryUtcDay = s.DeliveryLocalDay - 1 < 0 ? 6 : s.DeliveryLocalDay - 1;
                        s.DeliveryUtcHour = 24 - utcHour;
                    }
                    else
                    {
                        s.DeliveryUtcDay = s.DeliveryLocalDay;
                        s.DeliveryUtcHour = utcHour;
                    }

                    if (s.CreatedDateUtc == default(System.DateTime))
                    {
                        s.CreatedDateUtc = System.DateTime.UtcNow;
                    }
                });
        }
    }
}
