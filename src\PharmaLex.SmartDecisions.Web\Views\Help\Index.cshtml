﻿@using Microsoft.AspNetCore.Authorization
@using PharmaLex.SmartDecisions.Web.Helpers

@inject SmartDecisionsAppSettingsHelper AppSettings
@inject IAuthorizationService AuthorizationService

@{
    ViewData["Title"] = "Help";
}

<div>
    <div class="sub-header">
        <h2>Help</h2>
        <div class="controls">
        </div>
    </div>
    <section>
        <div>
            <h2>@ls.Localise("about")</h2>
            <div>
                <p><b>Version:</b> @AppSettings.Version</p>
                @if ((await AuthorizationService.AuthorizeAsync(User, "SuperAdmin")).Succeeded)
                {
                    <p><b>Build:</b> @AppSettings.BuildNumber</p>
                    <p><b>Framework:</b>@System.Runtime.InteropServices.RuntimeInformation.FrameworkDescription</p>
                }
            </div>
        </div>
    </section>
    <br />
    <section>
        <div class="terms">
            <p><a target="_blank" href="https://www.pharmalex.com/terms-conditions/">@ls.Localise("terms-and-conditions")</a></p>
        </div>
        <p>Copyright &copy; @DateTime.Now.Year Cencora PharmaLex. All rights reserved.</p>
    </section>
</div>