﻿<script type="text/x-template" id="autocomplete-template">
    <div class="autocomplete mt-2"
         @@keydown.enter.prevent="onEnter">
        <input type="text" :placeholder="config.placeholder"
               @@input="onChange"
               v-model="search"
               ref="write"
               @@keydown.down.prevent="onArrowDown"
               @@keydown.up.prevent="onArrowUp" />
        <ul v-show="isOpen && search"
            class="autocomplete-items">
            <li v-for="(fi, i) in filteredItems" :title="fi.name"
                :key="i"
                @@click="onClick(i)"
                :class="['paragraph',{ 'is-active': i === arrowCounter }, 'autocomplete-item']">
                {{ fi.name }}
            </li>
        </ul>
    </div>
</script>

<script type="text/x-template" id="autocomplete-list-template">
    <div>
        <ul :class="['autocomplete-list', {'selectable':config.selectable}]" v-if="assignedItems.length">
            <li v-for="(p, i) in assignedItems" v-on:click="clicked(p)" :class="['flex justify-space-between flex-align-center',{'selected': p === selectedItem}]">
                {{p.name}}
                <i v-if="active" class="m-icon ml-auto error-color smaller"
                   v-on:click.stop="deleteItem(i)"
                   title="Remove">delete</i>
            </li>
        </ul>
        <div v-else :class="['m-icon-group autocomplete-list empty-msg', {'validation-error': !valid}]">
            <i class="m-icon warning-color">warning</i>
            <p>{{emptyMessage}}</p>
        </div>
        <autocomplete v-if="active" :items="unassignedItems" :config="autocompleteConfig" v-on:selected="addItem"></autocomplete>
        <template v-for="(item, index) in assignedItems">
            <input :name="modelProperty + '[' + index + '].Id'" type="hidden" :value="item.id" />
            <input :name="modelProperty + '[' + index + '].Name'" type="hidden" :value="item.name" />
        </template>
    </div>
</script>

<environment include="Development">
    <script src="/js/vue/v3/autocomplete-list-v3.js"></script>
</environment>

<environment exclude="Development">
    <script src="@VersionCdn.GetUrl("js/vue/v3/autocomplete-list-v3.js")"></script>
</environment>