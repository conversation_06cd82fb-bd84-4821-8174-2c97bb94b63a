﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities.Entities;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Data.Persistance.Repository
{
    public class NewsletterActivityRepository : TrackingGenericRepository<NewsletterActivity>, INewsletterActivityRepository
    {
        public NewsletterActivityRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
        {
        }

        public async Task<IEnumerable<NewsletterActivity>> GetAllItemsAsync()
        {
            return await context
            .Set<NewsletterActivity>()
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task AddItemAsync(NewsletterActivity item)
        {
            base.Add(item);
            await base.SaveChangesAsync();
        }

        public async Task AddItemsAsync(NewsletterActivity[] items)
        {
            await context.Set<NewsletterActivity>().AddRangeAsync(items);
            await base.SaveChangesAsync();
        }

        public async Task UpdateItemAsync(NewsletterActivity item)
        {
            context.Set<NewsletterActivity>().Update(item);
            await base.SaveChangesAsync();
        }
        public async Task<NewsletterActivity> GetItemAsync(int id)
        {
            return await context.Set<NewsletterActivity>().SingleOrDefaultAsync(d => d.Id == id);
        }
        public async Task DeleteItemAsync(int id)
        {
            var model = await context.Set<NewsletterActivity>().SingleAsync(c => c.Id == id);
            context.Set<NewsletterActivity>().Remove(model);
            await base.SaveChangesAsync();
        }

        public async Task<int> DeleteOldDataAsync(DateTime datetime)
        {
            return await context.Set<NewsletterActivity>().Where(x => x.CreatedDate < datetime).ExecuteDeleteAsync();
        }

        public IQueryable<NewsletterActivity> GetQueryableItems(Func<IQueryable<NewsletterActivity>, IIncludableQueryable<NewsletterActivity, object>> include = null)
        {
            var query = context.Set<NewsletterActivity>().AsQueryable();
            if (include != null)
            {
                query = include(query);
            }

            return query;
        }
    }
}
