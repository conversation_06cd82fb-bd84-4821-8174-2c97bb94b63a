﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class CompanyUserController : BaseController
    {
        private readonly IMapper mapper;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly ICompanyUserManagementService userService;
        private int UserCompanyId => this.User.GetClaimValue<int>("plx:companyid");

        public CompanyUserController(IDistributedCacheServiceFactory cache,
            IMapper mapper,
            ICompanyUserManagementService userService)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.userService = userService;
        }

        [HttpPost("/users/available")]
        [ValidateAntiForgeryToken]
        public async Task<bool> Available(string email)
        {
            if (string.IsNullOrWhiteSpace(email)) return false;

            User dbUser = await this.cache.CreateEntity<User>("CompanyUser")
                .FirstOrDefaultAsync(x => x.Email.ToLower() == email.ToLower());

            return dbUser == null || dbUser.CompanyUser == null;
        }

        [HttpGet("/users"), Authorize("CompanyManager")]
        public async Task<IActionResult> Index()
        {
            CompanyModel company = await cache.CreateMappedEntity<Company, CompanyModel>("CompanyUser")
                .FirstOrDefaultAsync(x => x.Id == this.UserCompanyId);
            if (company == null)
            {
                return this.NotFound();
            }

            var companyUsers = await cache.CreateEntity<User>("CompanyUser")
                .WhereAsync(x => x.CompanyUser != null && x.CompanyUser.CompanyId == company.Id);

            return View(new CompanyUsersViewModel(company) { Users = mapper.Map<List<CompanyUserModel>>(companyUsers) });
        }

        [HttpGet("/users/new"), Authorize("CompanyManager")]
        public async Task<IActionResult> CompanyNew()
        {
            CompanyModel company = await cache.CreateMappedEntity<Company, CompanyModel>("CompanyUser").FirstOrDefaultAsync(x => x.Id == this.UserCompanyId);

            return View("Edit", new CompanyUserViewModel(company));
        }

        [HttpPost("/users/new"), ValidateAntiForgeryToken, Authorize("CompanyManager")]
        public async Task<IActionResult> CompanyNew(CompanyUserModel companyUser, int[] sites)
        {
            var company = await cache.CreateMappedEntity<Company, CompanyModel>("CompanyUser")
                .FirstOrDefaultAsync(x => x.Id == this.UserCompanyId);

            var model = new CompanyUserViewModel(company) { CompanyUser = companyUser };
            if (this.ModelState.IsValid && this.userService.ValidateNewModelState(model, companyUser))
            {
                bool success = await this.userService.ProcessCompanyUser(companyUser, company, sites);
                if (!success)
                    return this.BadRequest();

                return this.Redirect("/users");
            }
            return View("Edit", new CompanyUserViewModel(company));
        }

        [HttpGet("/users/edit/{id}"), Authorize("CompanyManager")]
        public async Task<IActionResult> CompanyEdit(int id)
        {
            var user = await cache.CreateEntity<User>(
                "UserClaim", "UserClaim.Claim", "CompanyUser")
                .FirstOrDefaultAsync(x => x.Id == id && x.CompanyUser != null && x.CompanyUser.CompanyId == this.UserCompanyId);

            if (user == null)
            {
                return this.NotFound();
            }

            var company = await cache.CreateMappedEntity<Company, CompanyModel>("CompanyUser")
                .FirstOrDefaultAsync(x => x.Id == this.UserCompanyId);

            return View("Edit", new CompanyUserViewModel(company)
            {
                CompanyUser = mapper.Map<CompanyUserModel>(user)
            });
        }

        [HttpPost("/users/edit/{id}"), ValidateAntiForgeryToken, Authorize("CompanyManager")]
        public async Task<IActionResult> CompanyEdit(int id, CompanyUserModel companyUser, int[] sites)
        {
            var user = await cache.CreateEntity<User>()
                .Configure(o => o.Include(x => x.CompanyUser))
                .FirstOrDefaultAsync(x => x.Id == id && x.CompanyUser != null && x.CompanyUser.CompanyId == this.UserCompanyId);

            if (user == null)
            {
                return this.NotFound();
            }

            var company = await cache.CreateMappedEntity<Company, CompanyModel>()
                .Configure(o => o.Include(x => x.CompanyUser))
                .FirstOrDefaultAsync(x => x.Id == this.UserCompanyId);

            var model = new CompanyUserViewModel(company) { CompanyUser = companyUser };
            if (this.ModelState.IsValid && this.userService.ValidateEditModelState(model, user))
            {
                bool success = await this.userService.ProcessCompanyUser(companyUser, company, sites);
                if (!success)
                    return this.BadRequest();

                return this.Redirect("/users");
            }
            return this.Redirect($"/users/edit/{id}");
        }

        [HttpPost("/users/resendinvitation"), Authorize("CompanyAdmin")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResendInvitationEmail([FromBody] UserModel model)
        {
            var user = (await cache.CreateEntity<User>()
                .Configure(o =>
                    o.Include(x => x.CompanyUser)
                    .ThenInclude(x => x.Company)
                )
                .WhereAsync(x => x.Id == model.Id))
                .FirstOrDefault();

            var companyUser = mapper.Map<CompanyUserModel>(user);
            var company = mapper.Map<CompanyModel>(user?.CompanyUser.Company);

            var invitationLink = await this.userService.ProcessSignupInvitationResend(companyUser, company);

            return Json(invitationLink);
        }
    }
}
