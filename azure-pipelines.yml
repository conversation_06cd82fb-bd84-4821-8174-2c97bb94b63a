name: "$(Date:yyyyMMdd).$(Rev:r)"

trigger:
  - develop
  - master

parameters:
  - name: Analyse_Packages
    displayName: Analyse Packages
    type: boolean
    default: false

pool:
  vmImage: 'windows-latest'

variables:
  - name: Build_Configuration
    value: "Release"
  - name: Version_Number
    value: "5.8.0"
  - name: Build_Number
    value: $[counter(variables['Version_Number'], 0)]
  - name: Source_Branch
    value: ${{ replace(variables['Build.SourceBranch'],'refs/heads/','') }}
  - name: Long_Lived_Branch
    value: ${{ or(in(variables['Source_Branch'], 'main','master','develop'), startsWith(variables['Source_Branch'], 'release/')) }}
  #Shared Variable group
  - group: Nuget
  - name: NuGet_Source
    value: $[variables.Source]

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git

stages:
  - stage: Prerequisites
    jobs:
      - job: CalculateVersion
        displayName: "Calculate Version"
        steps:
          - template: General/calculate-version.yml@templates
            parameters:
              VersionNumber: "$(Version_Number)"
              BuildNumber: "$(Build_Number)"
              BranchName: "$(Source_Branch)"
              IgnorePostFixTag: true
              
  - stage: BuildAndTest
    dependsOn:
      - Prerequisites
    jobs:
      - job: BuildAndTest
        displayName: "Build and test"
        steps:          
           - template: Build/dotnet/build-test-analyse.yml@templates
             parameters:
              NugetSource: '$(NuGet_Source)'
              BuildConfiguration: '$(Build_Configuration)'
              SolutionName: 'PharmaLex.SmartDecisions.sln'
              VersionNumber: '$(Version_Number)'
              TestProjects: "test/**/*Tests.csproj"
              TestOptions: '/p:Include="[PharmaLex.SmartDecisions.*]*"'
              SonarProjectKey: 'Phlexglobal_SmartDecisions'
              SonarProjectName: 'SmartDecisions'
              AnalysePackages: "${{ parameters.Analyse_Packages }}"
              LongLivedBranch: '${{ variables.Long_Lived_Branch }}'
              CheckmarxTeam: "Code_Busters"
              CheckmarxProjectName: "SmartDecisions"              

  - stage: BuildArtifacts
    dependsOn:
      - BuildAndTest
    jobs:
      - job: BuildArtifacts
        displayName: "Build Artifacts"
        steps:
           - checkout: self
             persistCredentials: true
             submodules: true
             fetchDepth: 1
            
           - task: UseDotNet@2
             displayName: 'Install Dotnet Core 8'
             inputs:
                version: '8.0.x'
            
           - task: DotNetCoreCLI@2
             displayName: 'dotnet restore'
             inputs:
                command: 'restore'
                projects: '**/*.csproj'
                feedsToUse: 'select'
                vstsFeed: Phlexglobal
            
           - task: DotNetCoreCLI@2
             displayName: 'dotnet build'
             inputs:
                command: 'build'
                projects: '**/*.csproj'
                arguments: '--configuration $(Build_Configuration) --no-restore'
            
           - task: DotNetCoreCLI@2
             displayName: 'dotnet publish'
             inputs:
                command: publish
                publishWebProjects: true
                arguments: '--configuration $(Build_Configuration) --output $(Build.ArtifactStagingDirectory)'
           
           - task: DotNetCoreCLI@2
             displayName: Install EF Tool
             inputs:
                command: custom
                custom: 'tool'
                arguments: 'install --global dotnet-ef --version 8.0.2'
            
           - task: DotNetCoreCLI@2
             displayName: Create SQL Scripts
             inputs:
                command: custom
                custom: 'ef '
                arguments: migrations script --project "$(Build.SourcesDirectory)\src\PharmaLex.SmartDecisions.Data\PharmaLex.SmartDecisions.Data.csproj" --startup-project "$(Build.SourcesDirectory)\src\PharmaLex.SmartDecisions.Web\PharmaLex.SmartDecisions.Web.csproj" --output $(Build.artifactstagingdirectory)\Migrations\migration.sql --idempotent --verbose --no-build --configuration $(Build_Configuration)                                                      
            
           - task: PublishBuildArtifacts@1
             displayName: 'Publish Artifact: drop'
             inputs:
                PathtoPublish: '$(Build.ArtifactStagingDirectory)'
                ArtifactName: 'drop'
                publishLocation: 'Container'
             condition: succeededOrFailed()
              
           - task: DotNetCoreCLI@2
             displayName: 'publish newsletter'
             inputs:
                command: 'publish'
                publishWebProjects: false
                projects: '**/PharmaLex.NewsletterApp.csproj'
                arguments: '--configuration $(Build_Configuration) --output $(Build.ArtifactStagingDirectory)/newsletter'

           - task: PublishBuildArtifacts@1
             displayName: 'Publish Artifact: newsletter'
             inputs:
                PathtoPublish: '$(Build.ArtifactStagingDirectory)/newsletter'
                ArtifactName: 'newsletter'
                publishLocation: 'Container'