﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class AzureBlob : EntityBase
    {
        public AzureBlob()
        {
            NewsArticleContent = new HashSet<NewsArticleContent>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Uri { get; set; }
        public string ContentType { get; set; }
        public int Length { get; set; }
        public string Container { get; set; }

        public virtual ICollection<NewsArticleContent> NewsArticleContent { get; set; }
    }
}
