{"ConnectionStrings": {"default": "Server=tcp:sdc-dev-sqlserver-eun.database.windows.net,1433;Initial Catalog=sdc-dev-default-eun;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Authentication=Active Directory Default;"}, "Static": {"App": "smartdecisions", "Env": "local"}, "AppSettings": {"CacheExpirationTime": "0:0:55:0"}, "SqlOptions": {"EnableRetryOnFailure": true, "MaxRetryCountOnFailure": 10}, "AzureStorage": {"Account": "sdcdevsharedeun", "Container": "smartdecisions"}, "KeyVaultName": "sdc-dev-kv-eun", "VisualStudioTenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77", "emailsSendingEnabled": "false", "receiverEmails": "mila.pan<PERSON><PERSON><EMAIL>;<EMAIL>;galya.and<PERSON><EMAIL>;<EMAIL>;<EMAIL>", "AzureWebJobs.DeleteNewsletterInsightsDataTimer.Disabled": true}