﻿create trigger [dbo].[NewsletterNewsArticleContent_Insert] on [dbo].[NewsletterNewsArticleContent]
for insert as
insert into [Audit].[NewsletterNewsArticleContent_Audit]
select 'I'
      ,[NewsletterId]
      ,[NewsArticleContentId]
      ,[TimeOpenUtc]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsletterNewsArticleContent_Update] on [dbo].[NewsletterNewsArticleContent]
for update as
insert into [Audit].[NewsletterNewsArticleContent_Audit]
select 'U'
      ,[NewsletterId]
      ,[NewsArticleContentId]
      ,[TimeOpenUtc]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsletterNewsArticleContent_Delete] on [dbo].[NewsletterNewsArticleContent]
for delete as
insert into [Audit].[NewsletterNewsArticleContent_Audit]
select 'D'
      ,[NewsletterId]
      ,[NewsArticleContentId]
      ,[TimeOpenUtc]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
