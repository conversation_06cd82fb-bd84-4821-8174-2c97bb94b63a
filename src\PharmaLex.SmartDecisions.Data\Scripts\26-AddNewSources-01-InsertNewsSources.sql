insert into [dbo].[NewsSource] select 'Cosmed', 'https://ich.org', 74, N'cosmed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Health Data Hub', 'https://ich.org', 75, N'health-data-hub', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'FSPF', 'https://ich.org', 76, N'fspf', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Ministry of Research', 'https://ich.org', 77, N'ministry-of-research', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'GEMME', 'https://ich.org', 78, N'gemme', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'DASTRI', 'https://ich.org', 79, N'dastri', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'IMDRF', 'https://ich.org', 80, N'imdrf', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cosmed', 'Cosmed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].health-data-hub', 'Health Data Hub', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].fspf', 'FSPF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ministry-of-research', 'Ministry of Research', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].gemme', 'GEMME', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].dastri', 'DASTRI', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].imdrf', 'IMDRF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cosmed', N'Cosmed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].health-data-hub', N'Health Data Hub', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].fspf', N'FSPF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ministry-of-research', N'Ministère de la Recherche', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].gemme', N'GEMME', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].dastri', N'DASTRI', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].imdrf', N'IMDRF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
