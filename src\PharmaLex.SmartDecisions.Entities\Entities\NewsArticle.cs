﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsArticle : EntityBase
    {
        public NewsArticle()
        {
            NewsArticleContent = new HashSet<NewsArticleContent>();
            NewsArticleCategory = new HashSet<NewsArticleCategory>();
        }

        public int Id { get; set; }
        public int NewsSourceId { get; set; }
        public int ImportanceId { get; set; }

        public System.DateTime SourcePublicationDate { get; set; }

        public virtual NewsSource NewsSource { get; set; }
        public virtual ICollection<NewsArticleContent> NewsArticleContent { get; set; }
        public virtual ICollection<NewsArticleCategory> NewsArticleCategory { get; set; }
    }
}
