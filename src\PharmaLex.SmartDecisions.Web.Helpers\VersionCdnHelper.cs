﻿using Microsoft.Extensions.Configuration;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public class VersionCdnHelper
    {
        private readonly string? buildNumber;

        public VersionCdnHelper(IConfiguration configuration)
        {
            buildNumber = configuration.GetValue<string>("AppSettings:BuildNumber");
            var version = configuration.GetValue<string>("static:Version");
            var container = configuration.GetValue<string>("static:Container");
            var cdn = configuration.GetValue<string>("static:Cdn");

            this.Root = cdn;
            this.Host = $"{cdn}/{container}/{version}";
        }

        public string Host { get; }
        public string? Root { get; }

        public string GetUrl(string path, bool versioned = true)
        {
            string trimPath = path.Trim('/');

            if (string.IsNullOrEmpty(this.Host))
            {
                return $"/{trimPath}";
            }

            return $"{this.Host}/{trimPath}" + (versioned ? $"?v={this.buildNumber}" : string.Empty);
        }
    }
}
