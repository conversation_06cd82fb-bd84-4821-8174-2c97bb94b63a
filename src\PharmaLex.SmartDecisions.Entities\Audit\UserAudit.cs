﻿using PharmaLex.DataAccess;
using System;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class UserAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Email { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public int? LocaleId { get; set; }
        public string InvitationEmailLink { get; set; }
    }
}
