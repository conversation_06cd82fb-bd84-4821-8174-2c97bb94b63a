<?xml version="1.0"?>
<!-- Copyright (c) 2008-2020 New Relic, Inc.  All rights reserved. -->
<!-- For more information see: https://docs.newrelic.com/docs/agents/net-agent/configuration/net-agent-configuration/ -->
<configuration xmlns="urn:newrelic-config" agentEnabled="true">
  <service licenseKey="f516575a0aa863aadafb1852ffed18a3f4caNRAL" />
  <application />
  <log directory="C:\Home\LogFiles\NewRelic" level="info"></log>
  <allowAllHeaders enabled="true" />
  <attributes enabled="true">
    <exclude>request.headers.cookie</exclude>
    <exclude>request.headers.authorization</exclude>
    <exclude>request.headers.proxy-authorization</exclude>
    <exclude>request.headers.x-*</exclude>
    <include>request.headers.*</include>
  </attributes>
  <transactionTracer enabled="true" transactionThreshold="apdex_f" stackTraceThreshold="500" recordSql="obfuscated" explainEnabled="false" explainThreshold="500" />
  <distributedTracing enabled="true" />
  <errorCollector enabled="true">
    <ignoreClasses>
      <errorClass>System.IO.FileNotFoundException</errorClass>
      <errorClass>System.Threading.ThreadAbortException</errorClass>
    </ignoreClasses>
    <ignoreStatusCodes>
      <code>401</code>
      <code>404</code>
    </ignoreStatusCodes>
  </errorCollector>
  <browserMonitoring autoInstrument="true" />
  <threadProfiling>
    <ignoreMethod>System.Threading.WaitHandle:InternalWaitOne</ignoreMethod>
    <ignoreMethod>System.Threading.WaitHandle:WaitAny</ignoreMethod>
  </threadProfiling>
</configuration>