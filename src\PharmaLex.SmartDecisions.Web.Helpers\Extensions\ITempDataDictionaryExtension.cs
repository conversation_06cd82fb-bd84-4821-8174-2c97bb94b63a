﻿using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Newtonsoft.Json;

namespace PharmaLex.SmartDecisions.Web.Helpers.Extensions
{
    public static class ITempDataDictionaryExtension
    {
        public static void Set<T>(this ITempDataDictionary td, string key, T value) where T : class
        {
            td[key] = JsonConvert.SerializeObject(value);
        }

        public static T? Get<T>(this ITempDataDictionary td, string key) where T : class
        {
            td.TryGetValue(key, out object? o);
            return o == null ? null : JsonConvert.DeserializeObject<T>((string)o);
        }

        public static bool TryGet<T>(this ITempDataDictionary td, string key, out T? o) where T : class
        {
            if (td.TryGetValue(key, out object? v))
            {
                o = v != null ? JsonConvert.DeserializeObject<T>((string)v) : null;
            }
            else
            {
                o = null;
            }
            return o != null;
        }
    }
}
