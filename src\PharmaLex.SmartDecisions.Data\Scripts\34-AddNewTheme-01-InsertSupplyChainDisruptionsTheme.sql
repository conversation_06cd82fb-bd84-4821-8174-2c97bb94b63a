DECLARE @parentId int
SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Themes')

INSERT INTO [dbo].[NewsCategory] SELECT N'Supply chain disruptions and shortages', @parentId, 3, 81, N'supply-chain-disruptions', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].supply-chain-disruptions', 'Supply chain disruptions and shortages', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].supply-chain-disruptions', 'Tensions d’approvisionnement et pénuries', getdate(), '<EMAIL>', getdate(), '<EMAIL>'