﻿insert into [dbo].[NewsSource] select 'Ministry of Health', 'https://www.ema.europa.eu/', 0, N'ministry-health', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CEPS', 'https://www.ema.europa.eu/', 1, N'ceps', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ANSM', 'https://www.ema.europa.eu/', 2, N'ansm', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CMDh', 'https://www.ema.europa.eu/', 3, N'cmdh', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'EMA', 'https://www.ema.europa.eu/', 4, N'ema', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'eSubmission (EMA)', 'https://www.ema.europa.eu/', 5, N'ema-esubmission', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ICH', 'https://www.ema.europa.eu/', 6, N'ich', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'European Court of Justice', 'https://www.ema.europa.eu/', 8, N'european-instances-court', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'European Commission', 'https://www.ema.europa.eu/', 9, N'european-instances-comission', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'European Parliament', 'https://www.ema.europa.eu/', 10, N'european-instances-parliament', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'European Council', 'https://ich.org', 11, N'european-instances-council', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Council of the EU', 'https://ich.org', 12, N'european-instances-council-eu', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'HAS', 'https://ich.org', 13, N'has', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Health Insurance', 'https://ich.org', 14, N'health-insurance', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Anses', 'https://ich.org', 15, N'anses', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'French Parliament', 'https://ich.org', 16, N'french-parliament', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'National Assembly', 'https://ich.org', 17, N'french-parliament-assembly', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Senate', 'https://ich.org', 18, N'french-parliament-senate', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Council of State', 'https://ich.org', 19, N'council-state', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'SNITEM', 'https://ich.org', 20, N'snitem', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CNIL', 'https://ich.org', 21, N'cnil', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CNOP', 'https://ich.org', 22, N'cnop', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CNOM', 'https://ich.org', 22, N'cnom', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'DGCCRF', 'https://ich.org', 23, N'dgccrf', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ECHA', 'https://ich.org', 24, N'echa', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'EFSA', 'https://ich.org', 25, N'efsa', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'EDQM', 'https://ich.org', 26, N'edqm', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'EMVO', 'https://ich.org', 27, N'emvo', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'France MVO', 'https://ich.org', 28, N'france-mvo', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ENCePP', 'https://ich.org', 29, N'encepp', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'EUnetHTA', 'https://ich.org', 30, N'eunethta', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'JORF', 'https://ich.org', 31, N'jorf', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'OJEU', 'https://ich.org', 32, N'ojeu', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'LEEM', 'https://ich.org', 33, N'leem', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'GMED', 'https://ich.org', 34, N'gmed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Synadiet', 'https://ich.org', 35, N'synadiet', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'URSSAF', 'https://ich.org', 36, N'urssaf', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[NewsSource] select 'National Academy of Pharmacy', 'https://ich.org', 37, 'national-academy-pharmacy',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ANFOR', 'https://ich.org', 38, 'anfor', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Agency of Biomedicine', 'https://ich.org', 39, 'agency-biomedicine', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ARS', 'https://ich.org', 40, 'ars', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Competition Authority', 'https://ich.org', 41, 'competition-authority', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CITEO', 'https://ich.org', 42, 'citeo', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'HMA', 'https://ich.org', 43, 'hma', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Constitutional Council', 'https://ich.org', 44, 'constitutional-council', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Council of Europe', 'https://ich.org', 45, 'council-europe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Court of Auditors', 'https://ich.org', 46, 'court-auditors', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Cyclamed', 'https://ich.org', 47, 'cyclamed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'FDA', 'https://ich.org', 48, 'fda', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ECDC', 'https://ich.org', 49, 'ecdc', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'EFPIA', 'https://ich.org', 50, 'efpia', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'FEBEA', 'https://ich.org', 51, 'febea', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'HCSP', 'https://ich.org', 52, 'hcsp', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ISO', 'https://ich.org', 53, 'ico', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'MedDRA', 'https://ich.org', 54, 'meddra', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'MHRA', 'https://ich.org', 55, 'mhra', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Ministry of Ecological Transition', 'https://ich.org', 56, 'ministry-ecological-transition', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'France public health', 'https://ich.org', 57, 'france-public-health', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CCI (Chamber of Commerce and Industry)', 'https://ich.org', 58, 'chamber-commerce-industry', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'INPI', 'https://ich.org', 59, 'inpi', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'ICMRA', 'https://ich.org', 60, 'icmra', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'INCa', 'https://ich.org', 61, 'inca', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'OMS', 'https://ich.org', 62, 'oms', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[NewsSource] select 'Others', 'https://ich.org', 64, N'others', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
go
