﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class LocaleAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public string LocalisedName { get; set; }
        public string IsoLanguageCode { get; set; }
        public string IsoScriptCode { get; set; }
        public string IsoCountryCode { get; set; }
        public bool? Active { get; set; }
        public bool? Default { get; set; }
        public string LocalisationKey { get; set; }

    }
}
