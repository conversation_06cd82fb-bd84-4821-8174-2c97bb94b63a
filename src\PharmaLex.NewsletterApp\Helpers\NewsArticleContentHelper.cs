﻿using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.NewsletterApp.Helpers
{
    public static class NewsArticleContentHelper
    {
        public static NewsArticleContentModel FilterArticlesUsingLanguagePreference(
            IReadOnlyCollection<NewsArticleContentModel> newsLetterArticles,
            IReadOnlyCollection<int> articleLanguagePreferenceIds,
            IReadOnlyDictionary<int, string> languagePreferenceLocalizationKeysMap)
        {
            if (newsLetterArticles.Count == 1)
            {
                // newsletter article has only one version
                return newsLetterArticles.SingleOrDefault(x => languagePreferenceLocalizationKeysMap.Values.Contains(x.Locale.LocalisationKey));
            }

            var preferredLanguageLocalizationKey = languagePreferenceLocalizationKeysMap[articleLanguagePreferenceIds.First()];
            return newsLetterArticles.Single(
                x => preferredLanguageLocalizationKey == x.Locale.LocalisationKey);
        }
    }
}
