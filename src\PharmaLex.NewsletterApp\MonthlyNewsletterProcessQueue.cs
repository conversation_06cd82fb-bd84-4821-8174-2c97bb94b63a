using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class MonthlyNewsletterProcessQueue
    {
        private readonly INewsletterService newsletterService;
        public MonthlyNewsletterProcessQueue(
            INewsletterService newsletterService
            )
        {
            this.newsletterService = newsletterService;
        }

        [Function("MonthlyNewsletterProcessQueue")]
        public async Task Run([QueueTrigger("%qn-monthly%")] string subscriptionItem, FunctionContext context)
        {
            ILogger logger = context.GetLogger("MonthlyNewsletterProcessQueue");
            logger.LogInformation($"Start processing queue message: {subscriptionItem}.");

            int subscriptionId = JsonConvert.DeserializeObject<NewsletterQueueMessage>(subscriptionItem).Id;
            logger.LogInformation($"Subscription id is: {subscriptionId}");

            if (await this.newsletterService.TryInit(subscriptionId, logger))
            {
                if (await this.newsletterService.TryBuildNewsletterModel())
                {
                    await this.newsletterService.SendNewsletter();
                }
            }
        }
    }
}
