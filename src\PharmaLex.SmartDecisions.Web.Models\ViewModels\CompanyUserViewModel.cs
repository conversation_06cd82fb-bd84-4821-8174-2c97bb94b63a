﻿using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class CompanyUserViewModel : CompanyLicenseViewModel
    {
        public CompanyUserViewModel(CompanyModel company) : base(company) { }

        public CompanyUserModel CompanyUser { get; set; } = new CompanyUserModel();
    }

    public class CompanyUsersViewModel : CompanyLicenseViewModel
    {
        public CompanyUsersViewModel(CompanyModel company) : base(company) { }
        public List<CompanyUserModel> Users { get; set; } = new List<CompanyUserModel>();
    }

    public abstract class CompanyLicenseViewModel
    {
        public CompanyLicenseViewModel(CompanyModel company)
        {
            int activeUsersCount = company.CompanyUsers.Where(x => x.Active).Count();

            this.ActiveLicenseLimitReached = activeUsersCount >= company.MaximumActiveUsersCount;
            this.ActiveUsersCount = activeUsersCount;
            this.TotalLicenseLimitReached = company.CompanyUsers.Count >= company.MaximumUsersCount;
            this.TotalUsersCount = company.CompanyUsers.Count;
            this.Company = company;
        }
        public bool ActiveLicenseLimitReached { get; }
        public bool TotalLicenseLimitReached { get; }
        public int ActiveUsersCount { get; }
        public int TotalUsersCount { get; }
        public CompanyModel Company { get; }
    }
}
