﻿create trigger [dbo].[User_Insert] on [dbo].[User]
for insert as
insert into [Audit].[User_Audit]
select 'I', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[User_Update] on [dbo].[User]
for update as
insert into [Audit].[User_Audit]
select 'U', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[User_Delete] on [dbo].[User]
for delete as
insert into [Audit].[User_Audit]
select 'D', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go

insert into [dbo].[User]
select '<PERSON><PERSON><EMAIL>', 'Rob', 'Williams', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User]
select '<EMAIL>', 'Velin', 'Angelov', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User]
select '<EMAIL>', 'Tanya', 'Lisseva', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Tanya', 'Lisseva(Tester)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Tanya', 'Lisseva(Validator)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Velin', 'Angelov(Tester)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Rob', 'Williams(Tester)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Velin', 'Angelov(Admin)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[User] 
select '<EMAIL>', 'Jennie', 'May', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Jill', 'Elliott', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Harry', 'Rowland', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Harry', 'Rowland (Tester)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Dimitar', 'Angelov', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Dimitar', 'Angelov (Tester)', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Bill', 'Suon', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Martin', 'Law', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Jim', 'Benham', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Valentine', 'Monnier', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[User] 
select '<EMAIL>', 'Caroline', 'Pouget', null, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
