﻿using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public interface ILocalisationService
    {
        string LocaliseText(int localeId, string key);
        Task<List<T>> LocaliseLookupList<T>(int localeId) where T : class, ILocalisableLookup;

        Task<List<NewsCategory>> LocaliseNewsCategories(int localeId);
    }

    public class LocalisationService : ILocalisationService
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;

        public LocalisationService(IDistributedCacheServiceFactory cache)
        {
            this.cacheFactory = cache;
        }

        public async Task<List<T>> LocaliseLookupList<T>(int localeId) where T : class, ILocalisableLookup
        {
            var locResourcesCache = cacheFactory.CreateEntity<MultilingualResource>();
            var locResources = (await locResourcesCache.WhereAsync(x => x.Key.StartsWith($"[{typeof(T).Name}]"))).ToList();

            var localisableItemsCache = cacheFactory.CreateEntity<T>();
            var localisableItems = (await localisableItemsCache.AllAsync()).ToList();

            foreach (var item in localisableItems)
            {
                string localisationKey = $"[{typeof(T).Name}].{item.LocalisationKey}";
                item.Name =
                    locResources.FirstOrDefault(x => x.LocaleId == localeId && x.Key == localisationKey)?.Content ??
                    localisationKey;
            }

            return localisableItems;
        }

        public async Task<List<NewsCategory>> LocaliseNewsCategories(int localeId)
        {
            var localisableItemsCache = cacheFactory.CreateEntity<NewsCategory>().Configure(x => x
                       .Include(y => y.ChildCategory)
                        .ThenInclude(y => y.ChildCategory));

            var localisableItems = (await localisableItemsCache.AllAsync()).ToList();

            var locResourcesCache = cacheFactory.CreateEntity<MultilingualResource>();
            var locResources = (await locResourcesCache.WhereAsync(x => x.Key.StartsWith($"[NewsCategory]"))).ToList();

            foreach (var item in localisableItems)
            {
                var localisationKey = $"[NewsCategory].{item.LocalisationKey}";
                item.Name =
                    locResources.FirstOrDefault(x => x.LocaleId == localeId && x.Key == localisationKey)?.Content ??
                    localisationKey;

                foreach (var childItem in item.ChildCategory)
                {
                    localisationKey = $"[NewsCategory].{childItem.LocalisationKey}";
                    childItem.Name =
                        locResources.FirstOrDefault(x => x.LocaleId == localeId && x.Key == localisationKey)?.Content ??
                        localisationKey;
                }
            }

            return localisableItems;
        }

        public string LocaliseText(int localeId, string key)
        {
            var mrCache = cacheFactory.CreateEntity<MultilingualResource>();
            MultilingualResource mr = mrCache.FirstOrDefault(x => x.LocaleId == localeId && x.Key == key);         

            return mr?.Content ?? key;
        }
    }
}
