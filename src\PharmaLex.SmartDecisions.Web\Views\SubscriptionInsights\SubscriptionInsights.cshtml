﻿@model List<SubscriptionInsightModel>
@using Newtonsoft.Json;
@using PharmaLex.Authentication.B2C;
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.SmartDecisions.Web.Helpers;
@using PharmaLex.SmartDecisions.Web.Models
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@inject INewsCategoryLicenseHelper categoryHelper
@using Microsoft.AspNetCore.Localization

@{
    ViewData["Title"] = "Subscription Insights";

    var newsCategories = (await ls.LocaliseList<NewsCategory>()).OrderBy(x => x.Name).ToList();
    var languages = (await ls.LocaliseList<Locale>()).OrderByDescending(x => x.Default).ToList();
    int userId = this.User.GetClaimValue<int>("plx:userid");
    var licensedGeographicalScopeCategories = await categoryHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.GeographicalScope, true);
    var geographicalScopes = newsCategories.Where(x => licensedGeographicalScopeCategories.Contains(x.Id));
    var requestCulture = this.Context.Features.Get<IRequestCultureFeature>().RequestCulture.UICulture.Name;
}

<div id="subscription-insights">
    <div class="sub-header">
        <h2>Subscription Insights</h2>
    </div>

    <filtered-table :items="subscriptionInsights" :columns="columns" :filters="filters" :link="link" :resources="resources"></filtered-table>

</div>

@section Scripts {
<script type="text/javascript">

        const convertDate = date => {
            return date ? Intl.DateTimeFormat('@requestCulture', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            }).format(new Date(date)) : "Never logged";
        };

        const date = new Date();

        var pageConfig = {
            appElement: '#subscription-insights',
            data: function () {
                return {
                    link: '',
                    subscriptionInsights: @Html.Raw(Model.ToJson()),
                    resources: {
                        noRecordsMessage: '@ls.LocaliseSafe("[[table]].no-results-message")',
                        sortByFormat: '@ls.LocaliseSafe("[[table]].sort-by-format")',
                        filterByFormat: '@ls.LocaliseSafe("[[table]].filter-by-format")',
                        searchInFormat: '@ls.LocaliseSafe("[[table]].search-in-format")',
                        clearFilters: '@ls.LocaliseSafe("[[table]].clear-filters")',
                        addItem: '@ls.LocaliseSafe("[[table]].add-item")',
                        noValue: '@ls.LocaliseSafe("[[table]].no-value")',
                        pager: {
                            showingFormat: '@ls.LocaliseSafe("[[table]].pager.showing-format")',
                            showingFilteredFormat: '@ls.LocaliseSafe("[[table]].pager.showing-filtered-format")',
                            pageSize: '@ls.LocaliseSafe("[[table]].pager.page-size")',
                            first: '@ls.LocaliseSafe("[[table]].pager.first")',
                            previous: '@ls.LocaliseSafe("[[table]].pager.previous")',
                            next: '@ls.LocaliseSafe("[[table]].pager.next")',
                            last: '@ls.LocaliseSafe("[[table]].pager.last")',
                        }
                    },
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'company',
                                sortKey: 'company',
                                sortDirection: 1,
                                type: 'text',
                                header: 'Company'
                            },
                            {
                                dataKey: 'user',
                                sortKey: 'user',
                                sortDirection: 1,
                                header: 'User',
                                type: 'text'
                            },
                            {
                                dataKey: 'email',
                                sortKey: 'email',
                                sortDirection: 1,
                                header: 'Email',
                                type: 'text'
                            },
                            {
                                dataKey: 'products',
                                sortKey: 'sortProducts',
                                header: 'Products',
                                type: 'chips',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'themes',
                                sortKey: 'sortThemes',
                                header: 'Themes',
                                type: 'chips',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'languages',
                                sortKey: 'sortLanguages',
                                header: 'Languages',
                                type: 'chips',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'geographicalScope',
                                sortKey: 'sortGeographicalScope',
                                header: 'Geographical Scope',
                                type: 'chips',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'daysToReceive',
                                sortKey: 'sortDaysToReceive',
                                header: 'Days to Receive',
                                type: 'chips',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'isMonthlyMiscNewsletter',
                                sortKey: 'isMonthlyMiscNewsletter',
                                header: 'Monthly Misc Newsletter',
                                type: 'bool',
                                style: 'width: 50px;'
                            },
                            {
                                dataKey: 'isInfoflash',
                                sortKey: 'isInfoflash',
                                header: 'Infoflash',
                                type: 'bool',
                                style: 'width: 50px;'
                            },
                            {
                                dataKey: 'lastLoginDate',
                                sortKey: 'lastLoginDate',
                                header: 'Last Login Date',
                                sortDirection: 1,
                                sortComparer: 'date',
                                type: 'date',
                                edit: {
                                    type: 'plain',
                                    required: true,
                                    convert: convertDate,
                                },
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'company',
                            options: [],
                            type: 'search',
                            header: 'Search Company',
                            fn: v => p => plx.escapeAccent(p.company.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'user',
                            options: [],
                            type: 'search',
                            header: 'Search User',
                            fn: v => p => plx.escapeAccent(p.user.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'email',
                            options: [],
                            type: 'search',
                            header: 'Search email',
                            fn: v => p => plx.escapeAccent(p.email.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'products',
                            options: [],
                            type: 'search',
                            header: 'Search Products',
                            fn: v => p => plx.escapeAccent(p.products.join(' ').toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'themes',
                            options: [],
                            type: 'search',
                            header: 'Search Themes',
                            fn: v => p => plx.escapeAccent(p.themes.join(' ').toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'languages',
                            options: [],
                            type: 'search',
                            header: 'Search Languages',
                            fn: v => p => plx.escapeAccent(p.languages.join(' ').toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'geographicalScope',
                            options: [],
                            type: 'search',
                            header: 'Search Geographical Scope',
                            fn: v => p => plx.escapeAccent(p.geographicalScope.join(' ').toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'daysToReceive',
                            options: [],
                            type: 'search',
                            header: 'Search Days to Receive',
                            fn: v => p => plx.escapeAccent(p.daysToReceive.join(' ').toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'lastLoginDate',
                            options: [],
                            type: 'search',
                            header: 'Search Last Login Date',
                            fn: v => p => convertDate(p.lastLoginDate).toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
</script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}