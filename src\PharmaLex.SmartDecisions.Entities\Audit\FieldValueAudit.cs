﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class FieldValueAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public int? ContentItemId { get; set; }
        public int? FieldId { get; set; }
        public string Value { get; set; }
    }
}
