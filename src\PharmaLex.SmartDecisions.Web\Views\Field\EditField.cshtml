﻿@model FieldModel
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.SmartDecisions.Web.Models
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@{
    ViewData["Title"] = "Edit Field";

    var types = mapper.Map<IEnumerable<PicklistItemModel>>(Enum.GetValues(typeof(FieldType))).ToSelectList();
    var picklists = await cache.CreateMappedEntity<ContentType, PicklistItemModel>().WhereAsync(x => x.ContentTypeCategoryId == (int)ContentTypeCategory.Picklist);
    var relations = await cache.CreateMappedEntity<ContentType, PicklistItemModel>().WhereAsync(x => x.ContentTypeCategoryId != (int)ContentTypeCategory.Picklist);
}

<div id="app" v-cloak>

    <div class="sub-header">
        <h2 v-if="field.id > 0">@Model.Name Field</h2>
        <h2 v-else>Add Field</h2>
        <div class="controls">
            <a class="button secondary" href="/manage/content-type/edit/@Model.ContentTypeId">@Model.ContentTypeName Content Type</a>
            <a href="/manage/field/delete/@Model.Id" class="button" v-if="field.id > 0 && field.system !== true">Delete</a>
        </div>
    </div>


    <form method="post">

        <h5>Details</h5>

        <div class="flex flex-nowrap gapped-2">
            <div class="flex-item flex-x2 tile">
                <div class="form-group">
                    <label asp-for="Name">Name*</label>
                    <input type="text" asp-for="Name" required pattern=".*\S+.*" autofocus />
                    <span asp-validation-for="Name" class="error-color"></span>
                </div>
                <div class="form-group">
                    <label asp-for="Description">Description (Help text)</label>
                    <textarea asp-for="Description"></textarea>
                    <span asp-validation-for="Description" class="error-color"></span>
                </div>
                <div class="form-group">
                    <label asp-for="FieldTypeId">Field type*</label>
                    <div class="custom-select">
                        <select name="FieldTypeId" v-on:change="selectedFieldTypeChanged" required>
                            <option value="" :selected="field.fieldTypeId === 0">Select Field type</option>
                            <option v-for="ft in fieldTypes" :value="ft.value" :selected="field.fieldTypeId == ft.value">{{ft.text}}</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="flex-item flex-x2 tile">
                <div class="form-group">
                    <label>Must have value?</label>
                    <label class="switch-container">
                        No
                        <input type="checkbox" asp-for="Required" class="switch" />
                        <label asp-for="Required" class="switch">Required</label>
                        Yes
                    </label>
                </div>
                <div class="form-group">
                    <template v-if="field.fieldTypeId != @((int)FieldType.Bool)">
                        <label>Value must be unique?</label>
                        <label class="switch-container">
                            No
                            <input type="checkbox" asp-for="Unique" class="switch" />
                            <label asp-for="Unique" class="switch">Required</label>
                            Yes
                        </label>
                    </template>
                </div>
                <div class="form-group">
                    <label>Is system field?</label>
                    <div class="switch-display-only"><i class="icon-@(Model.System ? "check" : "cross")"></i> @(Model.System ? "Yes" : "No")</div>
                </div>

                <div class="form-group">
                    <template v-if="field.fieldTypeId == 1 || field.fieldTypeId == 2">
                        <label asp-for="Length">Length</label>
                        <input type="number" asp-for="Length" min="1" />
                    </template>
                    <input type="hidden" asp-for="Length" v-else />
                </div>

                <div class="form-group">
                    <template v-if="field.fieldTypeId == 8">
                        <label for="RelatedContentTypeId">Picklist</label>
                        <div class="select-wrapper custom-select mb-2">
                            <select id="RelatedContentTypeId" name="RelatedContentTypeId" required>
                                <option value="" :selected="field.relatedContentTypeId === 0">Select Picklist</option>
                                <option v-for="pl in picklists" :value="pl.id" :selected="field.relatedContentTypeId == pl.id">{{pl.name}}</option>
                            </select>
                        </div>
                    </template>
                    <template v-if="field.fieldTypeId == 9">
                        <label asp-for="RelatedContentTypeId">Related content type</label>
                        <div class="select-wrapper custom-select mb-2">
                            <select id="RelatedContentTypeId" name="RelatedContentTypeId" required>
                                <option value="" :selected="field.relatedContentTypeId === 0">Select Content type</option>
                                <option v-for="rl in relations" :value="rl.id" :selected="field.relatedContentTypeId == rl.id">{{rl.name}}</option>
                            </select>
                        </div>
                    </template>
                    <template v-if="field.fieldTypeId == 8 || field.fieldTypeId == 9">
                        <label>Allow user to select multiple values?</label>
                        <label class="switch-container">
                            No
                            <input type="checkbox" asp-for="MultiSelect" class="switch" />
                            <label asp-for="MultiSelect" class="switch">Required</label>
                            Yes
                        </label>
                    </template>
                </div>

                </div>
            </div>

        <div class="buttons mt-2">
            <a class="button secondary" href="/manage/content-type/edit/@Model.ContentTypeId">Cancel</a>
            <button v-if="field.system !== true" type="submit">Save</button>
            <button type="submit" title="Save field and add another field" v-if="field.id === 0" :formaction="saveAndAddAction">Save &amp; Add</button>
        </div>
        <input type="hidden" asp-for="ContentTypeId" />
    </form>
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#app',
            data: function () {
                return {
                    field: @Html.Raw(Model.ToJson()),
                    fieldTypes: [@Html.Raw(String.Join(',', types.Select(x => $"{{text:\"{x.Text}\",value:\"{x.Value}\"}}")))],
                    saveAndAddAction: document.location.href + '?add=true',
                    picklists: @Html.Raw(picklists.ToJson()),
                    relations: @Html.Raw(relations.ToJson())
                }
            },
            methods: {
                selectedFieldTypeChanged: function (e) {
                    this.field.relatedContentTypeId = null;
                    this.field.fieldTypeId = e.target.value;
                }
            }
        };
    </script>
}