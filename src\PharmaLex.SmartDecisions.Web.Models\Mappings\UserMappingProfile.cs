﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System.Linq;
using PharmaLex.Authentication.B2C;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class UserMappingProfile : Profile
    {
        public UserMappingProfile()
        {
            this.CreateMap<UserClaim, int>().ConvertUsing(x => x.ClaimId);

            this.CreateMap<User, UserModel>()
                .ForMember(d => d.DisplayFullName, s => s.MapFrom(x => $"{x.GivenName} {x.FamilyName}"))
                .ForMember(d => d.CompanyName, s => s.MapFrom(x => (x.CompanyUser != null && x.CompanyUser.Company != null) ? x.CompanyUser.Company.Name : "Not Assigned"))
                .ForMember(d => d.Claims, s => s.MapFrom(x => x.UserClaim))
                .ForMember(d => d.DisplayClaims, s => s.MapFrom(x => x.UserClaim.Select(u => u.Claim.Name)))
                .ForMember(d => d.DisplayClaimsText, s => s.MapFrom(x => string.Join(", ", x.UserClaim.Select(u => u.Claim.Name))));

            this.CreateMap<CompanyUser, CompanyUserModel>()
                .ForMember(d => d.Id, s => s.MapFrom(x => x.UserId))
                .ForMember(d => d.DisplayFullName, s => s.MapFrom(x => $"{x.User.GivenName} {x.User.FamilyName}"))
                .ForMember(d => d.Email, s => s.MapFrom(x => x.User.Email))
                .ForMember(d => d.Claims, s => s.MapFrom(x => x.User.UserClaim))
                .ForMember(d => d.DisplayClaims, s => s.MapFrom(x => x.User.UserClaim.Select(u => u.Claim.Name)))
                .ForMember(d => d.DisplayClaimsText, s => s.MapFrom(x => string.Join(", ", x.User.UserClaim.Select(u => u.Claim.Name))))
                .ForMember(d => d.ClaimId, s => s.Ignore())
                .ForMember(d => d.GivenName, s => s.Ignore())
                .ForMember(d => d.FamilyName, s => s.Ignore())
                .ForMember(d => d.LastLoginDate, s => s.Ignore())
                .ForMember(d => d.InvitationEmailLink, s => s.Ignore());

            this.CreateMap<UserModel, User>()
                .ForMember(d => d.LocaleId, s => s.Ignore())
                .ForMember(d => d.Locale, s => s.Ignore())
                .ForMember(d => d.CompanyUser, s => s.Ignore())
                .ForMember(d => d.UserClaim, s => s.Ignore())
                .ForMember(d => d.NewsArticleContentAuthor, s => s.Ignore())
                .ForMember(d => d.NewsArticleContentReviewer, s => s.Ignore())
                .ForMember(d => d.NewsletterSubscription, s => s.Ignore())
                .ForMember(d => d.UserNewsCategory, s => s.Ignore())
                .ForMember(d => d.Newsletter, s => s.Ignore())
                .ForMember(d => d.CreatedDate, s => s.Ignore())
                .ForMember(d => d.CreatedBy, s => s.Ignore())
                .ForMember(d => d.LastUpdatedDate, s => s.Ignore())
                .ForMember(d => d.LastUpdatedBy, s => s.Ignore())
                .AfterMap((m, u) =>
                {
                    foreach (int id in m.Claims)
                    {
                        if (u.UserClaim.All(x => x.ClaimId != id))
                        {
                            u.UserClaim.Add(new UserClaim
                            {
                                ClaimId = id,
                                UserId = u.Id
                            });
                        }
                    }
                    foreach (int id in u.UserClaim.Where(x => !m.Claims.Contains(x.ClaimId)).Select(x => x.ClaimId).ToList())
                    {
                        u.UserClaim.Remove(u.UserClaim.First(x => x.ClaimId == id));
                    }
                });

            this.CreateMap<User, UserFindResultModel>()
                .ForMember(um => um.Name, u => u.MapFrom(x => $"{x.GivenName} {x.FamilyName} ({x.Email})"))
                .ForMember(um => um.Value, u => u.MapFrom("Email"))
                .ForMember(d => d.Claims, s => s.MapFrom(x => x.UserClaim))
                .ForMember(d => d.Active, o => o.Ignore());

            this.CreateMap<Microsoft.Graph.Models.User, UserFindResultModel>()
                .ForMember(um => um.Name, u => u.MapFrom(x => $"{x.GivenName} {x.Surname} ({x.GetEmail()})"))
                .ForMember(um => um.Email, u => u.MapFrom(x => x.GetEmail()))
                .ForMember(um => um.FamilyName, u => u.MapFrom("Surname"))
                .ForMember(um => um.Id, u => u.MapFrom(x => 0))
                .ForMember(d => d.Value, o => o.Ignore())
                .ForMember(d => d.Active, o => o.Ignore())
                .ForMember(d => d.Claims, o => o.Ignore());


            this.CreateMap<User, CompanyUserModel>()
                .ForMember(d => d.DisplayFullName, s => s.MapFrom(x => $"{x.GivenName} {x.FamilyName}"))
                .ForMember(d => d.Active, s => s.MapFrom(x => x.CompanyUser.Active))
                .ForMember(d => d.ClaimId, s => s.MapFrom(x => x.UserClaim.FirstOrDefault(y => y.Claim.ClaimType == "company")))
                .ForMember(d => d.Claims, o => o.Ignore())
                .ForMember(d => d.DisplayClaims, o => o.Ignore())
                .ForMember(d => d.DisplayClaimsText, o => o.Ignore())
                .ForMember(d => d.CompanyName, o => o.Ignore());

            this.CreateMap<CompanyUserModel, User>()
                .ForMember(d => d.Id, s => s.Ignore())
                .ForMember(d => d.LastLoginDate, s => s.Ignore())
                .ForMember(d => d.LocaleId, s => s.Ignore())
                .ForMember(d => d.Locale, s => s.Ignore())
                .ForMember(d => d.CompanyUser, s => s.Ignore())
                .ForMember(d => d.UserClaim, s => s.Ignore())
                .ForMember(d => d.NewsArticleContentAuthor, s => s.Ignore())
                .ForMember(d => d.NewsArticleContentReviewer, s => s.Ignore())
                .ForMember(d => d.NewsletterSubscription, s => s.Ignore())
                .ForMember(d => d.UserNewsCategory, s => s.Ignore())
                .ForMember(d => d.Newsletter, s => s.Ignore())
                .ForMember(d => d.CreatedDate, s => s.Ignore())
                .ForMember(d => d.CreatedBy, s => s.Ignore())
                .ForMember(d => d.LastUpdatedDate, s => s.Ignore())
                .ForMember(d => d.LastUpdatedBy, s => s.Ignore())
                .AfterMap((m, u) =>
                {
                    if (u.CompanyUser == null)
                    {
                        u.CompanyUser = new CompanyUser();
                    }

                    u.CompanyUser.Active = m.Active;

                    UserClaim uc = u.UserClaim.FirstOrDefault(x => x.Claim.ClaimType == "company");
                    if (uc != null)
                    {
                        u.UserClaim.Remove(uc);
                    }

                    uc = new UserClaim();
                    u.UserClaim.Add(uc);
                    uc.ClaimId = m.ClaimId.Value;
                });
        }
    }
}
