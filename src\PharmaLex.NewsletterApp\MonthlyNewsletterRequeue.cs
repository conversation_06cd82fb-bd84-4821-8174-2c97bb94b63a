﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using PharmaLex.NewsletterApp.Helpers;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class MonthlyNewsletterRequeue
    {
        INewsletterRequeueHelper helper;

        public MonthlyNewsletterRequeue(INewsletterRequeueHelper helper)
        {
            this.helper = helper;   
        }

        [Function("retrymonthlynewsletters")]
        [QueueOutput("%qn%")]
        public async Task<NewsletterQueueMessage[]> Run([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequestData req, FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("RetryMonthlyNewsletters");

            var processList = await helper.ExtractSubscriptionsFromRequest(req);

            return processList.Select(x => new NewsletterQueueMessage { Id = x }).ToArray();
        }
    }
}
