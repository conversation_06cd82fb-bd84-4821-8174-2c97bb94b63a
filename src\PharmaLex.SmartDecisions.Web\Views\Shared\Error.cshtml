﻿@{
    ViewData["Title"] = "Ooops!";
}

<div class="manage-container">
    <header class="manage-header">
        <h2>Ooops!</h2>
        <a href="/home" class="button secondary icon-button-back">Main Page</a>
    </header>


    <p>Something unexpected happened and we were unable to complete your request. The error has been logged but a system administrator has not been notified.</p>
    <p>&nbsp;</p>
    <p>
        If you repeatedly experience the same issue please
        <a class="button icon-mail" style="margin:0;" href="https://cencoragcs.zendesk.com/hc/en-gb" target="_blank">let us know</a>
        providing as much detail as possible about the error e.g. data being managed, steps to reproduce etc.
    </p>
    
</div>
