﻿using Microsoft.Extensions.Configuration;
using PharmaLex.Helpers;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public class SmartDecisionsAppSettingsHelper : AppSettingsHelper
    {
        public string? Version { get; private set; }
        public string? BuildNumber { get; private set; }

        public SmartDecisionsAppSettingsHelper(IConfiguration configuration) : base(configuration)
        {
            Version = configuration.GetValue<string>("appSettings:Version");
            BuildNumber = configuration.GetValue<string>("appSettings:BuildNumber");
        }
    }
}
