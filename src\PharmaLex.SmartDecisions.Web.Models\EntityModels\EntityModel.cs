﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public abstract class EntityModel : IModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public static class EntityModelExtensions
    {
        public static IEnumerable<SelectListItem> ToSelectList(this IEnumerable<EntityModel> models)
        {
            return models.Select(x => new SelectListItem
            {
                Value = x.Id.ToString(),
                Text = x.Name
            });
        }
    }
}
