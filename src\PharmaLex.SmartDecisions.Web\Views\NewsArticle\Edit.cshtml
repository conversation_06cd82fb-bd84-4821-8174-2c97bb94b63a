﻿@model NewsArticleModel
@using PharmaLex.Authentication.B2C
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.DataAccess
@using System.Linq
@using Newtonsoft.Json

@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@{
    string tinyLang = $"{System.Threading.Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName}_" +
                $"{System.Threading.Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName.ToUpper()}";
    int currentUserId = this.User.GetClaimValue<int>("plx:userid");
    var publishingStates = mapper.Map<IEnumerable<NewsArticlePublishingStateModel>>(Enum.GetValues(typeof(NewsArticlePublishingState)));

    var newsSources = (await ls.LocaliseList<NewsSource>()).OrderBy(x => x.Name);
    var newsAuthors = cache.CreateEntity<User>()
        .Configure(o => o.Include(x => x.UserClaim).ThenInclude(x => x.Claim))
        .WhereProjected<NewsUserModel>(x => x.UserClaim.Any(y => y.Claim.ClaimType == "news"))
        .OrderBy(x => x.Name);

    var newsCategories = (await ls.LocaliseList<NewsCategory>()).OrderBy(x => x.Name);
    var locales = (await ls.LocaliseList<Locale>())
        .Where(x => x.Active)
        .OrderByDescending(x => x.Default)
        .ToList();

    ViewData["Title"] = ls.Localise("edit-news-article").Value;
}
<div id="editArticle" v-cloak>

    <div class="sub-header">
        <h2>@ls.Localise("edit-news-article")</h2>
        <div class="controls">
            <a class="button secondary" href="/articles">@ls.Localise("news-articles")</a>
        </div>
    </div>

    <section class="pl-1">
        <form method="post" id="editArticleContent" v-on:submit="preSubmit">

            <div class="flex flex-nowrap pl-1">
                <div class="flex-item flex-70-percent pr-2 white-inputs">

                    <div class="tab-container">
                        <div v-for="tab in tabs" :class="[{'active': selectedTab === tab.id}]" v-on:click="switchTab($event, tab.id)">{{tab.name}}<span v-if="tab.default"></span></div>
                    </div>

                    <div class="p-2 grey-5-background">
                        <h5>@ls.Localise("details")</h5>
                        <div class="form-group">
                            <label for="title">@ls.Localise("title")</label>
                            <input type="text" id="title"
                                   name="NewsArticleContent[0].Title"
                                   v-model="selectedTabContent.title"
                                   required autocomplete="off" placeholder="@ls.Localise("enter-title")" @@change="hasChanges = true" />
                        </div>
                        <div class="form-group">
                            <label for="url">
                                @ls.Localise("source-url")
                                <a v-if="selectedTabContent.sourceUrl" target="_blank"
                                   :href="selectedTabContent.sourceUrl">@ls.Localise("open")</a>
                            </label>
                            <input type="url" id="url"
                                   name="NewsArticleContent[0].SourceUrl"
                                   v-model="selectedTabContent.sourceUrl"
                                   required autocomplete="off"
                                   pattern="https?://.*"
                                   placeholder="@($"http(s)://-{ls.Localise("enter-source-url")}")"
                                   size="255"
                                   @@change="hasChanges = true" />
                        </div>

                        <div class="flex flex-nowrap mb-3">
                            <div class="flex-item flex-x3 pr-2">
                                <div class="form-group">
                                    <label for="authorId">@ls.Localise("author")</label>
                                    <searchlist id="authorId" v-model="selectedTabContent.author"
                                                :options="newsAuthors" :config="selectedTabContent.authorConfig" @@change="hasChanges = true">
                                    </searchlist>
                                </div>
                            </div>
                            <div class="flex-item flex-x3 pl-2">
                                <div class="form-group">
                                    <label for="reviewerId">@ls.Localise("reviewer")</label>
                                    <searchlist id="reviewerId" v-model="selectedTabContent.reviewer"
                                                :options="newsAuthors" :config="selectedTabContent.reviewerConfig" @@change="hasChanges = true">
                                    </searchlist>
                                </div>
                            </div>
                            <div class="flex-item flex-x3 pl-2">
                                <div class="form-group">
                                    <label v-if="!isInfoflash" for="goLiveDate">@ls.Localise("(news).edit.go-live-date")</label>
                                    <input v-if="!isInfoflash" type="date" id="goLiveDate"
                                           name="NewsArticleContent[0].GoLiveDate"
                                           v-model="selectedTabContent.goLiveDate"
                                           autocomplete="off"
                                           required
                                           :disabled="isInfoflash || isBeforeToday"
                                           :min="todayDate"
                                           @@change="hasChanges = true" />
                                </div>
                            </div>
                        </div>


                        <template v-if="selectedTabContent">
                        </template>

                        <h5 class="mt-4">@ls.Localise("impact-assessment")</h5>

                        <template v-if="isPdf">
                            <!-- Migrated document -->
                            <iframe id="iframeContent" style="width:100%; height: 50vh;" :src="`/js/lib/pdfjs/web/viewer.html`"></iframe>
                        </template>
                        <template v-else>
                            <textarea id="impact" style="min-height: 50vh; margin-top: inherit;"
                                      name="NewsArticleContent[0].Body"
                                      v-model="selectedTabContent.body"
                                      autocomplete="off"
                                      @@change="hasChanges = true"></textarea>
                        </template>

                        <template v-if="canSendForReview">
                            <div class="buttons">
                                <button type="submit" :disabled="formSubmit" v-on:click="preventDefaultEventAndSetState($event, @((int)NewsArticlePublishingState.PendingReview))">@ls.Localise("send-review")</button>
                            </div>
                        </template>
                        <template v-if="canReview">
                            <div class="buttons">
                                <button type="submit" :disabled="formSubmit" v-on:click="approveSendNotifications">@ls.Localise("approve")</button>
                                <button type="submit" :disabled="formSubmit" v-on:click="preventDefaultEventAndSetState($event, @((int)NewsArticlePublishingState.Rejected))">@ls.Localise("reject")</button>
                            </div>
                        </template>
                        <template v-if="this.selectedTabContent.publishingStateId === 3">
                            <div class="buttons">
                                <button disabled>@ls.Localise("[NewsArticlePublishingState].approved")</button>
                            </div>
                        </template>
                    </div>
                </div>


                <div class="flex-item flex-30-percent">
                    <h5>@ls.Localise("information")</h5>

                    <div class="form-group">
                        <label for="newsSourceId">@ls.Localise("news-source")</label>
                        <searchlist id="newsSourceId" v-model="selectedNewsSource"
                                    :options="newsSources" :config="newsSourcesConfig" @@change="hasChanges = true">
                        </searchlist>
                        <input type="hidden" name="NewsSourceId" :value="selectedNewsSource?.id" />
                    </div>

                    <div class="form-group">
                        <label for="sourcePubDate">@ls.Localise("source-publication-date")</label>
                        <input type="date" id="sourcePubDate"
                               name="SourcePublicationDate"
                               v-model="article.sourcePublicationDate"
                               autocomplete="off"
                               required
                               :max="todayDate"
                               @@change="hasChanges = true" />
                    </div>

                    <div class="form-group" v-if="article.createdBy">
                        <label for="createdBy">@ls.Localise("created-by")</label>
                        <input type="text" id="createdBy" disabled="disabled" :value="article.createdBy" />
                        <input type="hidden" name="CreatedBy" :value="article.createdBy" />
                    </div>

                    <div class="form-group">
                        <template v-for="(c, i) in dropdownNewsCategories">
                            <div class="mt-3">
                                <label :for="`nc${i}`">{{c.name}}</label>
                                <searchlist :id="`nc${i}`" v-model="dropdownNewsCategoriesConfig[i].selected"
                                            :options="c.children" :config="dropdownNewsCategoriesConfig[i]"
                                            @@change="hasChanges = true">
                                </searchlist>
                            </div>

                        </template>
                    </div>

                    <div class="flex flex-nowrap">
                        <div class="flex-item flex-x2 flex flex-nowrap flex-align-center">
                            <input id="headline" type="checkbox" v-model="isHeadline" @@click="isMiscellaneous = false; isInfoflash = false;  hasChanges = !hasChanges" />
                            <label for="headline" class="m-0">@ls.Localise("(news).headline")</label>
                        </div>
                        <div class="flex-item flex-x2 flex flex-nowrap flex-align-center">
                            <input id="miscellaneous" type="checkbox" v-model="isMiscellaneous" @@click="isHeadline = false; isInfoflash = false; hasChanges = !hasChanges" />
                            <label for="miscellaneous" class="m-0">@ls.Localise("(news).miscellaneous")</label>
                        </div>
                        <div class="flex-item flex-x2 flex flex-nowrap flex-align-center">
                            <input id="newsflash" type="checkbox" v-model="isInfoflash" @@click="isHeadline = false; isMiscellaneous = false; hasChanges = !hasChanges" />
                            <label for="newsflash" class="m-0">@ls.Localise("(news).infoflash")</label>
                        </div>
                    </div>

                    <h5 class="mt-4">@ls.Localise("categories")</h5>
                    <treeview v-model:items="hierarchicalNewsCategories" style="max-height:500px;"></treeview>
                </div>
            </div>

            <input type="hidden" name="NewsArticleContent[0].LocaleId" :value="selectedTabContent.localeId" />
            <input type="hidden" name="NewsArticleContent[0].Id" :value="selectedTabContent.id" />
            <input type="hidden" name="NewsArticleContent[0].AuthorId" :value="selectedTabContent.author?.id" />
            <input type="hidden" name="NewsArticleContent[0].ReviewerId" :value="selectedTabContent.reviewer?.id" />
            <input type="hidden" name="NewsArticleContent[0].PublishingStateId" :value="selectedTabContent.publishingStateId" />
            <input type="hidden" name="NewsArticleContent[0].GoLiveDate" :value="selectedTabContent.goLiveDate" />
            <input type="hidden" name="NewsArticleContent[0].ImpactAssessmentSummary" :value="impactAssessmentSummary" />
            <input type="hidden" name="NewsArticleContent[0].ImpactAssessmentText" :value="impactAssessmentText" />
            <input type="hidden" name="SelectedLocaleId" :value="selectedTab" />
            <input type="hidden" name="IsMigrated" :value="isPdf" />
            <input type="hidden" name="ImportanceId" :value="importance" />
            <input v-for="(c, i) in [...new Set([...hierarchicalCategories().filter(x => x.selected), ...dropdownNewsCategoriesConfig.map(x => x.selected).filter(Boolean)])]"
                   type="hidden"
                   :name="`NewsCategoryIds[${i}]`"
                   :value="c.id" />
            <div class="buttons">
                <a class="button secondary icon-button-cancel" href="/articles">@ls.Localise("cancel")</a>
                <button :disabled="formSubmit" v-on:click.once="savingChanges = true">@ls.Localise("save")</button>
            </div>
            <input type="hidden" name="Id" value="@Model.Id" />
        </form>
    </section>


    <modal-dialog v-if="confirmResolve"
                  :title="'@ls.LocaliseSafe("(news).(articles).confirm-approve-infoflash-title")'"
                  :height="'50'"
                  v-on:close="confirmReject"
                  v-on:confirm="confirmResolve">
        <p>{{confirmText}}</p>
    </modal-dialog>

</div>

@section Scripts {
    <script src="https://cdn.tiny.cloud/1/d1bebbpjpekufn4wupmtojcyuuwhwz45h5jjcaya1ddatlbe/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
    <script type="text/javascript">
            const loadArticleContentBody = (content) => {
                if (content.body) {
                    articleContentsInitial.find(x => x.localeId === content.localeId).body = content.body;
                    tinymce.activeEditor.setContent(content.body);
                    initialArticleImpactAssessment = tinymce.activeEditor.getContent({ format: 'text' });
                }
            };
        const tinyLang = '@tinyLang';

        let initialArticleImpactAssessment = '';
        const publishingStates = @Html.Raw(publishingStates.ToJson());
        const locales = @Html.Raw(JsonConvert.SerializeObject(locales));
        const article = @Html.Raw(Model.ToJson());
        const sources = @Html.Raw(JsonConvert.SerializeObject(newsSources));
        const authors = @Html.Raw(newsAuthors.ToJson());
        const articleContentsInitial = locales.map(x => {
            const newsArticleContentForLocale = article.newsArticleContent.find(y => y.localeId === x.id);
            const lang = {
                localeId: x.id,
                title: '',
                body: '',
                author: authors.find(x => x.id === newsArticleContentForLocale?.authorId),
                authorConfig: {
                    resources: {
                        placeholderText: '@ls.LocaliseSafe("select-author")',
                        clearSelectionText: '@ls.LocaliseSafe("clear-selection")'
                    }
                },
                reviewer: authors.find(x => x.id === newsArticleContentForLocale?.reviewerId),
                reviewerConfig: {
                    resources: {
                        placeholderText: '@ls.LocaliseSafe("select-reviewer")',
                        clearSelectionText: '@ls.LocaliseSafe("clear-selection")'
                    }
                },
                sourceUrl: '',
                publishingStateId: publishingStates.find(x => x.name === 'Draft')?.id || 1,
                publishingState: publishingStates.find(x => x.id === newsArticleContentForLocale?.publishingStateId || 1)?.name
            };

            return newsArticleContentForLocale ? { ...lang, ...newsArticleContentForLocale } : lang;
        });

        var pageConfig = {
            appElement: '#editArticle',
            data: function () {
                const buildCategories = (categories, current) => {
                    current = current || categories.filter(x => !x.parentId);
                    current.forEach(c => {
                        c.children = categories.filter(x => x.parentId == c.id);
                        buildCategories(categories, c.children);
                    });

                    return current;
                };
                const newsCategories = buildCategories(@Html.Raw(JsonConvert.SerializeObject(newsCategories))).filter(x => !x.parentId);
                const hierarchicalNewsCategories = newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.Products) || x.groupId === @((int)NewsCategoryGroup.Themes));
                const dropdownNewsCategories = newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.GeographicalScope) || x.groupId === @((int)NewsCategoryGroup.TypeOfText))
                    .sort((a, b) => a.localisationKey.localeCompare(b.localisationKey));

                return {
                    formSubmit: false,
                    userId: @currentUserId,
                    article,
                    newsAuthors: <AUTHORS>
                    newsSources: sources,
                    isHeadline: article.importanceId == @((int)NewsArticleImportance.Headline),
                    isMiscellaneous: article.importanceId == @((int)NewsArticleImportance.Miscellaneous),
                    isInfoflash: article.importanceId == @((int)NewsArticleImportance.INFOFLASH),
                    confirmResolve: null,
                    confirmReject: null,
                    confirmText: null,
                    todayDate: this.getDate(new Date()),

                    selectedNewsSource: sources.find(x => x.id === article.newsSourceId),
                    newsSourcesConfig: {
                        resources: {
                            placeholderText: '@ls.LocaliseSafe("select-news-source")',
                            clearSelectionText: '@ls.LocaliseSafe("clear-selection")'
                        },
                        required: true
                    },
                    dropdownNewsCategoriesConfig: dropdownNewsCategories.map(x => {
                        return {
                            selected: x.children.find(y => article.newsCategoryIds.includes(y.id)),
                            resources: {
                                placeholderText: x.name,
                                clearSelectionText: '@ls.LocaliseSafe("clear-selection")'
                            },
                            required: true
                        };
                    }),
                    hierarchicalNewsCategories: this.initHierarchicalCategories(hierarchicalNewsCategories),
                    dropdownNewsCategories,
                    tabs: locales,
                    articleContents: articleContentsInitial.map(x => { return { ...x }; }),
                    selectedTab: article.selectedLocaleId || locales.find(x => article.newsArticleContent.find(y => y.localeId === x.id))?.id || locales.find(x => x.default)?.id,
                    publishingStates,
                    impactAssessmentSummary: '',
                    impactAssessmentText: '',
                    hasChanges: false,
                    savingChanges: false
                };
            },
            computed: {
                publishingState() {
                    return this.publishingStates.find(x => x.id === this.selectedTabContent.publishingStateId)?.name;
                },
                selectedTabContent() {
                    return this.articleContents.find(x => x.localeId === this.selectedTab);
                },
                isBeforeToday() {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    return new Date(this.selectedTabContent.goLiveDate) < today && this.selectedTabContent.publishingStateId == 3;
                },
                canSendForReview() {
                    return this.userId === this.selectedTabContent.author?.id &&
                        [1, 4].includes(this.selectedTabContent.publishingStateId) &&
                        this.article?.id != 0 &&
                        this.selectedTabContent.body;
                },
                canReview() {
                    return this.userId === this.selectedTabContent.reviewer?.id &&
                        this.selectedTabContent.publishingStateId === 2;
                },
                isPdf() {
                    return this.selectedTabContent.blobName?.indexOf('.pdf') > 0;
                },
                selectedCategories() {
                    const products = this.hierarchicalNewsCategories.find(x => x.groupId === @((int)NewsCategoryGroup.Products));
                    let selectedCategoryChildren = [...products.children];
                    let selectedCategories = [];
                    selectedCategoryChildren.forEach(x => {
                        if (x.children) {
                            x.children.forEach(child => {
                                if (child.selected) {
                                    selectedCategories.push({ ...child })
                                }
                            })
                        }
                    })
                    return selectedCategories;
                },
                importance() {
                    if (this.isHeadline) {
                        return @((int)NewsArticleImportance.Headline);
                    }
                    else if (this.isMiscellaneous) {
                        return @((int)NewsArticleImportance.Miscellaneous);
                    }
                    else if (this.isInfoflash) {
                        return @((int)NewsArticleImportance.INFOFLASH);
                    }
                    else {
                        return @((int)NewsArticleImportance.Newsletter);
                    }
                },
                articleTitle() {
                    return this.selectedTabContent.title;
                }
            },
            watch: {
                selectedTab(selectedTabId) {
                    tinyMCE.activeEditor.setContent(this.selectedTabContent.body);
                    initialArticleImpactAssessment = this.selectedTabContent.body;
                },
                articleTitle(value) {
                    let maxTitleLength = 256;
                    if (value.length > maxTitleLength) {
                        this.$nextTick(() => {
                            this.selectedTabContent.title = value.substring(0, maxTitleLength);
                            plx.toast.show(`@ls.Localise("max-field-length") ${maxTitleLength}`, 2, 'failed', null, 5000, { useIcons: true });
                        });
                    }
                },
                selectedNewsSource(newVal) {
                    this.$nextTick(() => {
                        document.querySelector('input[name="NewsSourceId"]').value = newVal?.id || '';
                    });
                }
            },
            methods: {
                save(e) {
                    this.preSubmit(e)
                    Vue.nextTick(() => document.getElementById('editArticleContent').submit());
                },
                preventDefaultEventAndSetState(e, stateId) {
                    e.preventDefault();
                    this.setStateAndSave(stateId);
                },
                setStateAndSave(stateId) {
                    if (this.hasChanges === true) {
                        plx.toast.show('@ls.LocaliseSafe("warning-save-message")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }
                    this.selectedTabContent.publishingStateId = stateId;
                    this.savingChanges = true;
                    this.preSubmit();
                    Vue.nextTick(() => document.getElementById('editArticleContent').submit());
                },
                checkIfAnySelected(child) {
                    if (child.selected === true) {
                        return child;
                    } else if (child.children.length) {
                        var i;
                        let result = null;
                        for (i = 0; result == null && i < child.children.length; i++) {
                            result = this.checkIfAnySelected(child.children[i]);
                        }
                        return result;
                    }
                    return null;
                },
                approveSendNotifications(e) {
                    e.preventDefault();
                    this.savingChanges = true;
                    let products = this.hierarchicalNewsCategories.find(x => x.groupId === @((int)NewsCategoryGroup.Products));
                    if (this.checkIfAnySelected(products) == null) {
                        plx.toast.show('@ls.LocaliseSafe("select-one-product")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }
                    else {
                        if (this.isInfoflash) {
                            let parent = this;
                            var infoFlashPromise = new Promise((claimResolve, claimReject) => {
                                var confirm = new Promise((resolve, reject) => {
                                    parent.confirmText = `${'@ls.LocaliseSafe("(news).(articles).confirm-approve-infoflash-text")'}`;
                                    parent.confirmResolve = resolve;
                                    parent.confirmReject = reject;
                                });
                                confirm.then(() => {

                                    // golivedate here
                                    let date = new Date();
                                    parent.selectedTabContent.goLiveDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;

                                    parent.setStateAndSave(@((int)NewsArticlePublishingState.Approved));
                                }, claimReject)
                                    .finally(() => {
                                        parent.confirmResolve = null;
                                        parent.confirmReject = null;
                                    });
                            });
                        }
                        else {
                            this.setStateAndSave(@((int)NewsArticlePublishingState.Approved));
                        }
                    }
                },
                switchTab(e, tabId) {
                    const goToNewTab = () => {
                        const newTab = this.articleContents.find(x => x.localeId === tabId);
                        this.selectedTabContent.body = tinyMCE.activeEditor.getContent();
                        if (newTab.id) {
                            window.location.href = newTab.path;
                        } else this.selectedTab = tabId;
                    };

                    const localeContent = articleContentsInitial.find(x => x.localeId === this.selectedTabContent.localeId);
                    const editorContents = tinyMCE.activeEditor.getContent({ format: 'text' });
                    if (!this.compareEqual(localeContent, this.selectedTabContent) ||
                        initialArticleImpactAssessment !== editorContents) {
                        if (confirm('@ls.LocaliseSafe("warn-article-changed")')) {
                            goToNewTab();
                        } else e.preventDefault();
                    } else goToNewTab();
                },
                compareEqual(o1, o2) {
                    if (!o1 || !o2) {
                        return false;
                    }
                    return Object.keys(o1).length === Object.keys(o2).length
                        && JSON.stringify(o1) === JSON.stringify(o2);
                },
                initHierarchicalCategories(categories) {
                    const ids = article.newsCategoryIds;
                    const iterator = (item) => {
                        item.selected = ids.includes(item.id);
                        item.children.forEach(iterator);
                    }

                    categories.forEach(iterator);
                    return categories;
                },
                hierarchicalCategories() {
                        const reducer = (acc, cat) => {
                            acc.push(cat);
                            if (cat.children.length) {
                                return cat.children.reduce(reducer, acc);
                            }

                            return acc;
                        };

                        return this.hierarchicalNewsCategories.reduce(reducer, []);
                },
                preSubmit(e) {
                    this.formSubmit = true;
                    this.hasChanges = false;

                    if (!this.isPdf) {
                        const maxLength = 250;
                        this.editorContents = tinyMCE.activeEditor.getContent({ format: 'text' });

                        // Business rule: truncate at first control character /n, /r, /t etc.
                        let regex = RegExp(/[\u0000-\u001F\u007F-\u009F]/g, 'g');
                        var firstControlChar = regex.exec(this.editorContents);
                        let cutoff = firstControlChar ? Math.min(firstControlChar.index, maxLength) : maxLength;
                        this.impactAssessmentSummary = this.editorContents.substring(0, cutoff);
                        if (this.editorContents.length > maxLength) {
                            this.impactAssessmentSummary = this.impactAssessmentSummary.slice(0, -3) + "...";
                        }
                        this.impactAssessmentText = this.editorContents;
                    }
                },
                getDate(d) {
                    const date = typeof d === 'object' ? d : new Date(d);
                    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
                }
            },
            mounted() {
                const self = this;
                window.addEventListener('beforeunload', function (e) {
                    const isSubset = (array1, array2) => array2.every((element) => array1.includes(element));
                    const localeContent = articleContentsInitial.find(x => x.localeId === self.selectedTabContent.localeId);
                    const newCategoriesSelected = self.hierarchicalCategories().filter(x => x.selected).flatMap(x => x.id);
                    self.editorContents = tinyMCE.activeEditor.getContent({ format: 'text' });
                    if ((!self.compareEqual(localeContent, self.selectedTabContent) ||
                        !isSubset(self.article.newsCategoryIds, newCategoriesSelected) ||
                        self.hasChanges ||
                        initialArticleImpactAssessment !== self.editorContents) && self.savingChanges !== true) {
                        e.preventDefault();
                        event.returnValue = '';
                    }
                })

                loadPdfContent(this.selectedTabContent.bodyBase64);

                const tinyInitObj = {
                    selector: '#impact',
                    plugins: 'powerpaste fullscreen advcode casechange export formatpainter autolink image lists advlist link checklist media permanentpen table advtable',
                    toolbar: 'link | table | bold italic underline strikethrough casechange | fontsizeselect forecolor backcolor | checklist numlist bullist | outdent indent | alignleft aligncenter alignright | code formatpainter',
                    toolbar_mode: 'floating',
                    default_link_target: '_blank',
                    target_list: false,
                    relative_urls: false,
                    powerpaste_word_import: 'merge',
                    powerpaste_allow_local_images: true,
                    powerpaste_keep_unsupported_src: true,
                    link_assume_external_targets: 'https',
                    setup: function (editor) {
                        editor.on('keyup', function () {
                            editor.targetElm.dispatchEvent(new Event('change'));
                        });
                        editor.on('init', function (e) {
                            loadArticleContentBody(self.selectedTabContent);
                        });
                    }
                };
                if (!tinyLang.startsWith('en_')) {
                    tinyInitObj.language = tinyLang;
                }
                tinymce.init(tinyInitObj);
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/ImageCell" />
    <partial name="Components/SearchList" />
    <partial name="Components/TreeView" />
    <partial name="Components/ModalDialog" />
}
