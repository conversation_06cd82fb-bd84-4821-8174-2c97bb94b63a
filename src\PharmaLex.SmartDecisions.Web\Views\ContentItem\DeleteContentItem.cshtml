﻿@model EditContentItemViewModel
@{
    ViewData["Title"] = "Delete ContentItem";
}

<div class="sub-header">
    <h2>Delete <em>@Model.Item.Name</em> @Model.ContentType.Name</h2>
    <div class="controls">
        <a class="button secondary" href="/data/@Model.ContentType.Id">@Model.ContentType.PluralName</a>
    </div>
</div>

<section>
    <form method="post">
        <div class="flex mb-4">
            <i class="m-icon warning-color">warning</i>
            <p class="lead m-0 p-0 pl-1"><strong>Warning:</strong> Deleting is permanent, and once deleted, the data cannot be restored.</p>
        </div>

        <p class="lead">Are you sure you want to delete the @Model.ContentType.Name, <strong><em>@Model.Item.Name</em></strong>?</p>

        <div class="buttons">
            <a class="button secondary" href="/data/edit/@Model.Item.Id">Cancel</a>
            <button type="submit">Delete</button>
        </div>
    </form>
</section>
