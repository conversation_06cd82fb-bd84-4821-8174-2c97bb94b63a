﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleModel : IModel
    {
        public int Id { get; set; }
        [Required]
        public int? NewsSourceId { get; set; }
        public string CreatedBy { get; set; }
        [Required, MinLength(1)]
        public List<int> NewsCategoryIds { get; set; } = new List<int>();
        [Required]
        public string SourcePublicationDate { get; set; }
        public int? SelectedLocaleId { get; set; }
        public int? ImportanceId { get; set; }
        [Required, MinLength(1), MaxLength(1)]
        public List<NewsArticleContentModel> NewsArticleContent { get; set; } = new List<NewsArticleContentModel>();
        public bool IsMigrated { get; set; }
    }

    public class NewsArticleContentModel : IModel
    {
        public int? Id { get; set; }
        [Required, Max<PERSON>ength(256)]
        public string Title { get; set; }
        public string Body { get; set; }
        public string BodyBase64 { get; set; }
        public string BodyUrl { get; set; }
        [Required]
        public int LocaleId { get; set; }
        public int? AuthorId { get; set; }
        public int? ReviewerId { get; set; }
        [Required, MaxLength(256)]
        public string SourceUrl { get; set; }
        public int PublishingStateId { get; set; }
        [JsonIgnore]
        public string BlobName { get; set; }
        public string Path { get; set; }
        public string ImpactAssessmentSummary { get; set; }
        public string ImpactAssessmentText { get; set; }
        public string GoLiveDate { get; set; }
    }
}
