{"version": 3, "file": "css/smartDecisionsCss.css", "mappings": "AAAA;AAIA;AA6FA;AAcA;AC/GA;ACAA;EACI,cFuBe;;AEpBnB;EACI,cFOe;;AEJnB;EACI,cFoCS;;AG7Cb;AAEA;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,mBAAe;MAAf,eAAe;EAHnB;IAMQ,kBAAkB;IAClB,yBHwBQ;IGvBR,WHiEG;IGhEH,eAAe;IACf,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,gBAAgB;IAbxB;MAgBY,yBHeI;MGdJ,cHgBI;IGjChB;MAqBY,iBAAiB;IArB7B;MAyBY,eAAe;MACf,iBAAiB;;AC5B7B;AAEI;EACI,oBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,qBAAgC;;AADpC;EACI,sBAAgC;;AAKpC;EACI,qBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,sBAAiC;;AADrC;EACI,uBAAiC;;AAIzC;EACI,WAAW;EADf;IAIQ,UAAU;EAJlB;IAQQ,aAAa;;AJrBrB;AAIA;AA6FA;AAcA;AK7GA;EACI,yBAAyB;EAD7B;IAIQ,iBAAiB;EAJzB;IAQQ,iBAAiB;EARzB;IAYQ,mBLuBQ;EKnChB;IAgBQ,yBLgBQ;IKhChB;MAmBY,yBAAyB;MACzB,yBLYI;EKhChB;IA8BM,oBAAoB;;AAO1B;EACI,uBAAe;UAAf,eAAe;;ACxCnB;EACI;IACI,cAAc,IACjB;;AAGL;EACI;IACI,cAAc,IACjB;;AAGL;EACI;IACI,cAAc,IACjB;;AAIL;EACI,0BAA0B;EAC1B,2BAA2B;;AAG/B;AACA;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,yBNSY;EMRZ,WN8CO;EM7CP,eAAe;EACf,sBAAsB;EACtB,iBAAiB;EACjB,oBAAoB;EACpB,mBAAmB;EAKnB;EAIA;EAIA;EAIA;EAIA;EAIA,qBAAoB;EAlCxB;IAYQ,kBAAkB;EAZ1B;IAgBQ,yBNoCO;EMpDf;IAoBQ,yBNtBY;EMEpB;IAwBQ,oCAAsC;EAxB9C;IA4BQ,yBNYY;EMxCpB;IAgCQ,yBNHY;EM7BpB;IAoCQ,yBNSa;EM7CrB;IAwCQ,gBAAgB;EAxCxB;IA4CQ,8BAA8B;IAC9B,cNyBM;IMxBN,2BAAyB;IACzB,2BAAmB;IAAnB,mBAAmB;;AAI3B;EACI,kBAAkB;EAClB;EAKA;EAKA;EAKA;EAKA,YAAW;EAtBf;IAIQ,yBN9DkB;IM+DlB,cNcM;EMnBd;IASQ,yBNnEkB;IMoElB,cNSM;EMnBd;IAcQ,yBN3BY;IM4BZ,cNIM;EMnBd;IAmBQ,yBN9CW;IM+CX,cNDM;EMnBd;IAwBQ,yBNrCY;IMsCZ,cNNM;;AMcd;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,iBAAiB;EACjB,oBAAoB;EACpB,gCN/EY;EMgFZ,mBAAmB;EANvB;IASQ,eAAe;IACf,cN/DY;EMqDpB;IAcQ,SAAS;IACT,iBAAiB;IACjB,eAAe;IACf,kBAAkB;;AAI1B;EAIY,kBAAkB;EAClB,oBAAoB;EACpB,iBAAiB;EAN7B;IASgB,kBAAkB;IAClB,MAAM;IACN,OAAO;IACP,gBAAgB;IAChB,4BAA4B;IAC5B,cNxFI;IMyFJ,eAAe;;AAS/B;AAGA;EACI,iBAAiB;EADrB;IAIQ,eAAe;EAJvB;IAQQ,gBAAgB;;AAIxB;EACI,8BNrIY;;AMwIhB;EACI,mBAAmB;EADvB;IAKQ,qBAAqB;IAL7B;MAQY,wBAAmB;MAAnB,kCAAmB;SAAnB,+BAAmB;cAAnB,mBAAmB;MACnB,2BAA2B;MAC3B,eAAe;MACf,qBAAqB;MACrB,iBAAiB;MACjB,oBAAoB;MAbhC;QAgBgB,cAAc;;AAS9B;AAGA;EACI,aAAa;EACb,wDAAwD;EACxD,gBAAW;EAAX,WAAW;;AAGf;EACI,WAAW;EACX,WAAW;EACX,6BAA6B;;AAGjC;EACI,YAAY;;AAGhB;EACI,yBNnLsB;EMoLtB,YAAY;EACZ,yBAAyB;EACzB,eAAe;EACf,WAAW;EACX,YAAY;EACZ,sBAAsB;;AAG1B;EACI,qBAAqB;;AAGzB;EACI,cAAc;EACd,YAAY;EACZ,8BNvMY;;AM0MhB;EACI,iBAAiB;;AAGrB;EACI,yBN9MY;EM+MZ,qBAAqB;EACrB,WN9OQ;EM+OR,qBAAqB;EACrB,eAAe;EACf,cAAc;EACd,kBAAkB;;AAItB;EACI,kBAAkB;EADtB;IAIQ,kBAAkB;IAClB,yBNhOQ;IMiOR,WNvLG;IMwLH,kBAAkB;IAClB,qBAAqB;IACrB,eAAe;IACf,4BAAoB;IAApB,oBAAoB;IACpB,UAAU;IACV,kBAAkB;IAClB,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,YAAY;IACZ,WAAW;IACX,gBAAgB;EAlBxB;IAsBQ,mBAAmB;IACnB,UAAU;;AAIlB;EACI,WAAW;EACX,gBAAgB;EAChB,iBAAiB;;AAGrB;EACI,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,wBAAkB;MAAlB,qBAAkB;UAAlB,kBAAkB;EAClB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,WAAW;EACX,cAAc;EACd,sBAAsB;;AAG1B;EACI,uBAAuB;;AAG3B;EAEQ,gBAAgB;EAChB,mBAAmB;;AAH3B;EAOQ,aAAa;;AAIrB;AAEA;EACI,kBAAkB;EAClB,aAAa;;AAGjB;EACI,cAAc;;AAGlB;EACI,mBAAmB;;AAGvB;EACI,gBAAgB;EAChB,eAAe;EACf,iBAAiB;EACjB,6BAA6B;EAC7B,SAAS;EACT,eAAe;;AAGnB;EACI,eAAe;EACf,6BAA6B;;AAGjC;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;;AAGjB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,gBAAgB;EAChB,8BAAsB;UAAtB,sBAAsB;;AAG1B;EACI,kBAAkB;;AAGtB;EACI,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;;AAG3B;EACI,eAAe;;AAGnB;EACI,gBAAgB;;AAGpB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAA8B;MAA9B,sBAA8B;UAA9B,8BAA8B;EAC9B,mBAAmB;;AAGvB;EACI,kBAAkB;;AAGtB;EACI,qBAAqB;EACrB,oBAAoB;EACpB,iBAAiB;EACjB,mBAAmB;EACnB,cAAc;;AAGlB;EACI,mBAAmB;;AAGvB;EACI,kBAAkB;;AAGtB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,yBAAyB;EACzB,eAAe;EACf,kBAAkB;EAClB,qBAAqB;EACrB,YAAY;EACZ,cAAc;EACd,WAAW;EACX,iBAAiB;EACjB,iCAAyB;EAAzB,yBAAyB;EAX7B;IAcQ,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,kBAAkB;EAhB1B;IAoBQ,WAAW;IACX,YAAY;IACZ,gBAAgB;;AAIxB;EACI,yBAAyB;;AAG7B;EACI,yBAAyB;;AAG7B;EACI,yBAAyB;;AAG7B;EACI,yBAAyB;;AAG7B;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,eAAe;EACf,eAAe;EACf,6BAA6B;;AAGjC;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;;AAGjB;EACI,cAAc;EACd,WAAW;EACX,YAAY;;AAGhB;EACI,cAAc;EACd,iBAAiB;EACjB,qBAAqB;EACrB,kBAAkB;;AAGtB;EAEI;IACI,aAAa;EAGjB;IACI,mBAAe;QAAf,eAAe;IACf,8BAA8B;EAGlC;IACI,mBAAmB;IACnB,UAAU;IACV,UAAU;EAGd;IACI,kBAAkB;EAGtB;IACI,iBAAiB;EAGrB;IACI,WAAW,IACd;;AAGL;EAEI;IACI,eAAe;IACf,aAAa;EAGjB;IACI,4BAAsB;IAAtB,6BAAsB;QAAtB,0BAAsB;YAAtB,sBAAsB,IACzB;;AAGL;EAEI;IACI,mBAAe;QAAf,eAAe;EAGnB;IACI,UAAU;IACV,mBAAmB,IACtB;;AAGL;EACI,uBAAuB;EACvB,YAAY;EACZ,qBAAqB;;AAGzB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,0BAAqB;MAArB,qBAAqB;EACrB,yBAA8B;MAA9B,sBAA8B;UAA9B,8BAA8B;EAC9B,uBAAuB;EACvB,aAAa;EACb,uBAAuB;EAEvB;IACI,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,0BAAqB;QAArB,qBAAqB;IACrB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;;AAO/B;EACI,kBAAkB;EAClB,kBAAkB;EAClB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,aAAY;EACZ,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,cNjgBY;;AMogBhB;EACI,sBAAsB;EACtB,uBAAsB;MAAtB,oBAAsB;UAAtB,sBAAsB;EACtB,0BAAqB;MAArB,qBAAqB;EACrB,6BNtgBY;EMugBZ,iBAAiB;EAEjB;IACI,kBAAkB;IAClB,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,0BAAqB;QAArB,qBAAqB;IACrB,uBAAsB;QAAtB,oBAAsB;YAAtB,sBAAsB;IACtB,yBN/gBQ;IMghBR,sBAAsB;IANzB;MASO,qBNphBI;IM2gBX;MAaO,qBN7eG;IMgeV;MAiBO,aAAa;MACb,cAAc;MAlBrB;QAwBW,wBAAwB;MAxBnC;QA6BW,cNxiBA;QMyiBA,UAAU;MA9BrB;QA6BW,cNxiBA;QMyiBA,UAAU;MA9BrB;QA6BW,cNxiBA;QMyiBA,UAAU;MA9BrB;QA6BW,cNxiBA;QMyiBA,UAAU;MA9BrB;QA6BW,cNxiBA;QMyiBA,UAAU;EAKtB;IACI,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;IACvB,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,mBAAmB;IACnB,qBAAqB;IACrB,4BAAoB;IAApB,oBAAoB;IACpB,sBAAsB;IAPzB;MAUO,yBNtjBI;MMujBJ,eAAe;IAXtB;MAeO,uBAAuB;MAf9B;QAkBW,eAAe;IAlB1B;MAuBO,2BAA2B;MAvBlC;QA0BW,eAAe;;AAM/B;EAEI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,mBN/kBY;EMglBZ,0BAAqB;MAArB,qBAAqB;EACrB,qBAAqB;EACrB,mBAAmB;EACnB,8BAAmB;EAAnB,6BAAmB;MAAnB,uBAAmB;UAAnB,mBAAmB;EACnB,yBAA8B;MAA9B,sBAA8B;UAA9B,8BAA8B;EAC9B,0BAAsB;MAAtB,sBAAsB;EACtB,iBAAiB;EAEjB;IAZJ;MAcY,mBAAe;UAAf,eAAe,IAClB;EAGL;IACI,uBAAuB;IACvB,sBNzjBG;IM0jBH,uBNhhBS;IMihBT,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,kBAAkB;IAClB,4BAAoB;IAApB,oBAAoB;IACpB,oBAAmB;IACnB,YAAY;IATf;MAYO,yBN5mBI;IMgmBX;MAgBO,YAAY;MACZ,uBAAuB;MACvB,4BAAoB;MAApB,oBAAoB;MACpB,kBAAkB;MAClB,cAAc;MACd,iBAAiB;MArBxB;QAwBW,UAAU;QACV,WAAW;QACX,eAAe;MA1B1B;QA8BW,4BAA4B;QAC5B,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;QACX,kBAAkB;QAClB,qBAAqB;IAnChC;MAwCO,eAAe;EAIvB;IACI,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;IAEvB;MACI,kBAAkB;MAClB,yBAAmB;UAAnB,sBAAmB;cAAnB,mBAAmB;MACnB,wBAAuB;UAAvB,qBAAuB;cAAvB,uBAAuB;MACvB,qBAAqB;MACrB,4BAAoB;MAApB,oBAAoB;MACpB,sBAAsB;MAEtB;QACI,sBAAsB;QACtB,4BAAoB;QAApB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAmB;YAAnB,sBAAmB;gBAAnB,mBAAmB;QACnB,wBAAuB;YAAvB,qBAAuB;gBAAvB,uBAAuB;QACvB,kBAAkB;QAClB,kBAAkB;QAPrB;UAUO,yBNjqBJ;UMkqBI,eAAe;QAGnB;UACI,qBAAqB;UACrB,kBAAkB;UAClB,kBAAkB;UAClB,aAAa;MA1BxB;QA+BO,kBAAkB;MAGtB;QACI,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,uBAAuB;QACvB,gBAAgB;QAChB,gBAAgB;QAChB,WAAW;QACX,yBNxrBA;QMyrBA,sBAAsB;QACtB,YAAY;QACZ,cAAc;QAXjB;UAcO,cAAc;QAdrB;UAkBO,eAAe;UACf,eAAe;UACf,cAAc;UACd,iBAAiB;UArBxB;YAwBW,sBAAsB;YACtB,oBAAa;YAAb,oBAAa;YAAb,aAAa;YACb,yBAAmB;gBAAnB,sBAAmB;oBAAnB,mBAAmB;YACnB,sBAAsB;YA3BjC;cA8Be,cAAc;cACd,eAAe;cACf,uBAAuB;cACvB,yBNltBZ;cMmtBY,kBAAkB;cAClB,iBAAiB;cAnChC;gBAsCmB,eAAe;cAtClC;gBA0CmB,kBAAkB;gBAClB,oBAAa;gBAAb,oBAAa;gBAAb,aAAa;gBACb,wBAAuB;oBAAvB,qBAAuB;wBAAvB,uBAAuB;gBACvB,yBAAmB;oBAAnB,sBAAmB;wBAAnB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,oCAAoC;gBACpC,gBAAgB;gBAChB,mBN5vBb;gBM6vBa,qBN7vBb;gBM8vBa,kBAAkB;gBAClB,WAAW;YAvD9B;cA4De,kBAAkB;cAClB,gBAAgB;cAChB,cNhvBZ;cMivBY,cAAc;cA/D7B;gBAkEmB,eAAe;EAS3C;IACI,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;;AAI/B;AACA;EACI,aAAa;EACb,cAAc;;AAGlB;EACI,gBAAgB;EAChB,qBAAqB;EACrB,mBAAmB;EACnB,gBAAgB;EAChB,mBAAmB;EACnB,cAAc;EACd,yBNvyBc;;AM0yBlB;AACA;EACI,iBAAiB;;AAGrB;EACI,kBAAkB;;AAGtB;EACI,iBAAiB;;AAGrB;EACI,kBAAkB;EADtB;IAIQ,iBAAiB;IACjB,mBAAmB;EAL3B;IASQ,kBAAkB;;AAI1B;EACI,0BAA0B;;AAG9B;EACI,YAAY;;AAGhB;EAEQ,WAAW;;AAInB;EACI,WAAW;EACX,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,gBAAgB;EAJpB;IAOQ,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,4BAAsB;IAAtB,6BAAsB;QAAtB,0BAAsB;YAAtB,sBAAsB;IACtB,uBAA2B;QAA3B,oBAA2B;YAA3B,2BAA2B;IAC3B,eAAe;IAVvB;MAaY,gBAAgB;IAb5B;MAiBY,oEN3wB6D;;AMgxBzE;EAEQ,eAAe;EACf,gBAAgB;EAChB,cAAc;EAJtB;IAOY,cAAc;IAP1B;MAUgB,yENzxB2D;;AM+xB3E;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;;AAG1B;EACI,cAAc;EACd,WAAW;EACX,YAAY;EAHhB;IAKQ,kCAAkC;IAClC,mBAAmB;IACnB,iBAAiB;;AAIzB;EACI,sBAAsB;;AAG1B;EACI,sBAAsB;EACtB,uBAAuB;EACvB,mBAAmB;EACnB,iBAAiB;;AAGrB;EACI,MAAM;EACN,aAAa;;AAGjB;EACI,SAAQ;;AAGZ;EACI,WAAY;EAAZ,cAAY;;AAGhB;EACI,sBAAsB;EACtB,uBAAuB;EACvB,iBAAiB;;AAGrB;EAEQ,cAAc;;AAItB;EAGY,SAAS;EACT,WACJ;;AAIR;EACI,QAAQ;;AAGZ;EACI,QAAQ;;AAGZ;EACI,UAAU;;AAGd;EACI,YAAY;;AAGhB;EAEI,SAAS;;AAGb;EACI,gBAAgB", "sources": ["webpack://SmartDecisions/./src/scss/new/_variables.scss", "webpack://SmartDecisions/./src/scss/_application-variables.scss", "webpack://SmartDecisions/./src/scss/new/_colors.scss", "webpack://SmartDecisions/./src/scss/new/_badges.scss", "webpack://SmartDecisions/./src/scss/new/_utilities.scss", "webpack://SmartDecisions/./src/scss/new/_tables.scss", "webpack://SmartDecisions/./src/scss/application.scss"], "sourcesContent": ["﻿/*Units*/\r\n\r\n$default-unit: 1rem!default;\r\n\r\n/*Colours*/\r\n\r\n$black: #000!default;\r\n\r\n$blue-900: #062239!default;\r\n$blue-800: #0A3150!default;\r\n$blue-700: #043E67!default;\r\n$blue-600: #005C98!default;\r\n$blue-dark: #0073BE!default;\r\n$blue-400: #0391D7!default;\r\n$blue: #00B4E6!default;\r\n$blue-disabled: #B3D5EC;\r\n$blue-alt: #80DEFF !default;\r\n$blue-extra-light: #B7E8FE!default;\r\n$blue-ultra-light: #E7F7FF!default;\r\n\r\n$green-900: #0B2517!default;\r\n$green-800: #0C3722!default;\r\n$green-700: #09442A!default;\r\n$green-dark: #095F3B!default;\r\n$green-500: #007F50!default;\r\n$green-400: #119D63!default;\r\n$green-300: #06C07A!default;\r\n$green: #00DC8C!default;\r\n$green-light: #8AF6BB!default;\r\n$green-50: #D8FEE7!default;\r\n$green-alt: #B3D9CB!default;\r\n\r\n$grey-dark: #1E1E1E;\r\n$grey-1: #3B3B3B!default;\r\n$grey-2: #8A8A8A!default; \r\n$grey-3: #CACACA!default;\r\n$grey-4: #E8E8E8!default;\r\n$grey-5: #F5F5F5!default;\r\n$grey-6: #f1f1f1!default;\r\n$grey-ultra-light: #F5F5F5!default; \r\n\r\n$red-900: #420F03!default;\r\n$red-800: #5D1602!default;\r\n$red-700: #761701!default;\r\n$red-dark: #9E2305!default;\r\n$red: #E22F00!default;\r\n$red-400: #F35439!default;\r\n$red-300: #F8866D!default;\r\n$red-200: #FDAC9A!default;\r\n$red-light: #FCD9D1!default;\r\n$red-50: #FEEFEB!default;\r\n\r\n$yellow-900: #A04223!default;\r\n$yellow-800: #C2551F!default;\r\n$yellow-700: #DD7005!default;\r\n$yellow-dark: #FFA400!default;\r\n$yellow-500: #FBC618!default;\r\n$yellow-400: #FBE12A!default;\r\n$yellow: #FAEB1E!default;\r\n$yellow-200: #FBF175!default;\r\n$yellow-100: #FFF8BA!default;\r\n$yellow-50: #FFFCDB!default;\r\n\r\n$magenta-50: #FFF0F6;\r\n$magenta-100: #FED0E2;\r\n$magenta-200:#FCA2C9;\r\n$magenta-300: #FD6CB0;\r\n$magenta-400: #F93FA1;\r\n$magenta-500: #E6008C;\r\n$magenta-600: #A30463;\r\n$magenta-700: #750748;\r\n$magenta-800: #5D0D3A;\r\n$magenta-900: #43042B;\r\n\r\n\r\n$white:#fff!default;\r\n\r\n$brand: #461E96!default;\r\n$brand-darker: #211359 !default;\r\n$completed: $blue-dark;\r\n$quality: $blue-dark;\r\n$reg: $brand;\r\n$safety: $red-700;\r\n$stats: $yellow-800;\r\n$sub-brand: $brand!default;\r\n\r\n\r\n$neutral: $grey-3!default;\r\n\r\n$success: $green-500;\r\n$warning: $yellow-500;\r\n$error: $red;\r\n$active: $green-400;\r\n$active-alt: $yellow-800;\r\n$active-alt-secondary: $red-dark;\r\n$text: #1E1E1E!default;\r\n\r\n/*Fonts*/\r\n\r\n$cencora-regular: 'cencora-gilroy', Arial, Helvetica, Verdana, sans-serif;\r\n$cencora-bold: 'cencora-gilroy-bold', Arial, Helvetica, Verdana, sans-serif;\r\n$main-font: Arial, Helvetica, Verdana, sans-serif;\r\n\r\n$h1-font-size: 1.875rem!default;\r\n$h2-font-size: 1.5rem!default;\r\n$h3-font-size: 1rem!default;\r\n$h4-font-size: .8rem!default;\r\n$paragraph-font-size: .85rem!default;\r\n$table-header-font-size: .85rem!default;\r\n$table-cell-font-size: .85rem!default;\r\n\r\n/*Space*/\r\n\r\n$margin-unit: .5rem!default;\r\n$padding-unit: .5rem!default;\r\n\r\n$radius-small: 0.25rem;\r\n$radius: 0.375rem;\r\n$radius-large: 0.5rem;\r\n\r\n$space-XXL: 3rem;\r\n$space-XL: 2rem;\r\n$space-L: 1.5rem;\r\n$space-M: 1rem;\r\n$space-S: 0.75rem;\r\n$space-XS: 0.5rem;\r\n$space-XXS: 0.25rem;\r\n$space-XXXS: 0.125rem;\r\n", "﻿/*This will apply the quality color to the brand variable, globally (within the context of this application).*/\r\n$sub-brand: $reg !global;\r\n\r\n\r\n$medicines: $green-dark;\r\n$medical-devices: $magenta-700;\r\n$cosmetics: $magenta-200;\r\n$biocides: $green-400;\r\n$nutrition: $yellow-700;\r\n\r\n$brand-25: $blue-extra-light;\r\n$medicines-25: $blue-ultra-light;\r\n$medical-devices-25: $magenta-50;\r\n$cosmetics-25: $magenta-50;\r\n$biocides-25: $blue-ultra-light;\r\n$nutrition-25: $red-light;\r\n\r\n$dragdrophoverspacer: $grey-ultra-light;\r\n", "﻿.m-icon-group i.confirm-color {\r\n    color: $success;\r\n}\r\n\r\n.m-icon-group i.info-color, .m-icon-group i.number-color {\r\n    color: $blue-dark;\r\n}\r\n\r\n.m-icon-group i.failed-color {\r\n    color: $error;\r\n}", "﻿/* Badges */\r\n\r\n.badges {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n\r\n    .badge {\r\n        border-radius: 5px;\r\n        background-color: $grey-1;\r\n        color: $white;\r\n        font-size: 12px;\r\n        text-align: center;\r\n        margin-right: .5rem;\r\n        padding: 5px 8px;\r\n        font-weight: 400;\r\n\r\n        &.inactive {\r\n            background-color: $grey-1;\r\n            color: $grey-3;\r\n        }\r\n\r\n        &.medium {\r\n            font-size: .85rem;\r\n        }\r\n\r\n        &.large {\r\n            font-size: 1rem;\r\n            padding: 8px 12px;\r\n        }\r\n    }\r\n}\r\n", "﻿/*Height/Width*/\r\n@for $unit from 0 through 10 {\r\n    .width-#{$unit* 10} {\r\n        width: #{$unit * 10}% !important;\r\n    }\r\n}\r\n\r\n@for $unit from 0 through 10 {\r\n    .height-#{$unit * 10} {\r\n        height: #{$unit * 10}% !important;\r\n    }\r\n}\r\n\r\n.sticky {\r\n    z-index: 80;\r\n\r\n    &.top {\r\n        top: -1rem;\r\n    }\r\n\r\n    &.bottom {\r\n        bottom: -1rem;\r\n    }\r\n}", "﻿@import 'variables';\r\n\r\ntable {\r\n    border-collapse: separate;\r\n\r\n    thead {\r\n        background: white;\r\n    }\r\n\r\n    th, td {\r\n        font-size: .75rem;        \r\n    }\r\n\r\n    tbody tr:nth-child(odd) {\r\n        background: $grey-5;\r\n    }\r\n\r\n    &.bordered {\r\n        border: 1px solid $grey-2;\r\n\r\n        th, td {\r\n            border-collapse: collapse;\r\n            border: 1px solid $grey-2;\r\n        }\r\n    }\r\n\r\n    th.actions {\r\n\r\n        & > div {\r\n            i.m-icon {\r\n                &.clear {\r\n                    &.active {\r\n\t\t\t\t\t\tfont-weight: inherit;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.table-header .sorter:before, .table-header .sorter.sorting_asc:before {\r\n    transform: none;\r\n}\r\n", "﻿@media screen and (max-width: 1780px) {\r\n    html, body {\r\n        font-size: 90%;\r\n    }\r\n}\r\n\r\n@media screen and (max-width: 1500px) {\r\n    html, body {\r\n        font-size: 82%;\r\n    }\r\n}\r\n\r\n@media screen and (max-width: 1350px) {\r\n    html, body {\r\n        font-size: 74%;\r\n    }\r\n}\r\n\r\n\r\n.core-container {\r\n    min-width: 80vw !important;\r\n    max-width: 100vw !important;\r\n}\r\n\r\n/*Drag/Drop*/\r\n.draggable-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: $grey-5;\r\n    color: $white;\r\n    padding: .75rem;\r\n    border-radius: 0.75rem;\r\n    font-size: .85rem;\r\n    line-height: 1.25rem;\r\n    margin-bottom: 1rem;\r\n\r\n    &:first-of-type {\r\n        margin-top: 1.5rem;\r\n    }\r\n    /*Default */\r\n    &.category-default {\r\n        background-color: $brand;\r\n    }\r\n    /*Medicines*/\r\n    &.category-medicines {\r\n        background-color: $medicines;\r\n    }\r\n    /*Biocides */\r\n    &.category-biocides {\r\n        background-color: $biocides !important;\r\n    }\r\n    /*Cosmetics */\r\n    &.category-cosmetics {\r\n        background-color: $cosmetics;\r\n    }\r\n    /*Nutrition */\r\n    &.category-nutrition {\r\n        background-color: $nutrition;\r\n    }\r\n    /*Medical devices */\r\n    &.category-med-devices {\r\n        background-color: $medical-devices;\r\n    }\r\n\r\n    h5 {\r\n        margin-bottom: 0;\r\n    }\r\n\r\n    &.target-entered {\r\n        background: $grey-3 !important;\r\n        color: $text;\r\n        outline: 2px $text dotted;\r\n        transition: all .3s;\r\n    }\r\n}\r\n\r\n.drag-drop-list {\r\n    position: relative;\r\n    /*Medicine*/\r\n    .category-medicines {\r\n        background-color: $medicines-25;\r\n        color: $text;\r\n    }\r\n    /*Biocides*/\r\n    .category-biocides {\r\n        background-color: $biocides-25;\r\n        color: $text;\r\n    }\r\n    /*Cosmetics*/\r\n    .category-cosmetics {\r\n        background-color: $cosmetics-25;\r\n        color: $text;\r\n    }\r\n    /*Nutrition*/\r\n    .category-nutrition {\r\n        background-color: $nutrition-25;\r\n        color: $text;\r\n    }\r\n    /*Devices*/\r\n    .category-med-devices {\r\n        background-color: $medical-devices-25;\r\n        color: $text;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n.import-summary {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 1.5rem;\r\n    padding-bottom: 1rem;\r\n    border-bottom: 1px solid $grey-3;\r\n    margin-bottom: 1rem;\r\n\r\n    i {\r\n        font-size: 2rem;\r\n        color: $warning;\r\n    }\r\n\r\n    p {\r\n        margin: 0;\r\n        padding-bottom: 0;\r\n        font-size: 1rem;\r\n        padding-left: 1rem;\r\n    }\r\n}\r\n\r\n.import-errors {\r\n\r\n    ul {\r\n        li {\r\n            position: relative;\r\n            padding-left: 1.5rem;\r\n            font-size: .85rem;\r\n\r\n            &:before {\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                content: '\\e002';\r\n                font-family: 'MaterialIcons';\r\n                color: $warning;\r\n                font-size: 1rem;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n/*Topics*/\r\n\r\n\r\n.topic-column {\r\n    padding: 0 1.5rem;\r\n\r\n    &:first-of-type {\r\n        padding-left: 0;\r\n    }\r\n\r\n    &:last-of-type {\r\n        padding-right: 0;\r\n    }\r\n}\r\n\r\n.topic-sidebar {\r\n    border-left: 1px solid $grey-5;\r\n}\r\n\r\n.topic-value-group {\r\n    margin-bottom: 2rem;\r\n\r\n\r\n    ul {\r\n        list-style-type: disc;\r\n\r\n        li {\r\n            break-inside: avoid;\r\n            margin: .5rem 0 .75rem 1rem;\r\n            padding-left: 0;\r\n            list-style-type: disc;\r\n            font-size: .85rem;\r\n            line-height: 1.25rem;\r\n\r\n            strong {\r\n                display: block;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n/*Jim 16 nov*/\r\n\r\n\r\n.search-option {\r\n    display: grid;\r\n    grid-template-columns: calc(10% - 2rem) calc(90% - 2rem);\r\n    gap: 0 2rem;\r\n}\r\n\r\n.dragdropspacer {\r\n    width: 100%;\r\n    height: 5px;\r\n    background-color: transparent;\r\n}\r\n\r\n.dragdropspacermeds {\r\n    height: 15px;\r\n}\r\n\r\n.dragdropspacerhover {\r\n    background-color: $dragdrophoverspacer;\r\n    cursor: move;\r\n    margin: .50rem 0 .50rem 0;\r\n    padding: .25rem;\r\n    width: 100%;\r\n    height: 10px;\r\n    border-radius: 0.75rem;\r\n}\r\n\r\n.draggable-group {\r\n    margin-bottom: 0.5rem;\r\n}\r\n\r\n.vertical-line {\r\n    margin: 0 1rem;\r\n    height: 100%;\r\n    border-left: 1px solid $grey-3;\r\n}\r\n\r\n.group-section-container {\r\n    min-height: 30rem;\r\n}\r\n\r\n.chip {\r\n    background-color: $grey-4;\r\n    border-radius: 0.8rem;\r\n    color: $black;\r\n    display: inline-block;\r\n    padding: 0.5rem;\r\n    margin: 0.2rem;\r\n    font-size: 0.85rem;\r\n}\r\n\r\n\r\n.hero-banner-tooltip {\r\n    position: relative;\r\n\r\n    & .hero-banner-tooltiptext {\r\n        visibility: hidden;\r\n        background-color: $grey-1;\r\n        color: $white;\r\n        text-align: center;\r\n        border-radius: 0.5rem;\r\n        padding: 0.5rem;\r\n        transition: all 0.4s;\r\n        opacity: 0;\r\n        font-size: 0.85rem;\r\n        position: absolute;\r\n        top: -3.7rem;\r\n        left: 0;\r\n        width: 60rem;\r\n        z-index: 20;\r\n        font-weight: 200;\r\n    }\r\n\r\n    &:hover .hero-banner-tooltiptext {\r\n        visibility: visible;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.hero-banner-textarea {\r\n    width: 100%;\r\n    resize: vertical;\r\n    max-height: 15rem;\r\n}\r\n\r\n.loader {\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: start;\r\n    justify-content: center;\r\n    opacity: .8;\r\n    z-index: 10000;\r\n    background-color: #fff;\r\n}\r\n\r\n.add-view-display {\r\n    width: 23rem !important;\r\n}\r\n\r\n.view-topic-dialog {\r\n    .dialog-content {\r\n        padding-top: 0px;\r\n        padding-bottom: 0px;\r\n    }\r\n\r\n    #dialog-closer {\r\n        display: none;\r\n    }\r\n}\r\n\r\n/* News Search Article*/\r\n\r\n#news-wall {\r\n    font-size: 0.85rem;\r\n    padding: 50px;\r\n}\r\n\r\n#news-wall a {\r\n    color: #333333;\r\n}\r\n\r\n#news-wall article {\r\n    padding: 0 0 10px 0;\r\n}\r\n\r\n#news-wall article h1 {\r\n    font-weight: 400;\r\n    font-size: 24px;\r\n    line-height: 29px;\r\n    border-top: 1px solid #E5E5E5;\r\n    margin: 0;\r\n    padding: 10px 0;\r\n}\r\n\r\n#news-wall article section {\r\n    padding: 15px 0;\r\n    border-top: 1px solid #E5E5E5;\r\n}\r\n\r\n#news-wall article section.meta {\r\n    display: flex;\r\n}\r\n\r\n#news-wall article section.meta div {\r\n    display: flex;\r\n    padding: 0 30px;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n}\r\n\r\n#news-wall article section.meta div strong {\r\n    padding: 0 5px 0 0;\r\n}\r\n\r\n#news-wall article section.meta div span {\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n#news-wall article section.meta div:first-of-type {\r\n    padding-left: 0;\r\n}\r\n\r\n#news-wall article section.meta div:last-of-type {\r\n    padding-right: 0;\r\n}\r\n\r\n#news-wall article section#pills {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: 5px;\r\n}\r\n\r\n#news-wall article section#pills div {\r\n    position: relative;\r\n}\r\n\r\n#news-wall #pills span {\r\n    display: inline-block;\r\n    margin: 0 5px 10px 0;\r\n    padding: 5px 12px;\r\n    border-radius: 12px;\r\n    color: #FFFFFF;\r\n}\r\n\r\n#news-wall #pills .legend .legend-help:hover + .legend-container {\r\n    visibility: visible;\r\n}\r\n\r\n#news-wall #pills .legend .legend-container {\r\n    visibility: hidden;\r\n}\r\n\r\n#news-wall #pills .legend-container .legend-container-items {\r\n    display: flex;\r\n    flex-direction: column;\r\n    border: 1px solid #E5E5E5;\r\n    padding: 0.5rem;\r\n    position: absolute;\r\n    border-radius: 0.4rem;\r\n    top: -1.8rem;\r\n    left: -10.5rem;\r\n    z-index: 10;\r\n    background: white;\r\n    transition: all 0.2s ease;\r\n\r\n    & div {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 0.75rem;\r\n    }\r\n\r\n    & div span {\r\n        width: 20px;\r\n        height: 20px;\r\n        border-radius: 0;\r\n    }\r\n}\r\n\r\n#news-wall span.purple {\r\n    background-color: #4A1671;\r\n}\r\n\r\n#news-wall span.gray {\r\n    background-color: #557595;\r\n}\r\n\r\n#news-wall span.orange {\r\n    background-color: #DF8043;\r\n}\r\n\r\n#news-wall span.red {\r\n    background-color: #B10000;\r\n}\r\n\r\n#news-wall footer {\r\n    display: flex;\r\n    font-size: 12px;\r\n    padding: 35px 0;\r\n    border-top: 1px solid #E5E5E5;\r\n}\r\n\r\n#news-wall footer div {\r\n    display: flex;\r\n}\r\n\r\n#news-wall footer span {\r\n    display: block;\r\n    width: 20px;\r\n    height: 20px;\r\n}\r\n\r\n#news-wall footer i {\r\n    display: block;\r\n    line-height: 20px;\r\n    padding: 0 13px 0 8px;\r\n    font-style: normal;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n\r\n    #news-wall {\r\n        padding: 30px;\r\n    }\r\n\r\n    #news-wall article section.meta {\r\n        flex-wrap: wrap;\r\n        padding-bottom: 5px !important;\r\n    }\r\n\r\n    #news-wall article section.meta div {\r\n        margin-bottom: 10px;\r\n        padding: 0;\r\n        width: 50%;\r\n    }\r\n\r\n    #news-wall article section.meta div:nth-child(odd) {\r\n        padding-right: 5px;\r\n    }\r\n\r\n    #news-wall article section.meta div:nth-child(even) {\r\n        padding-left: 5px;\r\n    }\r\n\r\n    #news-wall article section.meta div.source {\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n\r\n    #news-wall {\r\n        font-size: 12px;\r\n        padding: 20px;\r\n    }\r\n\r\n    #news-wall article section.meta div {\r\n        flex-direction: column;\r\n    }\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n\r\n    #news-wall footer {\r\n        flex-wrap: wrap;\r\n    }\r\n\r\n    #news-wall footer div {\r\n        width: 50%;\r\n        margin-bottom: 10px;\r\n    }\r\n}\r\n\r\ninput[type=search] {\r\n    background-color: white;\r\n    border: none;\r\n    border-radius: 0.5rem;\r\n}\r\n\r\n.search-download-articles {\r\n    display: flex;\r\n    align-content: center;\r\n    justify-content: space-between;\r\n    background-color: white;\r\n    padding: 1rem;\r\n    margin-bottom: -1.25rem;\r\n\r\n    &-selected {\r\n        display: flex;\r\n        align-content: center;\r\n        justify-content: center;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.search-icon {\r\n    position: relative;\r\n    font-size: 1.25rem;\r\n    display: flex;\r\n    left:0.25rem;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: $grey-2;\r\n}\r\n\r\n.search-content {\r\n    margin-bottom: 0.75rem;\r\n    justify-content: start;\r\n    align-content: center;\r\n    border-top: 1px solid $grey-4;\r\n    padding-top: 1rem;\r\n\r\n    &__input {\r\n        position: relative;\r\n        display: flex;\r\n        align-content: center;\r\n        justify-content: start;\r\n        border: 1px solid $grey-3;\r\n        border-radius: 0.55rem;\r\n\r\n        &:hover {\r\n            border-color: $grey-2;\r\n        }\r\n    \r\n        &:has(.search-content-input:focus), &:has(.search-content-input:active) {\r\n            border-color: $brand;;\r\n        }\r\n\r\n        & input {\r\n            outline: none;\r\n            width: 53.5rem;\r\n\r\n            &::-webkit-search-decoration,\r\n            &::-webkit-search-cancel-button,\r\n            &::-webkit-search-results-button,\r\n            &::-webkit-search-results-decoration {\r\n                -webkit-appearance: none;\r\n            }\r\n\r\n\r\n            &::placeholder {\r\n                color: $grey-2;\r\n                opacity: 1;\r\n            }\r\n        }\r\n    }\r\n\r\n    &__only-title {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin-left: 0.5rem;\r\n        border-radius: 0.5rem;\r\n        transition: all 0.2s;\r\n        padding: 0.3rem 0.5rem;\r\n\r\n        &:hover {\r\n            background-color: $grey-4;\r\n            cursor: pointer;\r\n        }\r\n\r\n        input[type=checkbox] {\r\n            background-color: white;\r\n\r\n            &:hover {\r\n                cursor: pointer;\r\n            }\r\n        }\r\n\r\n        label {\r\n            margin-bottom: 0 !important;\r\n\r\n            &:hover {\r\n                cursor: pointer;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.search-filters {\r\n\r\n    display: flex;\r\n    background: $grey-4;\r\n    align-content: center;\r\n    border-radius: 0.5rem;\r\n    margin-bottom: 1rem;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    flex-direction: column;\r\n    padding: 0.2rem 0;\r\n\r\n    @media only screen and (max-width: 1640px) {\r\n        & {\r\n            flex-wrap: wrap;\r\n        }\r\n    }\r\n\r\n    &__date-range {\r\n        background-color: white;\r\n        border: 1px solid $white;\r\n        border-radius: $radius;\r\n        display: flex;\r\n        align-items: center;\r\n        position: relative;\r\n        transition: all 0.2s;\r\n        margin-left:0.75rem;\r\n        height: 2rem;\r\n\r\n        &:hover {\r\n            border: 1px solid $grey-2;\r\n        }\r\n\r\n        & input[type=date] {\r\n            border: none;\r\n            background-color: white;\r\n            transition: all 0.2s;\r\n            position: relative;\r\n            padding-top: 0;\r\n            padding-bottom: 0;\r\n\r\n            &::-webkit-calendar-picker-indicator {\r\n                opacity: 0;\r\n                z-index: 10;\r\n                cursor: pointer;\r\n            }\r\n\r\n            &:after {\r\n                font-family: 'MaterialIcons';\r\n                content: '\\e916';\r\n                position: relative;\r\n                left: -1rem;\r\n                font-size: 1.25rem;\r\n                margin-bottom: 0.1rem;\r\n            }\r\n        }\r\n\r\n        & span {\r\n            font-size: 2rem;\r\n        }\r\n    }\r\n\r\n    &__fields {\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        &-field {\r\n            position: relative;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 0.5rem;\r\n            transition: all 0.2s;\r\n            padding: 0.3rem 0.5rem;\r\n\r\n            &-container {\r\n                padding: 0.3rem 0.5rem;\r\n                transition: all 0.2s;\r\n                border-radius: 0.5rem;\r\n                align-items: center;\r\n                justify-content: center;\r\n                position: relative;\r\n                font-size: 0.85rem;\r\n\r\n                &:hover {\r\n                    background-color: $grey-3;\r\n                    cursor: pointer;\r\n                }\r\n\r\n                &-selected {\r\n                    display: inline-block;\r\n                    min-width: 1.25rem;\r\n                    position: relative;\r\n                    left: 0.25rem;\r\n                }\r\n            }\r\n\r\n            & .m-icon {\r\n                font-size: 1.75rem;\r\n            }\r\n\r\n            &-options {\r\n                position: absolute;\r\n                top: 2.5rem;\r\n                left: -1.5rem;\r\n                background-color: white;\r\n                min-height: 4rem;\r\n                min-width: 15rem;\r\n                z-index: 50;\r\n                border: 2px solid $grey-4;\r\n                border-radius: 0.75rem;\r\n                cursor: auto;\r\n                display: block;\r\n\r\n                &.active {\r\n                    display: block;\r\n                }\r\n\r\n                & ul {\r\n                    min-width: 5rem;\r\n                    padding: 0.5rem;\r\n                    overflow: auto;\r\n                    max-height: 25rem;\r\n\r\n                    li {\r\n                        padding: 0.2rem 0.2rem;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        margin-bottom: 0.25rem;\r\n\r\n                        input[type=checkbox] {\r\n                            width: 1.05rem;\r\n                            height: 1.05rem;\r\n                            background-color: white;\r\n                            border: 1px solid $grey-3;\r\n                            position: relative;\r\n                            font-size: 0.7rem;\r\n\r\n                            &:hover {\r\n                                cursor: pointer;\r\n                            }\r\n\r\n                            &:checked:before {\r\n                                position: absolute;\r\n                                display: flex;\r\n                                justify-content: center;\r\n                                align-items: center;\r\n                                top: -0;\r\n                                left: 0;\r\n                                width: 1rem;\r\n                                height: 1rem;\r\n                                font-family: 'MaterialIconsOutlined';\r\n                                content: 'check';\r\n                                background: $blue-dark;\r\n                                border-color: $blue-dark;\r\n                                border-radius: 3px;\r\n                                color: #fff;\r\n                            }\r\n                        }\r\n\r\n                        label {\r\n                            font-size: 0.75rem;\r\n                            margin-bottom: 0;\r\n                            color: $grey-2;\r\n                            padding-top: 0;\r\n\r\n                            &:hover {\r\n                                cursor: pointer;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &-buttons {\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n}\r\n\r\n/*Table*/\r\n.filter-table-image {\r\n    width: 1.5rem;\r\n    height: 1.5rem;\r\n}\r\n\r\n.tag-pill {\r\n    font-size: .85em;\r\n    display: inline-block;\r\n    margin: 0 5px 5px 0;\r\n    padding: 3px 7px;\r\n    border-radius: 12px;\r\n    color: #FFFFFF;\r\n    background-color: $blue-600;\r\n}\r\n\r\n/* Newsletter resends */\r\n#searchFailures a, button {\r\n    margin-left: 10px;\r\n}\r\n\r\n#searchFailures > div > div.flex.justify-end.flex-align-end {\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.newsletter-article-page-container {\r\n    max-height: 55rem;\r\n}\r\n\r\n.newsletter-article-item {\r\n    font-size: 0.85rem;\r\n\r\n    & .highlight-title {\r\n        font-weight: bold;\r\n        padding-top: 0.2rem;\r\n    }\r\n\r\n    & a {\r\n        word-break: normal;\r\n    }\r\n}\r\n\r\n.m-icon-medium {\r\n    font-size: 2rem !important;\r\n}\r\n\r\n.app-title {\r\n    width: 150px;\r\n}\r\n\r\n.login-box {\r\n    a {\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n.login-box .app-title {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-bottom: 0;\r\n\r\n    .dual-title {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: flex-start;\r\n        font-size: 2rem;\r\n\r\n        span {\r\n            text-align: left;\r\n        }\r\n\r\n        .app-title-regular {\r\n            font-family: $cencora-regular;\r\n        }\r\n    }\r\n}\r\n\r\n.app-title {\r\n    h1 {\r\n        font-size: 21px;\r\n        font-weight: 500;\r\n        line-height: 1;\r\n\r\n        span {\r\n            display: block;\r\n\r\n            &.app-title-bold {\r\n                font-family: $cencora-bold;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.app-title-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\ni.icon-zoom-out {\r\n    display: block;\r\n    width: 25px;\r\n    height: 25px;\r\n    &:before {\r\n        font-family: MaterialIconsOutlined;\r\n        content: \"zoom_out\";\r\n        font-size: 1.5rem;\r\n    }\r\n}\r\n\r\n.page-wrapper {\r\n    padding: 1rem 1.875rem;\r\n}\r\n\r\n.sub-header {\r\n    margin-left: -1.875rem;\r\n    margin-right: -1.875rem;\r\n    margin-bottom: 1rem;\r\n    margin-top: -1rem;\r\n}\r\n\r\n.sub-header.sticky {\r\n    top: 0;\r\n    z-index: 9999;\r\n}\r\n\r\nfooter.sticky{\r\n    bottom:0;\r\n}\r\n\r\n.buttons {\r\n    width: unset;\r\n}\r\n\r\n.home-wrapper {\r\n    margin-left: -1.875rem;\r\n    margin-right: -1.875rem;\r\n    margin-top: -1rem;\r\n}\r\n\r\nselect {\r\n    &.inline {\r\n        height: 41.6px;\r\n    }\r\n}\r\n\r\n.custom-select {\r\n    &.card {\r\n        &:before {\r\n            top: 22px;\r\n            right: 22px\r\n        }\r\n    }\r\n}\r\n\r\n.nav-menu .m-icon {\r\n    top: 3px;\r\n}\r\n\r\n.account-navigation .nav-menu .m-icon {\r\n    top: 1px;\r\n}\r\n\r\n.custom-select:before {\r\n    z-index: 1;\r\n}\r\n\r\n.core-container>header.page-header {\r\n    height: 50px;\r\n}\r\n\r\n.nav-menu > li.full-menu .flyout\r\n{\r\n    top: 50px;\r\n}\r\n\r\n.terms {\r\n    margin-top: 10px;\r\n}\r\n"], "names": [], "sourceRoot": ""}