﻿@model List<ContentItemModel>
@using PharmaLex.SmartDecisions.Entities
@{
    ViewData["Title"] = "Index";
    var contentTypes = String.Join("\",\"", Model.Select(x => x.ContentTypeName).Distinct());
    bool expired = (bool)this.ViewData["expired"];
}

<div id="content-items">
    <div class="sub-header">
        <h2>@(expired ? "Expired" : "My") Records</h2>
        <div class="controls">
            <a v-if="expired" class="button" href="/records">All my records</a>
        </div>
    </div>

        <filtered-table id="content-items"
                        :items="items"
                        :pager-location="'topbottom'"
                        :columns="columns"
                        :filters="filters"
                        :no-records-message="'@($"No records found")'"
                        :link="link"></filtered-table>
    

</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#content-items',
            data: function () {
                return {
                    link: '/data/edit/',
                    items: @Html.Raw(Model.ToJson()),
                    expired: @expired.ToString().ToLower(),
                    filters: [
                        {
                            "key": "name",
                            "dataKey": "name",
                            "options": [],
                            "type": "search",
                            "header": "Search Name",
                            "display": "Name",
                            "filterCollection": "name"
                        },
                        {
                            "key": "contentTypeName",
                            "dataKey": "contentTypeName",
                            "options": @Html.Raw($"[\"{contentTypes}\"]"),
                            "type": "select",
                            "header": "Filter by Content Type",
                            "display": "contentTypeName",
                            "filterCollection": "contentTypeName"
                        }
                    ],
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                "name": "Name",
                                "sortDirection": 1,
                                "type": "text",
                                "dataKey": "name",
                                "sortKey": "name",
                                style: 'width: 60%;'
                            },
                            {
                                "name": "Content Type",
                                "sortDirection": 1,
                                "type": "text",
                                "dataKey": "contentTypeName",
                                "sortKey": "contentTypeName",
                                "header": "Content Type",
                                style: 'width: 20%;'
                            },
                            {
                                "name": "Last verified",
                                "sortDirection": 1,
                                "type": "html",
                                "dataKey": "lastVerified",
                                "sortKey": "lastVerified",
                                "header": "Last verified",
                                style: 'width: 20%;'
                            }
                        ]
                    }
                };
            },
            methods: {
            },
            computed: {
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}
