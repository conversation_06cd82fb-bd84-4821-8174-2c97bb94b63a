﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class AzureBlobAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public string Uri { get; set; }
        public string ContentType { get; set; }
        public int? Length { get; set; }
        public string Container { get; set; }
    }
}
