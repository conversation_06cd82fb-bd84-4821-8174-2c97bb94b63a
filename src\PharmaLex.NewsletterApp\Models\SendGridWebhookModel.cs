﻿using AutoMapper;
using Newtonsoft.Json;
using PharmaLex.SmartDecisions.Entities.Entities;
using PharmaLex.SmartDecisions.Entities.Enums;
using System;

namespace PharmaLex.NewsletterApp
{
    public class SendGridWebhook
    {
        public SendGridWebhookModel[] Logs { get; set; }
    }

    public class SendGridWebhookModel
    {
        public string Email { get; set; }
        public long Timestamp { get; set; } = 0;
        public string Event { get; set; }
        [JsonProperty("sg_template_id")]
        public string TemplateId { get; set; }
        public string Reason { get; set; }
        public string Response { get; set; }
    }

    public class SendGridWebhookModelMappingProfile : Profile
    {
        public SendGridWebhookModelMappingProfile()
        {
            this.CreateMap<SendGridWebhookModel, NewsletterActivity>()
                .ForMember(d => d.EventType, s => s.MapFrom(src => (NewsletterActivityEventType)Enum.Parse(typeof(NewsletterActivityEventType), src.Event, true)))
                .ForMember(d => d.ReceivedUTCDate, s => s.MapFrom(src => DateTimeOffset.FromUnixTimeSeconds(src.Timestamp).UtcDateTime))
                .ForMember(d => d.Id, s => s.Ignore());
        }
    }
}
