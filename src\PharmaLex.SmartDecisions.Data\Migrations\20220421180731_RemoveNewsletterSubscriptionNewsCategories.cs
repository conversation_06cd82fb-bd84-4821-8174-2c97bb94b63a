﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class RemoveNewsletterSubscriptionNewsCategories : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_NewsletterSubscriptionNewsCategory_NewsCategory",
                table: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_NewsletterSubscriptionNewsCategory_NewsletterSubscription",
                table: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.DropTable(
                name: "NewsletterSubscriptionNewsCategory_Audit",
                schema: "Audit");

            migrationBuilder.DropIndex(
                name: "IX_NewsletterSubscriptionNewsCategory_NewsCategoryId",
                table: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.DropIndex(
                name: "UC_NewsletterSubscriptionNewsCategory",
                table: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.DropColumn(
                name: "NewsCategoryId",
                table: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.DropColumn(
                name: "NewsletterSubscriptionId",
                table: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.AlterColumn<DateTime>(
                name: "LastUpdatedDate",
                table: "NewsletterSubscriptionNewsCategory",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime");

            migrationBuilder.AlterColumn<string>(
                name: "LastUpdatedBy",
                table: "NewsletterSubscriptionNewsCategory",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "NewsletterSubscriptionNewsCategory",
                type: "datetime2",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime");

            migrationBuilder.AlterColumn<string>(
                name: "CreatedBy",
                table: "NewsletterSubscriptionNewsCategory",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "LastUpdatedDate",
                table: "NewsletterSubscriptionNewsCategory",
                type: "datetime",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<string>(
                name: "LastUpdatedBy",
                table: "NewsletterSubscriptionNewsCategory",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "NewsletterSubscriptionNewsCategory",
                type: "datetime",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<string>(
                name: "CreatedBy",
                table: "NewsletterSubscriptionNewsCategory",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NewsCategoryId",
                table: "NewsletterSubscriptionNewsCategory",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NewsletterSubscriptionId",
                table: "NewsletterSubscriptionNewsCategory",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "NewsletterSubscriptionNewsCategory_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: false),
                    NewsletterSubscriptionId = table.Column<int>(type: "int", nullable: false),
                    Order = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterSubscriptionNewsCategory_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NewsletterSubscriptionNewsCategory_NewsCategoryId",
                table: "NewsletterSubscriptionNewsCategory",
                column: "NewsCategoryId");

            migrationBuilder.CreateIndex(
                name: "UC_NewsletterSubscriptionNewsCategory",
                table: "NewsletterSubscriptionNewsCategory",
                columns: new[] { "NewsletterSubscriptionId", "NewsCategoryId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_NewsletterSubscriptionNewsCategory_NewsCategory",
                table: "NewsletterSubscriptionNewsCategory",
                column: "NewsCategoryId",
                principalTable: "NewsCategory",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_NewsletterSubscriptionNewsCategory_NewsletterSubscription",
                table: "NewsletterSubscriptionNewsCategory",
                column: "NewsletterSubscriptionId",
                principalTable: "NewsletterSubscription",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
