﻿using Newtonsoft.Json;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class CompanyUserCreationEmail
    {
        [JsonProperty("fullName")]
        public string FullName { get; set; }

        [JsonProperty("inviterName")]
        public string InviterName { get; set; }

        [<PERSON>sonProperty("inviterEmail")]
        public string InviterEmail { get; set; }

        [JsonProperty("company")]
        public string Company { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("loginUrl")]
        public string LoginUrl { get; set; }

        [JsonProperty("expiryDays")]
        public string ExpiryDays { get; set; }
    }
}
