﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Helpers.Constants;
using PharmaLex.SmartDecisions.Web.Models;
using PharmaLex.SmartDecisions.Web.Models.AzureSearch;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers;

[Authorize(policy: "NewsSubscriber")]
public class SearchNewsArticleController(
    ISearchService searchService,
    IRepositoryFactory repoFactory,
    IDecisionsBlobContainer storage,
    IConfiguration configuration)
    : BaseController
{
    private readonly int newsletterKeyLength = configuration.GetValue<int>("AppSettings:NewsletterUrlKeyLength");
    private readonly int newsletterKeyLengthLegacy = configuration.GetValue<int>("AppSettings:NewsletterUrlKeyLengthLegacy");
    private readonly int newsletterArticleKeyMinLength = configuration.GetValue<int>("AppSettings:NewsletterArticleKeyMinLength");
    private readonly int newsletterArticleUrlKeySeparatorIndex = configuration.GetValue<int>("AppSettings:NewsletterArticleUrlKeySeparatorIndex");
    private readonly int newsletterArticleUrlKeySeparatorIndexLegacy =
        configuration.GetValue<int>("AppSettings:NewsletterArticleUrlKeySeparatorIndexLegacy");

    [HttpGet("/articles/search")]
    public async Task<IActionResult> Search()
    {
        var search = await searchService.Search(new NewsArticleSearchModel());
        return this.View(search);
    }

    [HttpPost("/articles/search"), ValidateAntiForgeryToken]
    public async Task<IActionResult> Search(NewsArticleSearchModel model)
    {
        var search = await searchService.Search(model);

        return this.View(search);
    }

    [HttpGet("/article/{article}")]
    public async Task<IActionResult> Open(string article)
    {
        if (article?.IndexOf('-') != newsletterArticleUrlKeySeparatorIndex &&
            article?.IndexOf('-') != newsletterArticleUrlKeySeparatorIndexLegacy)
        {
            return NotFound();
        }

        var search = await searchService.FindArticle(article);

        return View(new NewsArticleFullReadModel()
        {
            NewsArticle = search
        });
    }

    [HttpGet("/article/{newsletter}/{article}"), AllowAnonymous]
    public async Task<IActionResult> Open(string newsletter, string article)
    {
        if ((newsletter?.Length != newsletterKeyLength && newsletter?.Length != newsletterKeyLengthLegacy) ||
            article?.Length < newsletterArticleKeyMinLength ||
            (article?.IndexOf('-') != newsletterArticleUrlKeySeparatorIndex &&
             article?.IndexOf('-') != newsletterArticleUrlKeySeparatorIndexLegacy))
        {
            return NotFound();
        }

        var model = await searchService.FindNewsletterArticle(newsletter, article);
        return View(model);
    }

    [HttpGet("/article/download/{articleUrl}")]
    public async Task<IActionResult> Download(string articleUrl)
    {
        if ((articleUrl?.IndexOf('-') != newsletterArticleUrlKeySeparatorIndex &&
             articleUrl?.IndexOf('-') != newsletterArticleUrlKeySeparatorIndexLegacy) ||
            articleUrl?.Length < newsletterArticleKeyMinLength)
        {
            return NotFound();
        }

        var article = repoFactory.Create<NewsArticleContent>().Configure(o => o
            .Include(x => x.AzureBlob)
            .Include(x => x.NewsArticle)
            .ThenInclude(x => x.NewsArticleCategory)
            .Include(x => x.NewsArticle)
            .ThenInclude(x => x.NewsSource)
            .Include(x => x.Locale)
        ).FirstOrDefault(x =>
            x.FriendlyUrl == articleUrl.ToLower()
            && x.AzureBlobId.HasValue);

        HttpResponseMessage response = null;

        if(article.AzureBlob.Name.LastIndexOf(".pdf") > 0)
        {
            var pdfBlobClient = await storage.GetBlobClientAsync(article.AzureBlob.Name);
            using var ms = new MemoryStream();
            await pdfBlobClient.DownloadToAsync(ms);
            var byteArray = ms.ToArray();

            return File(byteArray, "application/pdf", fileDownloadName: $"{article.Title}.pdf");
        }

        response = await searchService.GetArticleHtmlAsync(article);

        var responseContent = await response.Content.ReadAsByteArrayAsync();

        return File(responseContent, "application/pdf");
    }

    [HttpPost("article/download/articles")]
    public async Task<IActionResult> DownloadArticles([FromBody] NewArticleDownloadModel newsArticlesModel)
    {
        var response =
            await searchService.GetArticlesZipArchiveAsync(newsArticlesModel.ArticleUrls
                .Take(SearchConstants.DownloadLimit).ToList());

        return File(response, "application/zip");
    }
}