﻿<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
	<meta name="color-scheme" content="only">
	<title>Newsletter</title>
	<style>
		/*reset*/
		html,
		body {
			margin: 0 auto !important;
			padding: 0 !important;
			height: 100% !important;
			width: 100% !important;
		}

		* {
			-ms-text-size-adjust: 100%;
		}

		table,
		td {
			mso-table-lspace: 0pt !important;
			mso-table-rspace: 0pt !important;
		}

		img {
			-ms-interpolation-mode: bicubic;
		}

		a, a:hover, a:visited, a:active {
			text-decoration: none;
			color: inherit;
		}
		/*end reset*/

		body {
			background: white;
			font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>;
			font-size: 15px;
		}

		table {
			font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>;
		}

		.header a {
			color: white;
		}

		h1 {
			color: #557595;
			font-size: 40px;
			margin: 0;
		}

			h1 span {
				font-weight: normal;
			}

		td {
			vertical-align: top;
		}

		/*colours*/
		.medicines {
			color: #006068 !important;
		}

		.medicines-bg {
			background: #006068 !important;
			color: white !important;
		}

		.devices {
			color: #632340 !important;
		}

		.devices-bg {
			background: #632340 !important;
			color: white !important;
		}

		.cosmetics {
			color: #ddb5c8 !important;
		}

		.cosmetics-bg {
			background: #ddb5c8 !important;
			color: white !important;
		}

		.biocides {
			color: #338f40 !important;
		}

		.biocides-bg {
			background: #338f40 !important;
			color: white !important;
		}

		.nutrition {
			color: #ed7a31 !important;
		}

		.nutrition-bg {
			background: #ed7a31 !important;
			color: white !important;
		}

		/*END colours*/

		.category-summary {
			text-align: center;
			padding: 15px 0;
			line-height: 22px;
			font-size: 18px;
			text-transform: uppercase;
		}

		.section-header {
			font-weight: bold;
			font-size: 18px;
			text-transform: uppercase;
			background: #557595;
		}

		.date {
			display: inline-block;
			color: #727d84;
			margin: 0;
		}

		.product-categories {
			color: #555;
		}

		.article-categories {
			color: #727d84;
		}

		.headline-article .image-cell {
			width: 150px;
			padding-right: 25px;
		}

		.headline-article .flag-cell {
			width: 40px;
			padding-left: 40px;
		}

		.headline-article h2 {
			font-size: 25px;
			color: #557595;
		}

		.category-group {
			margin: 20px 0;
		}

		.category-article {
			padding: 15px 15px 10px;
			background white;
		}

			.category-article h2 {
				font-size: 26px;
				color: #557595;
			}

		.category-articles .category-article tr td {
			padding-bottom: 15px;
			border-bottom: 1px solid #e5e5e5;
		}

		.category-articles:last-of-type .category-article td {
			border-bottom: none;
			padding-bottom: 0;
		}

		p.impact-assessment-summary {
			font-size: 18px;
		}

		.category-group .flag-cell {
			width: 40px;
			padding: 30px 5px 0 20px;
		}

		.footer {
			margin-top: 30px;
			padding-top: 20px;
			border-top: 1px dotted #e5e5e5;
		}

			.footer img {
				padding-right: 10px
			}
	</style>
</head>
<body style="background:white">
	<table class="wrapper"
		   cellspacing="0"
		   cellpadding="15"
		   border="0"
		   role="presentation">
		<tr>
			<td>
				<!--header-->
				<table width="100%"
					   cellspacing="0"
					   cellpadding="0"
					   border="0"
					   class="header"
					   role="presentation"
					   style="background:#557595;color:white;border-collapse:collapse;">
					<thead>
						<tr>
							<!--Need language version here-->
							<td style="padding: 15px"><a href="{{editPreferencesLink}}">Don’t miss anything and update your preferences</a></td>
							<td style="padding: 15px; text-align: right">
							</td>
						</tr>
					</thead>
				</table>

				<table width="100%" cellspacing="0" cellpadding="15" border="0" role="presentation">
					<tr>
						<td style="vertical-align:middle">
							<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/logo.png" style="max-width:300px" alt="Knowledge Accelerated" />
						<td style="text-align: right;vertical-align:middle">
							<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/pharmalex-logo.png"
								 alt="PharmaLex" />
						</td>
					</tr>
					<tr>
						<td>
							<b class="date">{{date}}</b>
						</td>
					</tr>
				</table>

				<!--sub header-->
				<table width="100%"
					   cellspacing="0"
					   cellpadding="0"
					   border="0"
					   class="category-summary"
					   role="presentation">
					<tr>
						<td>
							{{#if headlineArticles}}
							<a href="#{{headlineArticles.productGroupClassName}}">
								{{headlineArticles.productGroupName}} <span> | </span>
							</a>
							{{/if}}
							{{#each productGroupArticles}}
							<a href="#{{this.productGroupClassName}}"
							   style="color:{{this.productGroupHexColour}}">
								{{this.productGroupName}}
							</a>
							{{#notEquals true this.isLastItem }}
							<span> | </span>
							{{/notEquals}}
							{{/each}}
						</td>
					</tr>
				</table>

				{{#if headlineArticles}}
				<a name="{{headlineArticles.productGroupClassName}}">
				</a>
				<table width="100%"
					   cellspacing="0"
					   cellpadding="0"
					   border="0"
					   role="presentation"
					   style="padding-bottom:15px">
					<tr>
						<td class="section-header" style="background:{{headlineArticles.productGroupHexColour}};color:white;padding:17px 15px">
							<h2 style="margin:0;font-size:18px">
								{{headlineArticles.productGroupName}}
							</h2>
						</td>
					</tr>

					<tr>
						<td colspan="2">
							<table width="100%"
								   cellspacing="0"
								   cellpadding="0"
								   role="presentation">
								<tr>
									<td>
										{{#each headlineArticles.articles}}
										<table width="100%"
											   cellspacing="0"
											   cellpadding="15"
											   class="headline-article"
											   role="presentation"
											   style="background:#e2e8ee">
											<tr>
												<td class="image-cell" style="width: 140px;background:white;vertical-align:middle">
													<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/NewsSources/{{this.newsSourceClassName}}.png" style="width: 140px;max-width: 140px;height:auto;" alt={{this.newsSource}} />
												</td>
												<td class="text-cell width-100">
													<span class="date">{{this.date}}</span>
													<a href="{{this.publicUrl}}" target="_blank" style="text-decoration:none">
														<h2 style="margin:10px 0;"> {{this.title}}</h2>
													</a>
													{{#if this.description}}
													<p class="description">{{this.description}}</p>
													{{/if}}

													{{#if this.impactAssessmentSummary}}
													<p>{{this.impactAssessmentSummary}}</p>
													{{/if}}

													<p class="product-categories" style="padding-bottom:20px">
														{{#if this.products}}
														{{#each this.products}}
														<span>{{this.name}}</span>
														{{#notEquals true this.isLastItem }}
														<span> / </span>
														{{/notEquals}}
														{{/each}}
														{{/if}}

														{{#if this.themes}}
														<span> / </span>
														{{#each this.themes}}
														<span>{{this.name}}</span>
														{{#notEquals true this.isLastItem }}
														<span> / </span>
														{{/notEquals}}
														{{/each}}
														{{/if}}

														{{#if this.typeOfText}}
														<span>{{this.typeOfText.name}}</span>
														{{/if}}
													</p>
												</td>
												<td class="flag-cell">
													<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/flags/iso/flat/64/{{this.geographicalScope.countryCode}}.png" alt="{{this.geographicalScope.name}}" />
												</td>
											</tr>
										</table>
										{{/each}}
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				{{/if}}

				{{#if productGroupArticles}}
				{{#each productGroupArticles}}

				<!--PRODUCT ARTICLES-->
				<table width="100%"
					   cellspacing="0"
					   cellpadding="0"
					   border="0"
					   class="category-group"
					   style="margin-bottom:30px">
					<tr>
						<td class="section-header" style="background:{{this.productGroupHexColour}};">
							<table width="100%"
								   cellspacing="0"
								   cellpadding="0"
								   border="0">
								<tr>
									<td style="width:32px;background:{{this.productGroupHexColour}};color:white;padding:10px 15px;vertical-align:middle;text-align:left;">
										<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/{{this.productGroupClassName}}-icon.png" style="width:32px;height:32px" alt="{{this.productGroupName}}" />
									</td>

									<td class="section-header" style="width:100%;background:{{this.productGroupHexColour}};color:white;vertical-align:middle;text-align:left;">
										<span style="margin:0;font-size:18px">
											<a name="{{this.productGroupClassName}}">
											</a>
											{{this.productGroupName}}
										</span>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td colspan="2">
							{{#each this.articles}}
							<table width="100%"
								   cellspacing="0"
								   cellpadding="0"
								   border="0"
								   class="category-articles"
								   role="presentation" style="margin-bottom:20px">
								<tr>
									<td class="width-100">
										<table width="100%"
											   cellspacing="0"
											   cellpadding="0"
											   border="0"
											   class="category-article"
											   role="presentation">
											<tr>
												<td>
													<p class="date">{{date}}</p>
													<a href="{{this.publicUrl}}" target="_blank">
														<h2 style="margin:10px 0"> {{this.title}}</h2>
													</a>

													{{#if this.description}}
													<p class="description">{{this.description}}</p>
													{{/if}}

													{{#if this.impactAssessmentSummary}}
													<p>{{this.impactAssessmentSummary}}</p>
													{{/if}}


													<p class="product-categories">
														{{#if this.products}}
														{{#each this.products}}
														{{this.name}}
														{{#notEquals true this.isLastItem }}
														<span> / </span>
														{{/notEquals}}
														{{/each}}
														{{/if}}

														{{#if this.themes}}
														<span> / </span>
														{{#each this.themes}}
														{{this.name}}
														{{#notEquals true this.isLastItem }}
														<span> / </span>
														{{/notEquals}}
														{{/each}}

														{{#if this.typeOfText}}
														{{this.typeOfText.name}}
														{{/if}}
														{{/if}}
													</p>


												</td>
												<td class="flag-cell">
													<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/flags/iso/flat/64/{{this.geographicalScope.countryCode}}.png" alt="{{this.geographicalScope.name}}" />
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
							{{/each}}
						</td>
					</tr>
				</table>

				<!--END  PRODUCT ARTICLES -->
				{{/each}}
				{{/if}}

				<!--BEGIN footer-->
				<table width="100%"
					   cellspacing="0"
					   cellpadding="0"
					   border="0"
					   class="footer"
					   role="presentation"
					   style="padding-top:30px">
					<tr>
						<td>
							<table cellspacing="0"
								   cellpadding="0"
								   border="0">

								<tr>
									<td style="padding-left:15px">
										<a href="https://emea01.safelinks.protection.outlook.com/?url=https%3A%2F%2Ftwitter.com%2Fpharmalexglobal%3Flang%3Dde&amp;data=04%7C01%7C%7Cf9b1a595953b4e077c7408d8c9ae2b11%7C84df9e7fe9f640afb435aaaaaaaaaaaa%7C1%7C0%7C637481097981113052%7CUnknown%7CTWFpbGZsb3d8eyJWIjoiMC4wLjAwMDAiLCJQIjoiV2luMzIiLCJBTiI6Ik1haWwiLCJXVCI6Mn0%3D%7C1000&amp;sdata=21glJ%2Bteg736T%2FC7HnGAx2lhcRFjk5yjd4CczlUaWzo%3D&amp;reserved=0"
										   target="_blank"
										   rel="noopener noreferrer"
										   data-auth="Verified"
										   title="Protected by Outlook: https://twitter.com/pharmalexglobal?lang=de. Click or tap to follow the link."
										   data-linkindex="1">
											<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/twitter.png"
												 border="0" />
										</a>
									</td>
									<td style="padding-left:10px">
										<a href="https://emea01.safelinks.protection.outlook.com/?url=https%3A%2F%2Fwww.linkedin.com%2Fcompany%2Fpharmalexglobal%2F%3ForiginalSubdomain%3Dde&amp;data=04%7C01%7C%7Cf9b1a595953b4e077c7408d8c9ae2b11%7C84df9e7fe9f640afb435aaaaaaaaaaaa%7C1%7C0%7C637481097981123026%7CUnknown%7CTWFpbGZsb3d8eyJWIjoiMC4wLjAwMDAiLCJQIjoiV2luMzIiLCJBTiI6Ik1haWwiLCJXVCI6Mn0%3D%7C1000&amp;sdata=bbk6G8WgQyoOe0kSqhHDUH%2FEe5GU%2BcqFKykFBCzfyVg%3D&amp;reserved=0"
										   target="_blank"
										   rel="noopener noreferrer"
										   data-auth="Verified"
										   title="Protected by Outlook: https://www.linkedin.com/company/pharmalexglobal/?originalSubdomain=de. Click or tap to follow the link."
										   data-linkindex="2">
											<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/linkedin.png"
												 border="0" />
										</a>
									</td>
									<td style="padding-left:10px">
										<a href="https://emea01.safelinks.protection.outlook.com/?url=https%3A%2F%2Fgo.pharmalex.com%2Fl%2F484291%2F2019-08-07%2F24wpss&amp;data=04%7C01%7C%7Cf9b1a595953b4e077c7408d8c9ae2b11%7C84df9e7fe9f640afb435aaaaaaaaaaaa%7C1%7C0%7C637481097981132981%7CUnknown%7CTWFpbGZsb3d8eyJWIjoiMC4wLjAwMDAiLCJQIjoiV2luMzIiLCJBTiI6Ik1haWwiLCJXVCI6Mn0%3D%7C1000&amp;sdata=LBz6U4NlHEfJoWJvXAphKyC9WJMCUvEEEClO804HqP8%3D&amp;reserved=0"
										   target="_blank"
										   rel="noopener noreferrer"
										   data-auth="Verified"
										   originalsrc="https://go.pharmalex.com/l/484291/2019-08-07/24wpss"
										   shash="DlB45kCBe6gqvt+Hg1Q2m2PAqSWVZNaTIMrlwYX2WDJzAGKSXHULXiYAk0D68gbyFxlHmEc/NvlBxY+0JYlG4sH95oqd+x1LDOC4+J439hsZT+P+iHYNNrJSfet5hMHb4k2NQduiH3EIc+NFkNQxbO/FZJ/SRs9Nz4pg4zOWKlY="
										   title="Protected by Outlook: https://go.pharmalex.com/l/484291/2019-08-07/24wpss. Click or tap to follow the link."
										   data-linkindex="4">
											<img src="https://plxstaticcontent.blob.core.windows.net/$web/$images/SmartNEWS/Newsletter/envelope.png"
												 border="0" />
										</a>
									</td>
								</tr>
							</table>

						</td>
					</tr>
					<tr>
						<td>
							<table cellspacing="0"
								   cellpadding="15"
								   border="0">
								<tr>
									<td>
										<p>
											You will find all of this information on the Knowledge Accelerated database accessible via this <a href="#" style="font-weight:bold;text-decoration:underline">link</a>.
										</p>
									</td>
								</tr>
							</table>

						</td>
					</tr>
				</table>
				<!--END footer-->
			</td>
		</tr>
	</table>
</body>
</html>
