﻿DECLARE @parentId int
SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Type of text')

INSERT INTO [dbo].[NewsCategory] SELECT N'Form', @parentId, 4, 69, N'form', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Report', @parentId, 4, 70, N'report', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Public consultation', @parentId, 4, 71, N'public-consultation', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].form', 'Form - template', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].form', 'Formulaire – modèle', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].report', 'Report', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].report', 'Rapport', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].public-consultation', 'Public consultation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].public-consultation', 'Consultation publique', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

