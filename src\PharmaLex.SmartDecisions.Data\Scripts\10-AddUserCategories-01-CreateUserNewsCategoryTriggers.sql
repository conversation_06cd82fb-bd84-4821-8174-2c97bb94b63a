﻿DROP TRIGGER IF EXISTS [UserNewsCategory_Insert]
DROP TRIGGER IF EXISTS [UserNewsCategory_Update]
DROP TRIGGER IF EXISTS [UserNewsCategory_Delete]
GO
 
CREATE TRIGGER [dbo].[UserNewsCategory_Insert] ON [dbo].[UserNewsCategory]
FOR INSERT AS
INSERT INTO [Audit].[UserNewsCategory_Audit]
(AuditAction,[UserId], [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'I', [UserId], [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO
 
CREATE TRIGGER [dbo].[UserNewsCategory_Update] ON [dbo].[UserNewsCategory]
FOR UPDATE AS
INSERT INTO [Audit].[NewsletterUserNewsCategory_Audit]
(AuditAction, [UserId], [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'U', [UserId], [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO
 
CREATE TRIGGER [dbo].[UserNewsCategory_Delete] ON [dbo].[UserNewsCategory]
FOR DELETE AS
INSERT INTO [Audit].[UserNewsCategory_Audit]
(AuditAction, [UserId], [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'D', [UserId], [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO
 
