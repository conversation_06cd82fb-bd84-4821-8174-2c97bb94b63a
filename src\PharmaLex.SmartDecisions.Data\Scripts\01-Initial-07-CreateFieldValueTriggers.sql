﻿--alter table [dbo].[FieldValue]
--with check add constraint [FK_FieldValue_ContentItem] foreign key([ContentItemId]) 
--references [dbo].[ContentItem] ([Id]) on delete cascade
--go

--alter table [dbo].[FieldValue]
--with check add constraint [FK_FieldValue_Field] foreign key([FieldId]) 
--references [dbo].[Field] ([Id])
--go

create trigger [dbo].[FieldValue_Insert] on [dbo].[FieldValue]
for insert as
insert into [Audit].[FieldValue_Audit]
select 'I', [Id], [Name], [ContentItemId], [FieldId], [Value], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[FieldValue_Update] on [dbo].[FieldValue]
for update as
insert into [Audit].[FieldValue_Audit]
select 'U', [Id], [Name], [ContentItemId], [FieldId], [Value], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[FieldValue_Delete] on [dbo].[FieldValue]
for delete as
insert into [Audit].[FieldValue_Audit]
select 'D', [Id], [Name], [ContentItemId], [FieldId], [Value], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
