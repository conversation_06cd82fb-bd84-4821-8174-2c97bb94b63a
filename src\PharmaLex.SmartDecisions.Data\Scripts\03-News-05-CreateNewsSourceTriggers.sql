﻿create trigger [dbo].[NewsSource_Insert] on [dbo].[NewsSource]
for insert as
insert into [Audit].[NewsSource_Audit]
select 'I'
      ,[Id]
      ,[Name]
      ,[Url]
      ,[SortOrder]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsSource_Update] on [dbo].[NewsSource]
for update as
insert into [Audit].[NewsSource_Audit]
select 'U'
      ,[Id]
      ,[Name]
      ,[Url]
      ,[SortOrder]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsSource_Delete] on [dbo].[NewsSource]
for delete as
insert into [Audit].[NewsSource_Audit]
select 'D'
      ,[Id]
      ,[Name]
      ,[Url]
      ,[SortOrder]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
