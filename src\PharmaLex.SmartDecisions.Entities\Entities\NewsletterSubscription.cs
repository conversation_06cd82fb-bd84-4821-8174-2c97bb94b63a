﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsletterSubscription : EntityBase
    {
        public NewsletterSubscription()
        {
            Newsletter = new HashSet<Newsletter>();
        }

        public int Id { get; set; }
        public int UserId { get; set; }
        public string Timezone { get; set; }
        public int TimezoneOffset { get; set; }
        public int DeliveryLocalDay { get; set; }
        public int DeliveryUtcDay { get; set; }
        public int DeliveryLocalHour { get; set; }
        public int DeliveryUtcHour { get; set; }
        public DateTime CreatedDateUtc { get; set; }
        public bool IsMonthly { get; set; }
        public bool IsInfoflash { get; set; }
        public bool Active { get; set; }

        public User User { get; set; }

        public virtual ICollection<Newsletter> Newsletter { get; set; }
    }
}
