﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;

namespace PharmaLex.NewsletterApp
{
    public class SubscriptionModel
    {
        public int Id { get; set; }
        public bool IsInfoflash { get; set; }
        public bool IsMonthly { get; set; }
        public DateTime CreatedDateUtc { get; set; }
        public UserModel User { get; set; }
    }

    public class SubscriptionMappingProfile : Profile
    {
        public SubscriptionMappingProfile()
        {
            this.CreateMap<NewsletterSubscription, SubscriptionModel>();
        }
    }
}
