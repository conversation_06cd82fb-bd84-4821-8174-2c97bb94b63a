﻿insert into [dbo].[MultilingualResource] select 1, 'newsletter-publication-date', 'Newsletter Publication Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'newsletter-publication-date', 'Date de publication de la newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).search-newsletter-date', 'Search in Newsletter Pub. Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).search-newsletter-date', 'Recherche par date de publication de la newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


update [dbo].[MultilingualResource]
Set Content = 'Search in Source Pub. Date',
CreatedDate = getdate(),
CreatedBy = '<EMAIL>',
LastUpdatedDate = getdate(),
LastUpdatedBy = '<EMAIL>'
where [Key] = '(news).search-publication-date' and [LocaleId] = 1

update [dbo].[MultilingualResource]
Set Content = 'Recherche par date de publication source',
CreatedDate = getdate(),
CreatedBy = '<EMAIL>',
LastUpdatedDate = getdate(),
LastUpdatedBy = '<EMAIL>'
where [Key] = '(news).search-publication-date' and [LocaleId] = 2

