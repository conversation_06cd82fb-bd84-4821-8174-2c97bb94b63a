﻿:root {
    --main-theme-color: #009aa8;
    --background-image: url("https://phcgvcdn-endpoint.azureedge.net/smartdecisions/dev/images/background/Smartphlex_background_green.png");
}
.page-footer { justify-content: space-around; }
.login-content {
    display: inline-flex;
    justify-content: space-between;
    align-items: stretch;
    background: none;
    opacity: 1;
    padding: 0;
}
.login-provider {
    width: calc(50% - 1px);
    background-color: rgba(255, 255, 255, 0.9);
    padding: 2rem;
}

.dialog-custom-content {
    display: flex;
    flex-direction:column;
    align-items: stretch;
}
.dialog-custom-content:first-child {
    width: 100%;
}
.dialog-custom-content:nth-child(2) {
    align-items:flex-end;
}
.email-edit {
    display: flex;
    align-items: center;
    font-size: .9rem;
    font-family: ProximaNovaRegular,Verdana,sans-serif;
}

.email-edit > div {
    display: flex;
}

.email-edit :nth-child(2) {
    margin: 0 10px;
}

.email-edit :nth-child(3) {
    width: 15px;
}

.search-wrapper {
    background-color: #c7d8e5;
    position: relative;
}

    .search-wrapper:after {
        content: '\25bc';
        font-size: 0.7rem;
        position: absolute;
        right: 3px;
        top: 3px;
        width: 25px;
        height: 20px;
        color: #233c4c;
        background-color: #c7d8e5;
        border-left: 1px solid #515c65;
        padding: 6px 8px;
        pointer-events: none;
        cursor: pointer;
    }

    .search-wrapper .clear {
        position: relative;
        display: flex;
        direction: rtl;
        height: 0;
    }

    .search-wrapper i.cross {
        position: relative;
        right: 35px;
        top: 7px;
        cursor: pointer;
    }

.manage-nav-item {
    width: 120px;
}

.data-nav-item {
    width: 90px;
}
/*Forms*/
.form-col-no-header {
    padding-top: 0;
}

.form-col-header-only {
    padding-bottom: 0;
}

.form-col {
    margin-top: 0.5rem;
}

    .form-col:only-child {
        margin-top: 0;
    }

.icon-button-tick:before {
    content: '\2714';
}

.switch-display-only {
    margin: 0.4rem 0;
}

.content-type-display-json {
    height: 600px;
}

.import-items-drop-zone {
    width: 600px;
}

    .import-items-drop-zone .uppy-DragDrop-container {
        padding: 150px 56px 0 56px;
    }

.import-progress {
    display: flex;
    height: 400px;
    width: 600px;
    align-items: center;
    justify-content: center;
}

.import-results {
    height: 400px;
    width: 600px;
    padding: 1rem;
    overflow-y: auto;
}

.toolbox {
    position: absolute;
    left: 50px;
    top: 110px;
    width: 250px;
    background-color: rgba(255, 255, 255, 0.85);
    border: 2px solid #009aa8;
    z-index: 900;
    padding: 1rem 0.5rem 0.5rem 0.5rem;
}

.toolbox-item {
    text-align: left;
    opacity: 1;
    margin-top: 16px;
    overflow: hidden;
    width: 100%;
    position: relative;
    display: flex;
    flex: 0 1 auto;
}
/*.toolbox {
    position: absolute;
    left: 50px;
    top: 110px;
    min-width: 250px;
    background-color: #444;
    z-index: 900;
    padding: 2rem;
    opacity: .4;
}
.toolbox:hover {
    opacity: 1;
}
.toolbox .select-wrapper {
    background-color: #fff;
    height: 3.5rem;
    padding: 0;
}
.toolbox .select-wrapper:after {
    background-color: #fff;
    border-left: none;
    font-size: 1.2rem;
    height: 3.5rem;
    width: 2rem;
    padding: 1.3rem 0 0 0;
}

.toolbox select {
    font-size: 1.2rem;
    padding: 0 2rem 0 0.5rem;
    height: 50px;
}*/

.view-topic-dialog {
    overflow: visible;
    padding: 0;
}

    .view-topic-dialog .dialog-closer {
        top: 1rem;
        right: 0.5rem;
        font-size: 1.5rem;
    }

.dialog-button {
    margin-right: 5px;
}

.dialog-button-edit:before {
    font-family: 'smartphlex';
    content: '\e806';
    font-size: 1.2rem;
    color: #515c65;
    transform: scale(1.5);
    text-decoration: none;
}

.dialog-button-edit:hover {
    text-decoration: none;
}

.view-topic-dialog .dialog-content {
    height: 100%;
}

.view-topic-dialog .dialog-loader {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-topic-dialog header {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    height: 3.5rem;
    border-bottom: 1px solid #999;
    font-weight: bold;
    font-size: 1.8rem;
    color: #233c4c;
}

    .view-topic-dialog header img {
        margin-right: 0.5rem;
    }

.view-topic-dialog .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(100% - 30px);
}

.view-topic-dialog .title {
    display: flex;
    align-items: center;
}

.view-topic-dialog p, ul {
    font-size: 0.9rem;
}

.view-topic-dialog main {
    padding: 0 0.5rem;
    display: flex;
    height: calc(85vh - 5.5rem);
    overflow-y: auto;
    overflow-x: hidden;
    background: linear-gradient(#999, #999) no-repeat 78.5% / 1px 100%;
}

@media (max-width: 1310px) {
    .view-topic-dialog main {
        background-position: top 0 right 235px;
    }
}

.topic-content {
    padding-top: 1rem;
    height: 100%;
    width: 80%;
}

.topic-hero-text {
    margin: 0 4rem 1rem 4rem;
    padding: 1rem 2rem;
    line-height: 2rem;
    background-color: #72b2e5;
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
}

.topic-values {
    display: flex;
}

    .topic-values .form-col {
        padding: 0 1rem;
        overflow-x: hidden;
    }

    .topic-values h4 {
        color: #233c4c;
        margin-top: 0;
    }

    .topic-values p, .topic-values ul {
        margin-bottom: 0.5rem;
    }

.topic-value, .topic-value-group {
    margin-bottom: 1rem;
}

.topic-value-label {
    font-weight: bold;
    color: #233c4c;
    font-size: 1rem;
}

.topic-properties {
    height: 100%;
    width: 20%;
    min-width: 215px;
    padding-top: 1rem;
    font-size: 0.9rem;
}

    .topic-properties > h3, .topic-properties > p {
        text-align: center;
    }

    .topic-properties .topic-value {
        margin: 1rem;
    }

.topic-cycle-button {
    position: absolute;
    top: calc(50% - 2rem);
    background-color: #000;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 2rem;
    font-weight: bold;
    opacity: 0.1;
    cursor: pointer;
}

    .topic-cycle-button:hover {
        background-color: #009aa8;
        opacity: 1;
    }

.topic-cycle-button-forward {
    right: 2rem;
}

.topic-cycle-button-back {
    left: 2rem;
}

.view-topic-dialog footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 2rem;
    display: flex;
    align-items: center;
    padding: 0 0.5rem;
    border-color: #999;
}

/** News*/

.page-footer > div {
    display: flex;
    align-content: center;
    align-items: center;
}

    .page-footer > div:first-child {
        margin-left: 50px;
    }

    .page-footer > div:last-child {
        margin-right: 50px;
    }

    .page-footer > div > * {
        margin-right: 50px;
    }

.form-col .switch-block {
    display: flex;
    justify-content: space-between;
    margin: .5rem 0;
}

.switch-block :first-child {
    display: flex;
    align-items: center;
    font-weight: 600;
}

/*Tree view*/
.treeview {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 5px;
    flex: 1;
}

.node {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-top: 5px;
}

    .node .node-children {
        margin-left: 20px;
        flex-basis: 100%;
        /*padding-left: 6.5px;*/
    }

.node-row {
    display: flex;
    flex-wrap: nowrap;
    align-items: baseline;
    line-height: 1.5;
}

    .node-row > * {
        padding: 3px;
    }

    .node-row label {
        margin: 0;
    }


/* Preferences */
.preferences-list h6 {
    margin-top: 20px;
}

#newsletterSubscription .vue-dialog-container {
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    padding: 30px;
}

.select-preferences {
    display: flex;
    justify-content: space-between;
}

    .select-preferences.div {
        width: 30%;
        height: 20%;
    }

.select-preferences-treeview {
    height: 400px;
    width: 100%;
}

.checkbox-list-item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    -webkit-column-break-inside: avoid;
    border-top: solid 1px #e5e5e5;
    height: 2.0rem;
}

.location-dialog-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    width: 700px;
    height: 500px;
    max-width: 90%;
    max-height: 90%;
    z-index: 10001;
    padding: 15px;
    text-align: left;
    overflow-y: auto;
    overflow-x: hidden;
    border: none;
}

.product-dialog-container, .theme-dialog-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    width: 900px;
    /*height: 600px;*/
    max-width: 90%;
    max-height: 90%;
    z-index: 10001;
    padding: 15px;
    text-align: left;
    overflow-y: auto;
    overflow-x: hidden;
    border: none;
}

.form-container {
    position: relative;
    display: flex;
    flex: 1 0 auto;
    flex-wrap: wrap;
    margin: 1rem 0;
}

.preferences-days h5:first-child{
    margin-top: 10px;
}

#newsletterSubscription .form-col-third:nth-child(2) h5, #newsletterSubscription .form-col-third:nth-child(3) h5,
#newsletterSubscription .form-col-third:nth-child(2) .preferences-none-selected, #newsletterSubscription .form-col-third:nth-child(3) .preferences-none-selected {
    margin-left: 10px;
}

#newsletterSubscription > div.form-container > div:nth-child(1) > div.manage-header {
    margin-top: 50px;
}

.preferences-none-selected {
    margin-top: 10px;
}

.category-red {
    color: white;
    background-color: #FF6666;
}

.category-purple {
    color: #C1B2CC;
    background-color: #38125A;
}

.category-pink {
   /* color: #FFFFFF;*/
    background-color: #FF88FF;
}

.category-orange {
    color: #FFFFFF;
    background-color: #DF8043;
}

.category-blue {
    color: #FFFFFF;
    background-color: #3EADE3;
}

/*------- drag drop -------*/
.drag-drop-tile {
    background-color: #e5e7eb;
    border-radius: .375rem;
}

.preference-tile {
    padding: .55rem;
    margin: .70rem;
    border-radius: .375rem;
    width: 100%;
    cursor: move;
}

.preference-tile-embedded {
    padding: .55rem;
    margin: .70rem;
    border-radius: .375rem;
    cursor: move;
}

ul.drag-drop-list {
    list-style-type: none;
    text-align: left;
    margin-left: 0;
    list-style-image: none;
}

ul.location-list {
    list-style-type: none;
    text-align: left;
    margin-left: 0;
    margin-top: 20px;
    list-style-image: none;
    font-weight: 600;
}


.drag-drop-nested-header {
    list-style-type: none;
    cursor: move;
    text-align: left;
    padding: .25rem;
    margin: .50rem 0 .50rem .25rem;
}

.drag-drop-nested-title {
    background-color: #e5e7eb;
    border-radius: .375rem;
    padding: .25rem;
    width: 100%;
}

/*------- search articles -------*/
.search-result-no-article {
    margin-top: 50px;
}

#search-impact-assessment .buttons {
    border-top: none;
}

#search-impact-assessment .manage-container {
    height: 700px
}

.impact-assessment-header {
    display: flex;
    justify-content: space-between
}

#filter-articles .table-row:hover {
    cursor: pointer;
    background-color: #ebeeee;
}

.filter-table-image {
    width: 25px; 
    height: 25px;
}

/*------- edit / new -------*/
.headline-miscellaneous {
    height: 50px;
    display: flex;
    align-items: center;
}

    .headline-miscellaneous label {
        margin: 0;
    }

    .headline-miscellaneous input {
        margin-right: 10px;
    }



