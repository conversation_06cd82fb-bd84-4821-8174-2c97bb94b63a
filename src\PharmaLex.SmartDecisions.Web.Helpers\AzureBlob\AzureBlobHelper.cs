﻿using Azure.Storage.Blobs.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface IAzureBlobHelper
    {
        Task<string> GetBlobText(string blobName);
    }

    public class AzureBlobHelper : IAzureBlobHelper
    {
        private readonly IDecisionsBlobContainer blobContainer;

        public AzureBlobHelper(IDecisionsBlobContainer blobContainer)
        {
            this.blobContainer = blobContainer;
        }

        public async Task<string> GetBlobText(string blobName)
        {
            var blobText = "";

            using (Stream stream = await(await this.blobContainer.GetBlobClientAsync(blobName)).OpenReadAsync(new BlobOpenReadOptions(false)))
            using (StreamReader reader = new StreamReader(stream))
            {
                blobText = await reader.ReadToEndAsync();
            }

            return blobText;
        }
    }
}
