create table #statements(Id int identity(1,1), [Sql] nvarchar(max));
insert into #statements select 'declare @ctid int'

declare @ctid int

declare ct_cursor cursor for select [Id] from [ContentType] order by [ContentTypeCategoryId]
open ct_cursor
fetch next from ct_cursor into @ctid
while @@fetch_status = 0 begin
	insert into #statements select 'insert into [ContentType] ([Name], [PluralName], [ShortName], [ContentTypeCategoryId], [Owner], [System], [AutoManageName], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select
''' + replace([Name], '''', '''''') + ''',
''' + replace([PluralName], '''', '''''') + ''',
''' + replace([ShortName], '''', '''''') + ''',
' + cast([ContentTypeCategoryId] as varchar) + ',
''' + replace([Owner], '''', '''''') + ''',
' + cast([System] as varchar) + ',
' + cast([AutoManageName] as varchar) + ',
getdate(), ''<EMAIL>'', getdate(), ''<EMAIL>'''
	from [ContentType]
	where Id = @ctid
	
	insert into #statements select 'select @ctid = scope_identity()'

	insert into #statements select 'insert into [Field] ([Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select
''' + replace([Field].[Name], '''', '''''') + ''', @ctid,
' + cast([FieldTypeId] as varchar) + ',
' + isnull(cast([Length] as varchar), 'null') + ',
' + cast([Required] as varchar) + ',
' + cast([Unique] as varchar) + ',
' + isnull('''' + replace([Description], '''', '''''') + '''', 'null') + ',
' + cast([System] as varchar) + ',
' + isnull('[Id], getdate(), ''<EMAIL>'', getdate(), ''<EMAIL>'' from [ContentType] where [Name] = ''' + [ContentType].[Name] + '''', 'null, getdate(), ''<EMAIL>'', getdate(), ''<EMAIL>''')
	from [Field]
	left join [ContentType] on [RelatedContentTypeId] = [ContentType].[Id]
	where ContentTypeId = @ctid
	
	fetch next from ct_cursor into @ctid
end
close ct_cursor
deallocate ct_cursor

select * from #statements order by id

drop table #statements