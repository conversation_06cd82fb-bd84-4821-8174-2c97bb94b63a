﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "SystemAdmin")]
    public class ContentTypeController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;
        private readonly ITopicService topicService;

        public ContentTypeController(IDistributedCacheServiceFactory cache, IMapper mapper, ITopicService topicService)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.topicService = topicService;
        }

        [HttpGet("/manage/content-type")]
        public async Task<IActionResult> ContentTypes()
        {
            var ctc = cache.CreateMappedEntity<ContentType, ContentTypeModel>();
            return View(await ctc.AllAsync());
        }

        [HttpGet("/manage/content-type/new")]
        public IActionResult New()
        {
            return View("EditContentType", new ContentTypeModel());
        }

        [HttpPost("/manage/content-type/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New([Bind("Name,ContentTypeCategoryId,Category,PluralName,ShortName,Owner,System,AutoManageName,Field,Displays")] ContentTypeModel ctm)
        {
            if (this.ModelState.IsValid)
            {
                var ctc = cache.CreateTrackedEntity<ContentType>();
                ContentType ct = mapper.Map<ContentType>(ctm);
                ctc.Add(ct);
                await ctc.SaveChangesAsync();

                if (ct.Category == ContentTypeCategory.Topic)
                {
                    await topicService.EnsureSystemFields(ct);
                }

                return Redirect($"/manage/content-type/edit/{ct.Id}");
            }
            else
            {
                return View("EditContentType", ctm);
            }
        }

        [HttpGet("/manage/content-type/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            var ctc = cache.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field).Include(y => y.ContentTypeDisplay));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == id);
            return View("EditContentType", ctm);
        }

        [HttpPost("/manage/content-type/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, ContentTypeModel ctm)
        {
            if (this.ModelState.IsValid)
            {
                var ctc = cache.CreateTrackedEntity<ContentType>().Configure(x => x.Include(y => y.ContentItem));
                var ct = await ctc.FirstOrDefaultAsync(x => x.Id == id);
                mapper.Map(ctm, ct);
                if (ct.Category == ContentTypeCategory.Picklist)
                {
                    ct.AutoManageName = false;
                }
                await ctc.SaveChangesAsync();
                var ctcm = cache.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field).Include(y => y.ContentTypeDisplay));
                return View("EditContentType", await ctcm.FirstOrDefaultAsync(x => x.Id == id));
            }
            else
            {
                return View("EditContentType", ctm);
            }
        }

        [HttpGet("/manage/content-type/delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var ctc = cache.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
            return View("DeleteContentType", await ctc.FirstOrDefaultAsync(x => x.Id == id));
        }

        [HttpPost("/manage/content-type/delete/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            var ctc = cache.CreateTrackedEntity<ContentType>();
            ContentType ct = await ctc.FirstOrDefaultAsync(x => x.Id == id);
            if (ct.System) throw new InvalidOperationException("System managed Content Types cannot be deleted");
            ctc.Remove(ct);
            await ctc.SaveChangesAsync();
            return Redirect($"/manage/content-type");
        }
    }
}
