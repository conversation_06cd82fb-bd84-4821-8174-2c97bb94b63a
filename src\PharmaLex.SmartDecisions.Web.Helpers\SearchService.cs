using AutoMapper;
using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Pharmalex.AzureCloudStorage;
using Pharmalex.AzureCloudStorage.Enums;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers.AzureSearch;
using PharmaLex.SmartDecisions.Web.Helpers.Comparers;
using PharmaLex.SmartDecisions.Web.Helpers.Extensions;
using PharmaLex.SmartDecisions.Web.Models;
using PharmaLex.SmartDecisions.Web.Models.AzureSearch;
using PharmaLex.HtmlToPdfConverter;
using PharmaLex.HtmlToPdfConverter.Interfaces;
using System;
using System.Text;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface ISearchService
    {
        Task<NewsArticleSearchModel> Search(NewsArticleSearchModel model);
        Task<NewsArticleReadModel?> FindArticle(string articleUrl);
        Task<NewsArticleFullReadModel> FindNewsletterArticle(string newsletterKey, string articleUrl);
        Task<HttpResponseMessage> GetArticleHtmlAsync(NewsArticleContent article);
        Task<byte[]> GetArticlesZipArchiveAsync(List<string> articleUrls);
    }

    public class SearchService : ISearchService
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IRepositoryFactory repoFactory;
        private readonly INewsCategoryLicenseHelper license;
        private readonly IDecisionsBlobContainer storage;
        private readonly ILocalisationService localisationService;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IHtmlToPdfConverterService htmlToPdfConverterService;
        private readonly IMapper mapper;
        private readonly IConfiguration configuration;
        private readonly IAzureSearchHelper azureSearchHelper;
        private readonly INewsCategoryHelper newsCategoryHelper;
        private readonly IDecisionsBlobContainer blobContainer;

        public SearchService(
            ILocalisationService localisationService,
            IRepositoryFactory repoFactory,
            IDistributedCacheServiceFactory cache,
            INewsCategoryLicenseHelper license,
            IDecisionsBlobContainer storage,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            IConfiguration configuration,
            IHtmlToPdfConverterService htmlToPdfConverterService,
            IAzureSearchHelper azureSearchHelper,
            INewsCategoryHelper newsCategoryHelper,
            IDecisionsBlobContainer blobContainer)
        {
            this.repoFactory = repoFactory;
            this.cache = cache;
            this.license = license;
            this.storage = storage;
            this.localisationService = localisationService;
            this.httpContextAccessor = httpContextAccessor;
            this.mapper = mapper;
            this.configuration = configuration;
            this.htmlToPdfConverterService = htmlToPdfConverterService;
            this.azureSearchHelper = azureSearchHelper;
            this.newsCategoryHelper = newsCategoryHelper;
            this.blobContainer = blobContainer;
        }

        private int CurrentUserId => this.httpContextAccessor.HttpContext.User.GetClaimValue<int>("plx:userid");
        private bool IsPharmaLexUser => this.httpContextAccessor.HttpContext.User.IsPharmaLexUser();

        public Locale Locale { get; set; }
        public int DefaultLocaleId { get; set; }

        public Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>> SubscriberPreferenceGroups { get; set; }
        public Dictionary<NewsCategoryGroup, List<NewsCategory>> NewsCategoryGroups { get; set; }
        public List<NewsSource> NewsSources { get; set; }

        public async Task<NewsArticleFullReadModel> FindNewsletterArticle(string newsletterKey, string articleUrl)
        {
            var nl = await this.cache.CreateEntity<Newsletter>().Configure(o => o.Include(x => x.User).ThenInclude(x => x.Locale))
                .FirstOrDefaultAsync(x => x.UniqueKey == newsletterKey && !string.IsNullOrEmpty(x.NewsArticleContentIds));

            var newsArticleContentIds = JsonConvert.DeserializeObject<List<int>>(nl.NewsArticleContentIds);

            var nac = this.cache.CreateEntity<NewsArticleContent>().Configure(o => o.Include(x => x.NewsArticle).ThenInclude(x => x.NewsArticleCategory));
            var newsArticleContents = await nac.WhereAsync(x => newsArticleContentIds.Contains(x.Id));

            var newsArticles = this.mapper.Map<List<NewsArticleReadModel>>(newsArticleContents);

            newsArticleContents = newsArticleContents.Where(x => x.FriendlyUrl == articleUrl).ToList();

            var newsArticle = await this.Find(articleUrl, skipLicense: true);

            var defaultLocaleCache = this.cache.CreateEntity<Locale>();
            Locale defaultLocale = await defaultLocaleCache.FirstOrDefaultAsync(x => x.Default);

            this.DefaultLocaleId = defaultLocale?.Id ?? 0;
            this.Locale = nl.User.Locale ?? defaultLocale ?? new Locale();

            this.NewsCategoryGroups = await this.newsCategoryHelper.InitNewsCategoryGroups(this.Locale.Id);

            this.NewsSources = await this.localisationService.LocaliseList<NewsSource>();

            this.SubscriberPreferenceGroups = await this.newsCategoryHelper.InitSubscriberPreferenceGroups(this.CurrentUserId);

            var headlineArticles = newsArticles
                .Where(x => x.ImportanceId == (int)NewsArticleImportance.Headline)
                .ToList();

            var productGroupArticles = newsArticles
                .Where(x => !headlineArticles.Contains(x))
                .ToList();

            var headlines = await BuildProductGroupArticleList(headlineArticles);
            var productArticles = await BuildProductGroupArticleList(productGroupArticles);

            var orderedArticles = headlines.Union(productArticles).ToList();

            var fullModel = new NewsArticleFullReadModel()
            {
                NewsArticles = orderedArticles,
                NewsArticle = newsArticle,
                NewsletterKey = newsletterKey
            };
            return fullModel;
        }

        public async Task<NewsArticleReadModel?> FindArticle(string articleUrl)
        {
            return await Find(articleUrl);
        }

        private async Task<NewsArticleReadModel?> Find(string url, bool skipLicense = false)
        {
            var userId = this.CurrentUserId;
            var skipLicensing = this.IsPharmaLexUser || skipLicense;
            var today = DateTime.UtcNow.Date;

            var licensedProductCategories = await this.license.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.Products, true);
            var licensedGeographicalCategories = await this.license.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.GeographicalScope, true);

            var article = await cache.CreateEntity<NewsArticleContent>()
                .Configure(o => o
                    .Include(x => x.AzureBlob)
                    .Include(x => x.NewsArticle)
                        .ThenInclude(x => x.NewsSource)
                    .Include(x => x.NewsArticle)
                        .ThenInclude(x => x.NewsArticleCategory))
                .FirstOrDefaultAsync(x =>
                    x.FriendlyUrl == url.ToLower()
                    && x.PublishingStateId == (int)NewsArticlePublishingState.Approved
                    && x.AzureBlobId.HasValue
                    && (skipLicensing || (x.GoLiveDate.HasValue && x.GoLiveDate.Value <= today))
                    && (skipLicensing || x.NewsArticle.NewsArticleCategory.Any(x => licensedProductCategories.Contains(x.NewsCategoryId)))
                    && (skipLicensing || x.NewsArticle.NewsArticleCategory.Any(x => licensedGeographicalCategories.Contains(x.NewsCategoryId))));


            var publisherFullName = (await cache.CreateEntity<User>().FirstOrDefaultAsync(x => x.Email == article.CreatedBy))?.FullName;

            if (article == null) return null;

            var model = this.mapper.Map<NewsArticleReadModel>(article);

            var blobClient = await this.blobContainer.GetBlobClientAsync(article.AzureBlob?.Name);
            BlobDownloadResult downloadResult = await blobClient.DownloadContentAsync();
            model.Content = downloadResult.Content.ToString();
            model.ContentBase64 = Convert.ToBase64String(downloadResult.Content);
            model.ContentUrl = blobClient.GetDownloadLink(LinkType.Link);

            var newsCategoryCache = this.cache.CreateEntity<NewsCategory>();
            var newsArticleIds = article.NewsArticle.NewsArticleCategory.Select(x => x.NewsCategoryId);
            var newsCategories = await newsCategoryCache.WhereAsync(x => newsArticleIds.Contains(x.Id));
            var productParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Products")).Id;
            var productNewsCategories = newsCategories.Where(x => x.ParentId == productParentId).Select(x => x.Id).ToList();
            var medicinesProductParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Medicines")).Id;
            var medicinesProducts = newsCategories.Where(x => x.ParentId == medicinesProductParentId).Select(x => x.Id).ToList();

            var allProducts = new List<int>();

            allProducts.AddRange(productNewsCategories);

            if (medicinesProducts.Count > 3)
            {
                allProducts.Add(medicinesProductParentId);
            }
            else
            {
                allProducts.AddRange(medicinesProducts);
            }

            var themeParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Themes")).Id;
            var themeNewsCategories = newsCategories.Where(x => x.ParentId == themeParentId).Select(x => x.Id).ToList();

            var typeOfTextParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Type of text")).Id;
            var typeOfTextCategories = newsCategories.Where(x => x.ParentId == typeOfTextParentId).Select(x => x.Id).ToList();

            model.ProductIds = allProducts;
            model.ThemeIds = themeNewsCategories;
            model.NewsSourceId = article.NewsArticle.NewsSource.Id;
            model.TypeOfTextIds = typeOfTextCategories;
            model.GoLiveDate = article.GoLiveDate.HasValue ? article.GoLiveDate.Value.FormatLocateDate() : "";
            model.SourcePublicationDate = article.NewsArticle.SourcePublicationDate.FormatLocateDate();
            model.PublisherFullName = publisherFullName;

            return model;
        }

        public async Task<NewsArticleSearchModel> Search(NewsArticleSearchModel model)
        {
            var licensedProductCategories = await this.license.GetUserNewsCategoryGroupLicense(this.CurrentUserId, NewsCategoryGroup.Products, true);
            var licensedThemeCategories = await this.license.GetUserNewsCategoryGroupLicense(this.CurrentUserId, NewsCategoryGroup.Themes, true);
            var licensedTypeOfTextCategories = await this.license.GetUserNewsCategoryGroupLicense(this.CurrentUserId, NewsCategoryGroup.TypeOfText, true);
            var licensedGeographicalCategories = await this.license.GetUserNewsCategoryGroupLicense(this.CurrentUserId, NewsCategoryGroup.GeographicalScope, true);

            var categorySearch = !(model.Products.Count == 0 &&
                                    model.Themes.Count == 0 &&
                                    model.TypesOfText.Count == 0 &&
                                    model.NewsSources.Count == 0 &&
                                    model.GeographicalScopes.Count == 0 &&
                                    model.NewsletterTypes.Count == 0 &&
                                    model.Languages.Count == 0);

            var textSearch = !string.IsNullOrEmpty(model.SearchText);

            var searchOnPageLoad = (!categorySearch && !textSearch)
                || !model.Products.TrueForAll(x => licensedProductCategories.Contains(x))
                || !model.Themes.TrueForAll(x => licensedThemeCategories.Contains(x))
                || !model.TypesOfText.TrueForAll(x => licensedTypeOfTextCategories.Contains(x)
                || !model.GeographicalScopes.TrueForAll(x => licensedGeographicalCategories.Contains(x))
                );

            var textResults = (await GetArticleSearchResultsAsync(model.SearchText, model.NewsletterTypes, model.Languages, searchOnPageLoad, model.IsOnlyTitleSearch)).ToList();

            var today = DateTime.UtcNow.Date;
            var isPlxUser = this.IsPharmaLexUser;
            var results = textResults
                    .Where(x =>
                        (isPlxUser || (x.GoLiveDate.HasValue && x.GoLiveDate.Value <= today)) &&

                        (!model.StartDate.HasValue || (x.SourcePublicationDate.HasValue && model.StartDate.Value.Date <= x.SourcePublicationDate.Value.Date)) &&
                        (!model.EndDate.HasValue || (x.SourcePublicationDate.HasValue && model.EndDate.Value.Date >= x.SourcePublicationDate.Value.Date)) &&

                        (model.GeographicalScopes.Count == 0 || x.Categories.Exists(y => model.GeographicalScopes.Contains(y))) &&
                        (model.Products.Count == 0 || model.Products.Count == licensedProductCategories.Count || x.Categories.Exists(y => model.Products.Contains(y))) &&
                        (model.Themes.Count == 0 || model.Themes.Count == licensedThemeCategories.Count || x.Categories.Exists(y => model.Themes.Contains(y))) &&
                        (model.TypesOfText.Count == 0 || model.TypesOfText.Count == licensedTypeOfTextCategories.Count || x.Categories.Exists(y => model.TypesOfText.Contains(y))) &&
                        (model.NewsSources.Count == 0 || !x.NewsSourceId.HasValue || (x.NewsSourceId.HasValue && model.NewsSources.Contains(x.NewsSourceId.Value))) &&

                        (isPlxUser || x.Categories.Exists(y => licensedProductCategories.Contains(y))) &&
                        (isPlxUser || x.Categories.Exists(y => licensedGeographicalCategories.Contains(y)))
                        )
                .ToList();

            var newsSources = await this.localisationService.LocaliseList<NewsSource>();
            var newsCategories = await this.localisationService.LocaliseList<NewsCategory>();
            var importanceEnum = await this.localisationService.LocaliseEnum<NewsArticleImportance>();

            return new NewsArticleSearchModel
            {
                GeographicalScopes = model.GeographicalScopes,
                NewsletterTypes = model.NewsletterTypes,
                Products = model.Products,
                Themes = model.Themes,
                NewsSources = model.NewsSources,
                Languages = model.Languages,
                TypesOfText = model.TypesOfText,
                StartDate = model.StartDate,
                EndDate = model.EndDate,
                SearchText = model.SearchText,
                IsOnlyTitleSearch = model.IsOnlyTitleSearch,
                Results = results
                .Select(c => new NewsArticleFilterListModel
                {
                    Id = c.Id,
                    FriendlyUrl = !string.IsNullOrEmpty(c.FriendlyUrl) ? Uri.UnescapeDataString(c.FriendlyUrl) : c.FriendlyUrl,
                    Title = !string.IsNullOrEmpty(c.Title) ? Uri.UnescapeDataString(c.Title) : c.Title,
                    Source = newsSources.Find(x => x.Id == c.NewsSourceId)?.Name,
                    SourceUrl = !string.IsNullOrEmpty(c.SourceUrl) ? Uri.UnescapeDataString(c.SourceUrl) : c.SourceUrl,
                    NewsSourceId = c.NewsSourceId ?? 0,
                    SourcePublicationDate = c.SourcePublicationDate ?? DateTime.MinValue,
                    GoLiveDate = c.GoLiveDate ?? DateTime.MinValue,
                    GeographicalScopeId = newsCategories.Where(x => x.GroupId == (int)NewsCategoryGroup.GeographicalScope && c.Categories.Contains(x.Id)).Select(x => x.Id).FirstOrDefault(),
                    GeographicalScope = string.Join(", ", newsCategories.Where(x => x.GroupId == (int)NewsCategoryGroup.GeographicalScope && c.Categories.Contains(x.Id)).Select(x => x.Name).ToArray()),
                    Themes =
                    [
                        .. newsCategories.Where(x => x.GroupId == (int)NewsCategoryGroup.Themes && c.Categories.Contains(x.Id)).Select(x => x.Name).OrderBy(x => x),
                    ],
                    TypeOfText = string.Join(", ", newsCategories.Where(x => x.GroupId == (int)NewsCategoryGroup.TypeOfText && c.Categories.Contains(x.Id)).Select(x => x.Name).ToArray()),
                    ImportanceId = c.ImportanceId ?? 0,
                    ImportanceName = importanceEnum.Find(x => x.Id == (c.ImportanceId ?? -1))?.Name
                }).ToList()
            };
        }

        public async Task<HttpResponseMessage> GetArticleHtmlAsync(NewsArticleContent article)
        {
            if (article == null) return null;
            var nonIndexedBlobPath = configuration.GetValue<string>("AppSettings:ImpactAssessmentNotIndexedBlobPath") ?? throw new InvalidOperationException();
            var htmlArticleBlobPath = configuration.GetValue<string>("AppSettings:HtmlArticleBlobPath") ?? throw new InvalidOperationException();

            var articleTemplate = await BuildHtmlTemplateAsync(article);
            var htmlFileBlobName = BuildFileNameOfArticleHtmlFile(article.AzureBlob.Name, nonIndexedBlobPath, htmlArticleBlobPath);
            var blobHttpHeader = new BlobHttpHeaders { ContentType = "text/html" };
            var blobClient = await this.storage.UploadBlobAsync(new MemoryStream(Encoding.UTF8.GetBytes(articleTemplate)), htmlFileBlobName, httpHeaders: blobHttpHeader);

            var htmlToPdfModel = new HtmlToPdfModel()
            {
                ReferenceLogId = Guid.NewGuid(),
                Url = blobClient.GetDownloadLink(LinkType.Link, timeExpirationInMinutes: 1).ToString(),
            };
            var fileResult = await this.htmlToPdfConverterService.ConvertToPdfAsync(htmlToPdfModel);

            fileResult.Headers.Add("X-File-Name", article.Title);
            return fileResult;
        }

        private string BuildFileNameOfArticleHtmlFile(string blobName, string nonIndexedBlobPath, string htmlBlobPath)
        {
            var htmlBlobName = blobName.Replace(nonIndexedBlobPath, htmlBlobPath);
            htmlBlobName = htmlBlobName.Replace(Path.GetFileName(blobName), $"{Path.GetFileNameWithoutExtension(blobName)}.html");
            return htmlBlobName;
        }

        public async Task<byte[]> GetArticlesZipArchiveAsync(List<string> articleUrls)
        {
            byte[] compressedBytes;

            using var outputStream = new MemoryStream();
            bool isPlxUser = this.IsPharmaLexUser;
            var licensedProductCategories = await this.license.GetUserNewsCategoryGroupLicense(this.CurrentUserId, NewsCategoryGroup.Products, true);
            var licensedGeographicalCategories = await this.license.GetUserNewsCategoryGroupLicense(this.CurrentUserId, NewsCategoryGroup.GeographicalScope, true);

            var articles = this.repoFactory.Create<NewsArticleContent>().Configure(o => o
                    .Include(x => x.AzureBlob)
                    .Include(x => x.NewsArticle)
                        .ThenInclude(x => x.NewsArticleCategory)
                    .Include(x => x.NewsArticle)
                        .ThenInclude(x => x.NewsSource)
                ).Where(x => articleUrls.Contains(x.FriendlyUrl.ToLower())
                    && x.AzureBlobId.HasValue
                    && (isPlxUser || x.NewsArticle.NewsArticleCategory.Any(y => licensedProductCategories.Contains(y.NewsCategoryId)))
                    && (isPlxUser || x.NewsArticle.NewsArticleCategory.Any(y => licensedGeographicalCategories.Contains(y.NewsCategoryId))));

            using (var zipArchive = new ZipArchive(outputStream, ZipArchiveMode.Create, true))
            {
                foreach (var article in articles)
                {
                    var blobClient = (await this.storage.GetBlobClientAsync(article.AzureBlob.Name));
                    var fileName = article.Title;
                    fileName = fileName.RemoveSpecialCharactersForFilePath();
                    fileName += ".pdf";

                    if (article.AzureBlob.Uri.LastIndexOf(".pdf") > 0)
                    {
                        var fileEntry = zipArchive.CreateEntry(fileName, CompressionLevel.Optimal);

                        using var entryStream = fileEntry.Open();
                        await blobClient.DownloadToAsync(entryStream);
                    }
                    else
                    {
                        var articleHtml = await this.GetArticleHtmlAsync(article);

                        var fileEntry = zipArchive.CreateEntry(fileName, CompressionLevel.Optimal);

                        using var entryStream = fileEntry.Open();
                        if (articleHtml != null) await articleHtml.Content.CopyToAsync(entryStream);
                    }
                }
            }

            compressedBytes = outputStream.ToArray();

            return compressedBytes;
        }

        private async Task<string> BuildHtmlTemplateAsync(NewsArticleContent article)
        {
            var newsCategoryCache = this.cache.CreateEntity<NewsCategory>();
            var publisherFullName = (await cache.CreateEntity<User>().FirstOrDefaultAsync(x => x.Email == article.CreatedBy))?.FullName;

            var template = "";

            using (var stream = new StreamReader(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "src\\templates\\newsletter.html")))
            {
                template = await stream.ReadToEndAsync();
            }

            template = template.Replace("{{newsletterPublicationDate}}", article.GoLiveDate.HasValue ? article.GoLiveDate.Value.FormatLocateDate() : "");
            template = template.Replace("{{sourcePublicationDate}}", article.NewsArticle.SourcePublicationDate.FormatLocateDate());
            template = template.Replace("{{publisher}}", publisherFullName);
            template = template.Replace("{{sourceUrl}}", article.SourceUrl);
            template = template.Replace("{{title}}", article.Title);

            StringBuilder templateProducts = new("");

            var newsArticleIds = article.NewsArticle.NewsArticleCategory.Select(x => x.NewsCategoryId);
            var newsCategories = (await newsCategoryCache.WhereAsync(x => newsArticleIds.Contains(x.Id))).OrderBy(x => x.Name);
            var productParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Products")).Id;
            var productNewsCategories = newsCategories.Where(x => x.ParentId == productParentId).OrderBy(x => x.Name).Select(x => new Tuple<int, string>(x.Id, x.Name)).ToList();
            var medicinesProduct = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Medicines"));
            var medicinesProductId = medicinesProduct.Id;
            var medicinesProducts = newsCategories.Where(x => x.ParentId == medicinesProductId).OrderBy(x => x.Name).Select(x => new Tuple<int, string>(x.Id, x.Name)).ToList();

            var allProducts = new List<Tuple<int, string>>();

            allProducts.AddRange(new List<Tuple<int, string>>(productNewsCategories));

            if (medicinesProducts.Count > 3)
            {
                allProducts.Add(new Tuple<int, string>(medicinesProduct.Id, medicinesProduct.Name));
            }
            else
            {
                allProducts.AddRange(medicinesProducts);
            }

            allProducts = allProducts.OrderBy(x => x.Item2).ToList();

            foreach (var product in allProducts)
            {
                templateProducts.Append(@$"<span class=""purple"">{product.Item2}</span>");
                templateProducts.Append(Environment.NewLine);
            }

            template = template.Replace("{{products}}", templateProducts.ToString());

            StringBuilder templateThemes = new("");

            var themeParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Themes")).Id;
            var themeNewsCategories = newsCategories.Where(x => x.ParentId == themeParentId);

            themeNewsCategories = themeNewsCategories.OrderBy(x => x.Name);

            foreach (var theme in themeNewsCategories)
            {
                templateThemes.Append(@$"<span class=""gray"">{theme.Name}</span>");
                templateThemes.Append(Environment.NewLine);
            }

            template = template.Replace("{{themes}}", templateThemes.ToString());

            StringBuilder typeOfTextTemplate = new("");

            var typeOfTextParentId = (await newsCategoryCache.FirstOrDefaultAsync(x => x.Name == "Type of text")).Id;
            var typeOfTextCategories = newsCategories.Where(x => x.ParentId == typeOfTextParentId);

            typeOfTextCategories = typeOfTextCategories.OrderBy(x => x.Name);

            foreach (var typeOfText in typeOfTextCategories)
            {
                typeOfTextTemplate.Append(@$"<span class=""orange"">{typeOfText.Name}</span>");
                typeOfTextTemplate.Append(Environment.NewLine);
            }

            template = template.Replace("{{typeOfText}}", typeOfTextTemplate.ToString());

            var newsSourceTemplate = @$"<span class=""red"">{article.NewsArticle.NewsSource.Name}</span>" + Environment.NewLine;

            template = template.Replace("{{newsSources}}", newsSourceTemplate);

            var blob = (await this.storage.GetBlobClientAsync(article.AzureBlob.Name));
            BlobDownloadResult downloadResult = await blob.DownloadContentAsync();

            if (article.AzureBlob.Uri.LastIndexOf(".pdf") <= 0)
            {
                var articleBody = downloadResult.Content.ToString();
                template = template.Replace("{{body}}", articleBody);
            }

            return template;
        }
        private async Task<IEnumerable<AzureSearchResultModel>> GetArticleSearchResultsAsync(
            string searchText,
            List<int> newsletterTypes,
            List<int> languages,
            bool searchOnPageLoad,
            bool isSearchOnlyInTitle = false)
        {
            var newsArticlesFilter = $"publishingStateId eq '{(int)NewsArticlePublishingState.Approved}' and " +
                                                    $"golivedate le {DateTimeOffset.Now:O}";

            if (searchOnPageLoad)
            {
                Newsletter? latestNewsletter = null;

                var userNewslettersCache = (await this.cache.CreateEntity<Newsletter>()
                    .WhereAsync(x => x.UserId == this.CurrentUserId))
                    .OrderByDescending(x => x.Id);

                var newsArticleContentCache = this.cache.CreateEntity<NewsArticleContent>()
                    .Configure(o =>
                        o.Include(x => x.NewsArticle));

                foreach (var newsletter in userNewslettersCache)
                {
                    var contentIds = JsonConvert.DeserializeObject<List<int>>(newsletter.NewsArticleContentIds).Select(x => x);

                    var newsArticle = (await newsArticleContentCache.FirstOrDefaultAsync(x => contentIds.Contains(x.Id))).NewsArticle;

                    if (newsArticle != null && newsArticle.ImportanceId != (int)NewsArticleImportance.INFOFLASH)
                    {
                        latestNewsletter = newsletter;
                        break;
                    }
                }

                string newsArticleContentIds = "";

                if (latestNewsletter != null)
                {
                    newsArticleContentIds = string.Join(", ", JsonConvert.DeserializeObject<List<int>>(latestNewsletter.NewsArticleContentIds).Select(x => x.ToString()));
                }

                searchText = "*";
                newsArticlesFilter += $" and search.in(id, '{newsArticleContentIds}')";
            }
            else
            {
                var starRegex = new Regex(@"\s+\*", RegexOptions.None, TimeSpan.FromMilliseconds(500));
                searchText = string.IsNullOrEmpty(searchText) ? string.Empty : starRegex.Replace(searchText, "*");

                var newsletterTypesGroupString = string.Join(", ", newsletterTypes);

                if (newsletterTypes.Count > 0)
                {
                    newsArticlesFilter += $" and search.in(importanceId, '{newsletterTypesGroupString}')";
                }
            }

            newsArticlesFilter += $" and importanceId ne '{(int)NewsArticleImportance.INFOFLASH}'";

            if (languages.Count > 0)
            {
                newsArticlesFilter += $" and search.in(localeId, '{string.Join(", ", languages)}')";
            }

            string[] returnFields = {
                    "id", "locale", "localeId", "newsArticleId", "friendlyUrl", "searchableTitle", "newsSourceId", "sourceUrl", "sourcePublicationDate", "categories", "importanceId", "golivedate", "publishingStateId"
                };

            var textSearchResults = await azureSearchHelper.
                TextSearchAsync<IndexedDocumentModel>(searchString: searchText.Trim().EscapeSpecialCharacters(),
                                                      isSearchOnlyInTitle: isSearchOnlyInTitle,
                                                      selectFields: returnFields,
                                                      filter: newsArticlesFilter);

            return textSearchResults.GetResults().Select(x =>
            {
                return new AzureSearchResultModel
                {
                    Id = int.Parse(x.Document.Id),
                    Locale = x.Document.Locale,
                    LocaleId = int.Parse(x.Document.LocaleId),
                    NewsArticleId = int.Parse(x.Document.NewsArticleId),
                    Categories = string.IsNullOrEmpty(x.Document.Categories) ? new List<int>() : JsonConvert.DeserializeObject<List<int>>(x.Document.Categories),
                    FriendlyUrl = x.Document.FriendlyUrl,
                    GoLiveDate = x.Document.GoLiveDate,
                    ImportanceId = int.Parse(x.Document.ImportanceId),
                    NewsSourceId = int.Parse(x.Document.NewsSourceId),
                    PublishingStateId = int.Parse(x.Document.PublishingStateId),
                    SourcePublicationDate = x.Document.SourcePublicationDate,
                    SourceUrl = x.Document.SourceUrl,
                    Title = x.Document.SearchableTitle,
                };
            });
        }

        private async Task<List<NewsArticleReadModel>> BuildProductGroupArticleList(List<NewsArticleReadModel> articles)
        {
            var comparer = new PreferenceArrayComparer();

            var arts = articles.Select(article =>
            {
                var themesPreferences = this.SubscriberPreferenceGroups[NewsCategoryGroup.Themes].OrderBy(x => x.Order).ToList();
                var themesPreferenceOrder = themesPreferences
                    .Where(x => article.CategoryIds.Exists(y => y == x.NewsCategoryId))
                    .Select(x => themesPreferences.IndexOf(x))
                    .ToArray();

                var productsPreferences = this.SubscriberPreferenceGroups[NewsCategoryGroup.Products].OrderBy(x => x.Order).ToList();
                var productsPreferenceOrder = productsPreferences
                    .Where(x => article.CategoryIds.Exists(y => y == x.NewsCategoryId))
                    .OrderBy(x => x.Order)
                    .Select(x => productsPreferences.IndexOf(x))
                    .ToArray();

                var model = new
                {
                    article = new NewsArticleReadModel
                    {
                        Title = article.Title,
                        SourceUrl = article.SourceUrl,
                        Id = article.Id,
                        FriendlyUrl = article.FriendlyUrl,
                        GoLiveDate = article.GoLiveDate,
                        PublisherFullName = article.PublisherFullName,
                        ThemeIds = article.ThemeIds,
                        TypeOfTextIds = article.TypeOfTextIds,
                        ProductIds = article.ProductIds,
                        ContentUrl = article.ContentUrl,
                        CategoryIds = article.CategoryIds,
                        LocaleId = article.LocaleId,
                        SourcePublicationDate = article.SourcePublicationDate,
                    },
                    themesOrder = themesPreferenceOrder,
                    productsOrder = productsPreferenceOrder
                };

                return model;
            }).ToList();

            var result = await Task.Run(() => arts.Select(x => x).OrderBy(x => x.productsOrder, comparer)
                                    .ThenBy(x => x.themesOrder, comparer)
                                    .Select(x => x.article)
                                    .ToList());

            return result;
        }
    }
}
