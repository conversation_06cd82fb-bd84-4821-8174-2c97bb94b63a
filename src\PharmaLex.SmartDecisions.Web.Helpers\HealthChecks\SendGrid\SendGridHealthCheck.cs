﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using SendGrid.Helpers.Mail;
using SendGrid;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using System.Net.Http;

namespace PharmaLex.SmartDecisions.Web.Helpers.HealthChecks.SendGrid
{
    public class SendGridHealthCheck : IHealthCheck
    {
        private const string MAIL_ADDRESS_NAME = "Health Check User";
        private const string MAIL_ADDRESS = "<EMAIL>";
        private const string SUBJECT = "Checking health!";

        private readonly string _apiKey;
        private readonly IHttpClientFactory _httpClientFactory;

        public SendGridHealthCheck(string apiKey, IHttpClientFactory httpClientFactory)
        {
            _apiKey = apiKey ?? throw new ArgumentNullException(nameof(apiKey));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                using var httpClient = _httpClientFactory.CreateClient(SendGridHealthCheckExtensions.NAME);

                var client = new SendGridClient(httpClient, _apiKey);
                var from = new EmailAddress(MAIL_ADDRESS, MAIL_ADDRESS_NAME);
                var to = new EmailAddress(MAIL_ADDRESS, MAIL_ADDRESS_NAME);
                var msg = MailHelper.CreateSingleEmail(from, to, SUBJECT, SUBJECT, null);
                msg.SetSandBoxMode(true);

                var response = await client.SendEmailAsync(msg, cancellationToken).ConfigureAwait(false);

                if (response.StatusCode != HttpStatusCode.OK)
                {
                    return new HealthCheckResult(context.Registration.FailureStatus,
                        $"Sending an email to SendGrid using the sandbox mode is not responding with 200 OK, the current status is {response.StatusCode}",
                        null,
                        new Dictionary<string, object>
                        {
                        { "responseStatusCode", (int)response.StatusCode }
                        });
                }

                return HealthCheckResult.Healthy();
            }
            catch (Exception ex)
            {
                return new HealthCheckResult(context.Registration.FailureStatus, exception: ex);
            }
        }
    }
}
