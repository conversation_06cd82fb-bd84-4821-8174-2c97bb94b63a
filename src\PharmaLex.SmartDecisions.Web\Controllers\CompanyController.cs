﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Entities.Enums;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize("CompanyAdmin")]
    public class CompanyController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;

        public CompanyController(IDistributedCacheServiceFactory cache, IMapper mapper)
        {
            this.cache = cache;
            this.mapper = mapper;
        }

        [HttpGet("/manage/companies")]
        public async Task<IActionResult> Index()
        {
            var acc = cache.CreateMappedEntity<Company, CompanyModel>();
            var all = await acc.AllAsync();
            return View("Companies", all);
        }

        [HttpGet("/manage/company/new")]
        public IActionResult New()
        {
            return View("EditCompany", new CompanyModel());
        }

        [HttpPost("/manage/company/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New([Bind("Name,PrimaryContactName,PrimaryContactEmail,PrimaryContactAddress,PrimaryContactPhone,MaximumUsersCount,MaximumActiveUsersCount,CompanyUsers,LicensedTopics,LicensedNewsCategories")] CompanyModel company)
        {
            if (this.ModelState.IsValid)
            {
                var companyCache = cache.CreateTrackedEntity<Company>();
                var existing = await companyCache.AllAsync();
                if (existing.Any(x => x.Name.IsEqualTo(company.Name)))
                {
                    this.AddNotification("The company could not be created as another company with the same name already exists.", UserNotificationType.Failed, options: new NotificationOptions() { UseIcons = true });
                    return View("EditCompany", company);
                }
                var c = mapper.Map<Company>(company);

                foreach (int topicId in company.LicensedTopics)
                {
                    c.CompanyContentType.Add(new CompanyContentType
                    {
                        ContentTypeId = topicId
                    });
                }

                foreach (int categoryId in company.LicensedNewsCategories)
                {
                    c.CompanyNewsCategory.Add(new CompanyNewsCategory
                    {
                        NewsCategoryId = categoryId
                    });
                }

                companyCache.Add(c);
                await companyCache.SaveChangesAsync();
                var escapedName = HttpUtility.HtmlEncode(c.Name);
                this.AddConfirmationNotification($"<em>{escapedName}</em> created", new NotificationOptions() { UseIcons = true });
                return Redirect($"/manage/company/edit/{c.Id}");
            }
            else
            {
                return View("EditCompany", company);
            }
        }

        [HttpGet("/manage/company/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            var companyCache = cache.CreateMappedEntity<Company, CompanyModel>()
                .Configure(o => o
                    .Include(x => x.CompanyUser)
                        .ThenInclude(x => x.User)
                    .Include(x => x.CompanyContentType)
                    .Include(x => x.CompanyNewsCategory)
                );
            var c = await companyCache.FirstOrDefaultAsync(x => x.Id == id);
            return View("EditCompany", c);
        }

        [HttpPost("/manage/company/edit/{id:int}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CompanyModel company)
        {
            if (this.ModelState.IsValid)
            {
                var companyCache = cache.CreateTrackedEntity<Company>();
                var existing = await companyCache.AllAsync();
                if (existing.Any(x => x.Id != id && x.Name.IsEqualTo(company.Name)))
                {
                    this.AddNotification("The company could not be updated as another company with the same name already exists.", UserNotificationType.Failed, options: new NotificationOptions() { UseIcons = true });
                    return View("EditCompany", company);
                }
                var c = await companyCache.FirstOrDefaultAsync(x => x.Id == id);
                mapper.Map(company, c);
                await companyCache.SaveChangesAsync();

                var companyContentTypeCache = cache.CreateTrackedEntity<CompanyContentType>();
                var topics = await companyContentTypeCache.WhereAsync(x => x.CompanyId == id);
                foreach (var topic in topics)
                {
                    if (!company.LicensedTopics.Contains(topic.ContentTypeId))
                    {
                        companyContentTypeCache.Remove(topic);
                    }
                }
                foreach (int topicId in company.LicensedTopics)
                {
                    if (!topics.Any(x => x.ContentTypeId == topicId))
                    {
                        companyContentTypeCache.Add(new CompanyContentType
                        {
                            CompanyId = id,
                            ContentTypeId = topicId
                        });
                    }
                }
                await companyContentTypeCache.SaveChangesAsync();

                var companyNewsCategoryCache = cache.CreateTrackedEntity<CompanyNewsCategory>();
                var categories = await companyNewsCategoryCache.WhereAsync(x => x.CompanyId == id);
                foreach (var category in categories)
                {
                    if (!company.LicensedNewsCategories.Contains(category.NewsCategoryId))
                    {
                        companyNewsCategoryCache.Remove(category);
                    }
                }
                foreach (int categoryId in company.LicensedNewsCategories)
                {
                    if (!categories.Any(x => x.NewsCategoryId == categoryId))
                    {
                        companyNewsCategoryCache.Add(new CompanyNewsCategory
                        {
                            CompanyId = id,
                            NewsCategoryId = categoryId
                        });
                    }
                }
                await companyNewsCategoryCache.SaveChangesAsync();
                var escapedName = HttpUtility.HtmlEncode(c.Name);
                this.AddConfirmationNotification($"<em>{escapedName}</em> updated", new NotificationOptions() { UseIcons = true });
                return await this.Edit(id);
            }
            else
            {
                return this.View("EditCompany", company);
            }
        }

        [HttpGet("/manage/company/delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var companyCache = cache.CreateMappedEntity<Company, CompanyModel>();
            var company = await companyCache.FirstOrDefaultAsync(x => x.Id == id);
            if (company != null)
            {
                return View("DeleteCompany", company);
            }
            else
            {
                return Redirect($"/manage/companies");
            }
        }

        [HttpPost("/manage/company/delete/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            var subscriptionsCache = cache.CreateTrackedEntity<NewsletterSubscription>()
                .Configure(o => o
                    .Include(x => x.Newsletter)
                    .Include(x => x.User)
                    .ThenInclude(x => x.CompanyUser));

            var subscriptions = subscriptionsCache.Where(x =>
                x.User.CompanyUser != null &&
                x.User.CompanyUser.CompanyId == id);

            foreach (var s in subscriptions)
            {
                subscriptionsCache.Remove(s);
            }

            await subscriptionsCache.SaveChangesAsync();

            var companyCache = cache.CreateTrackedEntity<Company>();
            var company = await companyCache.FirstOrDefaultAsync(x => x.Id == id);
            if (company != null)
            {
                companyCache.Remove(company);
                await companyCache.SaveChangesAsync();
                var escapedName = HttpUtility.HtmlEncode(company.Name);
                this.AddConfirmationNotification($"<em>{escapedName}</em> deleted", new NotificationOptions() { UseIcons = true });
            }

            return Redirect($"/manage/companies");
        }
    }
}
