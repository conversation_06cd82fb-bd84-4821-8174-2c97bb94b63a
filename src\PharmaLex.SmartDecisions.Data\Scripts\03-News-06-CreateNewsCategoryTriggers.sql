﻿create trigger [dbo].[NewsCategory_Insert] on [dbo].[NewsCategory]
for insert as
insert into [Audit].[NewsCategory_Audit]
select 'I'
      ,[Id]
      ,[Name]
      ,[ParentId]
      ,[GroupId]
      ,[SortOrder]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsCategory_Update] on [dbo].[NewsCategory]
for update as
insert into [Audit].[NewsCategory_Audit]
select 'U'
      ,[Id]
      ,[Name]
      ,[ParentId]
      ,[GroupId]
      ,[SortOrder]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsCategory_Delete] on [dbo].[NewsCategory]
for delete as
insert into [Audit].[NewsCategory_Audit]
select 'D'
      ,[Id]
      ,[Name]
      ,[ParentId]
      ,[GroupId]
      ,[SortOrder]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
