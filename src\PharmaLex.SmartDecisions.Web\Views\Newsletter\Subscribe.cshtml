﻿@model NewsletterSubscriptionViewModel
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.SmartDecisions.Entities
@using Newtonsoft.Json
@using Microsoft.Extensions.Configuration
@using PharmaLex.Authentication.B2C
@using PharmaLex.SmartDecisions.Web.Helpers

@inject IConfiguration Configuration
@inject IAuthorizationService AuthorizationService
@inject INewsCategoryLicenseHelper categoryLicenseHelper
@inject INewsCategoryHelper categoryHelper

@using Microsoft.AspNetCore.Localization
@{
    var requestCulture = this.Context.Features.Get<IRequestCultureFeature>().RequestCulture.UICulture.Name;
    ViewData["Title"] = ls.Localise("(news).subscribe.newsletter-subscription").Value;

    var newsCategories = (await ls.LocaliseList<NewsCategory>()).OrderBy(x => x.SortOrder);
    var userId = this.User.GetClaimValue<int>("plx:userid");
    var licensedGeographicalCategories = await categoryLicenseHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.GeographicalScope);
    var licensedProductCategories = await categoryLicenseHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.Products);

    var adminUser = (await AuthorizationService.AuthorizeAsync(User, "SuperAdmin")).Succeeded;

    var franceTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Romance Standard Time");
    var franceLocalTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, franceTimeZone);

    var franceLocalHour = franceLocalTime.Hour;

    var nonAdminDays = Configuration.GetValue<string>("AppSettings:NonAdminNewsletterPublicationDays").Split(",").Select(x => int.Parse(x)).ToArray();
    var newsCategoryLookup = await categoryHelper.GetNewsCategoryLocalisationIds();
}

<div id="newsletterSubscription" v-cloak>
    @Html.AntiForgeryToken()
    <div class="sub-header">
        <h2>@ls.Localise("(news).subscribe.newsletter-subscription")</h2>
        <div class="controls">
            @if ((await AuthorizationService.AuthorizeAsync(User, "NewsAuthor")).Succeeded)
            {
                <a class="button secondary" href="/articles">@ls.Localise("news-articles")</a>
            }
            else
            {
                <a class="button secondary" href="/articles/search">@ls.Localise("news-articles")</a>
            }
            <button type="submit" v-on:click="savePreferences">@ls.Localise("save")</button>
        </div>
    </div>

    <location-preferences-dialog :showdialog="showLocationDialog" :preferencedata="editingPreferences" @@location-preferences-closing="showLocationDialog=false" @@locations-updated="updateLocations"></location-preferences-dialog>
    <product-preferences-dialog :showdialog="showProductDialog" :preferencedata="editingPreferences" @@product-preferences-closing="showProductDialog=false" @@products-updated="updateProducts"></product-preferences-dialog>
    <theme-preferences-dialog :showdialog="showThemeDialog" :preferencedata="editingPreferences" @@theme-preferences-closing="showThemeDialog=false" @@themes-updated="updateThemes"></theme-preferences-dialog>
    <article-language-preferences-dialog :showdialog="showArticleLanguageDialog" :preferencedata="editingPreferences" @@article-language-preferences-closing="showArticleLanguageDialog=false" @@article-language-updated="updateArticleLanguage"></article-language-preferences-dialog>

    <div class="flex flex-nowrap gapped-2">
        <div class="flex-item flex-x3 tile">
            <h5>@ls.Localise("(news).subscribe.week-days")</h5>
            <template v-for="(d, i) in model">
                <div class="flex-centred-spaced-nowrapped">
                    <div><span>{{ getDayDisplay(d.displayDay, d.deliveryLocalHour, d.timezoneOffset )}}</span></div>
                    <label class="switch-container">
                        <input type="checkbox" :id="`D[${i}].created`" v-model="d.created" class="switch" @@change="onSubscribeToggle($event, d)" />
                        <label :for="`D[${i}].created`" class="switch" :title="`@ls.LocaliseSafe("(news).subscribe.click-to-format")${d.created ? '@ls.LocaliseSafe("(news).subscribe.remove")' : '@ls.LocaliseSafe("(news).subscribe.add")'}`">x</label>
                    </label>
                </div>
                <div v-if="adminUser">
                    <div v-if="d.created" class="flex-centred-spaced-nowrapped mb-3">
                        <label class="flex-item flex-30-percent sub-brand-color m-0">@ls.Localise("(news).subscribe.delivery-time")</label>
                        <div class="custom-select card">
                            <select class="flex-item flex-70-percent concise" required v-model="d.deliveryLocalHour">
                                <option value="">{{`@ls.LocaliseSafe("(news).subscribe.select-time-format")`.replace(/\{0\}/, timezoneDisplay(d.timezone, d.timezoneOffset))}}</option>
                                <option v-for="h in hours()" :value="h.id" :selected="h.id === d.deliveryLocalHour">{{h.name}}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </template>
            <template v-if="showNewsletterWarningMessage()">
                <div class="mt-3 flex flex-nowrap flex-align-center">
                    <i class="m-icon warning-color pr-1">warning</i>
                    <span>@ls.Localise("(news).subscribe.newsletters-not-sent")</span>
                </div>
            </template>
            <div class="mt-3 gap-top-2">
                <h5>@ls.Localise("(news).subscribe.monthly-newsletter")</h5>
                <div class="flex-centred-spaced-nowrapped">
                    <div><span>@ls.Localise("(news).subscribe.first-monday") @@{{monthlySubscriptionModel.deliveryLocalHour}}:00</span></div>
                    <label class="switch-container">
                        <input type="checkbox" id="monthly" v-model="monthlySubscriptionModel.created" class="switch" @@change="onSubscribeToggle($event, monthlySubscriptionModel)" />
                        <label for="monthly" class="switch" :title="`@ls.LocaliseSafe("(news).subscribe.click-to-format")${monthlySubscriptionModel.created ? '@ls.LocaliseSafe("(news).subscribe.remove")' : '@ls.LocaliseSafe("(news).subscribe.add")'}`">x</label>
                    </label>
                </div>
            </div>
            <div class="mt-3 gap-top-2">
                <h5>@ls.Localise("(news).infoflash")</h5>
                <div class="flex-centred-spaced-nowrapped">
                    <div><span>@ls.Localise("(news).subscribe.infoflash")</span></div>
                    <label class="switch-container">
                        <input type="checkbox" id="infoflash" v-model="infoflashSubscriptionModel.created" class="switch" />
                        <label for="infoflash" class="switch" :title="`@ls.LocaliseSafe("(news).subscribe.click-to-format")${infoflashSubscriptionModel.created ? '@ls.LocaliseSafe("(news).subscribe.remove")' : '@ls.LocaliseSafe("(news).subscribe.add")'}`">x</label>
                    </label>
                </div>
            </div>

            <div class="mt-3 gap-top-2">
                <h5>@ls.Localise("(news).subscribe.newsletter-article-language")</h5>
                <a @@click="chooseArticleLanguage" class="button">@ls.Localise("(news).subscribe.choose")</a>
                <p class="drag-drop-help mt-2">
                    @ls.Localise("drag-drop-article-language-help")
                </p>
            </div>
            <div>
                <template v-if="categoriesOrderedByPreference(findCategoryByGroupId(@((int)NewsCategoryGroup.NewsletterArticleLanguage))).length > 0">
                    <div ref="articleLanguage" class="drag-drop-list mt-3">
                        <template v-for="pref in categoriesOrderedByPreference(findCategoryByGroupId(@((int)NewsCategoryGroup.NewsletterArticleLanguage)))">

                            <div class="dragdropspacer"
                                 v-bind:class="{dragdropspacerhover: dragTarget == `${pref.groupId}-1-${pref.id}-spacer`}"
                                 v-on:drop="drop"
                                 v-on:dragover="allowDrop"
                                 v-on:dragenter="spacerDragEnter"
                                 v-on:dragleave="spacerDragLeave"
                                 :id="`${pref.groupId}-1-${pref.id}-spacer`">
                            </div>

                            <div draggable="true"
                                 class="drop-target lozenge"
                                 v-bind:class="getColour(pref)"
                                 v-on:dragstart="dragStart"
                                 v-on:dragover="allowDrop"
                                 :id="`${pref.groupId}-1-${pref.id}`">
                                <span class="no-user-select">{{pref.name}}</span>
                                <div class="drag-controls no-user-select">
                                    <i class="m-icon" @@click="removePreference(@((int)NewsCategoryGroup.NewsletterArticleLanguage), pref.id)">close</i>
                                    <i class="m-icon drag-handle">drag_indicator</i>
                                </div>
                            </div>
                        </template>
                        <template v-if="categoriesOrderedByPreference(findCategoryByGroupId(@((int)NewsCategoryGroup.NewsletterArticleLanguage))).length > 0">
                            <div class="dragdropspacer"
                                 v-bind:class="{dragdropspacerhover: dragTarget == '1-endoflist'}"
                                 v-on:drop="drop"
                                 v-on:dragover="allowDrop"
                                 v-on:dragenter="spacerDragEnter"
                                 v-on:dragleave="spacerDragLeave"
                                 id="1-endoflist"></div>
                        </template>

                    </div>
                </template>
                <template v-else>
                    <div class="mt-3 flex flex-nowrap flex-align-center">
                        <i class="m-icon warning-color pr-1">warning</i>
                        <span>@ls.Localise("(news).none-selected")</span>
                    </div>
                </template>
            </div>

            <div class="mt-6 gap-top-2">
                <h5>@ls.Localise("(news).subscribe.geographical-scope")</h5>
                <a @@click="chooseLocation" class="button" icon-button-back>@ls.Localise("(news).subscribe.choose")</a>
            </div>
            <div>
                <template v-if="availablePreferences[@((int)NewsCategoryGroup.GeographicalScope)-1].children.filter(x => x.selected == true).length > 0">
                    <ul class="drag-drop-list">
                        <li v-for="pref in availablePreferences[@((int)NewsCategoryGroup.GeographicalScope)-1].children.filter(x => x.selected == true)"
                            :id="`${pref.id}`"
                            class="lozenge brand-background">
                            <div class="flex flex-nowrap justify-space-between width-100">
                                <span>{{pref.name}}</span>
                                <div class="drag-controls">
                                    <i class="m-icon" @@click="removePreference(@((int)NewsCategoryGroup.GeographicalScope), pref.id)">close</i>
                                </div>
                            </div>
                        </li>
                    </ul>
                </template>
                <template v-else>
                    <div class="mt-3 flex flex-nowrap flex-align-center">
                        <i class="m-icon warning-color pr-1">warning</i>
                        <span>@ls.Localise("(news).none-selected")</span>
                    </div>
                </template>
            </div>
        </div>

        <div class="flex-item flex-x3 tile">
            <h5>@ls.Localise("(news).subscribe.products")</h5>
            <a @@click="chooseProducts" class="button">@ls.Localise("(news).subscribe.choose")</a>
            <p class="drag-drop-help mt-2">
                @ls.Localise("drag-drop-product-help")
            </p>
            <template v-if="categoriesOrderedByPreference(availablePreferences[@((int)NewsCategoryGroup.Products) - 1]).length > 0">
                <div ref="product">
                    <template v-for="pref in categoriesOrderedByPreference(availablePreferences[@((int)NewsCategoryGroup.Products) - 1])">

                        <div class="dragdropspacer"
                             v-bind:class="{dragdropspacerhover: dragTarget == `${pref.groupId}-1-${pref.id}-spacer`}"
                             v-on:drop="drop"
                             v-on:dragover="allowDrop"
                             v-on:dragenter="spacerDragEnter"
                             v-on:dragleave="spacerDragLeave"
                             :id="`${pref.groupId}-1-${pref.id}-spacer`">
                        </div>

                        <div draggable="true"
                             class="draggable-group"
                             v-bind:class="getColour(pref)"
                             v-on:dragstart="dragStart"
                             v-on:dragover="allowDrop"
                             :id="`${pref.groupId}-1-${pref.id}`">
                            <div class="flex flex-nowrap justify-space-between width-100">
                                <span class="no-user-select">{{pref.name}}</span>
                                <div class="drag-controls">
                                    <i class="m-icon" @@click="removePreference(@((int)NewsCategoryGroup.Products), pref.id)">close</i>
                                    <i class="m-icon drag-handle">drag_indicator</i>
                                </div>
                            </div>

                            <div class="drag-drop-list">
                                <template v-for="pf in categoriesOrderedByPreference(pref)">

                                    <div class="dragdropspacermeds"
                                         v-bind:class="{dragdropspacerhover: dragTarget == `${pf.groupId}-2-${pf.id}-spacer`}"
                                         v-on:drop="drop"
                                         v-on:dragover="allowDrop"
                                         v-on:dragenter="spacerDragEnter"
                                         v-on:dragleave="spacerDragLeave"
                                         :id="`${pf.groupId}-2-${pf.id}-spacer`">
                                    </div>
                                    <div draggable="true"
                                         class="category-medicines lozenge"
                                         v-on:dragstart="dragStart"
                                         v-on:dragover="allowDrop"
                                         :id="`${pf.groupId}-2-${pf.id}`">
                                        <span>{{pf.name}}</span>
                                        <div class="drag-controls">
                                            <i class="m-icon" @@click="removePreferenceFromGroup(@((int)NewsCategoryGroup.Products), pf.id,pref.id )">close</i>
                                            <i class="m-icon drag-handle">drag_indicator</i>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="categoriesOrderedByPreference(pref).length > 0">
                                    <div class="dragdropspacermeds"
                                         v-bind:class="{dragdropspacerhover: dragTarget == '2-2-endoflist'}"
                                         v-on:drop="drop"
                                         v-on:dragover="allowDrop"
                                         v-on:dragenter="spacerDragEnter"
                                         v-on:dragleave="spacerDragLeave"
                                         id="2-2-endoflist">
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                    <template v-if="categoriesOrderedByPreference(availablePreferences[@((int)NewsCategoryGroup.Products) - 1]).length > 0">
                        <div class="dragdropspacer"
                             v-bind:class="{dragdropspacerhover: dragTarget == '2-endoflist'}"
                             v-on:drop="drop"
                             v-on:dragover="allowDrop"
                             v-on:dragenter="spacerDragEnter"
                             v-on:dragleave="spacerDragLeave"
                             id="2-endoflist"></div>
                    </template>

                </div>

            </template>
            <template v-else>
                <div class="mt-3 flex flex-nowrap flex-align-center">
                    <i class="m-icon warning-color pr-1">warning</i>
                    <span>@ls.Localise("(news).none-selected")</span>
                </div>
            </template>
        </div>

        <div class="flex-item flex-x3 tile">
            <h5>@ls.Localise("(news).subscribe.themes")</h5>
            <a @@click="chooseThemes" class="button">@ls.Localise("(news).subscribe.choose")</a>
            <p class="drag-drop-help mt-2">
                @ls.Localise("drag-drop-theme-help")
            </p>
            <template v-if="categoriesOrderedByPreference(availablePreferences[@((int)NewsCategoryGroup.Themes) - 1]).length > 0">
                <div ref="theme" class="drag-drop-list mt-3">
                    <template v-for="pref in categoriesOrderedByPreference(availablePreferences[@((int)NewsCategoryGroup.Themes) - 1])">

                        <div class="dragdropspacer"
                             v-bind:class="{dragdropspacerhover: dragTarget == `${pref.groupId}-1-${pref.id}-spacer`}"
                             v-on:drop="drop"
                             v-on:dragover="allowDrop"
                             v-on:dragenter="spacerDragEnter"
                             v-on:dragleave="spacerDragLeave"
                             :id="`${pref.groupId}-1-${pref.id}-spacer`">
                        </div>

                        <div draggable="true"
                             class="drop-target lozenge"
                             v-bind:class="getColour(pref)"
                             v-on:dragstart="dragStart"
                             v-on:dragover="allowDrop"
                             :id="`${pref.groupId}-1-${pref.id}`">
                            <span class="no-user-select">{{pref.name}}</span>
                            <div class="drag-controls no-user-select">
                                <i class="m-icon" @@click="removePreference(@((int)NewsCategoryGroup.Themes), pref.id)">close</i>
                                <i class="m-icon drag-handle">drag_indicator</i>
                            </div>
                        </div>
                    </template>
                    <template v-if="categoriesOrderedByPreference(availablePreferences[@((int)NewsCategoryGroup.Themes) - 1]).length > 0">
                        <div class="dragdropspacer"
                             v-bind:class="{dragdropspacerhover: dragTarget == '3-endoflist'}"
                             v-on:drop="drop"
                             v-on:dragover="allowDrop"
                             v-on:dragenter="spacerDragEnter"
                             v-on:dragleave="spacerDragLeave"
                             id="3-endoflist"></div>
                    </template>

                </div>
            </template>
            <template v-else>
                <div class="mt-3 flex flex-nowrap flex-align-center">
                    <i class="m-icon warning-color pr-1">warning</i>
                    <span>@ls.Localise("(news).none-selected")</span>
                </div>
            </template>
        </div>
    </div>

    <div class="buttons mt-2">
        @if ((await AuthorizationService.AuthorizeAsync(User, "NewsAuthor")).Succeeded)
        {
            <a class="button secondary" href="/articles">@ls.Localise("cancel")</a>
        }
        else
        {
            <a class="button secondary" href="/articles/search">@ls.Localise("cancel")</a>
        }
        <button type="submit" v-on:click="savePreferences">@ls.Localise("save")</button>
    </div>
</div>
@section Scripts {
    <script type="text/javascript">
        function preferencesNotNested(prefs) {
            let flatPreferences = [];
            for (const pref of flattenPreferences(prefs.children)) {
                flatPreferences.push(pref);
            };
            return flatPreferences;
        }

        function* flattenPreferences(prefs) {
            for (const pref of prefs) {
                yield pref;
                yield* flattenPreferences(pref.children);
            }
        };

        function SetPreferenceSelections(array, categoryIds) {
            array.forEach(child => {
                SetPreferenceSelections(child.children, categoryIds);

                let order = categoryIds.indexOf(child.id);
                if (order > -1) {
                    child.selected = true;
                    child.prefOrder = order + 1;
                }
                else {
                    child.selected = false;
                }
            });
        }

    </script>

    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

        var pageConfig = {
            appElement: '#newsletterSubscription',
            data() {
                const buildCategories = (categories, current) => {
                    current = current || categories.filter(x => !x.parentId);
                    current.forEach(c => {
                        c.children = categories.filter(x => x.parentId == c.id).sort((a, b) => a.name.localeCompare(b.name, 'en', { sensitivity: 'base' }));
                        c.prefOrder = 1;
                        buildCategories(categories, c.children);
                    });

                    return current;
                };
                const buildProductsCategories = (categories, current, nestingLevel) => {
                    current = current || categories.filter(x => !x.parentId);
                    current.forEach(c => {
                        c.children = categories.filter(x => x.parentId == c.id);
                        if (nestingLevel === 1) {
                            c.children = c.children.sort((a, b) => a.name.localeCompare(b.name, 'en', { sensitivity: 'base' }));
                        }
                        c.prefOrder = 1;
                        buildProductsCategories(categories, c.children, (nestingLevel || 0) + 1);
                    });

                    return current;
                };

                const subscriptions = @Html.Raw(Model.Subscriptions.ToJson());
                const userNewsCategories = @Html.Raw(Model.NewsletterUserNewsCategories.ToJson());
                const monthlySubscription = subscriptions.find(x => x.isMonthly && x.active);
                const infoflashSubscription = subscriptions.find(x => x.isInfoflash && x.active);
                const infoflashDefined = subscriptions.find(x => x.isInfoflash); 
                const systemTimezone = new Intl.DateTimeFormat('@requestCulture').resolvedOptions().timeZone;
                const systemTimezoneOffset = new Date().getTimezoneOffset();
                const franceNewsletterHour = 16;
                const franceLocalHour = @franceLocalHour;
                const userLocalHourRelativeToFranceLocalHour = franceNewsletterHour + (new Date().getHours() - franceLocalHour);

                const licensedGeographicalCategories = @Html.Raw(JsonConvert.SerializeObject(licensedGeographicalCategories));
                const licensedProductCategories = @Html.Raw(JsonConvert.SerializeObject(licensedProductCategories));
                const newsCategories = @Html.Raw(JsonConvert.SerializeObject(newsCategories));
                const geographicalNewsCategories = buildCategories(newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.GeographicalScope)));
                geographicalNewsCategories[0].children = geographicalNewsCategories[0].children.filter(y => licensedGeographicalCategories.indexOf(y.id) >= 0).sort((a, b) => {
                    return a.sortOrder > b.sortOrder ? 1 : -1;
                });
                const productsCategories = buildProductsCategories(newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.Products)));
                productsCategories[0].children = productsCategories[0].children.filter(y => licensedProductCategories.indexOf(y.id) >= 0);
                const themesCategories = buildCategories(newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.Themes)));
                const languageCategories = buildCategories(newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.NewsletterArticleLanguage)))
                const hierarchicalNewsCategories = [...productsCategories, ...themesCategories];

                availablePreferences = geographicalNewsCategories.concat(hierarchicalNewsCategories, languageCategories);

                // update with the previously saved preferences
                var preferredCategories = [];
                userNewsCategories.forEach(y => {
                    preferredCategories.push(y.newsCategoryId);
                });

                // update the list of all categories with current preference
                SetPreferenceSelections(availablePreferences, preferredCategories);

                // business rule: only admins see all days of week (for testing)
                adminUser = @adminUser.ToString().ToLower();
                var daysToDisplay = (adminUser ? this.weekDays() : this.weekDays().filter(x => @(JsonConvert.SerializeObject(nonAdminDays)).includes(x.id)));

                return {
                    systemTimezone,
                    systemTimezoneOffset,
                    userLocalHourRelativeToFranceLocalHour,
                    model: daysToDisplay.map(d => {
                        const ds = subscriptions.find(x => x.deliveryLocalDay == d.id && !x.isMonthly && !x.isInfoflash && x.active);
                        return {
                            ...ds,
                            displayDay: d.name,
                            deliveryLocalDay: d.id,
                            created: !!ds,
                            deliveryLocalHour: ds?.deliveryLocalHour ?? userLocalHourRelativeToFranceLocalHour,
                            timezone: ds?.timezone ?? systemTimezone,
                            timezoneOffset: ds?.timezoneOffset ?? systemTimezoneOffset
                        };
                    }),
                    monthlySubscriptionModel: {
                        ...monthlySubscription,
                        created: !!monthlySubscription,
                        deliveryLocalDay: 1,
                        deliveryLocalHour: monthlySubscription?.deliveryLocalHour ?? userLocalHourRelativeToFranceLocalHour,
                        timezone: monthlySubscription?.timezone ?? systemTimezone,
                        timezoneOffset: monthlySubscription?.timezoneOffset ?? systemTimezoneOffset
                    },
                    infoflashSubscriptionModel: {
                        ...infoflashSubscription,
                        created: !!infoflashSubscription || !infoflashDefined,
                        deliveryLocalDay: 1,
                        deliveryLocalHour: 1,
                        timezone: infoflashSubscription?.timezone ?? systemTimezone,
                        timezoneOffset: infoflashSubscription?.timezoneOffset ?? systemTimezoneOffset
                    },
                    allSubscriptions: subscriptions,
                    availablePreferences,
                    editingPreferences: [],
                    showLocationDialog: false,
                    showProductDialog: false,
                    showThemeDialog: false,
                    showArticleLanguageDialog: false,
                    adminUser,
                    dragTarget: ''
                };
            },
            computed: {
                systemTimezoneDisplay() {
                    return `${this.systemTimezone} (UTC ${this.systemTimezoneOffset <= 0 ? '+' : '-'}${Math.abs(Math.round(this.systemTimezoneOffset / 60))})`;
                }
            },
            methods: {
                updateLocations(data) {
                    this.updatePreferences(this.availablePreferences[@((int)NewsCategoryGroup.GeographicalScope)-1], data.preferencedata[0]);
                    this.showLocationDialog = false;
                },
                updateProducts(data) {
                    this.updatePreferences(this.availablePreferences[@((int)NewsCategoryGroup.Products)-1], data.preferencedata[0]);
                    this.showProductDialog = false;
                },
                updateThemes(data) {
                    this.updatePreferences(this.availablePreferences[@((int)NewsCategoryGroup.Themes)-1], data.preferencedata[0]);
                    this.showThemeDialog = false;
                },
                updateArticleLanguage(data) {
                    this.updatePreferences(this.findCategoryByGroupId(@((int)NewsCategoryGroup.NewsletterArticleLanguage)), data.preferencedata[0]);
                    this.showArticleLanguageDialog = false;
                },
                timezoneDisplay(timezone, offset) {
                    return `${timezone} (UTC ${offset <= 0 ? '+' : '-'}${Math.abs(Math.round(offset / 60))})`;
                },
                weekDays() {
                    let result = [0, 1, 2, 3, 4, 5, 6].map(x => {
                        const d = new Date();
                        d.setDate(d.getDate() + x);

                        const weekDay = d.toLocaleString('@requestCulture', { weekday: 'long' });
                        return {
                            id: d.getDay(),
                            name: `${weekDay[0].toUpperCase()}${weekDay.substring(1)}`
                        }
                    }).sort((a, b) => {
                        return a.id > b.id ? 1 : -1;
                    });

                    const sunday = result.splice(0, 1);
                    result.push(sunday[0]);
                    return result;
                },
                hours() {
                    const hoursArr = new Array(24);
                    for (var i = 0; i < hoursArr.length; i++) {
                        hoursArr[i] = {
                            id: i,
                            name: `${i.toString().padStart(2, "0")}:00`
                        }
                    }

                    return hoursArr;
                },
                chooseLocation() {
                    this.editingPreferences = this.buildEditPreferences(this.availablePreferences.filter(x => x.groupId == @((int)NewsCategoryGroup.GeographicalScope)));
                    this.showLocationDialog = true;
                },
                chooseProducts() {
                    this.editingPreferences = this.buildEditPreferences(this.availablePreferences.filter(x => x.groupId == @((int)NewsCategoryGroup.Products)));
                    this.showProductDialog = true;
                },
                chooseThemes() {
                    this.editingPreferences = this.buildEditPreferences(this.availablePreferences.filter(x => x.groupId == @((int)NewsCategoryGroup.Themes)));
                    this.showThemeDialog = true;
                },
                chooseArticleLanguage() {
                    this.editingPreferences = this.buildEditPreferences(this.availablePreferences.filter(x => x.groupId == @((int)NewsCategoryGroup.NewsletterArticleLanguage)));
                    this.showArticleLanguageDialog = true;
                },
                buildEditPreferences(categories) {
                    return categories.map(x => {
                        let res = { ...x };

                        if (x.children.length)
                            res.children = this.buildEditPreferences(x.children);

                        return res;
                    });
                },
                onSubscribeToggle(e, model) {
                    if(!e.target.checked) {
                        model.deliveryLocalHour = this.userLocalHourRelativeToFranceLocalHour;
                    }
                },
                updatePreferences(existingPreferences, newPreferences) {

                    let flatPreferences = preferencesNotNested(newPreferences);
                    let preferredCategories = flatPreferences.filter(x => x.selected || x.children.filter(y => y.selected).length > 0)
                                                                .sort((a, b) => (a.prefOrder > b.prefOrder) ? 1 : -1).map(x => x.id);

                    SetPreferenceSelections(existingPreferences.children, preferredCategories);
                },
                spacerDragEnter: function (event) {
                    event.preventDefault();
                    this.setSpaceHover(event.target.id, true);
                },
                spacerDragLeave: function (event) {
                    event.preventDefault();
                    this.setSpaceHover(event.target.id, false);
                },
                dragStart(event) {
                    event.dataTransfer.setData("Text", event.target.id);
                },
                allowDrop(event) {
                    event.preventDefault();
                },
                drop(event) {
                    event.preventDefault();
                    event.stopPropagation()
                    var dragFromData = event.dataTransfer.getData("Text");
                    var dragToData = event.target.id;

                    // id is in format: "group-level-sequenceNo" (group is e.g. products, level = nested level (2 for medicines))
                    let dragFromId = dragFromData.split('-');
                    let dragToId = dragToData.split('-');

                    this.setSpaceHover(dragToData, false);

                    // business rule: ensure you can't drag outside the current nested level, or group
                    if ((dragFromId[0] != dragToId[0] || dragFromId[1] != dragToId[1]) && dragToId[1] !='endoflist') {
                        return;
                    }

                    // stop user dragging to it's own dropzone div (directly above it)
                    if (dragFromId[0] == dragToId[0] && dragFromId[1] == dragToId[1] && dragFromId[2] == dragToId[2]) {
                        return;
                    }

                    // move drag drop target spacer
                    let parentNode = document.getElementById(dragFromData).parentNode;
                    let dragFromNode = document.getElementById(dragFromData + "-spacer");
                    let dragToNode = document.getElementById(dragToData);
                    parentNode.insertBefore(dragFromNode, dragToNode);

                    // move drag drop source div
                    dragFromNode = document.getElementById(dragFromData);
                    dragToNode = document.getElementById(dragToData);
                    parentNode.insertBefore(dragFromNode, dragToNode);
                },
                processDragEvent(event, from, to, canDrag) {
                    var dragFromData = event.dataTransfer.getData("Text");
                    var dragToData = event.target.id;

                    // id is in format: "group-level-sequenceNo" (group is e.g. products, level = nested level (2 for medicines))
                    let dragFromId = dragFromData.split('-');
                    let dragToId = dragToData.split('-');

                    // business rule: ensure you can't drag outside the current nested level, or group
                    if ((dragFromId[0] != dragToId[0] || dragFromId[1] != dragToId[1]) && dragToId[1] != 'endoflist') {
                        canDrag = false;
                    }
                    else {
                        from = dragFromData;
                        to = dragToData;
                        canDrag = true;
                    }
                },
                setSpaceHover(targetId, value) {
                    this.dragTarget = value ? targetId : '';
                },
                calculatePreferenceOrder() {

                    let orderList = [];
                    this.getOrder(this.$refs.product, orderList);
                    this.getOrder(this.$refs.theme, orderList);
                    this.getOrder(this.$refs.articleLanguage, orderList);

                    // geographical location
                    availablePreferences[@((int)NewsCategoryGroup.GeographicalScope)-1].children
                        .filter(x => x.selected == true).forEach(x => { orderList.push(x.id) });

                    return orderList;
                },
                getOrder(nodes, array) {
                    // extract selected preferences from drag-drop DOM nodes
                    if (nodes) {
                        nodes.childNodes.forEach(x => {
                            if (x.childNodes) {
                                this.getOrder(x, array);
                            }

                            if (x.id) {
                                let id = x.id.split('-')[2];
                                if (!array.includes(id) && id && !isNaN(id)) {
                                    array.push(id);
                                }
                            }
                        });
                    }
                },
                getColour(pref) {
                    if (pref.id == @newsCategoryLookup["medicines"]) {
                        return 'category-medicines';
                    }
                    else if (pref.id == @newsCategoryLookup["biocides"]) {
                        return 'category-biocides';
                    }
                    else if (pref.id == @newsCategoryLookup["cosmetics"]) {
                        return 'category-cosmetics';
                    }
                    else if (pref.id == @newsCategoryLookup["nutrition"]) {
                        return 'category-nutrition';
                    }
                    else if (pref.id == @newsCategoryLookup["medical-devices"]) {
                        return 'category-med-devices';
                    }
                    else {
                        return ' category-default';
                    }
                },
                getDayDisplay(day, hour, timezoneOffset) {
                    return this.adminUser ? day : `${day} @@${hour.toString().padStart(2, "0")}:00`;
                },
                savePreferences() {
                    let preferenceOrder = this.calculatePreferenceOrder();
                    let subscriptions = this.getSubscriptions();

                    if (availablePreferences[@((int)NewsCategoryGroup.GeographicalScope)-1].children.filter(x => x.selected == true).length == 0) {
                        plx.toast.show('@ls.LocaliseSafe("select-location")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }

                    if (this.findCategoryByGroupId(@((int)NewsCategoryGroup.NewsletterArticleLanguage)).children.filter(x => x.selected == true).length == 0) {
                        plx.toast.show('@ls.LocaliseSafe("select-article-language")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }

                    if (preferencesNotNested(availablePreferences[@((int)NewsCategoryGroup.Products)-1]).filter(x => x.selected == true).length == 0) {
                        plx.toast.show('@ls.LocaliseSafe("select-one-product")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }

                    if (subscriptions.filter(x => !x.DeliveryLocalHour).length > 0) {
                        plx.toast.show('@ls.LocaliseSafe("(news).subscribe.fill-in-times-for-days")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }

                    if (preferenceOrder.length == 0) {
                        plx.toast.show('@ls.LocaliseSafe("(news).subscribe.one-product-or-theme")', 2, 'failed', null, 5000, { useIcons: true });
                        return;
                    }

                    let url = '/newsletter/subscribe';
                    fetch(url, {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: JSON.stringify({
                            PreferenceOrder: preferenceOrder,
                            Subscriptions: subscriptions
                        })
                    }).then(response => {

                        if (response.ok) {
                            var parent = this;
                            response.json().then(data => {

                                var viewData = JSON.parse(data);

                                this.allSubscriptions = viewData;
                                parent.model.forEach(day => {

                                    let sub = viewData.find(x => x.deliveryLocalDay == day.deliveryLocalDay);
                                    if (sub) {
                                        day.id = sub.id;
                                    }
                                });
                            });
                            plx.toast.show('@ls.LocaliseSafe("(news).subscribe.save-successful")', 2, 'confirm', null, 2500, { useIcons: true });
                        }
                        else {
                            plx.toast.show('@ls.LocaliseSafe("(news).subscribe.problem-saving-preferences")', 2, 'failed', null, 5000, { useIcons: true });
                        }
                    });
                },
                getSubscriptions() {
                    const subs = this.model.filter(x => x.created).map(x => {
                            return {
                                Timezone: this.systemTimezone,
                                TimezoneOffset: this.systemTimezoneOffset,
                                Id: x.id,
                                DeliveryLocalDay: x.deliveryLocalDay,
                                DeliveryLocalHour: x.deliveryLocalHour,
                                Active: true
                            };
                        });

                    if (this.monthlySubscriptionModel.created)
                        subs.push({
                            Timezone: this.systemTimezone,
                            TimezoneOffset: this.systemTimezoneOffset,
                            Id: this.monthlySubscriptionModel.id,
                            DeliveryLocalDay: 1,
                            DeliveryLocalHour: this.userLocalHourRelativeToFranceLocalHour,
                            IsMonthly: true,                            
                            Active: true
                        });

                    if (this.infoflashSubscriptionModel.created)
                        subs.push({
                            Timezone: this.infoflashSubscriptionModel.timezone || systemTimezone,
                            TimezoneOffset: this.infoflashSubscriptionModel.timezoneOffset ?? systemTimezoneOffset,
                            Id: this.infoflashSubscriptionModel.id,
                            DeliveryLocalDay: 1,
                            DeliveryLocalHour: 1,
                            IsInfoflash: true,
                            Active: true
                        });

                    return subs;
                },
                categoriesOrderedByPreference(array) {
                    return array
                        .children
                        .filter(x => x.selected == true || x.children.filter(y => y.selected == true).length > 0)
                        .sort((a, b) => {
                            return a.prefOrder > b.prefOrder ? 1 : -1;
                        });
                },
                findCategoryByGroupId(id) {
                    return this.availablePreferences.find(x => x.groupId == id);
                },
                removePreference(groupId, id) {
                    const currentCategory = this.findCategoryByGroupId(groupId);
                    const preferences = { ...currentCategory };
                    preferences.children = preferences.children.filter(x => x.id !== id);
                    this.updatePreferences(currentCategory, preferences);
                },
                removePreferenceFromGroup(categoryId, id, nestedGroupId) {
                    const preferences = { ...this.availablePreferences.find(x => x.groupId == categoryId).children.find(x => x.id === nestedGroupId) };
                    preferences.children = preferences.children.filter(x => x.id !== id);
                    this.updatePreferences(this.availablePreferences.find(x => x.groupId == categoryId).children.find(x => x.id === nestedGroupId), preferences);
                },
                showNewsletterWarningMessage(){
                    var regularSubscriptions = this.getSubscriptions().filter(x => !x.IsMonthly &&  !x.IsInfoflash);
                    return regularSubscriptions.length === 0;
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/TreeView" />
    <partial name="Components/LocationPreferencesDialog" />
    <partial name="Components/ProductPreferencesDialog" />
    <partial name="Components/ThemePreferencesDialog" />
    <partial name="Components/ArticleLanguagePreferencesDialog" />
}
