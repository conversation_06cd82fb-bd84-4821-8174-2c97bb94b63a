﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleFilterListModel : IModel
    {
        public int Id { get; set; }
        public string FriendlyUrl { get; set; }
        public string Title { get; set; }
        public string Source { get; set; }
        public string SourceUrl { get; set; }
        public int NewsSourceId { get; set; }
        public DateTime SourcePublicationDate { get; set; }
        public DateTime GoLiveDate { get; set; }
        public int GeographicalScopeId { get; set; }
        public string GeographicalScope { get; set; }
        public string TypeOfText { get; set; }
        public string Products { get; set; }
        public List<string> Themes { get; set; } = new List<string>();
        public string SortThemes { get => string.Join(", ", Themes); }
        public int ImportanceId { get; set; }
        public string ImportanceName { get; set; }
    }
}
