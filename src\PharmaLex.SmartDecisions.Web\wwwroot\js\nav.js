﻿plx.nav = {
    init: function () {
        document.querySelectorAll('.main-mega-menu-container .main-sub-nav > li').forEach((x, i) => {
            x.addEventListener('click', (e) => {
                let sibs = e.target.parentElement.children;
                [].forEach.call(sibs, x => x.classList.remove('selected'));
                e.target.classList.add('selected');
            });
            x.firstElementChild.style.top = `-${i * 3}rem`;
        });
    }
};
(function () {
    plx.nav.init();
})();