﻿CREATE TRIGGER [dbo].[CompanyNewsCategory_Insert] ON [dbo].[CompanyNewsCategory]
FOR INSERT AS
INSERT INTO [Audit].[CompanyNewsCategory_Audit] ([AuditAction], [CompanyId], [NewsCategoryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'I', [CompanyId], [NewsCategoryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[CompanyNewsCategory_Update] ON [dbo].[CompanyNewsCategory]
FOR UPDATE AS
INSERT INTO [Audit].[CompanyNewsCategory_Audit] ([AuditAction], [CompanyId], [NewsCategoryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'U', [CompanyId], [NewsCategoryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[CompanyNewsCategory_Delete] ON [dbo].[CompanyNewsCategory]
FOR DELETE AS
INSERT INTO [Audit].[CompanyNewsCategory_Audit] ([AuditAction], [CompanyId], [NewsCategoryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'D', [CompanyId], [NewsCategoryId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO