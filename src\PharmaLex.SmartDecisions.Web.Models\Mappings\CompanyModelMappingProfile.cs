﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class CompanyModelMappingProfile : Profile
    {
        public CompanyModelMappingProfile()
        {
            this.CreateMap<Company, CompanyModel>()
                .ForMember(d => d.CompanyUsers, s => s.MapFrom(x => x.CompanyUser))
                .ForMember(d => d.LicensedTopics, s => s.MapFrom(x => x.CompanyContentType.Select(y => y.ContentTypeId)))
                .ForMember(d => d.LicensedNewsCategories, s => s.MapFrom(x => x.CompanyNewsCategory.Select(y => y.NewsCategoryId)));

            this.CreateMap<CompanyModel, Company>()
                .ForMember(d => d.CompanyUser, o => o.Ignore())
                .ForMember(d => d.CompanyContentType, o => o.Ignore())
                .ForMember(d => d.CompanyNewsCategory, o => o.Ignore())
                .ForMember(d => d.CreatedDate, o => o.Ignore())
                .ForMember(d => d.CreatedBy, o => o.Ignore())
                .ForMember(d => d.LastUpdatedDate, o => o.Ignore())
                .ForMember(d => d.LastUpdatedBy, o => o.Ignore());
        }
    }
}
