﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleCategoryModel : IModel
    {
        public int NewsArticleId { get; set; }
        public int NewsCategoryId { get; set; }

        public NewsCategoryModel NewsCategory { get; set; }
        public NewsArticleModel NewsArticle { get; set; }
    }

    public class NewsArticleCategoryModelMappingProfile : Profile
    {
        public NewsArticleCategoryModelMappingProfile()
        {
            this.CreateMap<NewsArticleCategory, NewsArticleCategoryModel>();
        }
    }
}
