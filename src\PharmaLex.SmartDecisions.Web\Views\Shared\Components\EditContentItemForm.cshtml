﻿<script type="text/x-template" id="text-field-template">
    <div class="form-group">
        <label>
            {{field.name}}
            <template v-if="field.required">
                *
            </template> <i class="icon-info-circled" v-if="field.description" :title="field.description"></i>
        </label>
        <input :name="formElementName" :type="field.type" :value="value" :required="field.required === true" :maxlength="field.length" />
    </div>
</script>

<script type="text/x-template" id="multiline-field-template">
    <div class="form-group">
        <label>
            {{field.name}}
            <template v-if="field.required">
                *
            </template> <i class="icon-info-circled" v-if="field.description" :title="field.description"></i>
        </label>
        <textarea :name="formElementName" :required="field.required === true" :maxlength="field.length">{{value}}</textarea>
    </div>
</script>

<script type="text/x-template" id="bool-field-template">
    <div class="form-group">
        <label>{{field.name}}<i class="icon-info-circled" v-if="field.description" :title="field.description"></i></label>
        <label class="switch-container">
            No
            <input :id="formElementName" :name="formElementName" type="checkbox" :checked="value === 'true'" class="switch" />
            <label :for="formElementName" class="switch">{{formElementName}}</label>
            Yes
        </label>
    </div>
</script>

<script type="text/x-template" id="picklist-field-template">
    <div class="form-group">
        <label>
            {{field.name}}
            <template v-if="field.required">
                *
            </template> <i class="icon-info-circled" v-if="field.description" :title="field.description"></i>
        </label>
        <div :class="field.multiSelect === true ? 'multi-select-wrapper' : 'select-wrapper'">
            <div :class="field.multiSelect !== true ? 'custom-select' : ''">
                <select :name="formElementName" :required="field.required === true" :multiple="field.multiSelect === true">
                    <option value="" :selected="value == 0" v-if="field.multiSelect !== true">Select {{field.name}}</option>
                    <option v-for="p in picklists.find(x => x.contentTypeId == field.relatedContentTypeId).items" :value="p.id" :selected="(value || '').split('|').indexOf(p.id.toString()) > -1">{{p.name}}</option>
                </select>
            </div>
        </div>
    </div>
</script>

<script type="text/x-template" id="relationship-field-template">
    <div class="form-group">
        <label>
            {{field.name}}
            <template v-if="field.required">
                *
            </template> <i class="icon-info-circled" v-if="field.description" :title="field.description"></i>
        </label>
        <div :class="field.multiSelect === true ? 'multi-select-wrapper' : 'select-wrapper'">
            <div :class="field.multiSelect !== true ? 'custom-select' : ''">
                <select :name="formElementName" :required="field.required === true" :multiple="field.multiSelect === true">
                    <option value="" :selected="value == 0" v-if="field.multiSelect !== true">Select {{field.name}}</option>
                    <option v-for="r in relationships.find(x => x.contentTypeId == field.relatedContentTypeId).items" :value="r.id" :selected="(value || '').split('|').indexOf(r.id.toString()) > -1">{{r.name}}</option>
                </select>
            </div>
        </div>
    </div>
</script>

<script type="text/x-template" id="edit-field-template">
    <text-field v-if="['text', 'number', 'email', 'url', 'date'].indexOf(field.type) > -1" :field="field" :value="value" />
    <multiline-field v-else-if="field.type === 'multiline'" :field="field" :value="value" />
    <bool-field v-else-if="field.type === 'bool'" :field="field" :value="value" />
    <picklist-field v-else-if="field.type === 'picklist'" :field="field" :value="value" :picklists="picklists" />
    <relationship-field v-else-if="field.type === 'relationship'" :field="field" :value="value" :relationships="relationships" />
</script>

<script type="text/javascript">
    var fieldConfig = {
        props: {
            field: {
                required: true,
                type: Object
            },
            value: {
                type: String
            }
        },
        computed: {
            formElementName: function () {
                return `f${this.field.id}`
            }
        }
    };

    vueApp.component('text-field', {
        template: '#text-field-template',
        mixins: [fieldConfig]
    });

    vueApp.component('multiline-field', {
        template: '#multiline-field-template',
        mixins: [fieldConfig]
    });

    vueApp.component('bool-field', {
        template: '#bool-field-template',
        mixins: [fieldConfig]
    });

    vueApp.component('picklist-field', {
        template: '#picklist-field-template',
        props: {
            picklists: {
                required: true,
                type: Array
            }
        },
        mixins: [fieldConfig]
    });

    vueApp.component('relationship-field', {
        template: '#relationship-field-template',
        props: {
            relationships: {
                required: true,
                type: Array
            }
        },
        mixins: [fieldConfig]
    });

    vueApp.component('edit-field', {
        template: '#edit-field-template',
        props: {
            picklists: {
                required: true,
                type: Array
            },
            relationships: {
                required: true,
                type: Array
            }
        },
        mixins: [fieldConfig]
    });
</script>