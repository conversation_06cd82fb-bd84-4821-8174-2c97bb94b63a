﻿using Azure.Search.Documents;
using Azure;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers.HealthChecks
{
    public class AzureSearchHealthCheck : IHealthCheck
    {
        private readonly Uri _endpoint;
        private readonly string _indexName;
        private readonly string _authKey;

        private readonly ConcurrentDictionary<string, SearchClient> _connections = new();

        public AzureSearchHealthCheck(Uri endpoint, string indexName, string authKey)
        {
            _endpoint = endpoint ?? throw new ArgumentNullException(nameof(endpoint));
            _indexName = indexName ?? throw new ArgumentNullException(nameof(indexName));
            _authKey = authKey ?? throw new ArgumentNullException(nameof(authKey));
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var searchClient = _connections.GetOrAdd(GetCacheKey(), CreateSearchClient());

                _ = await searchClient.GetDocumentCountAsync(cancellationToken).ConfigureAwait(false);

                return HealthCheckResult.Healthy();
            }
            catch (Exception ex)
            {
                return new HealthCheckResult(context.Registration.FailureStatus, exception: ex);
            }
        }

        private SearchClient CreateSearchClient()
        {
            var credential = new AzureKeyCredential(_authKey);

            return new SearchClient(_endpoint, _indexName, credential);
        }

        private string GetCacheKey() => $"{_endpoint}_{_indexName}";
    }
}
