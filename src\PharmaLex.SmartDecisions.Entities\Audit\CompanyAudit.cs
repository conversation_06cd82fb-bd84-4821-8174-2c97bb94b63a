﻿using System;
using PharmaLex.DataAccess;

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class CompanyAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public string PrimaryContactName { get; set; }
        public string PrimaryContactEmail { get; set; }
        public string PrimaryContactAddress { get; set; }
        public string PrimaryContactPhone { get; set; }
        public int? MaximumUsersCount { get; set; }
        public int? MaximumActiveUsersCount { get; set; }
    }
}
