﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Authentication.B2C;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "RecordOwner")]
    public class MyRecordsController : BaseController
    {
        private readonly IContentItemRepository _contentItemRepository;
        private readonly IMapper _mapper;

        public MyRecordsController(IContentItemRepository contentItemRepository, IMapper mapper)
        {
            _contentItemRepository = contentItemRepository;
            _mapper = mapper;
        }

        [HttpGet("/records")]
        public async Task<IActionResult> Records(bool expired = false)
        {
            var items = _contentItemRepository.GetQueryableItems(x => x.Include(y => y.ContentType)).Where(x =>
                x.ContentType.ContentTypeCategoryId == (int)ContentTypeCategory.Topic && x.Owner.ToLower() ==
                ((ClaimsIdentity)this.User.Identity).GetEmail().ToLower());
            if (expired)
            {
                items = items.Where(x => x.VerifiedDate < DateTime.Now.AddDays(-180));
            }

            var itemsList = await items.ToListAsync();
            this.ViewData["expired"] = expired;
            return View("/Views/ContentItem/Records.cshtml", _mapper.Map<List<ContentItemModel>>(itemsList));
        }
    }
}
