﻿<script type="text/x-template" id="searchlist-template">
    <div>
        <div class="search-wrapper">
            <div class="clear" v-if="selectedName">
                <i class="m-icon" @@click="clear" :title="resources.clearSelectionText">clear</i>
            </div>
            <input type="text" :id="inputId"
                   :list="datalistUid"
                   v-model="selectedName"
                   v-on:change="processChange"
                   v-on:blur="processChange"
                   :placeholder="resources.placeholderText"
                   :required="config.required" autocomplete="off" />
        </div>
        <datalist :id="datalistUid">
            <option v-for="o in listOptions">{{o.name}}</option>
        </datalist>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('searchlist', {
        template: '#searchlist-template',
        data() {
            return {
                listOptions: [...this.options.map(x => {
                    return { ...x };
                })],
                datalistUid: `datalist-${this.id || this._uid}`,
                inputId: this.id,
                selectedName: this.modelValue?.name,
                resources: this.config.resources
            };
        },
        props: {
            options: {
                type: Array,
                default: () => []
            },
            config: {
                type: Object,
                default: () => { }
            },
            modelValue: {
                type: Object,
                default: () => { }
            },
            id: {
                type: String,
                default: ''
            }
        },
        methods: {
            clear() {
                this.selectedName = null;
                this.processChange();
            },
            processChange() {
                const selectedOption = this.listOptions.find(x => x.name === this.selectedName);
                this.selectedName = selectedOption?.name;
                this.$emit('update:modelValue', selectedOption);
            }
        },
        watch: {
            modelValue() {
                this.selectedName = this.modelValue?.name;
            },
        },
    });
</script>
