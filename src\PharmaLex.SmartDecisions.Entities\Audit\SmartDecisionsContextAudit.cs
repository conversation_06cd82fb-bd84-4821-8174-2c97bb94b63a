﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities.Audit;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class SmartDecisionsContext
    {
        public virtual DbSet<ClaimAudit> ClaimAudit { get; set; }
        public virtual DbSet<CompanyAudit> CompanyAudit { get; set; }
        public virtual DbSet<CompanyContentTypeAudit> CompanyContentTypeAudit { get; set; }
        public virtual DbSet<CompanyNewsCategoryAudit> CompanyNewsCategoryAudit { get; set; }
        public virtual DbSet<CompanyUserAudit> CompanyUserAudit { get; set; }
        public virtual DbSet<ContentItemAudit> ContentItemAudit { get; set; }
        public virtual DbSet<ContentTypeAudit> ContentTypeAudit { get; set; }
        public virtual DbSet<ContentTypeDisplayAudit> ContentTypeDisplayAudit { get; set; }
        public virtual DbSet<FieldAudit> FieldAudit { get; set; }
        public virtual DbSet<FieldValueAudit> FieldValueAudit { get; set; }
        public virtual DbSet<UserAudit> UserAudit { get; set; }
        public virtual DbSet<UserClaimAudit> UserClaimAudit { get; set; }
        public virtual DbSet<MultilingualResourceAudit> MultilingualResourceAudit { get; set; }
        public virtual DbSet<LocaleAudit> LocaleAudit { get; set; }
        public virtual DbSet<NewsArticleContentAudit> NewsArticleContentAudit { get; set; }
        public virtual DbSet<NewsArticleAudit> NewsArticleAudit { get; set; }
        public virtual DbSet<NewsSourceAudit> NewsSourceAudit { get; set; }
        public virtual DbSet<NewsCategoryAudit> NewsCategoryAudit { get; set; }
        public virtual DbSet<NewsArticleCategoryAudit> NewsArticleCategoryAudit { get; set; }
        public virtual DbSet<NewsletterSubscriptionAudit> NewsletterSubscriptionAudit { get; set; }
        public virtual DbSet<NewsletterAudit> NewsletterAudit { get; set; }
        public virtual DbSet<UserNewsCategory> UserNewsCategoryAudit { get; set; }
        public virtual DbSet<AzureBlobAudit> AzureBlobAudit { get; set; }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder)
        {
            modelBuilder.AuditEntityBase<UserNewsCategoryAudit>();

            modelBuilder.AuditEntityBase<NewsletterAudit>(entity => 
            {
                entity.Property(e => e.CreatedDateUtc)
                    .HasColumnType("datetime");
                
                entity.Property(e => e.UniqueKey)
                    .HasMaxLength(32);
            });
            
            modelBuilder.AuditEntityBase<MultilingualResourceAudit>(entity =>
            {
                entity.Property(e => e.Key)
                    .HasMaxLength(1024);
            });

            modelBuilder.AuditEntityBase<LocaleAudit>(entity =>
            {
                entity.Property(e => e.Name)
                    .HasMaxLength(256);

                entity.Property(e => e.LocalisedName)
                    .HasMaxLength(256);

                entity.Property(e => e.IsoLanguageCode)
                    .HasColumnType("nchar(2)");
                entity.Property(e => e.IsoScriptCode)
                    .HasColumnType("nchar(4)");
                entity.Property(e => e.IsoCountryCode)
                    .HasColumnType("nchar(2)");
            });

            modelBuilder.AuditEntityBase<NewsArticleContentAudit>(entity =>
            {
                entity.Property(e => e.PublishingStateDateUtc)
                    .HasColumnType("datetime");

                entity.Property(e => e.Title)
                    .HasMaxLength(256);
                    
                entity.Property(e => e.SourceUrl)
                    .HasMaxLength(256);

                entity.Property(e => e.ImpactAssessmentSummary)
                    .HasMaxLength(256);

                entity.Property(e => e.FriendlyUrl)
                    .HasMaxLength(300);
            });

            modelBuilder.AuditEntityBase<NewsArticleAudit>();

            modelBuilder.AuditEntityBase<NewsletterSubscriptionAudit>(entity =>
            {
                entity.Property(e => e.CreatedDateUtc)
                    .HasColumnType("datetime");

                entity.Property(e => e.Timezone)
                    .HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<NewsSourceAudit>(entity =>
            {
                entity.Property(e => e.Name)
                    .HasMaxLength(256);
                entity.Property(e => e.Url)
                    .HasMaxLength(256);
                entity.Property(e => e.LocalisationKey)
                    .HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<NewsCategoryAudit>(entity =>
            {
                entity.Property(e => e.Name)
                    .HasMaxLength(256);
                entity.Property(e => e.LocalisationKey)
                    .HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<NewsArticleCategoryAudit>();

            modelBuilder.AuditEntityBase<ClaimAudit>(entity =>
            {
                entity.Property(e => e.ClaimType).HasMaxLength(32);

                entity.Property(e => e.Name).HasMaxLength(1024);
            });

            modelBuilder.AuditEntityBase<CompanyAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.PrimaryContactAddress).HasMaxLength(256);

                entity.Property(e => e.PrimaryContactEmail).HasMaxLength(128);

                entity.Property(e => e.PrimaryContactName).HasMaxLength(256);

                entity.Property(e => e.PrimaryContactPhone).HasMaxLength(128);
            });

            modelBuilder.AuditEntityBase<CompanyContentTypeAudit>();
            modelBuilder.AuditEntityBase<CompanyNewsCategoryAudit>();
            modelBuilder.AuditEntityBase<CompanyUserAudit>();

            modelBuilder.AuditEntityBase<ContentItemAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Owner).HasMaxLength(256);

                entity.Property(e => e.VerifiedDate).HasColumnType("datetime");
            });

            modelBuilder.AuditEntityBase<ContentTypeAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Owner).HasMaxLength(256);

                entity.Property(e => e.PluralName).HasMaxLength(256);

                entity.Property(e => e.ShortName).HasMaxLength(31);
            });

            modelBuilder.AuditEntityBase<ContentTypeDisplayAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<FieldAudit>(entity =>
            {
                entity.Property(e => e.Description).HasMaxLength(1024);

                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<FieldValueAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<UserAudit>(entity =>
            {
                entity.Property(e => e.Email).HasMaxLength(512);

                entity.Property(e => e.FamilyName).HasMaxLength(1024);

                entity.Property(e => e.GivenName).HasMaxLength(1024);

                entity.Property(e => e.LastLoginDate).HasColumnType("datetime");
            });

            modelBuilder.AuditEntityBase<AzureBlobAudit>(e => {

                e.Property(e => e.Container).HasMaxLength(128);

                e.Property(e => e.ContentType).HasMaxLength(256);

                e.Property(e => e.Name).HasMaxLength(512);

                e.Property(e => e.Uri).HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<UserClaimAudit>();
        }

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder.Conventions.Add(_ => new BlankTriggerAddingConvention());
        }
    }
}
