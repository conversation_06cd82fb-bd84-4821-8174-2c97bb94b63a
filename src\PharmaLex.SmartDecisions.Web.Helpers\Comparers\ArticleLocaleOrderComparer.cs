﻿using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public class ArticleLocaleOrderComparer : IComparer<int>
    {
        private readonly int def;
        private readonly int cur;
        public ArticleLocaleOrderComparer(int cur, int def)
        {
            this.def = def;
            this.cur = cur;
        }

        public int Compare(int x, int y)
        {
            if (x == y) return 0;

            if (x == cur) return -1;
            if (y == cur) return 1;

            if (x == def) return -1;
            if (y == def) return 1;

            return 0;
        }
    }
}
