﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class NotificationController : BaseController
    {
        protected readonly IDistributedCacheServiceFactory cacheFactory;
        protected readonly IMapper mapper;
        private readonly IConfiguration configuration;
        private readonly IRepositoryFactory repoFactory;

        public NotificationController(IDistributedCacheServiceFactory cacheFactory, IMapper mapper, IConfiguration configuration, IRepositoryFactory repoFactory)
        {
            this.cacheFactory = cacheFactory;
            this.mapper = mapper;
            this.configuration = configuration;
            this.repoFactory = repoFactory;
        }

        [HttpGet("/notification/{key}/record-expiry"), AllowAnonymous, NotificationKeyRequired]
        public async Task<IActionResult> RecordExpiry(string key)
        {
            var cir = this.repoFactory.Create<ContentItem>();
            var expiring = await cir.Configure().Where(x => x.ContentType.ContentTypeCategoryId == (int)ContentTypeCategory.Topic && x.VerifiedDate < DateTime.Today.AddDays(-180)).ToListAsync();
            var owners = expiring.GroupBy(x => x.Owner);

            string apiKey = this.configuration.GetConnectionString("SendGrid");
            string senderEmail = this.configuration.GetValue<string>("AppSettings:SystemAdminEmail");
            string templateId = this.configuration.GetValue<string>("AppSettings:RecordExpirationNotificationTemplateId");
            var sendGridClient = new SendGridClient(apiKey);

            var uc = this.cacheFactory.CreateEntity<User>();            
            foreach (var owner in owners)
            {
                var msg = new SendGridMessage();
                msg.SetFrom(new EmailAddress(senderEmail, "SMARTDECISIONS Team"));
                var u = await uc.FirstOrDefaultAsync(x => x.Email.ToLower() == owner.Key.ToLower());
                msg.AddTo(owner.Key, u?.FullName ?? owner.Key);
                msg.SetTemplateId(templateId);
                var data = new
                {
                    Name = u?.FullName,
                    RecordCount = owner.Count(),
                    PriorityRecordCount = owner.Count(x => x.VerifiedDate < DateTime.Today.AddDays(-360)),
                    Url = $"{this.Request.Scheme}://{this.Request.Host}/records?expired=true"
                };
                msg.SetTemplateData(data);
                if(data.PriorityRecordCount > 0)
                {
                    msg.AddHeader("Importance", "High");
                }
                await sendGridClient.SendEmailAsync(msg);
            }
            return Json(true);
        }
    }
}
