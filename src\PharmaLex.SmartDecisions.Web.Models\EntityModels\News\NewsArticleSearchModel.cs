﻿using System;
using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleSearchModel : IModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public List<int> GeographicalScopes { get; set; } = new List<int>();
        public List<int> Products { get; set; } = new List<int>();
        public List<int> Themes { get; set; } = new List<int>();
        public List<int> TypesOfText { get; set; } = new List<int>();
        public List<int> NewsSources { get; set; } = new List<int>();
        public List<int> NewsletterTypes { get; set; } = new List<int>();
        public List<int> Languages { get; set; } = new List<int>();
        public string SearchText { get; set; } = string.Empty;
        public int LocaleId { get; set; }
        public bool IsOnlyTitleSearch { get; set; } = false;
        public List<NewsArticleFilterListModel> Results { get; set; } = new List<NewsArticleFilterListModel>();
    }
}
