﻿@model CompanyModel
@using PharmaLex.SmartDecisions.Entities
@using Newtonsoft.Json;
@using PharmaLex.Authentication.B2C
@using PharmaLex.SmartDecisions.Web.Helpers
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory cache
@inject INewsCategoryLicenseHelper categoryHelper
@{
    ViewData["Title"] = "Edit Company";

    var contentTypeCache = cache.CreateMappedEntity<ContentType, ContentTypeModel>();
    var topics = await contentTypeCache.WhereAsync(x => x.ContentTypeCategoryId == (int)ContentTypeCategory.Topic);

    var geographicalCategories = await categoryHelper.GetNewsCategoryGroupLicense(NewsCategoryGroup.GeographicalScope);
    var productCategories = await categoryHelper.GetNewsCategoryGroupLicense(NewsCategoryGroup.Products);
}
<div id="company-app">
    <div class="sub-header">
        <h2>@Model.Name</h2>
        <div class="controls">
            <a class="button icon-button-delete" href="/manage/company/delete/@Model.Id">Delete</a>
        </div>
    </div>


    <form id="company-form" method="post">
        
        <div class="flex flex-nowrap gapped-2 mt-2">
            <div class="flex-item flex-67-percent tile gap-compensate">
                <h5>Details</h5>
                <div class="flex flex-nowrap">
                    <div class="flex-item flex-x2 pr-2">
                        <div class="form-group">
                            <label asp-for="Name">Name*</label>
                            <input type="text" asp-for="Name" required autocomplete="no-thanks" />
                            <span asp-validation-for="Name" class="error-color"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="PrimaryContactName">Contact name</label>
                            <input type="text" asp-for="PrimaryContactName" autocomplete="no-thanks" />
                            <span asp-validation-for="PrimaryContactName" class="error-color"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="PrimaryContactEmail">Contact email</label>
                            <input type="text" asp-for="PrimaryContactEmail" autocomplete="no-thanks" />
                            <span asp-validation-for="PrimaryContactEmail" class="error-color"></span>
                        </div>
                    </div>
                    <div class="flex-item flex-x2 pl-2">
                        <div class="form-group">
                            <label asp-for="PrimaryContactAddress">Contact address</label>
                            <input type="text" asp-for="PrimaryContactAddress" autocomplete="no-thanks" />
                            <span asp-validation-for="PrimaryContactAddress" class="error-color"></span>
                        </div>
                        <div class="form-group">
                            <label asp-for="PrimaryContactPhone">Contact phone</label>
                            <input type="text" asp-for="PrimaryContactPhone" autocomplete="no-thanks" />
                            <span asp-validation-for="PrimaryContactPhone" class="error-color"></span>
                        </div>
                    </div>
                </div>
               
               
            </div>
            <div class="flex-item flex-33-percent tile">
                <h5>License</h5>
                <div class="form-group">
                    <label asp-for="MaximumUsersCount">Maximum users</label>
                    <input type="number" asp-for="MaximumUsersCount" v-model="maxUsersCount" min="1" required autocomplete="no-thanks" />
                </div>
                <div class="form-group">
                    <label asp-for="MaximumActiveUsersCount">Maximum active users</label>
                    <input type="number" asp-for="MaximumActiveUsersCount" :max="maxUsersCount" min="1" required autocomplete="no-thanks" />
                </div>
            </div>

            </div>

            <div class="flex flex-nowrap gapped-2 mt-2">
                <div class="flex-item flex-33-percent tile">
                    <h5>Licensed Topics</h5>
                    <template v-for="topic in topics">
                        <label>{{topic.name}}</label>
                        <label class="switch-container">
                            No
                            <input :id="`Topic${topic.id}Licensed`" type="checkbox" class="switch" :checked="licensedTopics.indexOf(topic.id) > -1" v-on:change="licensedTopicChanged(topic.id)" />
                            <label :for="`Topic${topic.id}Licensed`" class="switch">Topic is licensed?</label>
                            Yes
                        </label>
                    </template>
                </div>


                <div class="flex-item flex-33-percent tile">
                    <h5>Licensed Geographical Scope</h5>
                    <template v-for="category, i in geographicalCategories">
                        <label>{{category.name}}</label>
                        <label class="switch-container">
                            No
                            <input :id="`Category${category.id}Licensed`" type="checkbox" class="switch" v-model="geographicalCategoriesModel[i].checked" />
                            <label :for="`Category${category.id}Licensed`" class="switch">Category is licensed?</label>
                            Yes
                        </label>
                    </template>
                </div>
                <div class="flex-item flex-33-percent tile">
                    <h5>Licensed Products</h5>
                    <template v-for="category, i in productCategories">
                        <label>{{category.name}}</label>
                        <label class="switch-container">
                            No
                            <input :id="`Category${category.id}Licensed`" type="checkbox" class="switch" v-model="productCategoriesModel[i].checked" />
                            <label :for="`Category${category.id}Licensed`" class="switch">Category is licensed?</label>
                            Yes
                        </label>
                    </template>

                </div>
            </div>



            <div class="buttons no-border mt-2 p-2">
                <a class="button secondary" href="/manage/companies">Cancel</a>
                <button type="submit">Save</button>
            </div>
            <input type="hidden" asp-for="Id" />
            <template v-for="id, index in licensedTopics">
                <input type="hidden" :name="`LicensedTopics[${index}]`" :value="id" />
            </template>
            <input v-for="c, i in [...geographicalCategoriesModel, ...productCategoriesModel].filter(x => x.checked)" :name="`LicensedNewsCategories[${i}]`" :value="c.id" type="hidden" />
</form>
    @if (Model.Id > 0)
    {

        <h3 class="mt-4">Users</h3>
        <filtered-table id="usersTable" :items="users" :columns="userColumns" :filters="userFilters" link="/company/user/edit/" addurl="/company/user/new/@Model.Id"></filtered-table>
    }
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#company-app',
            data: function () {
                let model = @Html.Raw(Model.ToJson());
                const productCategories = @Html.Raw(productCategories.ToJson());
                const geographicalCategories = @Html.Raw(geographicalCategories.ToJson());
                return {
                    maxUsersCount: @(Model.MaximumUsersCount ?? 0),
                    topics: @Html.Raw(topics.ToJson()),
                    licensedTopics: model.licensedTopics,
                    productCategories,
                    geographicalCategories,
                    licensedNewsCategories: model.licensedNewsCategories,
                    productCategoriesModel: productCategories.map(x => {
                        return {
                            checked: model.licensedNewsCategories.includes(x.id),
                            id: x.id
                        };
                    }),
                    geographicalCategoriesModel: geographicalCategories.map(x => {
                        return {
                            checked: model.licensedNewsCategories.includes(x.id),
                            id: x.id
                        };
                    }),
                    users: model.companyUsers.map(x => {
                        x.activeF = [x.active ? 1 : -1];
                        return x;
                    }),
                    userColumns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'displayFullName',
                                sortKey: 'displayFullName',
                                header: 'Name',
                                type: 'text',
                                style: 'width: 35%;'
                            },
                            {
                                dataKey: 'email',
                                sortKey: 'email',
                                header: 'ID',
                                type: 'text',
                                style: 'width: 35%;'
                            },
                            {
                                dataKey: 'active',
                                sortKey: 'active',
                                header: 'Enabled',
                                type: 'bool',
                                style: 'width: 130px;'
                            }
                        ]
                    },
                    userFilters: [
                        {
                            key: 'displayFullName',
                            options: [],
                            type: 'search',
                            header: 'Search Name',
                            fn: v => p => p.displayFullName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'email',
                            options: [],
                            type: 'search',
                            header: 'Search ID',
                            fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'active',
                            options: [{ name: 'Active', value: 1 }, { name: 'Inactive', value: -1 }],
                            filterCollection: 'activeF',
                            display: 'name',
                            dataKey: 'value',
                            type: 'select',
                            header: 'Filter By Active',
                            fn: v => p => (p.active ? 1 : -1) === v,
                            convert: v => parseInt(v)
                        }
                    ]
                }
            },
            methods: {
                licensedTopicChanged(id) {
                    var index = this.licensedTopics.indexOf(id);
                    if (index > -1) {
                        this.licensedTopics.splice(index, 1);
                    } else {
                        this.licensedTopics.push(id);
                    }
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}