﻿vueApp.component('collection-cell', {
    template: '#collection-cell-template',
    props: {
        val: {
            required: true,
            type: Array
        }
    },
    computed: {
        tipContent() {
            return `<ul>
                ${this.val.slice(1, this.val.length)
                    .map(x => `<li>${x}</li>`).join(' ')
                }</ul>`;
        }
    }
});
vueApp.component('text-cell', {
    template: '#text-cell-template',
    props: {
        val: String
    }
});
vueApp.component('chips-cell', {
    template: '#chips-cell-template',
    props: {
        val: Array
    },
    computed: {
        trimVals() {

            return Array.isArray(this.val) ?
                this.val.map(v => {
                    const o = {};
                    o.originalValue = v;
                    let trimVal = v.length > 7 ? v.substring(0, 7) + ' ...' : v;
                    o.trimValue = trimVal;
                    return o;
                })
                : this.val;
        }
    }
});
vueApp.component('text-edit-select-cell', {
    template: '#text-edit-select-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object
    },
    methods: {
        onChange(e) {
            if (this.invalid)
                e.target.reportValidity();
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});
vueApp.component('text-edit-plain-cell', {
    template: '#text-edit-plain-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object,
        inputType: {
            type: String,
            default: 'text'
        }
    },
    methods: {
        onChange(e) {
            if (this.invalid)
                e.target.reportValidity();
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});
vueApp.component('link-edit-plain-cell', {
    template: '#text-edit-plain-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object,
        inputType: {
            type: String,
            default: 'url'
        }
    },
    methods: {
        onChange(e) {
            if (this.invalid) {
                e.target.reportValidity();
            }
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});
vueApp.component('date-edit-plain-cell', {
    template: '#text-edit-plain-cell-template',
    data() {
        return {
            invalid: this.data.invalid
        }
    },
    props: {
        data: Object,
        inputType: {
            type: String,
            default: 'date'
        }
    },
    methods: {
        onChange(e) {
            if (this.invalid) {
                e.target.reportValidity();
            }
        },
        onInput(e) {
            this.data.invalid = this.invalid = this.data.required && !e.target.checkValidity();
        }
    }
});
vueApp.component('link-cell', { template: '<td><a class="action-link" :href="href" target="_blank" v-on:click.stop>{{val}}</a></td>', props: ['val', 'href'] });
vueApp.component('multiline-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('html-cell', { template: '<td v-html="val"></td>', props: ['val'] });
vueApp.component('number-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('email-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('url-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('date-cell', { template: '<td>{{convert ? convert(val) : val}}</td>', props: ['val', 'convert'] });
vueApp.component('picklist-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('relationship-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('bool-cell', {
    template: '#bool-cell-template',
    props: {
        val: Boolean
    }
});
vueApp.component('select-filter', {
    template: '#select-filter-template',
    data: function () {
        return {
            isOpen: false,
            selectedIndex: -1,
            textField: this.config.display,
            dataField: this.config.dataKey,
            strings: {
                ...this.resources
            }
        };
    },
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        },
        resources: Object
    },
    computed: {
        availableOptions: function () {
            return [...new Set(this.filtered.map(p => p[this.config.filterCollection]).flat())]
                .map(i => {
                    if (this.textField === this.dataField) {
                        return {
                            key: i === null ? '-' : i,
                            value: i === null ? this.strings.noValue : i
                        };
                    }

                    const v = this.config.options.find(x => x[this.dataField] === i);
                    return {
                        key: i === null ? '-' : i,
                        value: v ? v[this.textField] : this.strings.noValue
                    };
                })
                .sort((a, b) => a.value.localeCompare(b.value, 'en', { sensitivity: 'base' }));
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;

            if (this.isOpen)
                this.selectedIndex = this.value ? 0 : -1;
            this.$nextTick(function () {
                this.$refs.select.focus();
            });
        },
        change(val) {
            this.isOpen = false;

            this.$emit('input', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {
            if (!this.$el.contains(evt.target)) {
                this.isOpen = false;
                this.selectedIndex = -1;
            }
        },
        onArrowDown() {
            if (this.isOpen && this.selectedIndex < this.filteredItems.length - 1) {
                this.selectedIndex = this.selectedIndex + 1;
            }
        },
        onArrowUp() {
            if (this.isOpen && this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('select-multiple-filter', {
    template: '#select-multiple-filter-template',
    data: function () {
        return {
            isOpen: false,
            textField: this.config.display,
            dataField: this.config.dataKey,
            strings: {
                ...this.resources
            }
        };
    },
    name: 'select-multiple-filter',
    props: {
        modelValue: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        },
        resources: Object
    },
    computed: {
        availableOptions: function () {
            return this.value ? this.value.map(v => {
                return {
                    key: v.key,
                    value: v.value,
                    selected: v.selected
                }
            }) : this.config.options.map(i => {
                return {
                    key: i === null ? '-' : i[this.dataField],
                    value: i ? i[this.textField] : this.strings.noValue
                }
            })
            .sort((a, b) => a.value.localeCompare(b.value, 'en', { sensitivity: 'base' }));
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;

            this.$nextTick(function () {
                this.$refs.select.focus();
            });
        },
        change(val) {

            if (val) {
                var option = this.availableOptions.find(o => o.value === val);
                option.selected = !option.selected;

                this.$emit('update:modelValue', this.availableOptions.every(o => !o.selected) ? '' : this.availableOptions);
                this.$emit('filter', { key: this.config.key });
            }
            else {
                this.$emit('update:modelValue', val);
                this.$emit('filter', { key: this.config.key });
            }

        },
        handleClickOutside(evt) {

            var dropdownElement = Array.from(document.querySelectorAll('.table-filter-items')).find(d => d.style.display !== 'none');

            if (!this.$el.contains(evt.target) && dropdownElement && !dropdownElement.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        onArrowDown() {
            if (this.isOpen && this.selectedIndexes[0] < this.filteredItems.length - 1) {
                this.selectedIndexes[0] = this.selectedIndexes[0] + 1;
            }
        },
        onArrowUp() {
            if (this.isOpen && this.selectedIndexes[0] > 0) {
                this.selectedIndexes[0] = this.selectedIndexes[0] - 1;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('search-filter', {
    template: '#search-filter-template',
    data() {
        return {
            isOpen: false
        };
    },
    props: {
        modelValue: {
            default: '',
            type: String
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;
            if (this.isOpen)
                this.$nextTick(function () {
                    this.$refs.search.focus();
                });
        },
        change(val) {
            this.$emit('update:modelValue', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {
            if (!this.$el.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('date-filter', {
    template: '#date-filter-template',
    data() {
        return {
            isOpen: false
        };
    },
    props: {
        modelValue: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;
            if (this.isOpen)
                this.$nextTick(function () {
                    this.$refs.date.focus();
                });
        },
        change(val) {          
            this.$emit('update:modelValue', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {
            if (!this.$el.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('data-table-pager', {
    template: '#data-table-pager-template',
    data() {
        return {
            strings: {
                ...this.resources
            },
            localTypedPageIndex: this.typedPageIndex
        };
    },
    props: {
        pageIndex:
        {
            required: true,
            type: Number
        },
        pageSize:
        {
            required: true,
            type: Number
        },
        typedPageIndex:
        {
            required: true,
            type: Number
        },
        location: {
            type: String,
            default: 'bottom'
        },
        totalItems: {
            type: Number,
            required: true
        },
        filteredCount: Number,
        resources: Object
    },
    computed: {
        startIndex() {
            return this.pageSize * this.pageIndex;
        },
        endIndex() {
            return Math.min(parseInt(this.startIndex) + parseInt(this.pageSize), this.filteredCount);
        },
        pageCount() {
            let extra = this.filteredCount % this.pageSize ? 1 : 0;
            return Math.floor(this.filteredCount / this.pageSize) + extra;
        },
        isDisabled() {
            return this.pageCount === 0 || this.pageIndex === 0;
        }
    },
    watch: {
        typedPageIndex(newVal) {
            this.localTypedPageIndex = newVal;
        }
    },
    methods: {
        interpolate(format, totalCount, startIndex, endIndex) {
            if (format) {
                return `${format} ${totalCount}`;
            } else {
                return `${startIndex}-${endIndex} of ${totalCount}`;
            }
        },  
        pageSizeChange(size) {
            this.$emit('page-size-change', +size);
        },
        emitPageIndexChange() {
            this.$emit('page-index-change', this.localTypedPageIndex);
        },
    }
});
vueApp.component('filtered-table', {
    template: '#filtered-table-template',
    data: function () {
        let sortConfig = this.columns.config.map(col => {
            return {
                sortKey: col.sortKey,
                sortDirection: col.sortDirection,
                sortComparer: col.sortComparer || (this.items[0] ? typeof this.items[0][col.sortKey] : null)
            };
        });

        return {
            data: this.items.map(x => {
                return { ...x };
            }),
            selectedItems: [],
            size: this.pageSize,
            pageIndex: 0,
            typedPageIndex: 1,
            filterModel: this.filters.reduce((acc, val) => {
                acc[val.key] = '';
                return acc;
            }, {}),
            appliedFilters: {},
            sortConfig,
            sortMode: this.columns.sortMode || 'single',
            appliedSorts: sortConfig.reduce((acc, val) => {
                if (val.sortKey && val.sortDirection) {
                    acc.push({
                        key: val.sortKey,
                        comparer: this.getComparer(val.sortKey, val.sortDirection, val.sortComparer)
                    });
                }
                return acc;
            }, []),
            sortModel: this.columns.config.reduce((acc, c) => {
                if (c.sortKey)
                    acc[c.sortKey] = c.sortDirection || 0;
                return acc;
            }, {}),
            internalFilters: this.filters.map(f => {
                let fi = { ...f };

                fi.dataKey = fi.dataKey ? fi.dataKey : (fi.options[0] || {}).hasOwnProperty('id') ? 'id' : 'Id';
                fi.display = fi.display ? fi.display : (fi.options[0] || {}).hasOwnProperty('name') ? 'name' : 'Name';

                fi.convert = fi.convert || (v => v);
                fi.fn = fi.fn || (v => p => (p[fi.dataKey] === null ? this.strings.noValue : p[fi.dataKey]).toLowerCase().includes(v.toLowerCase()));

                return fi;
            }),
            storageKey: `${window.location.href}(${this.$attrs.id || ''})`,
            editItem: null,
            working: false,
            strings: {
                ...{
                    noRecordsMessage: 'No matching records found',
                    sortByFormat: 'Sort by {0}',
                    filterByFormat: 'Filter by {0}',
                    searchInFormat: 'Search in {0}',
                    clearFilters: 'Clear filters',
                    addItem: 'Add item',
                    edit: 'Edit',
                    remove: 'Delete',
                    save: 'Save',
                    cancel: 'Cancel',
                    noValue: '-- No Value',
                    pager: {
                        showingFormat: 'Showing {} to {} of {} entries',
                        showingFilteredFormat: 'filtered from a total of',
                        pageSize: 'Page size',
                        first: 'First',
                        previous: 'Previous',
                        next: 'Next',
                        last: 'Last',
                        page: 'Page:',
                        of: 'of '
                    }
                },
                ...this.resources
            }
        };
    },
    props: {
        items: Array,
        filters: {
            type: Array,
            default: () => []
        },
        pageSize: {
            type: Number,
            default: 25
        },
        filteredCount: Number,
        totalItemsCount: Number,
        pagerLocation: {
            type: String,
            default: 'bottom'
        },
        columns: Object,
        styling: String,
        link: String,
        behavior: String,
        addurl: String,
        resources: {
            type: Object,
            default() {
                return {};
            }
        },
        isSelectable: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        pageItemsAdd() {
            return this.editItem && !this.editItem.id ? [this.editItem, ...this.item] : this.item;
        },
        isFiltered() {
            return !!Object.keys(this.appliedFilters).length;
        },
        allSelected() {
            return this.pageItemsAdd.length && this.pageItemsAdd.filter(x => this.selectedItems.includes(x.id)).length == this.pageItemsAdd.length;
        }
    },
    methods: {
        interpolate(format, ...keys) {
            const parts = format.split('{}');
            let result = parts[0];

            keys.forEach((k, i) => {
                result += k;
                if (parts.length > i + 1) result += parts[i + 1];
            });

            return result;
        },
        loadState() {
            try {
                let stateString = localStorage.getItem(this.storageKey);
                let state = stateString ? JSON.parse(stateString) : { date: new Date() };
                let now = new Date();
                let stateDate = new Date(state.date);

                if (now.getDay() === stateDate.getDay() &&
                    now.getMonth() === stateDate.getMonth() &&
                    now.getYear() === stateDate.getYear()) {

                    if (state.filterModel) {
                        this.filterModel = state.filterModel;
                        Object.keys(this.filterModel).forEach(key => {
                            if (this.filterModel[key])
                                this.filter({ key, emit: false });
                        });
                    }

                    if (state.sortModel) {
                        state.sortModel.forEach(s => {
                            let [key, order] = s;
                            this.sortModel[key] = order;

                            let column = this.columns.config.find(x => x.sortKey === key);
                            this.applySort(column, order, false);
                        });
                    }

                    this.size = state.pageSize || this.size;
                    this.$nextTick(function () {
                        this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;
                        this.typedPageIndex = state.hasOwnProperty('typedPageIndex') ? state.typedPageIndex : this.typedPageIndex;
                    });
                } else localStorage.removeItem(this.storageKey);
            } catch {
                localStorage.removeItem(this.storageKey);
            }
        },
        updateState(partial) {
            let stateString = localStorage.getItem(this.storageKey);
            let state = stateString ? JSON.parse(stateString) : {};

            partial = { ...partial, date: new Date() };
            localStorage.setItem(this.storageKey, JSON.stringify(Object.assign(state, partial)));
        },
        getCellType(row, column) {
            if (this.editItem?.id === row[this.columns.idKey] && column.edit) {
                return `${column.type}-edit-${column.edit.type}-cell`;
            } else {
                return `${column.type}-cell`;
            }
        },
        getHref(item, column) {
            if (column?.type === 'link') return item[column.dataKey];

            let [match, field] = this.link.match(/\{([a-z0-9]+)\}/i) || [];
            if (field) {
                return this.link.replace(match, item[field]);
            }

            return this.link + item[this.columns.idKey];
        },
        rowClicked(item) {
            if (this.editItem) return;

            if (this.link?.length > 0) {
                let href = this.getHref(item);

                if (this.behavior === 'newtab') {
                    let link = document.createElement('a');
                    link.href = href;
                    link.setAttribute('target', '_blank');
                    link.click();
                } else
                    window.location.href = href;
            } else {
                this.$emit('row-clicked', item);
            }
        },
        getComparer(key, direction = 0, comparer = 'string') {
            if (comparer === 'number')
                return (a, b) => {
                    let akey = a[key] || 0,
                        bkey = b[key] || 0;

                    if (akey === bkey) return 0;
                    if (akey > bkey) return 1 * direction;

                    return -1 * direction;
                };

            if (comparer === 'string')
                return (a, b) => {
                    let akey = a[key] || '',
                        bkey = b[key] || '';

                    return direction * (akey).localeCompare(bkey, 'en', { sensitivity: 'base' });
                };

            if (comparer === 'boolean')
                return (a, b) => {
                    let akey = a[key] ? 1 : 0,
                        bkey = b[key] ? 1 : 0;

                    return direction * (akey - bkey);
                };

            if (comparer === 'date')
                return (a, b) => {
                    let akeyStr = a[key] || '1970-1-1',
                        bkeyStr = b[key] || '1970-1-1';
                    let akey = new Date(akeyStr),
                        bkey = new Date(bkeyStr);

                    if (akeyStr === bkeyStr) return 0;
                    if (akey > bkey) return 1 * direction;

                    return -1 * direction;
                };

            return () => 0;
        },
        sort(column) {
            if (column.sortKey) {
                let order = 0;
                let currentSort = this.sortModel[column.sortKey];

                if (!currentSort)
                    order = 1;
                else if (currentSort === 1)
                    order = -1;

                if (this.sortMode === 'single') {
                    Object.keys(this.sortModel).forEach(k => this.sortModel[k] = 0);
                }

                this.sortModel[column.sortKey] = order;
                this.applySort(column, order);
            }
        },
        applySort(column, order, emit = true) {
            let sorter = this.appliedSorts.find(x => x.key === column.sortKey);
            let sortComparer = this.sortConfig.find(x => x.sortKey === column.sortKey).sortComparer;

            if (sorter) {
                sorter.comparer = this.getComparer(column.sortKey, order, sortComparer);
                this.appliedSorts = this.appliedSorts.slice();
            } else {
                if (this.sortMode !== 'single') {
                    this.appliedSorts.splice(0, 0, {
                        key: column.sortKey,
                        comparer: this.getComparer(column.sortKey, order, sortComparer)
                    });
                } else if (order) {
                    this.appliedSorts = [{
                        key: column.sortKey,
                        comparer: this.getComparer(column.sortKey, order, sortComparer)
                    }];
                }
            }

            this.updateState({
                sortModel: Object.entries(this.sortModel)
            });

            if (emit) {
                this.$emit('sort', this.sortModel, this.pageIndex);
            }
        },
        filter({ key, emit = true }) {
            let val = this.filterModel[key];
            if (emit) {
                this.$emit('filter', this.filterModel);
            }

            if (!val && val !== false) {
                delete this.appliedFilters[key];
            } else {
                let f = this.getFilterByKey(key);
                this.appliedFilters[key] = f.fn(f.convert(val));
            }

            this.updateState({
                filterModel: this.filterModel,
                pageIndex: 0,
                typedPageIndex: 1
            });

            if (this.isSelectable) {
                const selectedCount = this.selectedItems.length;
                this.selectedItems = this.selectedItems.filter(x => this.items.map(y => y.id).includes(x));

                if (selectedCount !== this.selectedItems.length) {
                    this.$emit('on-selection-change', this.selectedItems);
                }
            }

            this.pageIndex = 0;
            this.typedPageIndex = 1;
        },
        select(id) {

            let toggleIndex = this.selectedItems.indexOf(id);
            if (toggleIndex >= 0) {
                this.selectedItems.splice(toggleIndex, 1);
            } else {
                this.selectedItems.push(id);
            }

            this.$emit('on-selection-change', this.selectedItems);
        },
        toggleAll(on) {

            if (on) {
                this.selectedItems = [...new Set([...this.selectedItems, ...this.pageItemsAdd.map(x => x.id)])]
            } else {
                this.selectedItems = this.selectedItems.filter(x => !this.pageItemsAdd.map(y => y.id).includes(x));
            }

            this.$emit('on-selection-change', this.selectedItems);
        },
        getFilterByKey(key) {
            return this.internalFilters.find(f => f.key === key);
        },
        getColumnStyle(column) {
            let s = '';
            if (column.type === 'bool') s += '';
            return (column.style || '') + s;
        },
        getColumnHeaderStyle(column) {
            let s = '';
            if (column.type === 'bool') s += '';
            if (column.headerStyle) s += column.headerStyle;
            return (column.style || '') + s;
        },
        clear() {
            this.internalFilters.forEach((f, i) => {
                this.filterModel[f.key] = '';
                this.updateState({ filterModel: this.filterModel });
                delete this.appliedFilters[f.key];
            });

            this.$emit('filter');
        },
        changePage(index) {
            let extra = this.filteredCount % this.size ? 1 : 0;
            let recalculatedPageCount = Math.floor(this.filteredCount / this.size) + extra;
            if (index >= 2 && index <= recalculatedPageCount) {
                this.pageIndex = index - 1;
                this.typedPageIndex = index;
            }
            else {
                this.pageIndex = 0;
                this.typedPageIndex = 1;
            }
            this.$emit('on-page-index-change', this.pageIndex);
            this.updateState({ pageIndex: this.pageIndex, typedPageIndex: this.typedPageIndex });
        },
        changePageSize(size) {
            this.pageIndex = 0;
            this.typedPageIndex = 1;
            this.updateState({ pageIndex: this.pageIndex, typedPageIndex: this.typedPageIndex });
            this.size = +size;
            this.updateState({ pageSize: this.size });
            this.$emit('on-page-size-change', this.size);
        },
        tippify() {
            tippy('.tipified', {
                //trigger: 'click',
                interactive: true,
                placement: 'auto-end',
                animateFill: false
            });
        },
        saveItemClicked(item) {
            if (this.working) return;
            this.working = true;
            if (this.editItem) {
                if (this.columns.config.find(({ dataKey }) => this.editItem[dataKey].invalid)) {
                    this.working = false;
                    return;
                }

                item = this.columns.config.reduce((acc, conf) => {
                    const col = conf.dataKey;
                    const filter = this.internalFilters.find(x => x.key === col);
                    const columnId = filter?.filterCollection ?? col;

                    const defaultOption = conf.edit ? null : filter?.options[0];
                    acc[columnId] = this.editItem[col].selected || defaultOption?.id || null;
                    if (columnId !== col) {
                        acc[col] = acc[columnId] ? filter?.options.find(x => x.id === acc[columnId]).name : defaultOption?.name;
                    }
                    return acc;
                }, { ...item });

                const applyChange = (res) => {
                    this.editItem = null;
                    if (item.id) {
                        const idx = this.data.findIndex(x => x[this.columns.idKey] === item.id);
                        this.data.splice(idx, 1, item);
                        this.$emit('edit', item);
                    }
                    else {
                        item.id = res.id;
                        if (res.path) item.path = res.path;
                        this.$emit('add', item);
                        this.data.push(item);
                    }
                    this.working = false;
                };

                const actionConfig = item.id ? this.columns.edit : this.columns.add;
                if (actionConfig?.enabled &&
                    (!actionConfig.canEditProp || item[actionConfig.canEditProp])) {
                    if (actionConfig.claim) {
                        actionConfig.claim(item).then(applyChange, () => { this.working = false; });

                    }
                    else {
                        applyChange();
                    }
                }
            }

        },
        editItemClicked(item = {}) {
            this.editItem = this.columns.config.reduce((acc, conf) => {
                const col = conf.dataKey;
                const filter = this.internalFilters.find(x => x.key === col);
                const columnId = filter?.filterCollection ?? col;
                const placeholder = `-- ${conf.header || col} --`;

                acc[col] = {
                    selected: item[columnId] || conf.edit?.value || '',
                    selectedText: item[col] || conf.edit?.value || filter?.options[conf.edit?.default ?? 0]?.name,
                    options: [...filter?.options || []],
                    columnId,
                    placeholder,
                    invalid: acc.id ? false : conf.edit?.required ? !item[columnId] && !conf.edit?.value : false,
                    ...conf.edit || {}
                };

                acc[col].options.splice(0, 0, { id: '', name: placeholder });
                return acc;
            }, { id: item[this.columns.idKey] });
        },
        removeItemClicked(item) {
            const applyChange = () => {
                this.data.splice(this.data.indexOf(item), 1);
                this.$emit('remove', item);
            };
            if (this.columns.remove?.enabled &&
                (!this.columns.remove.canRemoveProp || item[this.columns.remove.canRemoveProp])) {
                if (this.columns.remove.claim) {
                    this.columns.remove.claim(item).then(applyChange, () => { });
                } else {
                    applyChange();
                }
            }
        },
        addItemClicked() {
            if (this.addurl) {
                return window.location.href = this.addurl;
            }

            this.editItemClicked();
        }
    },
    updated() {
        this.$nextTick(function () {
            this.tippify();
        });
    },
    mounted() {
        this.tippify();
        this.loadState();
        this.$emit('on-load-state', true);
    }
});
