﻿vueApp.component('autocomplete', {
    template: '#autocomplete-template',
    data: function () {
        return {
            isOpen: false,
            search: '',
            searchUrl: '',
            arrowCounter: -1,
            currentItems: this.items,
            showSelectedName: !!this.config.showSelectedName
        };
    },
    props: {
        items: {
            type: Array,
            required: false,
            default: () => []
        },
        config: {
            type: Object,
            required: true
        }
    },
    computed: {
        filteredItems() {
            return this.currentItems.filter(i => {
                return plx.escapeAccent(i.name.toLowerCase()).indexOf(plx.escapeAccent(this.search.toLowerCase())) > -1;
            });
        }
    },
    methods: {
        onChange: function () {
            if (this.config.searchUrl && this.search.length > 2) {
                let [match, field] = this.config.searchUrl.match(/\{([a-z0-9]+)\}/i) || [];
                if (field) {
                    this.searchUrl = this.config.searchUrl.replace(match, this.search);
                }
                if (this.timeout) {
                    clearTimeout(this.timeout);
                }
                this.timeout = setTimeout(() => {
                    fetch(`${this.searchUrl}`)
                        .then(r => r.json())
                        .then(suggestions => {
                            this.currentItems = suggestions;
                            this.isOpen = !!this.currentItems.length;
                        })
                        .catch(error => {
                            console.log(error);
                        })
                }, 500)
            } else {
                this.isOpen = !!this.filteredItems.length;
            }
            if (!this.currentItems.length) {
                this.currentItems = this.items;
            }
            this.$emit('search', this.search);
        },
        onEnter() {
            if (this.arrowCounter < 0) {
                this.arrowCounter =
                    this.filteredItems.map(x => plx.escapeAccent(x.name.toLowerCase()).indexOf(this.search));
            }

            let selectedItem = this.filteredItems[this.arrowCounter];
            let existingItem = !!selectedItem;

            if (!selectedItem) {
                selectedItem = {
                    name: this.search
                };
            }
            if (existingItem || this.config.canAddNew) {
                this.$emit('selected', selectedItem);
                this.isOpen = false;
                this.search = this.showSelectedName ? selectedItem.name : '';
                this.arrowCounter = -1;
                this.$refs.write.focus();
            }
        },
        onClick(index) {
            this.arrowCounter = index;
            this.onEnter();
        },
        handleClickOutside(evt) {
            if (!this.$el.contains(evt.target)) {
                this.isOpen = false;
                this.arrowCounter = -1;
            }
        },
        onArrowDown() {
            if (this.arrowCounter < this.filteredItems.length - 1) {
                this.arrowCounter = this.arrowCounter + 1;
            }
        },
        onArrowUp() {
            if (this.arrowCounter > 0) {
                this.arrowCounter = this.arrowCounter - 1;
            }
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
    }
});

vueApp.component('autocomplete-list', {
    template: '#autocomplete-list-template',
    data: function () {
        return {
            selectedItem: null,
            assignedItems: [...this.addedItems],
            modelProperty: this.config.modelProperty,
            emptyMessage: this.config.emptyMessage,
            autocompleteConfig: {
                placeholder: this.config.placeholder,
                canAddNew: !this.config.hasOwnProperty('canAddNew') || this.config.canAddNew
            },
            active: !this.config.hasOwnProperty('active') || this.config.active
        };
    },
    computed: {
        unassignedItems() {
            return this.items.filter(p => !this.assignedItems.find(x => x.id == p.id));
        }
    },
    props: {
        addedItems: {
            type: Array,
            required: false,
            default: () => []
        },
        items: {
            type: Array,
            required: true
        },
        valid: {
            type: Boolean,
            required: false,
            default: () => true
        },
        config: {
            type: Object,
            required: true
        }
    },
    methods: {
        addItem(item) {
            if (!this.assignedItems.find(p =>
                plx.escapeAccent(p.name.toLowerCase()) === plx.escapeAccent(item.name.toLowerCase()))) {

                this.assignedItems = [...this.assignedItems, item].sort((a, b) => a.name.localeCompare(b.name, 'en', { sensitivity: 'base' }))

                this.$emit('change', this.assignedItems);
                this.$emit('itemsChanged', this.assignedItems);

                this.clicked(item);
            }
        },
        deleteItem(index) {
            let deletedItem = this.assignedItems.splice(index, 1);

            this.$emit('change', this.assignedItems);
            this.$emit('itemsChanged', this.assignedItems);

            if (deletedItem[0] === this.selectedItem)
                this.clicked(this.assignedItems[0]);
        },
        clicked(item) {
            if (this.config.selectable) {
                this.selectedItem = item;
                this.$emit('select', item);
            }
        }
    },
    mounted() {
        if (this.config.selectable) {
            this.clicked(this.addedItems[0]);
        }
    }
});