﻿using PharmaLex.DataAccess;
using System;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class ContentItemAudit : AuditEntityBase
    {
        public override  int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public int? ContentTypeId { get; set; }
        public string Owner { get; set; }
        public DateTime? VerifiedDate { get; set; }
    }
}
