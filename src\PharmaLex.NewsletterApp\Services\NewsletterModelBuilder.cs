﻿using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Helpers.Comparers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class NewsletterProductGroupBuilder
    {
        private readonly List<NewsArticleContentModel> newsletterArticles;
        private readonly Dictionary<NewsCategoryGroup, List<NewsCategory>> newsCategoryGroups;
        private readonly Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>> preferenceGroups;
        private readonly List<NewsSource> newsSources;
        private readonly CultureInfo cultureInfo;
        private readonly string articleUrlTemplate;
        private readonly string headlineArticleGroupHeader;
        private readonly string newsletterKey = Guid.NewGuid().ToString("N").ToLower();
        private readonly bool isInfoflash;
        private readonly IAzureBlobHelper blobHelper;

        private List<NewsArticleContentModel> headlineArticles;
        private List<NewsArticleContentModel> productGroupArticles;

        private readonly Dictionary<string, string> productGroupColours = new()
        {
            {"headline", "#557595" },
            {"medicines", "#006068" },
            {"medical-devices", "#632340" },
            {"cosmetics", "#ddb5c8" },
            {"biocides", "#338f40" },
            {"nutrition", "#ed7a31" },
        };

        private const string GeographicalScopeLocalizationKeyFrance = "france";
        private const string GeographicalScopeLocalizationKeySwitzerland = "switzerland";
        private const string GeographicalScopeLocalizationKeyUs = "unitedstates";

        public string NewsletterKey => this.newsletterKey;
        public NewsArticleProductGroupModel Headline { get; set; }
        public List<NewsArticleProductGroupModel> ByProductGroup { get; set; }

        public NewsletterProductGroupBuilder(
            IAzureBlobHelper blobHelper,
            List<NewsArticleContentModel> newsletterArticles,
            Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>> preferenceGroups,
            Dictionary<NewsCategoryGroup, List<NewsCategory>> newsCategoryGroups,
            List<NewsSource> newsSources,
            LocaleModel locale,
            string articleUrlTemplate,
            string headlineArticleGroupHeader,
            bool isInfoflash)
        {
            this.blobHelper = blobHelper;
            this.newsletterArticles = newsletterArticles;
            this.preferenceGroups = preferenceGroups;
            this.newsCategoryGroups = newsCategoryGroups;
            this.newsSources = newsSources;
            this.cultureInfo = new CultureInfo(locale.IsoLanguageCode); 
            this.articleUrlTemplate = articleUrlTemplate;
            this.headlineArticleGroupHeader = headlineArticleGroupHeader;
            this.isInfoflash = isInfoflash;
        }

        public async Task Build()
        {
            var productRootCategory = this.newsCategoryGroups[NewsCategoryGroup.Products].First(x => x.ParentId == null);

            var productGroups = new Dictionary<string, List<int>>();
            foreach (var group in productRootCategory.ChildCategory)
            {
                productGroups.Add(group.Name, new List<int> { group.Id }.Union(group.ChildCategory.Select(x => x.Id)).ToList());
            }

            this.headlineArticles = newsletterArticles.Where(x =>
                x.ImportanceId == (int)NewsArticleImportance.Headline
            ).ToList();

            if (this.headlineArticles.Count > 0)
            {
                this.Headline = new NewsArticleProductGroupModel
                {
                    ProductGroupName = this.headlineArticleGroupHeader,
                    ProductGroupClassName = "headline",
                    ProductGroupHexColour = productGroupColours["headline"],
                    Articles = await this.BuildProductGroupArticleList(this.headlineArticles)
                };
            }

            this.ByProductGroup = await this.BuildNewsArticleProductGroupsByProduct(productGroups);
        }

        private async Task<List<NewsArticleProductGroupModel>> BuildNewsArticleProductGroupsByProduct(Dictionary<string, List<int>> productGroups)
        {
            this.productGroupArticles = newsletterArticles.Where(x =>
                !this.headlineArticles.Contains(x)
            ).ToList();

            var productsPreferences = this.preferenceGroups[NewsCategoryGroup.Products]
                .OrderBy(x => x.Order).Select(x => x.NewsCategoryId).ToList();

            var articlesByProductGroup = new List<NewsArticleProductGroupModel>();

            foreach (var productGroup in productGroups.OrderBy(x => productsPreferences.IndexOf(x.Value[0])))
            {
                var productGroupName = productGroup.Key;
                var productGroupRootId = productGroup.Value[0];
                var productGroupIds = productGroup.Value;

                var productArticles = this.productGroupArticles.Where(x =>
                        x.NewsArticleCategoryIds.Any(y => productGroupIds.Contains(y))
                        && !articlesByProductGroup.Any(y => y.Articles.Any(z => z.Id == x.Id)))
                    .ToList();

                var className = newsCategoryGroups[NewsCategoryGroup.Products].First(x => x.Id == productGroupRootId).LocalisationKey;
                articlesByProductGroup.Add(new NewsArticleProductGroupModel
                {
                    ProductGroupClassName = className,
                    ProductGroupHexColour = productGroupColours[className],
                    ProductGroupName = productGroupName,
                    Articles = await this.BuildProductGroupArticleList(productArticles)
                });
            }

            articlesByProductGroup.Last().IsLastItem = true;

            return articlesByProductGroup.Where(x => x.Articles.Count > 0).ToList();
        }

        private async Task<List<NewsArticleModel>> BuildProductGroupArticleList(List<NewsArticleContentModel> articles)
        {
            var comparer = new PreferenceArrayComparer();

            var arts = articles.Select(async article => 
            {
                var themesPreferences = this.preferenceGroups[NewsCategoryGroup.Themes].OrderBy(x => x.Order).ToList();
                var themesPreferenceOrder = themesPreferences
                    .Where(x => article.NewsArticleCategoryIds.Any(y => y == x.NewsCategoryId))
                    .Select(x => themesPreferences.IndexOf(x))
                    .ToArray();

                var productsPreferences = this.preferenceGroups[NewsCategoryGroup.Products].OrderBy(x => x.Order).ToList();
                var productsPreferenceOrder = productsPreferences
                    .Where(x => article.NewsArticleCategoryIds.Any(y => y == x.NewsCategoryId))
                    .OrderBy(x => x.Order)
                    .Select(x => productsPreferences.IndexOf(x))
                    .ToArray();

                var geographicalScopeNewsCategoryGroups = this.newsCategoryGroups[NewsCategoryGroup.GeographicalScope]
                    .SingleOrDefault(x => Array.Exists(article.NewsArticleCategoryIds, y => y == x.Id));
                var geographicalScopeLocalisationKey = geographicalScopeNewsCategoryGroups?.LocalisationKey;
                var newsSource = newsSources.Find(x => x.Id == article.NewsSourceId);

                var countryCode = "EU";
                if (geographicalScopeLocalisationKey != null)
                {
                    if (geographicalScopeLocalisationKey.Equals(GeographicalScopeLocalizationKeyFrance, StringComparison.OrdinalIgnoreCase))
                    {
                        countryCode = "FR";
                    }
                    else if (geographicalScopeLocalisationKey.Equals(GeographicalScopeLocalizationKeySwitzerland, StringComparison.OrdinalIgnoreCase))
                    {
                        countryCode = "CH";
                    }
                    else if (geographicalScopeLocalisationKey.Equals(GeographicalScopeLocalizationKeyUs, StringComparison.OrdinalIgnoreCase))
                    {
                        countryCode = "US";
                    }
                }

                var model = new
                {
                    article = new NewsArticleModel
                    {
                        Date = article.SourcePublicationDate.ToString("dd MMMM yyyy", this.cultureInfo),
                        GeographicalScope = new GeographicalScopeCategoryModel
                        {
                            Name = geographicalScopeNewsCategoryGroups?.Name,
                            CountryCode = countryCode,
                        },
                        NewsSource = newsSource?.Name,
                        NewsSourceClassName = newsSource?.LocalisationKey,
                        Title = article.Title,
                        SourceUrl = article.SourceUrl,
                        PublicUrl = string.Format(this.articleUrlTemplate, $"{this.newsletterKey}/{article.FriendlyUrl}"),
                        Id = article.Id,
                        ImpactAssessmentSummary = article.ImpactAssessmentSummary,
                        BlobName = article.AzureBlobName,   
                        FullArticleText = this.isInfoflash ? await blobHelper.GetBlobText(article.AzureBlobName) : null,
                        IsLastItem = articles.IndexOf(article) == articles.Count - 1
                    },
                    themesOrder = themesPreferenceOrder,
                    productsOrder = productsPreferenceOrder
                };

                return model;
            }).ToList();

            var result = await Task.Run(() => arts.Select(x => x.Result).OrderBy(x => x.productsOrder, comparer)
                                    .ThenBy(x => x.themesOrder, comparer)
                                    .Select(x => x.article)
                                    .ToList());

            return result;
        }
    }
}
