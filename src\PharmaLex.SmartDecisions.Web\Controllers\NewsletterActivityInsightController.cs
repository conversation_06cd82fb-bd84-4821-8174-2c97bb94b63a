﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.SmartDecisions.Web.Helpers;
using System;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize("InsightsManager")]
    public class NewsletterActivityInsightController : BaseController
    {
        private readonly INewsletterActivityService newsletterActivityService;

        public NewsletterActivityInsightController(INewsletterActivityService newsletterActivityService)
        {
            this.newsletterActivityService = newsletterActivityService;
        }

        [HttpGet("/manage/newsletter-insights")]
        public async Task<IActionResult> Index(
            [FromQuery] int skip = 0,
            [FromQuery] int take = 25,
            [FromQuery] string[] filters = null,
            [FromQuery] string sort = null)
        {
            var newsletterActivities = await newsletterActivityService.GetPagedNewsletterActivitiesAsync(skip, take, filters ?? Array.Empty<string>(), sort);
            return View(newsletterActivities);
        }

        [HttpGet("/manage/paged-newsletter-insights")]
        public async Task<IActionResult> IndexPagedNewsletterActivities(
           [FromQuery] int skip = 0,
           [FromQuery] int take = 25,
           [FromQuery] string[] filters = null,
           [FromQuery] string sort = null)
        {
            var newsletterActivities = await newsletterActivityService.GetPagedNewsletterActivitiesAsync(skip, take, filters?? Array.Empty<string>(), sort);
            return Json(newsletterActivities);
        }
    }
}
