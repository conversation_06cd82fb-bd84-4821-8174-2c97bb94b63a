﻿alter trigger [dbo].[NewsletterSubscription_Insert] on [dbo].[NewsletterSubscription]
for insert as
insert into [Audit].[NewsletterSubscription_Audit]
select 'I'
		, [Id]
		, [UserId]
		, [Timezone]
		, [TimezoneOffset]
		, [DeliveryLocalDay]
		, [DeliveryUtcDay]
		, [DeliveryLocalHour]
		, [DeliveryUtcHour]
		, [CreatedDateUtc]
		, [IsMonthly]
		, [CreatedDate]
		, [CreatedBy]
		, [LastUpdatedDate]
		, [LastUpdatedBy]
		, [Active] from [Inserted]
go

alter trigger [dbo].[NewsletterSubscription_Update] on [dbo].[NewsletterSubscription]
for update as
insert into [Audit].[NewsletterSubscription_Audit]
select 'U'
		, [Id]
		, [UserId]
		, [Timezone]
		, [TimezoneOffset]
		, [DeliveryLocalDay]
		, [DeliveryUtcDay]
		, [DeliveryLocalHour]
		, [DeliveryUtcHour]
		, [CreatedDateUtc]
		, [IsMonthly]
		, [CreatedDate]
		, [CreatedBy]
		, [LastUpdatedDate]
		, [LastUpdatedBy]
		, [Active] from [Inserted]
go

alter trigger [dbo].[NewsletterSubscription_Delete] on [dbo].[NewsletterSubscription]
for delete as
insert into [Audit].[NewsletterSubscription_Audit]
select 'D'
		, [Id]
		, [UserId]
		, [Timezone]
		, [TimezoneOffset]
		, [DeliveryLocalDay]
		, [DeliveryUtcDay]
		, [DeliveryLocalHour]
		, [DeliveryUtcHour]
		, [CreatedDateUtc]
		, [IsMonthly]
		, [CreatedDate]
		, [CreatedBy]
		, getdate()
		, coalesce(rtrim(convert(varchar(128), context_info())), suser_name())
		, [Active] from [Deleted]
go