﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Collections.Generic;
using System.Net.Http;

namespace PharmaLex.SmartDecisions.Web.Helpers.HealthChecks.SendGrid
{
    public static class SendGridHealthCheckExtensions
    {
        internal const string NAME = "sendgrid";

        public static IHealthChecksBuilder AddSendGrid(
            this IHealthChecksBuilder builder,
            string apiKey,
            string name = default,
            HealthStatus? failureStatus = default,
            IEnumerable<string> tags = default,
            TimeSpan? timeout = default)
        {
            var registrationName = name ?? NAME;

            builder.Services.AddHttpClient(registrationName);

            return builder.Add(new HealthCheckRegistration(
                registrationName,
                sp => new SendGridHealthCheck(apiKey, sp.GetRequiredService<IHttpClientFactory>()),
                failureStatus,
                tags,
                timeout));
        }
    }
}
