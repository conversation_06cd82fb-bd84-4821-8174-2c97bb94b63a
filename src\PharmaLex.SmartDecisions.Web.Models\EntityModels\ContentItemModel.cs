﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    //TODO: Make and IContentItem interface for this and ContentItem and move the GetValue, ToDyynamic etc methods to extensions on the interface?
    public class ContentItemModel : EntityModel, IContentItemModel
    {
        public int ContentTypeId { get; set; }
        public ContentTypeModel ContentType { get; set; }
        public string ContentTypeName { get; set; }
        public string Owner { get; set; }
        public DateTime? VerifiedDate { get; set; }
        public string LastVerified 
        { 
            get
            {
                if(this.VerifiedDate.HasValue)
                {
                    var icon = this.VerifiedDate.Value < DateTime.Now.AddDays(-360) ? "<i class=\"icon-warning\"></i>" : this.VerifiedDate.Value < DateTime.Now.AddDays(-180) ? "<i class=\"icon-warning\"></i>" : "";
                    return $"{this.VerifiedDate?.ToString("yyyy-MM-dd")} {icon}";
                }
                return null;
            }
        }
        public IEnumerable<FieldValueModel> Values { get; set; }

        public ContentItemModel()
        {
            this.Values = new List<FieldValueModel>();
        }

        public dynamic ToDynamic()
        {
            dynamic d = new Dictionary<string, object>();
            d["Id"] = this.Id;
            d["Name"] = this.Name;
            //TODO: Get rid of dependency on ContentType? Easy enough to parse the field values but then won't have the empty/null values. If done we can move these to IContentItemModel to support use by TopicItemModel etc
            foreach(FieldModel fm in this.ContentType.Field)
            {
                d[fm.Name] = this.Values.FirstOrDefault(x => x.FieldId == fm.Id)?.Value;
            }
            return d;
        }

        public string ToDynamicJson()
        {
            return JsonConvert.SerializeObject(this.ToDynamic(), new JsonSerializerSettings
            {
                ContractResolver = new DefaultContractResolver()
            });
        }

        public (bool Success, string Message) Validate(ContentTypeModel ctm = null)
        {
            if(ctm == null)
            {
                if(this.ContentType == null)
                {
                    throw new Exception("ContentItemModel cannot be validated without the ContentTypeModel provided");
                }
                ctm = this.ContentType;
            }
            foreach(FieldModel fm in ctm.Field)
            {
                string v = this.GetFieldValue(fm.Name);
                if (String.IsNullOrEmpty(v))
                {
                    if (fm.Required)
                    {
                        return (false, $"Required field '{fm.Name}' not supplied or invalid");
                    }
                }
                else
                {
                    switch (fm.FieldType)
                    {
                        case "Text":
                        case "Multiline":
                            if (fm.Length > 0 && v.Length > fm.Length) return (false, $"Supplied value for field '{fm.Name}' is longer than allowed length of {fm.Length} characters");
                            else break;
                        case "Number":
                            if (!Int32.TryParse(v, out int i)) return (false, $"Supplied value for field '{fm.Name}' is not a number");
                            else break;
                        case "Bool":
                            if (!Boolean.TryParse(v, out bool b)) return (false, $"Supplied value for field '{fm.Name}' is not a boolean");
                            else break;
                        default: break;
                    }
                    //TODO: Unique validation
                }
            }
            return (true, null);
        }
    }

    public static class ContentItemModelExtensions
    {
        public static string ToDynamicJson(this IEnumerable<ContentItemModel> model)
        {
            return JsonConvert.SerializeObject(model.Select(x => x.ToDynamic()), new JsonSerializerSettings
            {
                ContractResolver = new DefaultContractResolver()
            });
        }
    }
}