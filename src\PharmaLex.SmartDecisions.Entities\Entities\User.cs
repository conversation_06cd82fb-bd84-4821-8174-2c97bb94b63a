using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class User : EntityBase
    {
        public User()
        {
            UserClaim = new HashSet<UserClaim>();
            NewsArticleContentAuthor = new HashSet<NewsArticleContent>();
            NewsArticleContentReviewer = new HashSet<NewsArticleContent>();
            NewsletterSubscription = new  HashSet<NewsletterSubscription>();
            UserNewsCategory = new HashSet<UserNewsCategory>();
            Newsletter = new  HashSet<Newsletter>();
        }

        public int Id { get; set; }
        public string Email { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public int? LocaleId { get; set; }
        public string InvitationEmailLink { get; set; }

        public virtual Locale Locale { get; set; }
		public virtual CompanyUser CompanyUser { get; set; }
        public virtual ICollection<UserClaim> UserClaim { get; set; }
        public virtual ICollection<NewsArticleContent> NewsArticleContentAuthor { get; set; }
        public virtual ICollection<NewsArticleContent> NewsArticleContentReviewer { get; set; }
        public virtual ICollection<NewsletterSubscription> NewsletterSubscription { get; set; }
        public virtual ICollection<UserNewsCategory> UserNewsCategory { get; set; }
        public virtual ICollection<Newsletter> Newsletter { get; set; }
    }
}
