﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class ContentItem : EntityBase
    {
        public ContentItem()
        {
            FieldValue = new HashSet<FieldValue>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int ContentTypeId { get; set; }
        public string Owner { get; set; }
        public DateTime VerifiedDate { get; set; }

        public virtual ContentType ContentType { get; set; }
        public virtual ICollection<FieldValue> FieldValue { get; set; }
    }
}
