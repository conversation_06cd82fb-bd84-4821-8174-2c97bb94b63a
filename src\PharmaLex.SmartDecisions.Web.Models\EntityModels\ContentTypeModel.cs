﻿using PharmaLex.SmartDecisions.Entities;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class ContentTypeModel : IModel
    {
        public int Id { get; set; }
        [Required]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Name field.")]
        public string Name { get; set; }
        public int ContentTypeCategoryId { get; set; }
        public string Category { get; set; }
        [Required]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the PluralName field.")]
        public string PluralName { get; set; }
        [Required]
        [RegularExpression(@"^[^\\/*?:\[\]<>]*$", ErrorMessage = "Invalid characters in the ShortName field.")]
        public string ShortName { get; set; }
        public string Owner { get; set; }
        public bool System { get; set; }
        public bool AutoManageName { get; set; }
        public IEnumerable<FieldModel> Field { get; set; }
        public IEnumerable<ContentTypeDisplayModel> Displays { get; set; }
        [JsonIgnore]
        public IEnumerable<FieldModel> PicklistFields => this.Field.Where(x => x.FieldTypeId == (int)FieldType.Picklist || x.FieldTypeId == (int)FieldType.Relationship);

        public ContentTypeModel()
        {
            this.Field = new List<FieldModel>();
            this.Displays = new List<ContentTypeDisplayModel>();
        }
    }
}
