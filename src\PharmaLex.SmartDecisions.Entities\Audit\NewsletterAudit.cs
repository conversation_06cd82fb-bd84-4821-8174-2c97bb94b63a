﻿using PharmaLex.DataAccess;
using System;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class NewsletterAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public int? UserId { get; set; }
        public DateTime? CreatedDateUtc { get; set; }
        public int? LocaleId { get; set; }
        public string UniqueKey { get; set; }
        public int? SubscriptionId { get; set; }
        public bool? IsMonthly { get; set; }
        public string NewsArticleContentIds { get; set; }
    }
}
