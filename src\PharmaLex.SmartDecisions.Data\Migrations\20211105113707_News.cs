﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.SmartDecisions.Data.Extensions;

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class News : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LocaleId",
                schema: "Audit",
                table: "User_Audit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LocaleId",
                table: "User",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "ContentTypeId",
                schema: "Audit",
                table: "CompanyContentType_Audit",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "CompanyId",
                schema: "Audit",
                table: "CompanyContentType_Audit",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "MaximumActiveUsersCount",
                schema: "Audit",
                table: "Company_Audit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaximumUsersCount",
                schema: "Audit",
                table: "Company_Audit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaximumActiveUsersCount",
                table: "Company",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "MaximumUsersCount",
                table: "Company",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "AzureBlob",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    Uri = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    ContentType = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    Length = table.Column<int>(type: "int", nullable: false),
                    Container = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AzureBlob", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AzureBlob_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: true),
                    Uri = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    ContentType = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    Length = table.Column<int>(type: "int", nullable: true),
                    Container = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AzureBlob_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "CompanyNewsCategory_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    CompanyId = table.Column<int>(type: "int", nullable: true),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyNewsCategory_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "Locale",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LocalisedName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    IsoLanguageCode = table.Column<string>(type: "nchar(2)", nullable: false),
                    IsoScriptCode = table.Column<string>(type: "nchar(4)", nullable: true),
                    IsoCountryCode = table.Column<string>(type: "nchar(2)", nullable: false),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    Default = table.Column<bool>(type: "bit", nullable: false),
                    LocalisationKey = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Locale", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Locale_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    LocalisedName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    IsoLanguageCode = table.Column<string>(type: "nchar(2)", nullable: true),
                    IsoScriptCode = table.Column<string>(type: "nchar(4)", nullable: true),
                    IsoCountryCode = table.Column<string>(type: "nchar(2)", nullable: true),
                    Active = table.Column<bool>(type: "bit", nullable: true),
                    Default = table.Column<bool>(type: "bit", nullable: true),
                    LocalisationKey = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Locale_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "MultilingualResource_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    LocaleId = table.Column<int>(type: "int", nullable: true),
                    Key = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MultilingualResource_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsArticle_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    NewsSourceId = table.Column<int>(type: "int", nullable: true),
                    ImportanceId = table.Column<int>(type: "int", nullable: true),
                    SourcePublicationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsArticle_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsArticleCategory_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    NewsArticleId = table.Column<int>(type: "int", nullable: true),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsArticleCategory_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsArticleContent_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    NewsArticleId = table.Column<int>(type: "int", nullable: true),
                    LocaleId = table.Column<int>(type: "int", nullable: true),
                    Title = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    AuthorId = table.Column<int>(type: "int", nullable: true),
                    ReviewerId = table.Column<int>(type: "int", nullable: true),
                    SourceUrl = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    PublishingStateId = table.Column<int>(type: "int", nullable: true),
                    PublishingStateDateUtc = table.Column<DateTime>(type: "datetime", nullable: true),
                    AzureBlobId = table.Column<int>(type: "int", nullable: true),
                    ImpactAssessmentSummary = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    FriendlyUrl = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsArticleContent_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsCategory",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    ParentId = table.Column<int>(type: "int", nullable: true),
                    GroupId = table.Column<int>(type: "int", nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    LocalisationKey = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsCategory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NewsCategory_NewsCategory",
                        column: x => x.ParentId,
                        principalTable: "NewsCategory",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "NewsCategory_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    ParentId = table.Column<int>(type: "int", nullable: true),
                    GroupId = table.Column<int>(type: "int", nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: true),
                    LocalisationKey = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsCategory_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "Newsletter_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    UserId = table.Column<int>(type: "int", nullable: true),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime", nullable: true),
                    LocaleId = table.Column<int>(type: "int", nullable: true),
                    UniqueKey = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    SubscriptionId = table.Column<int>(type: "int", nullable: true),
                    IsMonthly = table.Column<bool>(type: "bit", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Newsletter_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsletterNewsArticleContent_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    NewsletterId = table.Column<int>(type: "int", nullable: true),
                    NewsArticleContentId = table.Column<int>(type: "int", nullable: true),
                    TimeOpenUtc = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterNewsArticleContent_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsletterSubscription",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    Timezone = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    TimezoneOffset = table.Column<int>(type: "int", nullable: false),
                    DeliveryLocalDay = table.Column<int>(type: "int", nullable: false),
                    DeliveryUtcDay = table.Column<int>(type: "int", nullable: false),
                    DeliveryLocalHour = table.Column<int>(type: "int", nullable: false),
                    DeliveryUtcHour = table.Column<int>(type: "int", nullable: false),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime", nullable: false),
                    IsMonthly = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterSubscription", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NewsletterSubscription_User",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "NewsletterSubscription_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    UserId = table.Column<int>(type: "int", nullable: true),
                    Timezone = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    TimezoneOffset = table.Column<int>(type: "int", nullable: true),
                    DeliveryLocalDay = table.Column<int>(type: "int", nullable: true),
                    DeliveryUtcDay = table.Column<int>(type: "int", nullable: true),
                    DeliveryLocalHour = table.Column<int>(type: "int", nullable: true),
                    DeliveryUtcHour = table.Column<int>(type: "int", nullable: true),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsMonthly = table.Column<bool>(type: "bit", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterSubscription_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsletterSubscriptionNewsCategory_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    Order = table.Column<int>(type: "int", nullable: true),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NewsletterSubscriptionId = table.Column<int>(type: "int", nullable: false),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterSubscriptionNewsCategory_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "NewsSource",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    Url = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    LocalisationKey = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsSource", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NewsSource_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    Url = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: true),
                    LocalisationKey = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsSource_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateTable(
                name: "MultilingualResource",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LocaleId = table.Column<int>(type: "int", nullable: false),
                    Key = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MultilingualResource", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MultilingualResource_Locale",
                        column: x => x.LocaleId,
                        principalTable: "Locale",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CompanyNewsCategory",
                columns: table => new
                {
                    CompanyId = table.Column<int>(type: "int", nullable: false),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyNewsCategory", x => new { x.CompanyId, x.NewsCategoryId });
                    table.ForeignKey(
                        name: "FK_CompanyNewsCategory_Company",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CompanyNewsCategory_NewsCategory",
                        column: x => x.NewsCategoryId,
                        principalTable: "NewsCategory",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Newsletter",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime", nullable: false),
                    LocaleId = table.Column<int>(type: "int", nullable: false),
                    UniqueKey = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    NewsletterSubscriptionId = table.Column<int>(type: "int", nullable: true),
                    IsMonthly = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Newsletter", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Newsletter_Locale",
                        column: x => x.LocaleId,
                        principalTable: "Locale",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Newsletter_NewsletterSubscription",
                        column: x => x.NewsletterSubscriptionId,
                        principalTable: "NewsletterSubscription",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Newsletter_User",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NewsletterSubscriptionNewsCategory",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Order = table.Column<int>(type: "int", nullable: false),
                    NewsletterSubscriptionId = table.Column<int>(type: "int", nullable: false),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: false),
                    CreatedDateUtc = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterSubscriptionNewsCategory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NewsletterSubscriptionNewsCategory_NewsCategory",
                        column: x => x.NewsCategoryId,
                        principalTable: "NewsCategory",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NewsletterSubscriptionNewsCategory_NewsletterSubscription",
                        column: x => x.NewsletterSubscriptionId,
                        principalTable: "NewsletterSubscription",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NewsArticle",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NewsSourceId = table.Column<int>(type: "int", nullable: false),
                    ImportanceId = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    SourcePublicationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsArticle", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NewsArticle_NewsSource",
                        column: x => x.NewsSourceId,
                        principalTable: "NewsSource",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "NewsArticleCategory",
                columns: table => new
                {
                    NewsArticleId = table.Column<int>(type: "int", nullable: false),
                    NewsCategoryId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsArticleCategory", x => new { x.NewsArticleId, x.NewsCategoryId });
                    table.ForeignKey(
                        name: "FK_NewsArticleCategory_NewsArticle",
                        column: x => x.NewsArticleId,
                        principalTable: "NewsArticle",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NewsArticleCategory_NewsCategory",
                        column: x => x.NewsCategoryId,
                        principalTable: "NewsCategory",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "NewsArticleContent",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NewsArticleId = table.Column<int>(type: "int", nullable: false),
                    LocaleId = table.Column<int>(type: "int", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    AuthorId = table.Column<int>(type: "int", nullable: true),
                    ReviewerId = table.Column<int>(type: "int", nullable: true),
                    SourceUrl = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    PublishingStateId = table.Column<int>(type: "int", nullable: false),
                    PublishingStateDateUtc = table.Column<DateTime>(type: "datetime", nullable: false),
                    AzureBlobId = table.Column<int>(type: "int", nullable: true),
                    ImpactAssessmentSummary = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    FriendlyUrl = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsArticleContent", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NewsArticleContent_Author",
                        column: x => x.AuthorId,
                        principalTable: "User",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_NewsArticleContent_AzureBlob",
                        column: x => x.AzureBlobId,
                        principalTable: "AzureBlob",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_NewsArticleContent_Locale",
                        column: x => x.LocaleId,
                        principalTable: "Locale",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_NewsArticleContent_NewsArticle",
                        column: x => x.NewsArticleId,
                        principalTable: "NewsArticle",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NewsArticleContent_Riviewer",
                        column: x => x.ReviewerId,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "NewsletterNewsArticleContent",
                columns: table => new
                {
                    NewsletterId = table.Column<int>(type: "int", nullable: false),
                    NewsArticleContentId = table.Column<int>(type: "int", nullable: false),
                    TimeOpenUtc = table.Column<DateTime>(type: "datetime", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterNewsArticleContent", x => new { x.NewsletterId, x.NewsArticleContentId });
                    table.ForeignKey(
                        name: "FK_NewsletterNewsArticleContent_NewsArticleContent",
                        column: x => x.NewsArticleContentId,
                        principalTable: "NewsArticleContent",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NewsletterNewsArticleContent_Newsletter",
                        column: x => x.NewsletterId,
                        principalTable: "Newsletter",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_User_LocaleId",
                table: "User",
                column: "LocaleId");

            migrationBuilder.CreateIndex(
                name: "UC_AzureBlob_Name_Container",
                table: "AzureBlob",
                columns: new[] { "Name", "Container" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompanyNewsCategory_NewsCategoryId",
                table: "CompanyNewsCategory",
                column: "NewsCategoryId");

            migrationBuilder.CreateIndex(
                name: "UC_Locale_Code",
                table: "Locale",
                columns: new[] { "IsoLanguageCode", "IsoScriptCode", "IsoCountryCode" },
                unique: true,
                filter: "[IsoScriptCode] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "UC_Locale_Name",
                table: "Locale",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MultilingualResource_LocaleId",
                table: "MultilingualResource",
                column: "LocaleId");

            migrationBuilder.CreateIndex(
                name: "UC_MultilingualResource_Key_Locale",
                table: "MultilingualResource",
                columns: new[] { "Key", "LocaleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticle_NewsSourceId",
                table: "NewsArticle",
                column: "NewsSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticleCategory_NewsCategoryId",
                table: "NewsArticleCategory",
                column: "NewsCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticleContent_AuthorId",
                table: "NewsArticleContent",
                column: "AuthorId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticleContent_AzureBlobId",
                table: "NewsArticleContent",
                column: "AzureBlobId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticleContent_NewsArticleId",
                table: "NewsArticleContent",
                column: "NewsArticleId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticleContent_ReviewerId",
                table: "NewsArticleContent",
                column: "ReviewerId");

            migrationBuilder.CreateIndex(
                name: "UC_NewsArticleContent_FriendlyUrl",
                table: "NewsArticleContent",
                column: "FriendlyUrl",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "UC_NewsArticleContent_LocaleId_NewsArticleId",
                table: "NewsArticleContent",
                columns: new[] { "LocaleId", "NewsArticleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NewsCategory_ParentId",
                table: "NewsCategory",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_Newsletter_LocaleId",
                table: "Newsletter",
                column: "LocaleId");

            migrationBuilder.CreateIndex(
                name: "IX_Newsletter_NewsletterSubscriptionId",
                table: "Newsletter",
                column: "NewsletterSubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_Newsletter_UserId",
                table: "Newsletter",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "UC_Newsletter_UniqueKey",
                table: "Newsletter",
                column: "UniqueKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NewsletterNewsArticleContent_NewsArticleContentId",
                table: "NewsletterNewsArticleContent",
                column: "NewsArticleContentId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsletterSubscription_UserId",
                table: "NewsletterSubscription",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsletterSubscriptionNewsCategory_NewsCategoryId",
                table: "NewsletterSubscriptionNewsCategory",
                column: "NewsCategoryId");

            migrationBuilder.CreateIndex(
                name: "UC_NewsletterSubscriptionNewsCategory",
                table: "NewsletterSubscriptionNewsCategory",
                columns: new[] { "NewsletterSubscriptionId", "NewsCategoryId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "UC_NewsSource_Name",
                table: "NewsSource",
                column: "Name",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_User_Locale",
                table: "User",
                column: "LocaleId",
                principalTable: "Locale",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.SqlFileExec("03-News-01-CreateAzureBlobTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-02-CreateCompanyNewsCategoryTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-03-CreateLocaleTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-04-CreateMultilingualResourceTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-05-CreateNewsSourceTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-06-CreateNewsCategoryTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-07-CreateNewsArticleCategoryTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-08-CreateNewsArticleTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-09-CreateNewsArticleContentTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-10-CreateNewsletterSubscriptionTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-11-CreateNewsletterTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-12-CreateNewsletterNewsArticleContentTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-13-AlterCompanyTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-14-AlterUserTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-15-CreateNewsletterSubscriptionNewsCategoryTriggers.sql");
            migrationBuilder.SqlFileExec("03-News-16-AddNewsCategories.sql");
            migrationBuilder.SqlFileExec("03-News-17-AddNewsSources.sql");
            migrationBuilder.SqlFileExec("03-News-18-AddLocales.sql");
            migrationBuilder.SqlFileExec("03-News-19-AddMultilingualResources.sql");
            migrationBuilder.SqlFileExec("03-News-20-UpdateCompanies.sql");
            migrationBuilder.SqlFileExec("03-News-21-AddNewsRoles.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_User_Locale",
                table: "User");

            migrationBuilder.DropTable(
                name: "AzureBlob_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "CompanyNewsCategory");

            migrationBuilder.DropTable(
                name: "CompanyNewsCategory_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "Locale_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "MultilingualResource");

            migrationBuilder.DropTable(
                name: "MultilingualResource_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsArticle_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsArticleCategory");

            migrationBuilder.DropTable(
                name: "NewsArticleCategory_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsArticleContent_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsCategory_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "Newsletter_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsletterNewsArticleContent");

            migrationBuilder.DropTable(
                name: "NewsletterNewsArticleContent_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsletterSubscription_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsletterSubscriptionNewsCategory");

            migrationBuilder.DropTable(
                name: "NewsletterSubscriptionNewsCategory_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsSource_Audit",
                schema: "Audit");

            migrationBuilder.DropTable(
                name: "NewsArticleContent");

            migrationBuilder.DropTable(
                name: "Newsletter");

            migrationBuilder.DropTable(
                name: "NewsCategory");

            migrationBuilder.DropTable(
                name: "AzureBlob");

            migrationBuilder.DropTable(
                name: "NewsArticle");

            migrationBuilder.DropTable(
                name: "Locale");

            migrationBuilder.DropTable(
                name: "NewsletterSubscription");

            migrationBuilder.DropTable(
                name: "NewsSource");

            migrationBuilder.DropIndex(
                name: "IX_User_LocaleId",
                table: "User");

            migrationBuilder.DropColumn(
                name: "LocaleId",
                schema: "Audit",
                table: "User_Audit");

            migrationBuilder.DropColumn(
                name: "LocaleId",
                table: "User");

            migrationBuilder.DropColumn(
                name: "MaximumActiveUsersCount",
                schema: "Audit",
                table: "Company_Audit");

            migrationBuilder.DropColumn(
                name: "MaximumUsersCount",
                schema: "Audit",
                table: "Company_Audit");

            migrationBuilder.DropColumn(
                name: "MaximumActiveUsersCount",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "MaximumUsersCount",
                table: "Company");

            migrationBuilder.AlterColumn<int>(
                name: "ContentTypeId",
                schema: "Audit",
                table: "CompanyContentType_Audit",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "CompanyId",
                schema: "Audit",
                table: "CompanyContentType_Audit",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);
        }
    }
}
