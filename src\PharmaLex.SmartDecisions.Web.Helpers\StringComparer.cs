﻿using System;
using System.Globalization;
using System.Text.RegularExpressions;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public static class StringComparer
    {
        public static bool IsEqualTo(this string val1, string val2)
        {
            var v1 = Regex.Replace(val1.Trim().ToLowerInvariant(), @"\s+", " ", RegexOptions.None, TimeSpan.FromMilliseconds(500));
            var v2 = Regex.Replace(val2.Trim().ToLowerInvariant(), @"\s+", " ", RegexOptions.None, TimeSpan.FromMilliseconds(500));
            return string.Compare(v1, v2, CultureInfo.InvariantCulture, CompareOptions.IgnoreNonSpace) == 0;
        }
    }
}
