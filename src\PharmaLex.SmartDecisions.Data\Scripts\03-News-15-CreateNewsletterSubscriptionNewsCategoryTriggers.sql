﻿DROP TRIGGER IF EXISTS [NewsletterSubscriptionNewsCategory_Insert]
DROP TRIGGER IF EXISTS [NewsletterSubscriptionNewsCategory_Update]
DROP TRIGGER IF EXISTS [NewsletterSubscriptionNewsCategory_Delete]
GO
 
CREATE TRIGGER [dbo].[NewsletterSubscriptionNewsCategory_Insert] ON [dbo].[NewsletterSubscriptionNewsCategory]
FOR INSERT AS
INSERT INTO [Audit].[NewsletterSubscriptionNewsCategory_Audit]
(AuditAction,NewsletterSubscriptionId, [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'I' ,NewsletterSubscriptionId, [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO
 
CREATE TRIGGER [dbo].[NewsletterSubscriptionNewsCategory_Update] ON [dbo].[NewsletterSubscriptionNewsCategory]
FOR UPDATE AS
INSERT INTO [Audit].[NewsletterSubscriptionNewsCategory_Audit]
(AuditAction,NewsletterSubscriptionId, [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'U' ,NewsletterSubscriptionId, [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO
 
CREATE TRIGGER [dbo].[NewsletterSubscriptionNewsCategory_Delete] ON [dbo].[NewsletterSubscriptionNewsCategory]
FOR DELETE AS
INSERT INTO [Audit].[NewsletterSubscriptionNewsCategory_Audit]
(AuditAction,NewsletterSubscriptionId, [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'D' ,NewsletterSubscriptionId, [NewsCategoryId], [Id], [Order], [CreatedDateUtc], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO
 
