﻿using AutoMapper;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PharmaLex.Caching.Data;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Entities.Entities;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class SendGridWebhooksQueue
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly INewsletterActivityRepository repo;
        private readonly IMapper mapper;

        public SendGridWebhooksQueue(IDistributedCacheServiceFactory cacheFactory, INewsletterActivityRepository repo,  IMapper mapper)
        {
            this.cacheFactory = cacheFactory;
            this.repo = repo;
            this.mapper = mapper;
        }

        [Function("SendGridWebhooksQueue")]
        public async Task Run([QueueTrigger("%qn-webhooks%")] string sendgridWebhook, FunctionContext context)
        {
            ILogger logger = context.GetLogger("SendGridWebhooksQueue");
            logger.LogInformation("Start processing queue message: {sendgridWebhook}.", sendgridWebhook);

            var logs = JsonConvert.DeserializeObject<SendGridWebhook>(sendgridWebhook);
            if (logs != null && logs.Logs.Any())
            {
                var listToAdd = new List<NewsletterActivity>();
                var users = cacheFactory.CreateEntity<User>();

                foreach (var groupedLog in logs.Logs.GroupBy(l => l.Email))
                {
                    var user = await users.FirstOrDefaultAsync(x => x.Email.ToLower() == groupedLog.Key.ToLower());
                    if (user != null)
                    {
                        foreach (var log in groupedLog)
                        {
                            var newsletterActivity = mapper.Map<NewsletterActivity>(log);
                            newsletterActivity.UserId = user.Id;

                            listToAdd.Add(newsletterActivity);
                        }
                    }
                    else
                    {
                        logger.LogInformation("User {user} not found in the database.", groupedLog.Key);
                    }
                }
                await repo.AddItemsAsync(listToAdd.ToArray());
            }
        }
    }
}