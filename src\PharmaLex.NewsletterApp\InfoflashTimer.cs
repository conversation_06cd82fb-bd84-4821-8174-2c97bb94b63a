﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;

namespace PharmaLex.NewsletterApp
{
    public class InfoflashTimer
    {
        private readonly IRepositoryFactory repoFactory;

        public InfoflashTimer(IRepositoryFactory repoFactory)
        {
            this.repoFactory = repoFactory;
        }

        [Function("InfoflashTimer")]
        [QueueOutput("%qn-infoflash%")]
        public async Task<NewsletterQueueMessage[]> Run([TimerTrigger("0 30 6-18 * * MON-FRI")] MyInfo timer, FunctionContext context)
        {
            var logger = context.GetLogger("InfoflashTickFunction");
            logger.LogInformation($"Timer trigger function executed at: {DateTime.UtcNow}, Execution context: {context.InvocationId}");

            var allSubscriptions = this.repoFactory.Create<NewsletterSubscription>()
                .Configure(o => o
                    .Include(x => x.User)
                        .ThenInclude(x => x.CompanyUser));
            int day = (int)DateTime.UtcNow.DayOfWeek;
            int hour = DateTime.UtcNow.Hour;
            var eligibleSubscriptions = await allSubscriptions.Where(x =>
                    x.IsInfoflash && x.Active &&
                    (x.User.CompanyUser == null || x.User.CompanyUser.Active)).ToListAsync();
            return eligibleSubscriptions.Select(x => new NewsletterQueueMessage { Id = x.Id }).ToArray();
        }
    }
}
