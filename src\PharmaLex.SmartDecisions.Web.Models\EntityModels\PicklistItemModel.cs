﻿using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class PicklistModel : IModel
    {
        public int ContentTypeId { get; set; }
        public IEnumerable<PicklistItemModel> Items { get; set; }

        public PicklistModel()
        {
        }

        public PicklistModel(int contentTypeId, IEnumerable<PicklistItemModel> items)
        {
            this.ContentTypeId = contentTypeId;
            this.Items = items;
        }
    }

    public class PicklistItemModel : EntityModel, IModel
    {
    }
}
