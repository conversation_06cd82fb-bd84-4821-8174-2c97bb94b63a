﻿using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface IDbAccessProvider
    {
        Task<List<T>> Where<T>(Expression<Func<T, bool>> predicate, Func<IIncludable<T>, IIncludable<T>> options = null) where T : EntityBase;
        Task<List<T>> All<T>(Func<IIncludable<T>, IIncludable<T>> options = null) where T : EntityBase;
        Task<T> FirstOrDefault<T>(Expression<Func<T, bool>> predicate, Func<IIncludable<T>, IIncludable<T>> options = null) where T : EntityBase;
    }

    public class DbAccessProvider : IDbAccessProvider
    {
        private readonly IDistributedCacheServiceFactory cache;
        public DbAccessProvider(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }
        public async Task<List<T>> All<T>(Func<IIncludable<T>, IIncludable<T>> options = null) where T : EntityBase
        {
            return await this.Where<T>(_ => true, options);
        }
        public async Task<T> FirstOrDefault<T>(Expression<Func<T, bool>> predicate, Func<IIncludable<T>, IIncludable<T>> options = null) where T : EntityBase
        {
            return (await this.Where<T>(predicate, options)).FirstOrDefault();
        }
        public async Task<List<T>> Where<T>(Expression<Func<T, bool>> predicate, Func<IIncludable<T>, IIncludable<T>> options = null) where T : EntityBase
        {
            if (options == null) options = o => o;
            return (await this.cache.CreateEntity<T>().Configure(options).WhereAsync(predicate)).ToList();
        }
    }
}
