﻿using PharmaLex.DataAccess;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers.Interfaces
{
    public interface IRepository<T> where T : EntityBase
    {
        Task<IEnumerable<T>> GetAllItemsAsync();
        Task<T?> GetItemAsync(int id);
        Task AddItemAsync(T item);
        Task UpdateItemAsync(T item);
        Task DeleteItemAsync(int id);
    }
}
