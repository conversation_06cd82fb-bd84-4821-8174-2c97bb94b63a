﻿using AutoMapper;
using Microsoft.AspNetCore.Html;
using PharmaLex.Helpers;
using PharmaLex.SmartDecisions.Entities;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleContentListModel : IModel
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string SourceUrl { get; set; }
        public string SourcePublicationDate { get; set; }
        public string GoLiveDate { get; set; }
        public int ImportanceId { get; set; }
        public string ImportanceName { get; set; }

        public string Locale { get; set; }
        public int LocaleId { get; set; }

        public string Author { get; set; }
        public int? AuthorId { get; set; }

        public string Reviewer { get; set; }
        public int? ReviewerId { get; set; }

        public string PublishingState { get; set; }
        public int PublishingStateId { get; set; }
        public string NewsSource { get; set; }
        public int NewsSourceId { get; set; }
        public string Path { get; set; }
    }

    public class NewsArticleListModelMappingProfile : Profile
    {
        public NewsArticleListModelMappingProfile()
        {
            this.CreateMap<NewsSource, NewsSourceModel>();
            this.CreateMap<NewsCategory, NewsCategoryModel>()
                .ForMember(d => d.Children, s => s.MapFrom(x => x.ChildCategory))
                .ForMember(d => d.Selected, o => o.Ignore())
                .ForMember(d => d.DisplayTypeId, o => o.Ignore());
            this.CreateMap<User, NewsUserModel>()
                .ForMember(d => d.Name, s => s.MapFrom(x => x.FullName))
                .ForMember(d => d.IsLead, s => s.MapFrom(x => x.UserClaim.Any(y => y.Claim.ClaimType == "news" && y.Claim.Name == "NewsAuthoringLead")));
            this.CreateMap<NewsArticlePublishingState, NewsArticlePublishingStateModel>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }

    public class NewsSourceModel : EntityModel { }

    public class NewsCategoryModel : EntityModel
    {
        public int? ParentId { get; set; }
        public int SortOrder { get; set; }
        public bool Selected { get; set; }
        public int? DisplayTypeId { get; set; }
        public string LocalisationKey { get; set; }
        public List<NewsCategoryModel> Children { get; set; }
    }

    public class NewsArticlePublishingStateModel : EntityModel { }
    public class NewsUserModel : EntityModel
    {
        public bool IsLead { get; set; }
    }

    public class LocalisedModel : IModel
    {
        public int Id { get; set; }
        public HtmlString HtmlName { get; set; }
        public string Name => this.HtmlName?.Value;
    }

    public class LocalisedModel<T>: LocalisedModel, IModel
    {
        public T Localisee { get; set; }
    }
}
