﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using System;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class DeleteNewsletterInsightsDataTimer
    {
        private readonly INewsletterActivityRepository _newsletterActivityRepository;
        private readonly IEmailService _emailService;
        private readonly string[] _receiverEmails;

        public DeleteNewsletterInsightsDataTimer(INewsletterActivityRepository newsletterActivityRepository, IEmailService emailService, IConfiguration configuration)
        {
            _newsletterActivityRepository = newsletterActivityRepository;
            _emailService = emailService;
            _receiverEmails = configuration.GetValue<string>("receiverEmails").Split(";");
        }

        [Function("DeleteNewsletterInsightsDataTimer")]
        public async Task Run([TimerTrigger("0 0 10 * * SUN"/*, RunOnStartup = true*/)] FunctionContext context)
        {
            ILogger logger = context.GetLogger("DeleteNewsletterInsightsDataTimer");
            logger.LogInformation("Start processing time: {datetime}.", DateTime.Now);

            int totalItemsDeleted = 0;
            var startTime = DateTime.Now.AddYears(-1);
            logger.LogInformation("Delete newsletter activities older than: {startTime}.", startTime);

            try
            {
                totalItemsDeleted = await _newsletterActivityRepository.DeleteOldDataAsync(startTime);
                await _emailService.Send("Delete Newsletter Insights Data Function Run", $"Delete Newsletter Insights Data Function was executed successfully. Delete newsletter activities older than: {startTime}. Deleted items: {totalItemsDeleted}", _receiverEmails);
                logger.LogInformation("Delete Newsletter Insights Data Function was executed successfully. Delete newsletter activities older than: {startTime}. Deleted items: {totalItemsDeteled}", startTime, totalItemsDeleted);
            }
            catch (Exception ex)
            {
                await _emailService.Send("Delete Newsletter Insights Data Function Error", $"An error occurred while deleting old Newsletter Insights data. Exception: {ex.Message}", _receiverEmails);
                logger.LogError("An error occurred while deleting old Newsletter Insights data. Exception: {message}", ex.Message);
            }
        }
    }
}
