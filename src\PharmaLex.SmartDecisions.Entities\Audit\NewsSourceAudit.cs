﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsSourceAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
        public int? SortOrder { get; set; }
        public string LocalisationKey { get; set; }
    }
}
