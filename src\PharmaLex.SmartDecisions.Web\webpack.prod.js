const { merge } = require("webpack-merge");
const common = require("./webpack.common.js");
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
process.env.NODE_ENV = "development";

module.exports = merge(common, {
    mode: "development",
    devtool: "source-map",
    optimization: {
        minimize: true,
        minimizer: [new CssMinimizerPlugin(), "..."]
    },
    watch: false
});
