﻿plx = window.plx || {};

plx.formSubmitHandler = {
    init: () => {
        console.log("%cInitialising form error events.", "font-size:12px;font-weight:bold;color:#0c0");
        plx.formSubmitHandler.submitButtons = [...document.querySelectorAll("button[type='submit']")];
        plx.formSubmitHandler.controls = [...document.querySelectorAll("input, select, textarea")];
        plx.formSubmitHandler.setListeners();
    },
    setListeners: () => {
        plx.formSubmitHandler.submitButtons.forEach(btn => {
            btn.addEventListener("click", (e) => {
                const form = btn.closest("form");
                if (form) {
                    form.classList.add("submitted");
                    const validState = form.checkValidity();
                    if (!validState) {
                        const errorFields = [...form.querySelectorAll(":invalid")];
                        errorFields.forEach(el => {
                            el.classList.add("invalid");
                            el.addEventListener("input", () => {
                                el.classList.remove("invalid");
                            });
                            el.addEventListener("change", () => {
                                el.classList.remove("invalid");
                            });
                        });
                    }
                }
            });
        });

        plx.formSubmitHandler.controls.forEach(control => {
            control.addEventListener("focus", (e) => {
                control.classList.remove("invalid");
                control.removeAttribute("invalid");
            });
        });
    }
}