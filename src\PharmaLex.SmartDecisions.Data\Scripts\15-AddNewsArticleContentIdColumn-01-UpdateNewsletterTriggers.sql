﻿alter trigger [dbo].[Newsletter_Insert] on [dbo].[Newsletter]
for insert as
insert into [Audit].[Newsletter_Audit]
select 'I'
      ,[Id]
      ,[UserId]
      ,[CreatedDateUtc]
      ,[LocaleId]
      ,[UniqueKey]
      ,[NewsletterSubscriptionId] 
      ,[IsMonthly]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy]
      ,[NewsArticleContentIds] from [Inserted]
go

alter trigger [dbo].[Newsletter_Update] on [dbo].[Newsletter]
for update as
insert into [Audit].[Newsletter_Audit]
select 'U'
      ,[Id]
      ,[UserId]
      ,[CreatedDateUtc]
      ,[LocaleId]
      ,[UniqueKey] 
      ,[NewsletterSubscriptionId] 
      ,[IsMonthly]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy]
      ,[NewsArticleContentIds] from [Inserted]
go

alter trigger [dbo].[Newsletter_Delete] on [dbo].[Newsletter]
for delete as
insert into [Audit].[Newsletter_Audit]
select 'D'
      ,[Id]
      ,[UserId]
      ,[CreatedDateUtc]
      ,[LocaleId]
      ,[UniqueKey]
      ,[NewsletterSubscriptionId]
      ,[IsMonthly]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name())
      ,[NewsArticleContentIds]from [Deleted]
go
