﻿using System;
using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models.AzureSearch
{
    public class AzureSearchResultModel
    {
        public AzureSearchResultModel()
        {
            this.Categories = new List<int>();
        }
        public int Id { get; set; }
        public string Locale { get; set; }
        public int? LocaleId { get; set; }
        public int? NewsArticleId { get; set; }
#pragma warning disable CA1056 // Uri properties should not be strings
        public string FriendlyUrl { get; set; }
        public string SourceUrl { get; set; }
#pragma warning restore CA1056 // Uri properties should not be strings
        public string Title { get; set; }
        public int? NewsSourceId { get; set; }
        public DateTime? SourcePublicationDate { get; set; }
        public int PublishingStateId { get; set; }
        public List<int> Categories { get; set; }
        public int? ImportanceId { get; set; }
        public DateTime? GoLiveDate { get; set; }

    }
}
