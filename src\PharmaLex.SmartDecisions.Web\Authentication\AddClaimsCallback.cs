﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Authentication
{
    public class AddClaimsCallback : ITicketReceivedCallback
    {
        public async Task OnTicketReceived(TicketReceivedContext context)
        {
            ClaimsIdentity ci = context.Principal.Identity as ClaimsIdentity;

            var dbContextResolver = context.HttpContext.RequestServices.GetService<IPlxDbContextResolver>();
            var repoFactory = new RepositoryFactory(dbContextResolver, ci.GetEmail());
            ITrackingRepository<User> ur = repoFactory.CreateTracking<User>();

            User u = await ur.Configure(x =>
                x.Include(y => y.UserClaim)
                    .ThenInclude(y => y.Claim)
            ).FirstOrDefaultAsync(x => x.Email.ToLower() == ci.GetEmail());
            if (u == null)
            {
                if (!context.Principal.IsPharmaLexUser())
                {
                    context.Response.Redirect("/unauthorised");
                    context.HandleResponse();
                    return;
                }
                u = new User
                {
                    GivenName = ci.GetClaimValue(ClaimConstants.GivenName),
                    FamilyName = ci.GetClaimValue(ClaimConstants.FamilyName),
                    Email = ci.GetEmail(),
                    LastLoginDate = DateTime.Now
                };
                ur.Add(u);

                ci.AddClaim(new System.Security.Claims.Claim("plx:companyid", "0"));
                ci.AddClaim(new System.Security.Claims.Claim("plx:userisactive", "false"));
            }
            else
            {
                if (!context.Principal.IsPharmaLexUser())
                {
                    IRepository<CompanyUser> cur = repoFactory.Create<CompanyUser>();
                    CompanyUser cu = await cur.Configure(o => o
                        .Include(x => x.Company)
                            .ThenInclude(x => x.CompanyContentType)
                        .Include(x => x.Company)
                            .ThenInclude(x => x.CompanyNewsCategory)
                        ).FirstOrDefaultAsync(x => x.UserId == u.Id && x.Active);

                    if(cu == null || (cu.Company.CompanyContentType.Count < 1 && cu.Company.CompanyNewsCategory.Count < 1))
                    {
                        context.Response.Redirect("/unauthorised");
                        context.HandleResponse();
                        return;
                    }

                    ci.AddClaim(new System.Security.Claims.Claim($"plx:companyid", (cu?.CompanyId ?? 0).ToString()));
                    ci.AddClaim(new System.Security.Claims.Claim("plx:userisactive", (cu?.Active ?? false).ToString()));
                }
                else
                {
                    ci.AddClaim(new System.Security.Claims.Claim("plx:companyid", "0"));
                    ci.AddClaim(new System.Security.Claims.Claim("plx:userisactive", "false"));
                }

                foreach (UserClaim uc in u.UserClaim)
                {
                    ci.AddClaim(new System.Security.Claims.Claim($"{uc.Claim.ClaimType}:{uc.Claim.Name}", uc.ClaimId.ToString()));
                }

                IConfiguration config = context.HttpContext.RequestServices.GetService<IConfiguration>();
                u.LastLoginDate = DateTime.Now;
            }

            await ur.SaveChangesAsync();
            context.HttpContext.RequestServices.GetService<IDistributedCacheService>().Invalidate("User", "UserClaim", "UserClaim.Claim");
            ci.AddClaim(new System.Security.Claims.Claim("plx:userid", u.Id.ToString()));

            IRepository<ContentItem> cir = repoFactory.Create<ContentItem>();
            if ((await cir.Configure().FirstOrDefaultAsync(x => x.Owner.ToLower() == ci.GetEmail().ToLower())) != null)
            {
                ci.AddClaim(new System.Security.Claims.Claim("plx:RecordOwner", "true"));
            }
        }
    }
}
