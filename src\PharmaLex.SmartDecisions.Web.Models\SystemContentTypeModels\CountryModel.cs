﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class CountryModel : NamedContentTypeModel
    {
        public int? MapId { get; set; }
        public string TwoLetterCode { get; set; }

        public CountryModel()
        {
            this.ContentTypeName = "Country";
        }
    }

    public class CountryModelMappingProfile : Profile
    {
        public CountryModelMappingProfile()
        {
            this.CreateMap<ContentItem, CountryModel>()
                .ForMember(d => d.TwoLetterCode, s => s.MapFrom(x => x.GetFieldValue("Two letter code")))
                .ForMember(d => d.MapId, s => s.MapFrom(x => x.GetNullableIntFieldValue("Map ID")));
        }
    }
}
