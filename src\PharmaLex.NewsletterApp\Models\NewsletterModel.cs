﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace PharmaLex.NewsletterApp
{
    public class NewsletterModel
    {
        public string Date { get; set; }
        public string Key { get; set; }
        public string Recepient { get; set; }
        public string EditPreferencesLink { get; set; }
        public string SearchLink { get; set; }
        public string Subject { get; set; }
        public string UnsubscribeLink { get; set; }
        public string Environment { get; set; }
        public NewsArticleProductGroupModel HeadlineArticles { get; set; }
        public List<NewsArticleProductGroupModel> ProductGroupArticles { get; set; }

        [JsonIgnore]
        public bool IsMonthly { get; set; }
        [JsonIgnore]
        public bool IsInfoflash { get; set; }
        [JsonIgnore]
        public string IsoLanguageCode { get; set; }
        [JsonIgnore]
        public int LocaleId { get; set; }
    }

    public class NewsArticleProductGroupModel
    {
        public string ProductGroupClassName { get; set; }
        public string ProductGroupHexColour { get; set; }
        public string ProductGroupName { get; set; }
        public bool IsLastItem { get; set; }
        public List<NewsArticleModel> Articles { get; set; } = new List<NewsArticleModel>();
    }

    public class NewsArticleModel
    {
        public string Date { get; set; }
        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string NewsSource { get; set; }
        public string NewsSourceClassName { get; set; }
        public string SourceUrl { get; set; }
        public string PublicUrl { get; set; }
        public string ImpactAssessmentSummary { get; set; }
        public string BlobName { get; set; }
        public string FullArticleText { get; set; }
        public bool IsLastItem { get; set; }
        public GeographicalScopeCategoryModel GeographicalScope { get; set; }
    }

    public class CategoryModel
    {
        public bool Subscribed { get; set; }
        public string Name { get; set; }
        public bool IsLastItem { get; set; }
    }

    public class GeographicalScopeCategoryModel : CategoryModel
    {
        public string CountryCode { get; set; }
    }
}
