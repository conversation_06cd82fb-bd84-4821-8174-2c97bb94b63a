﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public abstract class BaseContentItemController : BaseController
    {
        protected readonly IDistributedCacheServiceFactory cacheFactory;
        protected readonly IMapper mapper;

        protected BaseContentItemController(IDistributedCacheServiceFactory cacheFactory, IMapper mapper)
        {
            this.cacheFactory = cacheFactory;
            this.mapper = mapper;
        }

        protected async Task<ListContentItemViewModel> GetContentItems(int id, bool ownedRecordsOnly = false)
        {
            var cache = cacheFactory.Create<ListContentItemViewModel>("ContentItem", "FieldValue");
            ListContentItemViewModel lcivm = await cache.GetOrCreateAsync($"ListContentItems-{id}", async () =>
            {
                var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
                var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue));
                ContentTypeModel ctm = await ctc.FirstOrDefaultAsync(x => x.Id == id);
                var fields = ctm.Field.Where(x => x.System && (x.FieldTypeId == (int)FieldType.Picklist || x.FieldTypeId == (int)FieldType.Relationship)).ToList();
                var fieldIds = fields.Select(x => x.Id).ToArray();
                var columnModels = mapper.Map<List<FieldColumnModel>>(fields);

                var relatedContentTypeIds = fields.Select(x => x.RelatedContentTypeId.Value).ToArray();
                List<FilterModel> filterModels = (await cacheFactory.CreateEntity<ContentType>().WhereAsync(x => relatedContentTypeIds.Contains(x.Id)))
                    .Select(x => new FilterModel(x.Name, cacheFactory.CreateEntity<ContentItem>().Where(y => y.ContentTypeId == x.Id).Select(x => x.Name))).ToList();

                var items = (await cic.WhereAsync(x => x.ContentTypeId == id && (!ownedRecordsOnly || x.Owner.ToLower() == ((ClaimsIdentity)this.User.Identity).GetEmail().ToLower()))).Select(x =>
                {
                    x.ContentType = ctm;
                    x.Values = x.Values.Where(y => fieldIds.Contains(y.FieldId));
                    return x;
                });

                columnModels.Insert(0, ListContentItemViewModel.NameColumn);
                filterModels.Insert(0, ListContentItemViewModel.NameFilter);
                return new ListContentItemViewModel
                {
                    ContentType = ctm,
                    Columns = columnModels,
                    Filters = filterModels,
                    Items = items
                };
            });
            return lcivm;
        }

        protected void MapFieldValues(IFormCollection model, ContentItem ci, ContentType ct, ITrackedEntityCacheServiceProxy<FieldValue> fvc)
        {
            foreach (Field f in ct.Field)
            {
                string key = $"f{f.Id}";
                if ((model.ContainsKey(key) && !String.IsNullOrEmpty(model[key])) || f.FieldType == FieldType.Bool)
                {
                    var fv = fvc.FirstOrDefault(x => x.ContentItemId == ci.Id && x.FieldId == f.Id) ?? new FieldValue
                    {
                        ContentItemId = ci.Id,
                        FieldId = f.Id,
                        Name = $"{ct.Name} - {f.Name}"
                    };
                    if (fv.Id == 0)
                    {
                        fvc.Add(fv);
                    }
                    switch (f.FieldType)
                    {
                        case FieldType.Bool: fv.Value = model.ContainsKey(key) && model[key] == "on" ? "true" : "false"; break;
                        case FieldType.Picklist or FieldType.Relationship:
                            if (f.Length > 1)
                            {
                                fv.Value = String.Join('|', model[key]);
                                break;
                            }
                            goto default;
                        default: fv.Value = model[key]; break;
                    }
                }
                else
                {
                    var fv = fvc.FirstOrDefault(x => x.ContentItemId == ci.Id && x.FieldId == f.Id);
                    if (fv != null)
                    {
                        fvc.Remove(fv);
                    }
                }
            }
        }
    }
}
