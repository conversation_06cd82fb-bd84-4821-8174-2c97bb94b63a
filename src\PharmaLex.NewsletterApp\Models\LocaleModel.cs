﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.NewsletterApp
{
    public class LocaleModel
    {
        public int Id { get; set; }
        public string IsoLanguageCode { get; set; }
        public string LocalisationKey { get; set; }
    }

    public class LocaleMappingProfile : Profile
    {
        public LocaleMappingProfile()
        {
            this.CreateMap<Locale, LocaleModel>();
        }
    }
}
