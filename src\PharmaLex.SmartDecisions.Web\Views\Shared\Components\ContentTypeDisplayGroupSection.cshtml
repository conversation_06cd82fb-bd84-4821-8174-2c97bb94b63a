﻿<script type="text/x-template" id="content-type-display-group-section-template">
    <div class="flex columns white-color rounded-large mb-1">
        <div class="bordered rounded-large grey-1-background pb-2 shadowed ">
            <div class="flex justify-end">
                <i class="m-icon pt-1" v-on:click="edit">edit</i>
                <i v-if="!isaside" class="m-icon pt-1 pr-1" v-on:click="remove">close</i>
            </div>
            <div v-if="!isaside" class="form-group p-2">
                <label for="SectionTitle1">Title</label>
                <input name="SectionTitle1" type="text" autofocus="autofocus" id="SectionTitle1" :value="data.title" class="white-background" disabled/>
            </div>
            <div class="p-1">
                <label for="SectionTitle1" class="ml-2">Groups</label>
                <div v-for="(group, index) in data.groups" :key="index" class="flex columns blue-background p-1 pt-2 m-1 bordered rounded-large">
                    <div v-for="field in group.fields" class="quality-background bordered rounded-large lozenge p-1 mb-1 justify-space-between mb-1">
                        <span class="pt-1 pb-1">{{fields.find(x => x.id === field).name}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('content-type-display-group-section', {
        template: '#content-type-display-group-section-template',
        data() {
            return { };
        },
        props: {
            data: {
                type: Object,
                requred: false
            },
            fields: {
                type: Array,
                required: false
            },
            isaside: {
                type: Boolean,
                required: true
            },
            columnindex: {
                type: Number,
                required: false
            },
            rowindex: {
                type: Number,
                required: false
            }
        },
        methods: {
            edit() {
                let updateData = {...this.data};;

                updateData.isAside = this.isaside;
                updateData.columnIndex = this.columnindex;
                updateData.rowIndex = this.rowindex;

                this.$emit("update-group-section", updateData);
            },
            remove() {
                this.$emit('remove-group-section', {columnIndex: this.columnindex, rowIndex: this.rowindex});
            }
        }
    });
</script>