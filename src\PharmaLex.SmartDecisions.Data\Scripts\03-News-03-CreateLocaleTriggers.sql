﻿create trigger [dbo].[Locale_Insert] on [dbo].[Locale]
for insert as
insert into [Audit].[Locale_Audit]
select 'I'
      ,[Id]
      ,[Name]
      ,[LocalisedName]
      ,[IsoLanguageCode]
      ,[IsoScriptCode]
      ,[IsoCountryCode]
      ,[Active]
      ,[Default]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[Locale_Update] on [dbo].[Locale]
for update as
insert into [Audit].[Locale_Audit]
select 'U'
      ,[Id]
      ,[Name]
      ,[LocalisedName]
      ,[IsoLanguageCode]
      ,[IsoScriptCode]
      ,[IsoCountryCode]
      ,[Active]
      ,[Default]
      ,[LocalisationKey]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[Locale_Delete] on [dbo].[Locale]
for delete as
insert into [Audit].[Locale_Audit]
select 'D'
      ,[Id]
      ,[Name]
      ,[LocalisedName]
      ,[IsoLanguageCode]
      ,[IsoScriptCode]
      ,[IsoCountryCode]
      ,[Active]
      ,[Default]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LocalisationKey]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
