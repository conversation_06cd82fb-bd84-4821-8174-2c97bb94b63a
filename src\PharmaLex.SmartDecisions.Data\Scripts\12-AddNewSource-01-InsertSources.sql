﻿insert into [dbo].[NewsSource] select 'ARPP', 'https://ich.org', 65, N'arpp', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Medicines for Europe', 'https://ich.org', 66, N'medicines-for-europe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Adelphe', 'https://ich.org', 67, N'adelphe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Digital Health Agency', 'https://ich.org', 68, N'digital-health-agency', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'RFCRPV', 'https://ich.org', 69, N'rfcrpc', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'CNCPP', 'https://ich.org', 70, N'cncpp', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'DGFIP', 'https://ich.org', 71, N'dgfip', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[NewsSource] select 'Ministry of Economy', 'https://ich.org', 72, N'ministry-of-economy', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsSource].arpp', 'ARPP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].medicines-for-europe', 'Medicines for Europe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].adelphe', 'Adelphe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].digital-health-agency', 'Digital Health Agency', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].rfcrpc', 'RFCRPV', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cncpp', 'CNCPP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].dgfip', 'DGFIP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ministry-of-economy', 'Ministry of Economy', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsSource].arpp', 'ARPP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].medicines-for-europe', 'Medicines for Europe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].adelphe', 'Adelphe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].digital-health-agency', 'Agence du numérique en santé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].rfcrpc', 'RFCRPV', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cncpp', 'CNCPP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].dgfip', 'DGFIP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ministry-of-economy', 'Ministère de l’économie', getdate(), '<EMAIL>', getdate(), '<EMAIL>'