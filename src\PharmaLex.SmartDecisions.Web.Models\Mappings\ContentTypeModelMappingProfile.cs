﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class ContentTypeModelMappingProfile : Profile
    {
        public ContentTypeModelMappingProfile()
        {
            this.CreateMap<ContentType, ContentTypeModel>()
                .ForMember(d => d.Displays, s => s.MapFrom(x => x.ContentTypeDisplay))
                .ForMember(d => d.Category, s => s.MapFrom(x => (ContentTypeCategory)x.ContentTypeCategoryId))
                .ForMember(d => d.PicklistFields, s => s.Ignore());

            this.CreateMap<ContentTypeModel, ContentType>()
                .ForMember(d => d.Category, s => s.Ignore())
                .ForMember(d => d.Id, s => s.Ignore())
                .ForMember(d => d.ContentItem, s => s.Ignore())
                .ForMember(d => d.ContentTypeDisplay, s => s.Ignore())
                .ForMember(d => d.CompanyContentType, s => s.Ignore())
                .ForMember(d => d.CreatedDate, s => s.Ignore())
                .ForMember(d => d.CreatedBy, s => s.Ignore())
                .ForMember(d => d.LastUpdatedDate, s => s.Ignore())
                .ForMember(d => d.LastUpdatedBy, s => s.Ignore());
        }
    }
}
