﻿using AutoMapper;
using Newtonsoft.Json;
using PharmaLex.SmartDecisions.Entities;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.NewsletterApp
{
    public class NewsletterNewsArticleModel
    {
        public NewsletterNewsArticleModel()
        {
            NewsArticleContentIds = new List<int>();
        }

        public List<int> NewsArticleContentIds { get; set; }

        public int UserId { get; set; }
    }

    public class NewsletterNewsArticleMappingProfile : Profile
    {
        public NewsletterNewsArticleMappingProfile()
        {
            this.CreateMap<Newsletter, NewsletterNewsArticleModel>()
                .ForMember(d => d.NewsArticleContentIds, s => s.MapFrom(x => !string.IsNullOrEmpty(x.NewsArticleContentIds) ? 
                                         JsonConvert.DeserializeObject<List<int>>(x.NewsArticleContentIds) : 
                                         new List<int>()));

        }
    }
}
