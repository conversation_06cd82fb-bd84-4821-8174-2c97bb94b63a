﻿BEGIN TRANSACTION;

INSERT INTO[dbo].[UserNewsCategory] ([Order], [UserId], [NewsCategoryId], [CreatedDateUtc] ,[CreatedDate], [CreatedBy] ,[LastUpdatedDate], [LastUpdatedBy])
SELECT [Order], [UserId], c.[NewsCategoryId], c.[CreatedDateUtc], c.[CreatedDate], c.[CreatedBy], c.[LastUpdatedDate], c.[LastUpdatedBy]
FROM [dbo].[NewsletterSubscriptionNewsCategory] as c
JOIN (SELECT MAX(Id) AS Id, UserId FROM [NewsletterSubscription] GROUP BY UserId) AS subs 
ON subs.Id = c.NewsletterSubscriptionId

COMMIT;