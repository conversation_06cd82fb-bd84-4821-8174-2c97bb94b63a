﻿using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Http;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface ILocalisationService
    {
        HtmlString LocaliseInterpolate(string formatKey, params string[] keys);
        HtmlString Localise(string key, bool escapeJsChars = false);
        HtmlString LocaliseSafe(string key);
        Task<List<T>> LocaliseList<T>() where T : class, ILocalisableLookup;
        Task<List<LocalisedModel<T>>> LocaliseEnum<T>() where T : Enum;
        Task<List<NewsCategory>> LocaliseNewsCategories(int localeId);
    }

    public class LocalisationService : ILocalisationService
    {
        private readonly ILocaleService localeService;
        private readonly IHttpContextAccessor contextAccessor;
        private readonly IDistributedCacheServiceFactory cacheService;

        public LocalisationService(IDistributedCacheServiceFactory cacheService,
            IHttpContextAccessor contextAccessor,
            ILocaleService localeService)
        {
            this.cacheService = cacheService;
            this.localeService = localeService;
            this.contextAccessor = contextAccessor;
        }
        public async Task<List<T>> LocaliseList<T>() where T : class, ILocalisableLookup
        {
            int currentLocale = this.localeService.Current.Id;
            int defaultLocale = this.localeService.Default.Id;
            var cache = this.cacheService.Create<List<T>>("MultilingualResource");
            return await cache.GetOrCreateAsync($"List<{typeof(T).Name}>-{currentLocale}", async () =>
            {
                var localisableItems = (await this.cacheService.CreateEntity<T>().AllAsync()).ToList();
                var locResources = (await this.cacheService.CreateEntity<MultilingualResource>()
                        .WhereAsync(x => x.Key.StartsWith($"[{typeof(T).Name}]"))).ToList();

                foreach (var item in localisableItems)
                {
                    string localisationKey = $"[{typeof(T).Name}].{item.LocalisationKey}";
                    item.Name = locResources.FirstOrDefault(x => x.LocaleId == currentLocale && x.Key == localisationKey)?.Content ??
                            locResources.FirstOrDefault(x => x.LocaleId == defaultLocale && x.Key == localisationKey)?.Content ??
                            $"[{localisationKey}]";
                }

                return localisableItems;
            });
        }

        public async Task<List<LocalisedModel<T>>> LocaliseEnum<T>() where T : Enum
        {
            int currentLocale = this.localeService.Current.Id;
            int defaultLocale = this.localeService.Default.Id;
            var cache = this.cacheService.Create<List<LocalisedModel<T>>>("MultilingualResource");
            return await cache.GetOrCreateAsync($"List<{typeof(T).Name}>-{currentLocale}", async () =>
            {
                var localisableItems = Enum.GetNames(typeof(T)).ToList();
                var locResources = (await this.cacheService.CreateEntity<MultilingualResource>()
                        .WhereAsync(x => x.Key.StartsWith($"[{typeof(T).Name}]"))).ToList();
                var result = new List<LocalisedModel<T>>();
                foreach (var item in localisableItems)
                {
                    string localisationKey = $"[{typeof(T).Name}].{item.ToLowerInvariant()}";
                    result.Add(new LocalisedModel<T>
                    {
                        HtmlName = new HtmlString(
                            locResources.FirstOrDefault(x => x.LocaleId == currentLocale && x.Key == localisationKey)?.Content ??
                            locResources.FirstOrDefault(x => x.LocaleId == defaultLocale && x.Key == localisationKey)?.Content ??
                            $"[{localisationKey}]"),
                        Id = (int)Enum.Parse(typeof(T), item),
                        Localisee = (T)Enum.Parse(typeof(T), item)
                    });
                }

                return result;
            });
        }

        public HtmlString LocaliseSafe(string key)
        {
            return this.Localise(key, true);
        }

        public HtmlString LocaliseInterpolate(string formatKey, params string[] keys)
        {
            string format = this.Localise(formatKey).Value;
            var parts = format.Split("{}");
            
            string result = parts[0];
            for (int i = 0; i < keys.Length; i++)
            {
                result += keys[i];
                if (parts.Length > i + 1) result += parts[i + 1];
            }

            return new HtmlString(result);
        }

        public HtmlString Localise(string key, bool escapeJsChars = false)
        {
            var cache = this.cacheService.Create<LocalisedModel>("MultilingualResource");
            var mrm = cache.GetOrCreate($"{key}-{this.localeService.Current.Id}", () =>
            {
                MultilingualResource mr = this.cacheService.CreateEntity<MultilingualResource>()
                .FirstOrDefault(x => x.LocaleId == this.localeService.Current.Id && x.Key == key);

                if (mr == null && this.localeService.Current.Id != this.localeService.Default.Id)
                {
                    mr = this.cacheService.CreateEntity<MultilingualResource>()
                    .FirstOrDefault(x => x.LocaleId == this.localeService.Default.Id && x.Key == key);
                }

                return new LocalisedModel
                {
                    HtmlName = new HtmlString(mr?.Content)
                };
            });

            return !string.IsNullOrEmpty(mrm.HtmlName.Value) ? 
                escapeJsChars ? new HtmlString(mrm.HtmlName.Value?.Replace("'", "\\'")) : mrm.HtmlName 
                : 
                new HtmlString(this.contextAccessor.HttpContext.User.Identity.IsAuthenticated ? "[" + key + "]" : "");
        }

        public async Task<List<NewsCategory>> LocaliseNewsCategories(int localeId)
        {
            var localisableItemsCache = this.cacheService.CreateEntity<NewsCategory>().Configure(x => x
                       .Include(y => y.ChildCategory)
                        .ThenInclude(y => y.ChildCategory));

            var localisableItems = (await localisableItemsCache.AllAsync()).ToList();

            var locResourcesCache = this.cacheService.CreateEntity<MultilingualResource>();
            var locResources = (await locResourcesCache.WhereAsync(x => x.Key.StartsWith($"[NewsCategory]"))).ToList();

            foreach (var item in localisableItems)
            {
                string localisationKey = $"[NewsCategory].{item.LocalisationKey}";
                item.Name =
                    locResources.FirstOrDefault(x => x.LocaleId == localeId && x.Key == localisationKey)?.Content ??
                    localisationKey;

                foreach (var childItem in item.ChildCategory)
                {
                    localisationKey = $"[NewsCategory].{childItem.LocalisationKey}";
                    childItem.Name =
                        locResources.FirstOrDefault(x => x.LocaleId == localeId && x.Key == localisationKey)?.Content ??
                        localisationKey;
                }
            }

            return localisableItems;
        }
    }
}
