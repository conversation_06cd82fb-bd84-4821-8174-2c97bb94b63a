﻿alter trigger [dbo].[User_Insert] on [dbo].[User]
for insert as
insert into [Audit].[User_Audit]
select 'I', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId] from [Inserted]
go

alter trigger [dbo].[User_Update] on [dbo].[User]
for update as
insert into [Audit].[User_Audit]
select 'U', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LocaleId] from [Inserted]
go

alter trigger [dbo].[User_Delete] on [dbo].[User]
for delete as
insert into [Audit].[User_Audit]
select 'D', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()), [LocaleId] from [Deleted]
go