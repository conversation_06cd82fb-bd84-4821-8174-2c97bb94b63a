﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32210.238
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.Web", "src\PharmaLex.SmartDecisions.Web\PharmaLex.SmartDecisions.Web.csproj", "{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.Data", "src\PharmaLex.SmartDecisions.Data\PharmaLex.SmartDecisions.Data.csproj", "{FADCAF14-BD07-431C-B407-34053807C85B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.Web.Helpers", "src\PharmaLex.SmartDecisions.Web.Helpers\PharmaLex.SmartDecisions.Web.Helpers.csproj", "{0955AEF4-5F03-44A4-9123-0BDDECC66F9A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.Web.Models", "src\PharmaLex.SmartDecisions.Web.Models\PharmaLex.SmartDecisions.Web.Models.csproj", "{D2D7F47E-AA18-48EF-8493-D543922619AC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.Entities", "src\PharmaLex.SmartDecisions.Entities\PharmaLex.SmartDecisions.Entities.csproj", "{CAE2F870-158E-440A-809A-A0D3EF1DE4FD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.NewsletterApp", "src\PharmaLex.NewsletterApp\PharmaLex.NewsletterApp.csproj", "{A09C6903-67CA-4544-AFFF-1E0C8954E8CB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{9DF4D823-A26A-4B9F-9B3F-64A223DA33D2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.IntegrationTests", "test\PharmaLex.SmartDecisions.IntegrationTests\PharmaLex.SmartDecisions.IntegrationTests.csproj", "{34F0819E-2E98-4256-B85B-149B7B138BD1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartDecisions.Tests", "test\PharmaLex.SmartDecisions.Tests\PharmaLex.SmartDecisions.Tests.csproj", "{0C1DF5A5-7193-49B3-BAA8-98C88BDFD63E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{44BBCFD6-FCE9-47C8-9765-8ADBCB41120B}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		azure-pipelines-deploy-dr.yml = azure-pipelines-deploy-dr.yml
		azure-pipelines-deploy.yml = azure-pipelines-deploy.yml
		azure-pipelines.yml = azure-pipelines.yml
		README.md = README.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Release|Any CPU.Build.0 = Release|Any CPU
		{FADCAF14-BD07-431C-B407-34053807C85B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FADCAF14-BD07-431C-B407-34053807C85B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FADCAF14-BD07-431C-B407-34053807C85B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FADCAF14-BD07-431C-B407-34053807C85B}.Release|Any CPU.Build.0 = Release|Any CPU
		{0955AEF4-5F03-44A4-9123-0BDDECC66F9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0955AEF4-5F03-44A4-9123-0BDDECC66F9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0955AEF4-5F03-44A4-9123-0BDDECC66F9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0955AEF4-5F03-44A4-9123-0BDDECC66F9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2D7F47E-AA18-48EF-8493-D543922619AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2D7F47E-AA18-48EF-8493-D543922619AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2D7F47E-AA18-48EF-8493-D543922619AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2D7F47E-AA18-48EF-8493-D543922619AC}.Release|Any CPU.Build.0 = Release|Any CPU
		{CAE2F870-158E-440A-809A-A0D3EF1DE4FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CAE2F870-158E-440A-809A-A0D3EF1DE4FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CAE2F870-158E-440A-809A-A0D3EF1DE4FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CAE2F870-158E-440A-809A-A0D3EF1DE4FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{A09C6903-67CA-4544-AFFF-1E0C8954E8CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A09C6903-67CA-4544-AFFF-1E0C8954E8CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A09C6903-67CA-4544-AFFF-1E0C8954E8CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A09C6903-67CA-4544-AFFF-1E0C8954E8CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{34F0819E-2E98-4256-B85B-149B7B138BD1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34F0819E-2E98-4256-B85B-149B7B138BD1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34F0819E-2E98-4256-B85B-149B7B138BD1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34F0819E-2E98-4256-B85B-149B7B138BD1}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C1DF5A5-7193-49B3-BAA8-98C88BDFD63E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C1DF5A5-7193-49B3-BAA8-98C88BDFD63E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C1DF5A5-7193-49B3-BAA8-98C88BDFD63E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C1DF5A5-7193-49B3-BAA8-98C88BDFD63E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1} = {1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}
		{FADCAF14-BD07-431C-B407-34053807C85B} = {1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}
		{0955AEF4-5F03-44A4-9123-0BDDECC66F9A} = {1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}
		{D2D7F47E-AA18-48EF-8493-D543922619AC} = {1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}
		{CAE2F870-158E-440A-809A-A0D3EF1DE4FD} = {1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}
		{A09C6903-67CA-4544-AFFF-1E0C8954E8CB} = {1EAEC5DF-E1AD-4AA5-8E42-006DAA80DA7C}
		{34F0819E-2E98-4256-B85B-149B7B138BD1} = {9DF4D823-A26A-4B9F-9B3F-64A223DA33D2}
		{0C1DF5A5-7193-49B3-BAA8-98C88BDFD63E} = {9DF4D823-A26A-4B9F-9B3F-64A223DA33D2}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {69747315-3ACC-4AD0-9BD5-40781B60028B}
	EndGlobalSection
EndGlobal
