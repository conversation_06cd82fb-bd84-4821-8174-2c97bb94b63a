﻿@using Microsoft.AspNetCore.Localization;
@using Newtonsoft.Json;
@using PharmaLex.SmartDecisions.Entities.Enums;
@using PharmaLex.SmartDecisions.Web.Models.EntityModels;
@using PharmaLex.SmartDecisions.Web.Models.ViewModels;
@{
    var requestCulture = this.Context.Features.Get<IRequestCultureFeature>().RequestCulture.UICulture.Name;
    var newsletterEventTypes = Enum.GetValues(typeof(NewsletterActivityEventType))
    .Cast<NewsletterActivityEventType>()
    .Select(v => new { id = (int)v, name = (v == NewsletterActivityEventType.Spamreport ? "Spam Report" : v.ToString()) })
    .ToList();
}


<div id="newsletter-activity" v-cloak>
    <div class="sub-header">
        <h2>Newsletter Activity Insights </h2>
    </div>
    <filtered-table :items="newsletterActivities"
                    :columns="columns"
                    :filters="filters"
                    :link="link"
                    :total-items-count="totalItemCount"
                    :filtered-count="filteredCount"
                    v-on:filter="onFilter"
                    v-on:sort="onSort"
                    v-on:on-page-index-change="onPageIndexChange"
                    v-on:on-page-size-change="onPageSizeChange"
                    v-on:on-load-state="onLoadState"
                    :resources="resources">
    </filtered-table>   
</div>


@section Scripts {
    <script type="text/javascript">
        const convertDate = date => {
            return Intl.DateTimeFormat('@requestCulture', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric'
            }).format(new Date(date));
        };

        const date = new Date();

        var pageConfig = {
            appElement: '#newsletter-activity',
            data: function () {
                return {
                    newsletterActivities:[],
                    link: '',
                    totalItemCount: 0,
                    filteredCount: 0,
                    offset: 0,
                    pageSize: 25,
                    resources: {
                        sortByFormat: 'Sort by {}',
                    },
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'company',
                                sortKey: 'company',
                                header: 'Company',
                                type: 'text'
                            },
                            {
                                dataKey: 'userFullName',
                                sortKey: 'userFullName',
                                header: 'User',
                                type: 'text'
                            },
                            {
                                dataKey: 'email',
                                sortKey: 'email',
                                header: 'Email',
                                type: 'text'
                            },
                            {
                                dataKey: 'eventTypeIconCell',
                                sortKey: 'eventType',
                                header: 'Event',
                                type: 'icon'
                            },
                            {
                                dataKey: 'message',
                                sortKey: 'message',
                                header: 'Message',
                                type: 'text',
                                style: 'width: 20%;'
                            },
                            {
                                dataKey: 'receivedUTCDate',
                                sortKey: 'receivedUTCDate',
                                header: 'Event Date (UTC)',
                                sortComparer: 'date',
                                type: 'date',
                                edit: {
                                    type: 'plain',
                                    required: true,
                                    convert: convertDate,
                                },
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'company',
                            options: [],
                            type: 'search',
                            header: 'Company',
                            fn: v => p => plx.escapeAccent(p.company.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'userFullName',
                            options: [],
                            type: 'search',
                            header: 'User',
                            fn: v => p => plx.escapeAccent(p.userFullName.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'email',
                            options: [],
                            type: 'search',
                            header: 'Email',
                            fn: v => p => plx.escapeAccent(p.email.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'eventTypeIconCell',
                            options: @Html.Raw(JsonConvert.SerializeObject(newsletterEventTypes)),
                            filterCollection: 'eventTypeId',
                            display: 'name',                           
                            type: 'select-multiple',
                            header: 'Event Types',                           
                            convert: v => v,
                        },
                        {
                            key: 'message',
                            options: [],
                            type: 'search',
                            header: 'Message',
                            fn: v => p => plx.escapeAccent(p.message.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'receivedUTCDate',
                            options: [],
                            type: 'date',
                            header: 'Event Date',
                            fn: v => p => convertDate(p.receivedUTCDate).toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ],
                    storageKey: `${window.location.href}(${this.$attrs.id || ''})`
                };
            },
            methods: {
                onLoadState() {
                    this.pageSize = this.getPageSize();
                    const filters = this.getFilters();
                    const sort = this.getSort();
                    this.getData(0, this.pageSize, filters, sort);
                },
                onSort(sortModel, pageIndex) {
                    this.pageSize = this.getPageSize();
                    this.getData(pageIndex * this.pageSize, this.pageSize, this.getFilters(), this.toSortValues(Object.entries(sortModel)));
                },
                onFilter(filters) {
                    const mapFilters = filters ? this.toKeyValueFilter(filters) : {};
                    this.getData(0, this.pageSize, mapFilters, this.getSort());
                },
                onPageIndexChange(pageIndex) {
                    this.pageSize = this.getPageSize();
                    this.getData(pageIndex * this.pageSize, this.pageSize, this.getFilters(), this.getSort());
                },
                onPageSizeChange(pageSize) {
                    this.pageSize = pageSize;
                    const filters = this.getFilters();
                    const sort = this.getSort();
                    this.getData(0, this.pageSize, filters, sort);
                },
                getData(skip, take, filters, sort) {
                    const requestOptions = {
                        method: "GET",
                        credentials: 'same-origin'
                    };

                    var tableFilters = Object.keys(filters).length !== 0 ? "&filters=" + filters.join('&filters=') : '';

                    fetch(`/manage/paged-newsletter-insights?skip=${skip}&take=${take}${tableFilters}&sort=${sort}`, requestOptions)
                        .then(r => r.json())
                        .then(body => {
                            this.newsletterActivities = body.data;
                            this.newsletterActivities.forEach(x => {
                                x.eventTypeIconCell = { title: '', icon: '' };
                                if (x.eventTypeId == @((int)NewsletterActivityEventType.Open)) {
                                    x.eventTypeIconCell = {
                                        title: 'Open',
                                        icon: 'mark_email_read',
                                        class: 'active-color'
                                    };
                                }
                                else if (x.eventTypeId == @((int)NewsletterActivityEventType.Dropped)) {
                                    x.eventTypeIconCell = {
                                        title: 'Dropped',
                                        icon: 'unsubscribe',
                                        class: 'error-color'
                                    };
                                }
                                else if (x.eventTypeId == @((int)NewsletterActivityEventType.Bounce)) {
                                    x.eventTypeIconCell = {
                                        title: 'Bounced',
                                        icon: 'unsubscribe',
                                        class: 'error-color'
                                    };
                                }
                                else if (x.eventTypeId == @((int)NewsletterActivityEventType.Spamreport)) {
                                    x.eventTypeIconCell = {
                                        title: 'Spam Report',
                                        icon: 'unsubscribe',
                                        class: 'error-color'
                                    };
                                }
                                else if (x.eventTypeId == @((int)NewsletterActivityEventType.Delivered)) {
                                    x.eventTypeIconCell = {
                                        title: 'Delivered',
                                        icon: 'move_to_inbox',
                                        class: 'success-color'
                                    };
                                }
                            });

                            this.filteredCount = body.paging.filteredCount;
                            this.totalItemCount = body.paging.totalItemCount;
                            this.offset = body.paging.offset;
                            this.pageSize = body.paging.limit;
                        });
                },
                getSort() {
                    let state = this.getState();
                    return state.sortModel ? this.toSortValues(state.sortModel) : "";
                },
                toSortValues(sortModel) {
                    let selectedProperty = sortModel.find(e => e[1] !== 0);

                    if (selectedProperty) {
                        if (selectedProperty[1] === 1) {
                            return `${selectedProperty[0]}=>asc`;
                        }
                        else if (selectedProperty[1] === -1) {
                            return `${selectedProperty[0]}=>desc`;
                        }
                    }

                    return "";
                },
                getFilters() {
                    let state = this.getState();
                    return state.filterModel ? this.toKeyValueFilter(state.filterModel) : {};
                },
                toKeyValueFilter(filters) {
                    return Object.keys(filters).map(function (key) {
                        if (key == "eventTypeIconCell" && filters[key].length > 0) {
                            var filterValue = filters[key].filter(x=>x.selected).map(y => y.key);
                            return `${key}=>${filterValue}`;
                        }
                        return `${key}=>${filters[key]}`;
                    });
                },
                getPageSize() {
                    const state = this.getState();
                    return state.pageSize || this.pageSize;
                },
                getState() {
                    let stateString = localStorage.getItem(this.storageKey);
                    return stateString ? JSON.parse(stateString) : {};
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/ImageCell" />
    <partial name="Components/FilteredTableV3" />
}