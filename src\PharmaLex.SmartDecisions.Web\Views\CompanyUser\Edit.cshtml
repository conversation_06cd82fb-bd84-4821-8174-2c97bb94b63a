﻿@model PharmaLex.SmartDecisions.Web.Models.CompanyUserViewModel
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.Authentication.B2C
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.Caching.Data
@inject IDistributedCacheServiceFactory cache
@inject IAuthorizationService AuthorizationService
@{
    ViewData["Title"] = ls.Localise("(news).users.manage");

    var model = Model.CompanyUser;
    var isPharmaLexUser = (await AuthorizationService.AuthorizeAsync(User, "Admin")).Succeeded;

    var companyManagerClaimId = (await cache.CreateEntity<Claim>()
        .FirstOrDefaultAsync(c => c.ClaimType == "company" && c.Name == "CompanyManager")).Id;
    var companyUserClaimId = (await cache.CreateEntity<Claim>()
        .FirstOrDefaultAsync(c => c.ClaimType == "company" && c.Name == "CompanyUser")).Id;
}

<div id="user" v-cloak>

    <div class="sub-header">
        <h2>@(model.Id > 0 ? $"{model.DisplayFullName} ({model.Email})" : string.Empty) @Model.Company.Name</h2>
        <div class="controls">
            @if (isPharmaLexUser)
            {
                <a href="/manage/company/edit/@Model.Company.Id" class="button secondary icon-button-back">@Model.Company.Name</a>
            }
            else
            {
                <a href="/users" class="button secondary">@ls.Localise("(news).users.manage")</a>
            }
            @if (Model.CompanyUser.Id > 0 && Model.CompanyUser.Email.ToLower() != User.GetEmail())
            {
                <a class="button icon-button-delete" href="/company/user/delete/@Model.CompanyUser.Id">@ls.Localise("[[table]].remove")</a>
            }
        </div>
    </div>
    <div v-if="model.activeLicenseLimitReached || model.totalLicenseLimitReached" class="flex-item flex flex-cols tile flex-x1 border-bottom">
        <div class="flex flex-nowrap flex-align-center" v-if="model.totalLicenseLimitReached">
            <i class="m-icon warning-color">warning</i>
            <p class="lead p-0 m-0 pl-2"> @ls.LocaliseInterpolate("(news).users.maximum-users-limit-format", Model.TotalUsersCount.ToString()) </p>
        </div>
        <div class="flex flex-nowrap flex-align-center" v-if="model.activeLicenseLimitReached">
            <i class="m-icon warning-color">warning</i>
            <p class="lead p-0 m-0 pl-2"> @ls.LocaliseInterpolate("(news).users.maximum-active-users-limit-format", Model.ActiveUsersCount.ToString()) </p>
        </div>
    </div>
    <form method="post" v-on:submit="onFormSubmit">
        <div class="flex flex-nowrap gapped-2">
            <div id="details-column" class="flex-item flex-x2 tile" v-if="user">
                <h5>@ls.Localise("details")</h5>
                <div class="form-group">
                    <label for="GivenName">@ls.Localise("(news).users.given-name")</label>
                    <input name="GivenName" v-model="user.givenName" type="text" :readonly="!!user.id" required autocomplete="no-thanks" />
                </div>
                <div class="form-group">
                    <label for="FamilyName">@ls.Localise("(news).users.family-name")</label>
                    <input name="FamilyName" v-model="user.familyName" type="text" :readonly="!!user.id" required autocomplete="no-thanks" />
                </div>
                <div class="form-group">
                    <label for="Email">Id</label>
                    <div class="flex flex-nowrap flex-align-center">
                        <input id="Email" name="Email" v-model="user.email" type="email" v-on:input="checkAvailability" :readonly="!!user.id" required autofocus autocomplete="no-thanks" />
                        <i v-if="available === false" class="m-icon error-color ml-2" title="This user id is not available">close</i>
                        <i v-if="available === true" class="m-icon success-color ml-2" title="This user id is available">check</i>
                    </div>
                </div>
                <template v-if="model.companyUser.id > 0 && model.companyUser.active">
                    <div class="form-group">
                        <a class="button" v-bind:class="{ secondary: lastLoginDate }" v-on:click="resend">@ls.Localise("(news).users.resend-email")</a>
                        <a class="button" v-bind:class="{ secondary: !invitationEmailLink || lastLoginDate }" v-on:click="copyLink">@ls.Localise("(news).users.copy-invitation-link")</a>
                    </div>
                </template>
            </div>
            <div class="flex-item flex-x1 tile" v-if="!user">
                <div v-if="isAdmin">
                    <h5>Find user</h5>
                    <label>Start typing the user's name or email address to select from list of existing users or hit enter to create a new user</label>
                    <autocomplete :config="config" v-on:selected="onSelected"></autocomplete>
                </div>
                <div v-else>
                    <h5>@ls.Localise("(news).users.add")</h5>
                    <label for="newId">Email</label>
                    <div class="email-edit">
                        <input id="newId" type="email" v-model="newUserModel.name" v-on:input="checkAvailability" required autofocus autocomplete="no-thanks" />
                        <div></div>
                        <div>
                            <i v-if="available === false" class="m-icon error-color" title="This user id is not available">close</i>
                            <i v-if="available === true" class="m-icon success-color" title="This user id is available">check</i>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <a ref="add" v-if="available" v-on:click="user = init(newUserModel)" class="button icon-button-next">@ls.Localise("(news).subscribe.add")</a>
                    </div>
                </div>
            </div>
            <div id="claims-column" v-bind:class="['flex-item flex-x2 tile', {'form-col-disabled': !user}]">
                <h5>@ls.Localise("(news).users.license")</h5>
                <label>@ls.Localise("(news).users.manager")?</label>
                <input v-if="user" name="ClaimId" type="hidden" :value="user.isManager ? @companyManagerClaimId : @companyUserClaimId" />
                <label class="switch-container">
                    @ls.Localise("(news).no")
                    <input v-if="user" id="companyUser" type="checkbox" class="switch" v-model="user.isManager" :value="user.isManager" />
                    <input v-else type="checkbox" class="switch" />
                    <label for="companyUser" class="switch">User is manager</label>
                    @ls.Localise("(news).yes")
                </label>
                <label>@ls.Localise("(news).users.column-active")?</label>
                <label class="switch-container">
                    @ls.Localise("(news).no")
                    <input v-if="user" id="userActive" name="Active" type="checkbox" class="switch" v-model="user.isActive" :value="user.isActive" />
                    <input v-else type="checkbox" class="switch" />
                    <label for="userActive" class="switch">User is enabled</label>
                    @ls.Localise("(news).yes")
                </label>
            </div>
        </div>

        <div class="buttons no-border mt-2 p-2">
            <a class="button secondary icon-button-cancel" href="@(isPharmaLexUser ? $"/manage/company/edit/{Model.Company.Id}" : "/users")">@ls.Localise("cancel")</a>
            <button v-if="!!user && available" type="submit" v-on:click.once>@ls.Localise("save")</button>
        </div>
    </form>

    <modal-dialog v-if="sendEmail"
                  :title="'@ls.Localise("(news).users.resend")'"
                  :height="20"
                  v-on:close="close"
                  v-on:confirm="confirmSend">
        <p>@ls.Localise("(news).users.ask-resend-email")</p>
    </modal-dialog>

</div>

@section Scripts {
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

        var pageConfig = {
            appElement: '#user',
            data: function () {
                let model = @Html.Raw(Json.Serialize(Model));
                return {
                    config: {
                        canAddNew: true,
                        searchUrl: `/company/user/find?term={term}&companyId=${model.company.id}`
                    },
                    model,
                    user: model.companyUser.id > 0 ? this.init(model.companyUser) : null,
                    licenseChange: 1,
                    timeout: null,
                    available: model.companyUser.id > 0 ? true : null,
                    newUserModel: { name: ''},
                    isAdmin: @Html.Raw(Json.Serialize(isPharmaLexUser)),
                    sendEmail: null,
                    lastLoginDate: model.companyUser.lastLoginDate,
                    invitationEmailLink: model.companyUser.invitationEmailLink
                };
            },
            methods: {
                init(user) {
                    let email = (user.email || user.name || '').split('@@')[0];
                    let splitNames = email.split('.');
                    let givenName = splitNames.splice(0, 1)[0];
                    let familyName = splitNames.join('.');

                    return {
                        id: user.id,
                        email: user.email || user.name || '',
                        familyName: user.familyName || [(familyName[0] || '').toUpperCase(), familyName.slice(1, familyName.length)].join(''),
                        givenName: user.givenName || [(givenName[0] || '').toUpperCase(), givenName.slice(1, givenName.length)].join(''),
                        isActive: user.active === false ? false : true,
                        isManager: user.claimId === @companyManagerClaimId
                    };
                },
                onSelected(user) {
                    if (user.id > 0) {
                        document.location.href = '@(isPharmaLexUser ? "/company/user/edit/" : "/users/edit/")' + user.id;
                    } else {
                        this.user = this.init(user);
                        this.checkAvailability();
                    }
                },
                checkAvailability(e) {
                    this.available = null;
                    if (e && !e.target.checkValidity()) {
                        return;
                    }

                    if (this.timeout) {
                        clearTimeout(this.timeout);
                    }
                    let data = new FormData();
                    data.append("email", (this.user ? this.user.email : this.newUserModel.name).trim());
                    this.timeout = setTimeout(() => {
                        fetch('/users/available', {
                            method: 'POST',
                            headers: {
                                'RequestVerificationToken': token
                            },
                            body: data
                        })
                            .then(r => r.json())
                            .then(available => {
                                this.available = available;
                            })
                            .catch(error => {
                                console.log(error);
                            })
                    }, 500)
                },
                onFormSubmit(e) {
                    if (!this.user) {
                        e.preventDefault();

                        if (this.available)
                            this.$refs.add.click();
                    }
                },
                resend() {
                    if (!this.lastLoginDate){
                        this.sendEmail = true;
                    }
                },
                close() {
                    this.sendEmail = false;
                },
                confirmSend() {
                    let parent = this;

                    let url = '/users/resendinvitation';
                    fetch(url, {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: JSON.stringify({
                            Id: parent.user.id,
                            Email: parent.user.email
                        })
                    }).then(response => {
                        response.json().then(data => {
                            plx.toast.show('Email sent successfully', 2, 'confirm', null, 2500, { useIcons: true });
                            parent.invitationEmailLink = data;
                        })
                        .catch(error => {
                            plx.toast.show('Failed to send email.', 2, 'failed', null, 5000, { useIcons: true })
                            console.log(error);
                        });

                        this.sendEmail = false;
                    })
                },
                copyLink() {
                    if (this.invitationEmailLink && !this.lastLoginDate) {
                        navigator.clipboard.writeText(this.invitationEmailLink);
                    }
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/AutoCompleteList" />
    <partial name="Components/ModalDialog" />
}
