﻿using AutoMapper;
using Azure.Storage.Blobs;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class AzureBlobModelProfile : Profile
    {
        public AzureBlobModelProfile()
        {
            CreateMap<BlobClient, AzureBlob>()
                .ForMember(d => d.ContentType, s => s.MapFrom(x => x.GetProperties(null, default).Value.ContentType))
                .ForMember(d => d.Length, s => s.MapFrom(x => x.GetProperties(null, default).Value.ContentLength))
                .ForMember(d => d.Container, s => s.MapFrom(x => x.BlobContainerName))
                .ForMember(d => d.Id, o => o.Ignore())
                .ForMember(d => d.NewsArticleContent, o => o.Ignore())
                .ForMember(d => d.CreatedDate, o => o.Ignore())
                .ForMember(d => d.CreatedBy, o => o.Ignore())
                .ForMember(d => d.LastUpdatedDate, o => o.Ignore())
                .ForMember(d => d.LastUpdatedBy, o => o.Ignore());
        }
    }
}