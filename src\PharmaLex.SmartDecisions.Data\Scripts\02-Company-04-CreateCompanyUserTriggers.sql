﻿CREATE TRIGGER [dbo].[CompanyUser_Insert] ON [dbo].[CompanyUser]
FOR INSERT AS
INSERT INTO [Audit].[CompanyUser_Audit] ([AuditAction], [CompanyId], [UserId], [Active], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'I', [CompanyId], [UserId], [Active], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[CompanyUser_Update] ON [dbo].[CompanyUser]
FOR UPDATE AS
INSERT INTO [Audit].[CompanyUser_Audit] ([AuditAction], [CompanyId], [UserId], [Active], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'U', [CompanyId], [UserId], [Active], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[CompanyUser_Delete] ON [dbo].[CompanyUser]
FOR DELETE AS
INSERT INTO [Audit].[CompanyUser_Audit] ([AuditAction], [CompanyId], [UserId], [Active], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'D', [CompanyId], [UserId], [Active], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO