﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class Field : EntityBase
    {
        public Field()
        {
            FieldValue = new HashSet<FieldValue>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int ContentTypeId { get; set; }
        public int FieldTypeId { get; set; }
        public int? Length { get; set; }
        public bool Required { get; set; }
        public bool Unique { get; set; }
        public string Description { get; set; }
        public bool System { get; set; }
        public int? RelatedContentTypeId { get; set; }

        public virtual ContentType ContentType { get; set; }
        public virtual ICollection<FieldValue> FieldValue { get; set; }
    }
}
