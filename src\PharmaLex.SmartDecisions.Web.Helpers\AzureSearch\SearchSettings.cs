﻿namespace PharmaLex.SmartDecisions.Web.Helpers.AzureSearch
{
    public class SearchSettings
    {
        public string ServiceName { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string IndexName { get; set; } = string.Empty;
        public string IndexerName { get; set; } = string.Empty;
        public int MaxRecords { get; set; }
        public string DataSourceName { get; set; } = string.Empty;
        public string BlobFolder { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public string BlobContainer { get; set; } = string.Empty;
    }
}
