﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities.Entities;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class SmartDecisionsContext : PlxDbContext
    {
        public virtual DbSet<Claim> Claim { get; set; }
        public virtual DbSet<Company> Company { get; set; }
        public virtual DbSet<CompanyContentType> CompanyContentType { get; set; }
        public virtual DbSet<CompanyNewsCategory> CompanyNewsCategory { get; set; }
        public virtual DbSet<CompanyUser> CompanyUser { get; set; }
        public virtual DbSet<ContentItem> ContentItem { get; set; }
        public virtual DbSet<ContentType> ContentType { get; set; }
        public virtual DbSet<ContentTypeDisplay> ContentTypeDisplay { get; set; }
        public virtual DbSet<Field> Field { get; set; }
        public virtual DbSet<FieldValue> FieldValue { get; set; }
        public virtual DbSet<User> User { get; set; }
        public virtual DbSet<UserClaim> UserClaim { get; set; }
        public virtual DbSet<MultilingualResource> MultilingualResource { get; set; }
        public virtual DbSet<Locale> Locale { get; set; }
        public virtual DbSet<NewsArticleContent> NewsArticleContent { get; set; }
        public virtual DbSet<NewsArticle> NewsArticle { get; set; }
        public virtual DbSet<NewsSource> NewsSource { get; set; }
        public virtual DbSet<NewsCategory> NewsCategory { get; set; }
        public virtual DbSet<NewsArticleCategory> NewsArticleCategory { get; set; }
        public virtual DbSet<NewsletterSubscription> NewsletterSubscription { get; set; }
        public virtual DbSet<Newsletter> Newsletter { get; set; }
        public virtual DbSet<UserNewsCategory> UserNewsCategory { get; set; }
        public virtual DbSet<AzureBlob> AzureBlob { get; set; }
        public virtual DbSet<Logging> Logging { get; set; }
        public virtual DbSet<NewsletterActivity> NewsletterActivity { get; set; }

        public SmartDecisionsContext(DbContextOptions options, IUserContext userContext, IDbConnectionService sqlService)
            : base(options, userContext, sqlService) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.EntityBase<AzureBlob>(e => {

                e.HasIndex(x => new { x.Name, x.Container })
                        .HasDatabaseName("UC_AzureBlob_Name_Container")
                        .IsUnique();

                e.Property(e => e.Container)
                    .IsRequired()
                    .HasMaxLength(128);

                e.Property(e => e.ContentType)
                    .IsRequired()
                    .HasMaxLength(256);

                e.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(512);

                e.Property(e => e.Uri)
                    .IsRequired()
                    .HasMaxLength(256);
            });

            // TODO: DELETE THIS TABLE AFTER MIGRATION
            modelBuilder.EntityBase<Logging>(e => {

                e.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(256);

                e.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(10);

                e.Property(e => e.ErrorMessage)
                    .IsRequired()
                    .HasMaxLength(1024);

                e.Property(e => e.CreationDate)
                    .HasColumnType("datetime");
            });

            modelBuilder.EntityBase<UserNewsCategory>(entity =>
            {
                entity.HasIndex(x => new { x.UserId, x.NewsCategoryId })
                        .HasDatabaseName("UC_UserNewsCategory")
                        .IsUnique();

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserNewsCategory)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_UserNewsCategory_User");

                entity.HasOne(d => d.NewsCategory)
                    .WithMany(p => p.UserNewsCategory)
                    .HasForeignKey(d => d.NewsCategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_UserNewsCategory_NewsCategory");

                entity.Property(e => e.Order)
                    .IsRequired();
            });

            modelBuilder.EntityBase<Newsletter>(entity =>
            {
                entity.Property(e => e.CreatedDateUtc)
                    .HasColumnType("datetime");

                entity.Property(e => e.UniqueKey)
                    .HasMaxLength(32).IsRequired();

                entity.HasIndex(x => x.UniqueKey)
                    .HasDatabaseName("UC_Newsletter_UniqueKey")
                    .IsUnique();

                entity.HasOne(d => d.User)
                    .WithMany(p => p.Newsletter)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_Newsletter_User");
                
                entity.HasOne(d => d.Locale)
                    .WithMany(p => p.Newsletter)
                    .HasForeignKey(d => d.LocaleId)
                    .HasConstraintName("FK_Newsletter_Locale");

                entity.HasOne(d => d.NewsletterSubscription)
                    .WithMany(p => p.Newsletter)
                    .HasForeignKey(d => d.NewsletterSubscriptionId)
                    .HasConstraintName("FK_Newsletter_NewsletterSubscription")
                    .OnDelete(DeleteBehavior.NoAction);
            });

            modelBuilder.EntityBase<MultilingualResource>(entity =>
            {
                entity.Property(e => e.Key)
                    .IsRequired()
                    .HasMaxLength(1024);

                entity.Property(e => e.Content)
                    .IsRequired();

                entity.HasOne(d => d.Locale)
                    .WithMany(p => p.MultilingualResource)
                    .HasForeignKey(d => d.LocaleId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_MultilingualResource_Locale");

                entity.HasIndex(x => new { x.Key, x.LocaleId })
                    .HasDatabaseName("UC_MultilingualResource_Key_Locale")
                    .IsUnique();
            });

            modelBuilder.EntityBase<Locale>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);
                entity.Property(e => e.LocalisedName)
                    .IsRequired()
                    .HasMaxLength(256);
                entity.Property(e => e.LocalisationKey)
                    .HasMaxLength(256);

                entity.Property(e => e.IsoLanguageCode)
                    .HasColumnType("nchar(2)")
                    .IsRequired();
                entity.Property(e => e.IsoScriptCode)
                    .HasColumnType("nchar(4)");
                entity.Property(e => e.IsoCountryCode)
                    .HasColumnType("nchar(2)")
                    .IsRequired();

                entity.HasIndex(x => x.Name)
                    .HasDatabaseName("UC_Locale_Name")
                    .IsUnique();

                entity.HasIndex(x => new { x.IsoLanguageCode, x.IsoScriptCode, x.IsoCountryCode })
                    .HasDatabaseName("UC_Locale_Code")
                    .IsUnique();
            });

            modelBuilder.EntityBase<NewsArticleContent>(entity =>
            {
                entity.Property(e => e.Title)
                    .HasMaxLength(256);

                entity.Property(e => e.SourceUrl)
                    .HasMaxLength(256);

                entity.Property(e => e.PublishingStateDateUtc)
                    .HasColumnType("datetime");

                entity.HasOne(d => d.Author)
                    .WithMany(p => p.NewsArticleContentAuthor)
                    .HasForeignKey(d => d.AuthorId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_NewsArticleContent_Author");

                entity.HasOne(d => d.Reviewer)
                    .WithMany(p => p.NewsArticleContentReviewer)
                    .HasForeignKey(d => d.ReviewerId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_NewsArticleContent_Riviewer");

                entity.HasOne(d => d.Locale)
                    .WithMany(p => p.NewsArticleContent)
                    .HasForeignKey(d => d.LocaleId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_NewsArticleContent_Locale");

                entity.HasOne(d => d.NewsArticle)
                    .WithMany(p => p.NewsArticleContent)
                    .HasForeignKey(d => d.NewsArticleId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_NewsArticleContent_NewsArticle");

                entity.HasOne(d => d.AzureBlob)
                    .WithMany(p => p.NewsArticleContent)
                    .HasForeignKey(x => x.AzureBlobId)
                    .HasConstraintName("FK_NewsArticleContent_AzureBlob");

                entity.HasIndex(e => 
                    new { e.LocaleId, e.NewsArticleId }, 
                    "UC_NewsArticleContent_LocaleId_NewsArticleId"
                ).IsUnique();

                entity.HasIndex(e =>
                   e.PublishingStateId,
                   "IX_NewsArticleContent_PublishingStateId");

                entity.Property(e => e.ImpactAssessmentSummary)
                    .HasMaxLength(256);

                entity.Property(e => e.FriendlyUrl)
                    .HasMaxLength(300).IsRequired();

                entity.HasIndex(x => x.FriendlyUrl)
                    .HasDatabaseName("UC_NewsArticleContent_FriendlyUrl")
                    .IsUnique();
            });

            modelBuilder.EntityBase<NewsArticle>(entity =>
            {
                entity.Property(e => e.ImportanceId).HasDefaultValue(default(int));

                entity.HasOne(d => d.NewsSource)
                    .WithMany(p => p.NewsArticle)
                    .HasForeignKey(d => d.NewsSourceId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_NewsArticle_NewsSource");

                entity.HasIndex(e =>
                     e.ImportanceId,
                     "IX_NewsArticle_ImportanceId");
            });

            modelBuilder.EntityBase<NewsletterSubscription>(entity =>
            {
                entity.Property(e => e.CreatedDateUtc)
                    .HasColumnType("datetime");

                entity.Property(e => e.Timezone)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.IsInfoflash)
                    .HasDefaultValue(0);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.NewsletterSubscription)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_NewsletterSubscription_User");
            });

            modelBuilder.EntityBase<NewsSource>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);
                entity.Property(e => e.Url)
                    .IsRequired()
                    .HasMaxLength(256);
                entity.Property(e => e.LocalisationKey)
                    .HasMaxLength(256);

                entity.HasIndex(x => x.Name)
                    .HasDatabaseName("UC_NewsSource_Name")
                    .IsUnique();
            });

            modelBuilder.EntityBase<NewsCategory>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);
                entity.Property(e => e.LocalisationKey)
                    .HasMaxLength(256);

                entity.HasOne(d => d.ParentCategory)
                    .WithMany(p => p.ChildCategory)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_NewsCategory_NewsCategory");
            });

            modelBuilder.EntityBase<NewsArticleCategory>(entity =>
            {
                entity.HasKey(e => new { e.NewsArticleId, e.NewsCategoryId })
                    .HasName("PK_NewsArticleCategory");

                entity.HasOne(d => d.NewsArticle)
                    .WithMany(p => p.NewsArticleCategory)
                    .HasForeignKey(d => d.NewsArticleId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_NewsArticleCategory_NewsArticle");

                entity.HasOne(d => d.NewsCategory)
                    .WithMany(p => p.NewsArticleCategory)
                    .HasForeignKey(d => d.NewsCategoryId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_NewsArticleCategory_NewsCategory");
            });

            modelBuilder.EntityBase<Claim>(entity =>
            {
                entity.Property(e => e.ClaimType).HasMaxLength(32);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(1024);
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.HasIndex(e => e.Name)
                    .HasDatabaseName("UC_Company_Name")
                    .IsUnique();

                entity.Property(e => e.PrimaryContactAddress).HasMaxLength(256);

                entity.Property(e => e.PrimaryContactEmail).HasMaxLength(128);

                entity.Property(e => e.PrimaryContactName).HasMaxLength(256);

                entity.Property(e => e.PrimaryContactPhone).HasMaxLength(128);
            });

            modelBuilder.EntityBase<CompanyContentType>(entity =>
            {
                entity.HasKey(e => new { e.CompanyId, e.ContentTypeId });

                entity.HasOne(d => d.ContentType)
                    .WithMany(p => p.CompanyContentType)
                    .HasForeignKey(d => d.ContentTypeId)
                    .HasConstraintName("FK_CompanyContentType_ContentType");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.CompanyContentType)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_CompanyContentType_Company");
            });

            modelBuilder.EntityBase<CompanyNewsCategory>(entity =>
            {
                entity.HasKey(e => new { e.CompanyId, e.NewsCategoryId });

                entity.HasOne(d => d.NewsCategory)
                    .WithMany(p => p.CompanyNewsCategory)
                    .HasForeignKey(d => d.NewsCategoryId)
                    .HasConstraintName("FK_CompanyNewsCategory_NewsCategory");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.CompanyNewsCategory)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_CompanyNewsCategory_Company");
            });

            modelBuilder.Entity<CompanyUser>(entity =>
            {
                entity.HasKey(x => new { x.CompanyId, x.UserId });

                entity.HasIndex(x => x.UserId)
                    .HasDatabaseName("UC_CompanyUser_UserId")
                    .IsUnique();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.CompanyUser)
                    .HasForeignKey(x => x.CompanyId)
                    .HasConstraintName("FK_CompanyUser_Company");

                entity.HasOne(d => d.User)
                    .WithOne(p => p.CompanyUser)
                    .HasForeignKey<CompanyUser>(x => x.UserId)
                    .HasConstraintName("FK_CompanyUser_User");
            });

            modelBuilder.EntityBase<ContentItem>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.Owner)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.VerifiedDate).HasColumnType("datetime");

                entity.HasOne(d => d.ContentType)
                    .WithMany(p => p.ContentItem)
                    .HasForeignKey(d => d.ContentTypeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ContentItem_ContentType");

                entity.HasIndex(e => new { e.ContentTypeId, e.Name }, "UC_ContentItem_ContentTypeId_Name")
                    .IsUnique();
            });

            modelBuilder.EntityBase<ContentType>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.Owner)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.PluralName)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.ShortName)
                    .IsRequired()
                    .HasMaxLength(31);

                entity.HasIndex(e => e.Name, "UC_ContentType_Name")
                    .IsUnique();
            });

            modelBuilder.EntityBase<ContentTypeDisplay>(entity =>
            {
                entity.Property(e => e.Json).IsRequired();

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.HasOne(d => d.ContentType)
                    .WithMany(p => p.ContentTypeDisplay)
                    .HasForeignKey(d => d.ContentTypeId)
                    .HasConstraintName("FK_ContentTypeDisplay_ContentType");
            });

            modelBuilder.EntityBase<Field>(entity =>
            {
                entity.Property(e => e.Description).HasMaxLength(1024);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.HasOne(d => d.ContentType)
                    .WithMany(p => p.Field)
                    .HasForeignKey(d => d.ContentTypeId)
                    .HasConstraintName("FK_Field_ContentType");

                entity.HasIndex(e => new { e.ContentTypeId, e.Name }, "UC_Field_ContentTypeId_Name")
                    .IsUnique();
            });

            modelBuilder.EntityBase<FieldValue>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.Value).IsRequired();

                entity.HasOne(d => d.ContentItem)
                    .WithMany(p => p.FieldValue)
                    .HasForeignKey(d => d.ContentItemId)
                    .HasConstraintName("FK_FieldValue_ContentItem");

                entity.HasOne(d => d.Field)
                    .WithMany(p => p.FieldValue)
                    .HasForeignKey(d => d.FieldId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FieldValue_Field");
            });

            modelBuilder.EntityBase<User>(entity =>
            {
                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.FamilyName).HasMaxLength(512);

                entity.Property(e => e.GivenName).HasMaxLength(512);

                entity.Property(e => e.LastLoginDate).HasColumnType("datetime");

                entity.HasOne(d => d.Locale)
                    .WithMany(p => p.User)
                    .HasForeignKey(d => d.LocaleId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_User_Locale");
            });

            modelBuilder.EntityBase<UserClaim>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.ClaimId });

                entity.HasOne(d => d.Claim)
                    .WithMany(p => p.UserClaim)
                    .HasForeignKey(d => d.ClaimId)
                    .HasConstraintName("FK_UserClaim_Claim");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserClaim)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_UserClaim_User");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
