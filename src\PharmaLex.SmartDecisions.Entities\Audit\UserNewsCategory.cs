﻿using PharmaLex.DataAccess;
using System;

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public class UserNewsCategoryAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public int? Order { get; set; }
        public DateTime? CreatedDateUtc { get; set; }

        public int UserId { get; set; }
        public int NewsCategoryId { get; set; }
    }
}
