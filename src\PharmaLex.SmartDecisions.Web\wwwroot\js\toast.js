﻿/* Toast notifications
 * 
 * position - integer from 1 - 9 indicating render location
 * 1    2   3
 * 4    5   6
 * 7    8   9
 * 
 * type - string
 *      info - grey
 *      confirm - green
 *      failed - red,
 *      warning - orange,
 *      number - blue
 */
plx.toast = {
    show: function (html, position, type, number, duration, options) {
        plx.toast.close();
        const toastStyles = position ? `toast toast-${position} ${type}` : `toast ${type}`
        const container = plx.createElement('div', 'toast', toastStyles);

        if (typeof number === 'number') {
            container.setAttribute('data-number', number);
        }

        if (options?.useIcons) {
            const iconContainer = plx.createElement('div', null, `m-icon-group`);
            const icon = plx.createElement('i', null, `m-icon ${type}-color`);

            let iconType = "info";

            switch (type) {
                case 'confirm':
                case 'success':
                    iconType = 'check_circle';
                    break;
                case 'warning':
                    iconType = 'warning';
                    break;
                case 'error':
                case 'failed':
                    iconType = 'error';
                    break;
            }

            icon.textContent = iconType;
            iconContainer.appendChild(icon);
            container.appendChild(iconContainer);

            const message = plx.createElement('div', null, 'message');
            message.innerHTML = html;
            iconContainer.appendChild(message);

            const closeIconContainer = plx.createElement('div', null, 'close');
            const closeIcon = plx.createElement('i', null, 'm-icon');
            closeIcon.textContent = 'close';
            closeIcon.addEventListener('click', () => plx.toast.fade(container));
            closeIconContainer.appendChild(closeIcon);
            container.appendChild(closeIconContainer);
        } else {
            const messageContainer = plx.createElement('span');
            messageContainer.innerHTML = html;
            container.appendChild(messageContainer);
        }


        document.body.appendChild(container);

        if (typeof duration === 'number' && duration > 0) {
            setTimeout(() => {
                plx.toast.fade(container);
                plx.toast.close();
            }, duration);
        }
    },

    close: function () {
        let container = document.getElementById('toast');
        if (container !== null) {
            document.body.removeChild(container);
        }
    },

    fade: function (container) {
        container.classList.add('fade-out');
    }
};