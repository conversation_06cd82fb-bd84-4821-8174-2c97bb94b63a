﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class ContentController : Controller
    {
        private readonly IContentService contentService;

        public ContentController(IContentService contentService)
        {
            this.contentService = contentService;
        }

        [HttpGet("/content/countries")]
        public async Task<IActionResult> Countries()
        {
            return Json(await contentService.ListItemsByContentType<CountryModel>(sorter: x => x.Name));
        }

        [HttpGet("/content/regulatory-authorities")]
        public async Task<IActionResult> RegulatoryAuthorities()
        {
            return Json(await contentService.ListItemsByContentType<RegulatoryAuthorityModel>());
        }
    }
}