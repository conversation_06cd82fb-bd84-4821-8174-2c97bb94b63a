﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Helpers.Interfaces
{
    public interface IContentItemRepository: ITrackingRepository<ContentItem>, IRepository<ContentItem>
    {
        IQueryable<ContentItem> GetQueryableItems(Func<IQueryable<ContentItem>, IIncludableQueryable<ContentItem, object>>? include = null);
    }
}
