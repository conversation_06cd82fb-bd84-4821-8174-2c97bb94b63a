﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.NewsletterApp
{
    public class UserNewsCategoryModel
    {
        public int NewsCategoryId { get; set; }
        public int Order { get; set; }
    }

    public class UserNewsCategoryMappingProfile : Profile
    {
        public UserNewsCategoryMappingProfile()
        {
            this.CreateMap<UserNewsCategory, UserNewsCategoryModel>();
        }
    }
}
