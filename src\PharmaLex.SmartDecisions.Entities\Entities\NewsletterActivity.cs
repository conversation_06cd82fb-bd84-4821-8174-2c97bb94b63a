﻿using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities.Enums;
using System;

namespace PharmaLex.SmartDecisions.Entities.Entities
{
    public class NewsletterActivity : EntityBase
    {
        public int Id { get; set; }
        public NewsletterActivityEventType EventType { get; set; }
        public string Response { get; set; }        
        public string Reason { get; set; }
        public DateTime ReceivedUTCDate { get; set; }
        public int UserId { get; set; }
        public virtual User User { get; set; }
    }
}
