﻿using PharmaLex.SmartDecisions.Web.Models.ViewModels;
using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class ContentTypeDisplayModel : EntityModel, IModel
    {
        public ContentTypeDisplayModel()
        {
            this.ContentTypeViewFieldModels = new List<ContentTypeViewFieldModel>();
            this.ContentTypeViewRecordModels = new List<ContentTypeViewRecordModel>();
        }

        public int ContentTypeId { get; set; }
        public string ContentTypeName { get; set; }
        public int ContentTypeDisplayTypeId { get; set; }
        public string DisplayType { get; set; }
        public string Json { get; set; }

        public List<ContentTypeViewFieldModel> ContentTypeViewFieldModels { get; set; }
        public List<ContentTypeViewRecordModel> ContentTypeViewRecordModels { get; set; }

        public bool IsNew { get; set; }
    }
}