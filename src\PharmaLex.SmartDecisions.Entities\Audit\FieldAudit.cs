﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class FieldAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public int? ContentTypeId { get; set; }
        public int? FieldTypeId { get; set; }
        public int? Length { get; set; }
        public bool? Required { get; set; }
        public bool? Unique { get; set; }
        public string Description { get; set; }
        public bool? System { get; set; }
        public int? RelatedContentTypeId { get; set; }
    }
}
