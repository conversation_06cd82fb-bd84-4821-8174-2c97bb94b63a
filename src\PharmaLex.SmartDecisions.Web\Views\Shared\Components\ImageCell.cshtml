﻿
<script type="text/javascript">
        vueApp.component('image-cell', {
            template: '<td><span :title="val.title" class="m-icon-medium mr-1 m-icon">{{val.icon}}</span></td>',
            props: ['val']
        });
    vueApp.component('icon-cell', {
        template: '<td><i class="m-icon" :class="val.class" style="vertical-align: -0.35rem; margin-right: 0.25rem;">{{val.icon}}</i>{{val.title}}</td>',
        props: ['val']
    });
</script>