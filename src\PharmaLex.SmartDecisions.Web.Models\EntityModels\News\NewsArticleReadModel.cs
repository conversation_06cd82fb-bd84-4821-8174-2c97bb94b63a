﻿using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleReadModel : IModel
    {
        public int Id { get; set; }
        public int NewsArticleId { get; set; }
#pragma warning disable CA1056 // Uri properties should not be strings
        public string FriendlyUrl { get; set; }
        public string SourceUrl { get; set; }
        public string ContentUrl { get; set; }
#pragma warning restore CA1056 // Uri properties should not be strings
        public string Content { get; set; }
        public string ContentBase64 { get; set; }
        public int LocaleId { get; set; }
        public string Title { get; set; }
        public string GoLiveDate { get; set; }
        public string SourcePublicationDate { get; set; }
        public List<int> CategoryIds { get; set; } = new List<int>();
        public List<int> ProductIds { get; set; } = new List<int>();
        public List<int> ThemeIds { get; set; } = new List<int>();
        public int NewsSourceId { get; set; }
        public int ImportanceId { get; set; }
        public List<int> TypeOfTextIds { get; set; }
        public string PublisherFullName { get; set; }
    }

    public class NewsArticleFullReadModel : IModel
    {
        public string NewsletterKey { get; set; }
        public NewsArticleReadModel NewsArticle { get; set; }
        public List<NewsArticleReadModel> NewsArticles { get; set; }
    }
}
