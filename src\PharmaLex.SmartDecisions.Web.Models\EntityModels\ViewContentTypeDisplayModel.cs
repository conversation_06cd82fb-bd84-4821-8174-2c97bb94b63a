﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class ViewContentTypeDisplayModel
    {
        public string HeroTemplate { get; set; }
        public string HeroHtml { get; private set; }
        public List<ContentTypeDisplayColumn> Columns { get; set; }
        public ContentTypeDisplaySection Aside { get; set; }

        public ViewContentTypeDisplayModel()
        {
            this.HeroTemplate = "";
            this.Columns = new List<ContentTypeDisplayColumn>();
            this.Aside = new ContentTypeDisplaySection();
        }

        public void MergeData(ContentTypeModel ct, TopicItemModel ti, IEnumerable<PicklistModel> picklists)
        {
            this.HeroHtml = this.MergeTemplate(this.HeroTemplate, ct, ti, picklists);
            foreach (var c in this.Columns)
            {
                foreach (var s in c.Sections)
                {
                    foreach (var g in s.Groups)
                    {
                        this.MergeFieldValues(ct, ti, picklists, g);
                    }
                }
            }
            foreach (var g in this.Aside.Groups)
            {
                this.MergeFieldValues(ct, ti, picklists, g);
            }
        }

        private string MergeTemplate(string template, ContentTypeModel ct, TopicItemModel ti, IEnumerable<PicklistModel> picklists)
        {
            Regex re = new Regex(@"\{\{([^}]+)\}\}", RegexOptions.None, TimeSpan.FromMilliseconds(500));
            foreach (Match match in re.Matches(template))
            {
                template = template.Replace(match.Groups[0].Value, $"<em title=\"{match.Groups[1].Value}\">{ti.GetFieldDisplayHtml(ct.Field.First(x => x.Name == match.Groups[1].Value), picklists)}</em>", StringComparison.InvariantCulture);
            }
            return template;
        }

        private void MergeFieldValues(ContentTypeModel ct, TopicItemModel ti, IEnumerable<PicklistModel> picklists, ContentTypeDisplayFieldGroup g)
        {
            foreach (var id in g.Fields.Keys.ToArray())
            {
                var f = ct.Field.First(x => x.Id == id);
                g.Fields[id] = new ContentTypeDisplayField
                {
                    Id = id,
                    Html = ti.GetFieldDisplayHtml(f, picklists),
                    Name = f.Name
                };
            }
        }
    }

    public class ContentTypeDisplayColumn
    {
        public List<ContentTypeDisplaySection> Sections { get; set; }

        public ContentTypeDisplayColumn()
        {
            this.Sections = new List<ContentTypeDisplaySection>();
        }
    }

    public class ContentTypeDisplaySection
    {
        public string Title { get; set; }
        public List<ContentTypeDisplayFieldGroup> Groups { get; set; }
        public bool HasContent
        {
            get { return this.Groups.Any(x => x.HasContent); }
        }

        public ContentTypeDisplaySection()
        {
            this.Title = "";
            this.Groups = new List<ContentTypeDisplayFieldGroup>();
        }
    }

    public class ContentTypeDisplayFieldGroup
    {
        [JsonConverter(typeof(FieldArrayConverter))]
        public Dictionary<int, ContentTypeDisplayField> Fields { get; set; }
        public bool IsList { get; set; }
        public bool HasContent
        {
            get { return this.Fields.Any(x => !String.IsNullOrEmpty(x.Value?.Html)); }
        }

        public ContentTypeDisplayFieldGroup()
        {
            this.Fields = new Dictionary<int, ContentTypeDisplayField>();
        }
    }

    public class ContentTypeDisplayField
    {
        public int Id { get; set; }
        public string Html { get; set; }
        public string Name { get; set; }
    }

    class FieldArrayConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return (objectType == typeof(Dictionary<int, ContentTypeDisplayField>));
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            JArray ja = JArray.Load(reader);
            var d = new Dictionary<int, ContentTypeDisplayField>();
            foreach (int key in ja)
            {
                d.Add(key, null);
            }
            return d;
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var f = value as Dictionary<int, ContentTypeDisplayField>;
            JArray ja = new JArray(f.Keys);
            ja.WriteTo(writer);
        }
    }
}
