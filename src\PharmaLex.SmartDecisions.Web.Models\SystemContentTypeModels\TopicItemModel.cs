﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace PharmaLex.SmartDecisions.Web.Models
{
    // TODO: Should this just inherit from ContentItemModel instead of a shared interface?
    public class TopicItemModel : SystemContentTypeModel, IContentItemModel
    {
        public int AuthorityId { get; set; }
        public int ContentTypeId { get; set; }
        public DateTime VerifiedDate { get; set; }
        public IEnumerable<FieldValueModel> Values { get; set; }
        public string Owner { get; set; }
    }

    public class TopicItemModelMappingProfile : Profile
    {
        public TopicItemModelMappingProfile()
        {
            this.CreateMap<ContentItem, TopicItemModel>()
                .ForMember(d => d.AuthorityId, s => s.MapFrom(x => x.GetNullableIntFieldValue("Regulatory Authority")))
                .ForMember(d => d.Values, s => s.MapFrom(x => x.FieldValue));
        }
    }
}
