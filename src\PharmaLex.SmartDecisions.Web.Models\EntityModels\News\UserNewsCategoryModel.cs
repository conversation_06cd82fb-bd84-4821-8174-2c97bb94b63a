﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class UserNewsCategoryModel : IModel
    {
        public int Id { get; set; }
        public int Order { get; set; }

        public int UserId { get; set; }
        public int NewsCategoryId { get; set; }
        public DateTime CreatedDateUtc { get; set; }

        public User User { get; set; }

        public NewsCategory NewsCategory { get; set; }
    }

    public class NewsletterUserNewsCategoryMappingProfile : Profile
    {
        public NewsletterUserNewsCategoryMappingProfile()
        {
            this.CreateMap<UserNewsCategory, UserNewsCategoryModel>();
        }
    }
}
