﻿
@import 'variables';
@import 'mixins';

.nav-menu {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin: 0 auto;

    a {
        color: $text;

        &:hover {
            color: $brand;
            text-decoration: none;
        }
    }

    .m-icon {
        color: $text;

        &:hover {
            transform: none;
        }
    }

    & > li {
        margin: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 1.25rem;
        color: $grey-2;
        border-bottom: 3px solid transparent;
        transition: all .3s;
        position: relative;
        margin-right: 1.5rem;
        white-space: nowrap;
        height: 90px;

        &:last-of-type {
            margin-right: 0;
        }

        &:hover {
            color: $brand;
        }
		
		&.selected {
			border-bottom-color: $brand;
			color: $brand;
			
			& > a {
				color: $brand;
			}
		}

        & > a {
			color: $grey-2;
		
            &:hover {
                text-decoration: none;
            }
        }

        .flyout {
            position: absolute;
            top: 91px;
            font-size: .85rem;
            color: $text;
            display: none;
            transition: all .3s;
            justify-content: space-between;
            align-items: flex-start;
            width: fit-content;
            padding: 1.5rem;
            background: $white;
            margin-top: 0;
            border-bottom: 1px solid $grey-3;

            li {
                font-size: 1rem;
                color: $text;
                margin-bottom: .75rem;
                white-space: nowrap;

                &:last-of-type {
                    margin-bottom: 0;
                }

                &.heading {
                    font-weight: 700;
                    margin-bottom: 1rem;
                }
            }
        }

        &.full-menu {
            position: unset;

            .flyout {
                width: 100%;
                top: 90px;
                left: 0;
                right: 0;
                display: none;
                padding: 2.5rem;
                justify-content: center;
                max-height: 70vh;
                overflow-x: hidden;
                overflow-y: auto;
            }

            .flex-item {
                width: fit-content;
                margin-right: 2rem;

                &:last-of-type {
                    margin-right: 0;
                }
            }

            li {
                white-space: normal;
            }
        }

        &:hover {
            border-bottom-color: $brand;
            transition: all .3s;
            display: flex;

            .flyout {
                display: flex;
                transition: all .3s;
            }
        }
    }
}

.main-navigation {
    flex-grow: 0;
    margin: 0 2rem;
}

.account-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        margin-right: .75rem;
    }
}
