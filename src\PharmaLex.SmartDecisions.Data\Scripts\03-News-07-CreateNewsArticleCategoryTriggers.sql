﻿create trigger [dbo].[NewsArticleCategory_Insert] on [dbo].[NewsArticleCategory]
for insert as
insert into [Audit].[NewsArticleCategory_Audit]
select 'I'
      ,[NewsArticleId]
      ,[NewsCategoryId]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsArticleCategory_Update] on [dbo].[NewsArticleCategory]
for update as
insert into [Audit].[NewsArticleCategory_Audit]
select 'U'
      ,[NewsArticleId]
      ,[NewsCategoryId]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[NewsArticleCategory_Delete] on [dbo].[NewsArticleCategory]
for delete as
insert into [Audit].[NewsArticleCategory_Audit]
select 'D'
      ,[NewsArticleId]
      ,[NewsCategoryId]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
