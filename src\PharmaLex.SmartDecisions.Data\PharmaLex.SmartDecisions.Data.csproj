﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Library</OutputType>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="Scripts\01-Initial-01-CreateUserTriggers.sql" />
		<None Remove="Scripts\01-Initial-02-CreateClaimTriggers.sql" />
		<None Remove="Scripts\01-Initial-03-CreateContentItemTriggers.sql" />
		<None Remove="Scripts\01-Initial-04-CreateContentTypeDisplayTriggers.sql" />
		<None Remove="Scripts\01-Initial-05-CreateContentTypeTriggers.sql" />
		<None Remove="Scripts\01-Initial-06-CreateFieldTriggers.sql" />
		<None Remove="Scripts\01-Initial-07-CreateFieldValueTriggers.sql" />
		<None Remove="Scripts\01-Initial-08-SetupContentTypes.sql" />
		<None Remove="Scripts\01-Initial-09-SetupPicklistData.sql" />
		<None Remove="Scripts\02-Company-01-CreateCompanyClaims.sql" />
		<None Remove="Scripts\02-Company-02-CreateCompanyTriggers.sql" />
		<None Remove="Scripts\02-Company-03-CreateCompanyContentTypeTriggers.sql" />
		<None Remove="Scripts\02-Company-04-CreateCompanyUserTriggers.sql" />
		<None Remove="Scripts\03-News-01-CreateAzureBlobTriggers.sql" />
		<None Remove="Scripts\03-News-02-CreateCompanyNewsCategoryTriggers.sql" />
		<None Remove="Scripts\03-News-03-CreateLocaleTriggers.sql" />
		<None Remove="Scripts\03-News-04-CreateMultilingualResourceTriggers.sql" />
		<None Remove="Scripts\03-News-05-CreateNewsSourceTriggers.sql" />
		<None Remove="Scripts\03-News-06-CreateNewsCategoryTriggers.sql" />
		<None Remove="Scripts\03-News-07-CreateNewsArticleCategoryTriggers.sql" />
		<None Remove="Scripts\03-News-08-CreateNewsArticleTriggers.sql" />
		<None Remove="Scripts\03-News-09-CreateNewsArticleContentTriggers.sql" />
		<None Remove="Scripts\03-News-10-CreateNewsletterSubscriptionTriggers.sql" />
		<None Remove="Scripts\03-News-11-CreateNewsletterTriggers.sql" />
		<None Remove="Scripts\03-News-12-CreateNewsletterNewsArticleContentTriggers.sql" />
		<None Remove="Scripts\03-News-13-AlterCompanyTriggers.sql" />
		<None Remove="Scripts\03-News-14-AlterUserTriggers.sql" />
		<None Remove="Scripts\03-News-15-CreateNewsletterSubscriptionNewsCategoryTriggers.sql" />
		<None Remove="Scripts\03-News-16-AddNewsCategories.sql" />
		<None Remove="Scripts\03-News-17-AddNewsSources.sql" />
		<None Remove="Scripts\03-News-18-AddLocales.sql" />
		<None Remove="Scripts\03-News-19-AddMultilingualResources.sql" />
		<None Remove="Scripts\03-News-20-UpdateCompanies.sql" />
		<None Remove="Scripts\03-News-21-AddNewsRoles.sql" />
		<None Remove="Scripts\05-ArticleGoLiveDate-01-AlterNewsArticleContentTriggers.sql" />
		<None Remove="Scripts\06-AddUserEmailLink-01-AlterUserTriggers.sql" />
		<None Remove="Scripts\07-NewsletterSubscriptionActiveState-01-UpdateNewsletterSubscriptionTriggers.sql" />
		<None Remove="Scripts\08-AddAdditionalTheme-01-AddGoodPracticeTheme.sql" />
		<None Remove="Scripts\09-AddGeographicalScope-01-AddSwitzerland.sql" />
		<None Remove="Scripts\1-1-CreateUserTriggers.sql" />
		<None Remove="Scripts\1-2-CreateClaimTriggers.sql" />
		<None Remove="Scripts\1-CreateContentItemTriggers.sql" />
		<None Remove="Scripts\1-CreateContentTypeDisplayTriggers.sql" />
		<None Remove="Scripts\1-CreateContentTypeTriggers.sql" />
		<None Remove="Scripts\1-CreateFieldTriggers.sql" />
		<None Remove="Scripts\1-CreateFieldValueTriggers.sql" />
		<None Remove="Scripts\1-SetupContentTypes.sql" />
		<None Remove="Scripts\1-SetupPicklistData.sql" />
		<None Remove="Scripts\10-AddUserCategories-01-CreateUserNewsCategoryTriggers.sql" />
		<None Remove="Scripts\10-AddUserCategories-02-MigrateUserCategories.sql" />
		<None Remove="Scripts\11-AddInfoFlashSubscriptionType-01-UpdateNewsletterSubscriptionTriggers.sql" />
		<None Remove="Scripts\11-AddNewTypeText-01-InsertTypeTexts.sql" />
		<None Remove="Scripts\12-AddNewSource-01-InsertSources.sql" />
		<None Remove="Scripts\13-AddNewsletterPublicationDate-01-InsertNewsletterPublicationDate.sql" />
		<None Remove="Scripts\14-UpdateNewsArticleFrenchFields-01-UpdateNewsArticleFrenchFields.sql" />
		<None Remove="Scripts\15-AddNewsArticleContentIdColumn-01-UpdateNewsletterTriggers.sql" />
		<None Remove="Scripts\15-AddNewsArticleContentIdColumn-02-MigrateNewsArticleContentData.sql" />
		<None Remove="Scripts\16-AddSearchInTitleOnly-01-AddSearchInTitleOnlyLabels.sql" />
		<None Remove="Scripts\17-UpdateGoLiveDateOldArticles-01-UpdateGoLiveDate.sql" />
		<None Remove="Scripts\18-UpdateFilteredTableV2Labels-01-UpdateFilteredTableV2Labels.sql" />
		<None Remove="Scripts\19-AddSearchPageLocalization-01-AddSearchPageLocalization.sql" />
		<None Remove="Scripts\20-AddNewsletterTypesEnumTranslations-01-AddNewsletterTypesEnumTranslations.sql" />
		<None Remove="Scripts\21-AddNewMetaDataForTheNewArticles-01-InsertNewMetaData.sql" />
		<None Remove="Scripts\22-UpdateGoLiveDateToEqualSourcePublicationDate-01-UpdateGoLiveDate.sql" />
		<None Remove="Scripts\23-SelectArticleExport-01-InsertTranslations.sql" />
		<None Remove="Scripts\24-FixSubscriptionLocalTime-01-UpdateTimezones.sql" />
		<None Remove="Scripts\25-AddNewsletterSummary-01-UpdateMultilingualResource.sql" />
		<None Remove="Scripts\26-AddNewSources-01-InsertNewsSources.sql" />
		<None Remove="Scripts\27-AddGeographicalScope-01-AddFDA.sql" />
		<None Remove="Scripts\28-AddLanguagePreferences-01-InsertLanguages.sql" />
		<None Remove="Scripts\29-Users-01-InsertDefaultLanguagePreference.sql" />
		<None Remove="Scripts\30-AddWarningSaveMessages-01-InsertWarningSaveMessages.sql" />
		<None Remove="Scripts\31-UpdateNewslettersSubject.sql" />
		<None Remove="Scripts\32-AddInsightsManagerRole-01-InsertInsightsManagerRole.sql" />
		<None Remove="Scripts\33-UpdateMultilingualResources-01-AddHelpPageLocalization.sql" />
		<None Remove="Scripts\34-AddNewTheme-01-InsertSupplyChainDisruptionsTheme.sql" />
		<None Remove="Scripts\35-AddNewTheme-01-InsertArtificialIntelligenceTheme.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Scripts\06-AddUserEmailLink-01-AlterUserTriggers.sql" />
		<EmbeddedResource Include="Scripts\05-ArticleGoLiveDate-01-AlterNewsArticleContentTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-21-AddNewsRoles.sql" />
		<EmbeddedResource Include="Scripts\03-News-13-AlterCompanyTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-11-CreateNewsletterTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-20-UpdateCompanies.sql" />
		<EmbeddedResource Include="Scripts\03-News-09-CreateNewsArticleContentTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-02-CreateCompanyNewsCategoryTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-01-CreateAzureBlobTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-12-CreateNewsletterNewsArticleContentTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-17-AddNewsSources.sql" />
		<EmbeddedResource Include="Scripts\03-News-15-CreateNewsletterSubscriptionNewsCategoryTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-14-AlterUserTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-16-AddNewsCategories.sql" />
		<EmbeddedResource Include="Scripts\03-News-18-AddLocales.sql" />
		<EmbeddedResource Include="Scripts\03-News-19-AddMultilingualResources.sql" />
		<EmbeddedResource Include="Scripts\03-News-07-CreateNewsArticleCategoryTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-06-CreateNewsCategoryTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-05-CreateNewsSourceTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-04-CreateMultilingualResourceTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-03-CreateLocaleTriggers.sql" />
		<EmbeddedResource Include="Scripts\03-News-10-CreateNewsletterSubscriptionTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-01-CreateUserTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-02-CreateClaimTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-05-CreateContentTypeTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-06-CreateFieldTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-03-CreateContentItemTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-07-CreateFieldValueTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-04-CreateContentTypeDisplayTriggers.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-08-SetupContentTypes.sql" />
		<EmbeddedResource Include="Scripts\01-Initial-09-SetupPicklistData.sql" />
		<EmbeddedResource Include="Scripts\03-News-08-CreateNewsArticleTriggers.sql" />
		<EmbeddedResource Include="Scripts\02-Company-01-CreateCompanyClaims.sql" />
		<EmbeddedResource Include="Scripts\02-Company-03-CreateCompanyContentTypeTriggers.sql" />
		<EmbeddedResource Include="Scripts\02-Company-02-CreateCompanyTriggers.sql" />
		<EmbeddedResource Include="Scripts\02-Company-04-CreateCompanyUserTriggers.sql" />
		<EmbeddedResource Include="Scripts\08-AddAdditionalTheme-01-AddGoodPracticeTheme.sql" />
		<EmbeddedResource Include="Scripts\07-NewsletterSubscriptionActiveState-01-UpdateNewsletterSubscriptionTriggers.sql" />
		<EmbeddedResource Include="Scripts\09-AddGeographicalScope-01-AddSwitzerland.sql" />
		<EmbeddedResource Include="Scripts\10-AddUserCategories-01-CreateUserNewsCategoryTriggers.sql" />
		<EmbeddedResource Include="Scripts\10-AddUserCategories-02-MigrateUserCategories.sql" />
		<EmbeddedResource Include="Scripts\11-AddInfoFlashSubscriptionType-01-UpdateNewsletterSubscriptionTriggers.sql" />
		<EmbeddedResource Include="Scripts\12-AddNewSource-01-InsertSources.sql" />
		<EmbeddedResource Include="Scripts\13-AddNewsletterPublicationDate-01-InsertNewsletterPublicationDate.sql" />
		<EmbeddedResource Include="Scripts\14-UpdateNewsArticleFrenchFields-01-UpdateNewsArticleFrenchFields.sql" />
		<EmbeddedResource Include="Scripts\15-AddNewsArticleContentIdColumn-01-UpdateNewsletterTriggers.sql" />
		<EmbeddedResource Include="Scripts\15-AddNewsArticleContentIdColumn-02-MigrateNewsArticleContentData.sql" />
		<EmbeddedResource Include="Scripts\16-AddSearchInTitleOnly-01-AddSearchInTitleOnlyLabels.sql" />
		<EmbeddedResource Include="Scripts\17-UpdateGoLiveDateOldArticles-01-UpdateGoLiveDate.sql" />
		<EmbeddedResource Include="Scripts\18-UpdateFilteredTableV2Labels-01-UpdateFilteredTableV2Labels.sql" />
		<EmbeddedResource Include="Scripts\19-AddSearchPageLocalization-01-AddSearchPageLocalization.sql" />
		<EmbeddedResource Include="Scripts\20-AddNewsletterTypesEnumTranslations-01-AddNewsletterTypesEnumTranslations.sql" />
		<EmbeddedResource Include="Scripts\21-AddNewMetaDataForTheNewArticles-01-InsertNewMetaData.sql" />
		<EmbeddedResource Include="Scripts\22-UpdateGoLiveDateToEqualSourcePublicationDate-01-UpdateGoLiveDate.sql" />
		<EmbeddedResource Include="Scripts\23-SelectArticleExport-01-InsertTranslations.sql" />
		<EmbeddedResource Include="Scripts\24-FixSubscriptionLocalTime-01-UpdateTimezones.sql" />
		<EmbeddedResource Include="Scripts\25-AddNewsletterSummary-01-UpdateMultilingualResource.sql" />
		<EmbeddedResource Include="Scripts\26-AddNewSources-01-InsertNewsSources.sql" />
		<EmbeddedResource Include="Scripts\27-AddGeographicalScope-01-AddFDA.sql" />
		<EmbeddedResource Include="Scripts\11-AddNewTypeText-01-InsertTypeTexts.sql" />
		<EmbeddedResource Include="Scripts\28-AddLanguagePreferences-01-InsertLanguages.sql" />
		<EmbeddedResource Include="Scripts\29-Users-01-InsertDefaultLanguagePreference.sql" />
		<EmbeddedResource Include="Scripts\30-AddWarningSaveMessages-01-InsertWarningSaveMessages.sql" />
		<EmbeddedResource Include="Scripts\31-UpdateNewslettersSubject.sql" />
		<EmbeddedResource Include="Scripts\32-AddInsightsManagerRole-01-InsertInsightsManagerRole.sql" />
		<EmbeddedResource Include="Scripts\33-UpdateMultilingualResources-01-AddHelpPageLocalization.sql" />
		<EmbeddedResource Include="Scripts\34-AddNewTheme-01-InsertSupplyChainDisruptionsTheme.sql" />
		<EmbeddedResource Include="Scripts\35-AddNewTheme-01-InsertArtificialIntelligenceTheme.sql" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.SmartDecisions.Entities\PharmaLex.SmartDecisions.Entities.csproj" />
		<ProjectReference Include="..\PharmaLex.SmartDecisions.Web.Helpers\PharmaLex.SmartDecisions.Web.Helpers.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
