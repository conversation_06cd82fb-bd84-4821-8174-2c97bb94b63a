﻿

create trigger [dbo].[ContentItem_Insert] on [dbo].[ContentItem]
for insert as
insert into [Audit].[ContentItem_Audit]
select 'I', [Id], [Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[ContentItem_Update] on [dbo].[ContentItem]
for update as
insert into [Audit].[ContentItem_Audit]
select 'U', [Id], [Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[ContentItem_Delete] on [dbo].[ContentItem]
for delete as
insert into [Audit].[ContentItem_Audit]
select 'D', [Id], [Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
