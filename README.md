# Smart Decisions (SmartNews)
[![Build Status](https://dev.azure.com/Phlexglobal/SmartDecisions/_apis/build/status/SmartDecisions.Build?repoName=SmartDecisions&branchName=develop)](https://dev.azure.com/Phlexglobal/SmartDecisions/_build/latest?definitionId=1681&repoName=SmartDecisions&branchName=develop)

## Project Overview
A Regulatory Intelligence system for managing officially published Health Authority guidelines, combined with our own experience and recommended best practices. SmartDecisions is a tool to collate news articles and produce relevant newsletters for subscribers according to their interests.

SmartDecisions has two parts:
- core regulatory intelligence system
- SmartNEWS service providing the latest news and information on regulatory changes

### Features:
- Defines Regulatory Intelligence into specific topics, allowing users to view the latest regulatory intelligence for a selected topic for a selected country
- Structured data approach for intelligence information allows automated processing
- Information is managed by defined data governance processes ensuring it remains current, accurate and trustworthy
- Regulatory Intelligence information is complemented by extensive experience from 10,000s of regulatory submissions worldwide every year

### Project Structure

```

├── src
     ├── PharmaLex.NewsletterApp                          # Azure functions used to send emails to subscribers via SendGrid based on user subscriptions and manage logs from SendGrid
     ├── PharmaLex.SmartDecisions.Data                    # Data layer providing repositories, database migrations and scripts
     ├── PharmaLex.SmartDecisions.Entities                # Domain layer for domain entities
     ├── PharmaLex.SmartDecisions.Web                     # MVC/Razor Web Application
          ├── Authentication                              # Azure AD B2C user authentication callbacks
          ├── Controllers                                 # MVC Controllers
          ├── newrelic                                    # Folder for NewRelic license and configuration
          ├── src                                         # Folder storing styles, images and templates
          ├── Views                                       # MVC Views (Vue.js)
     ├── PharmaLex.SmartDecisions.Web.Helpers             # Helper methods and services
     ├── PharmaLex.SmartDecisions.Web.Models              # DTOs
├── test
     ├── PharmaLex.SmartDecisions.IntegrationTests        # Integration tests
     ├── PharmaLex.SmartDecisions.Tests                   # Unit tests

```

## Local Infrastructure
SmartDecisions requires the following infrastructure locally:
- Microsoft SQL Server
- Azurite emulator for local Azure Storage development

## Migrations
To add new migration open Package Manager Console, set the default project to PharmaLex.SmartDecisions.Data and use command Add-Migration <migration name>
Example command:
```
Add-Migration InitialCreate
```

To create or update the database with new migrations open Package Manager Console, set the default project to PharmaLex.SmartDecisions.Data and use command Update-Database.
This will update database specified in setting `ConnectionStrings:default`.

## Getting Started
1. Clone the repository. If using the command line, run the following commands.
	- `git clone https://<EMAIL>/Phlexglobal/SmartDecisions/_git/SmartDecisions` to fetch the parent repository and check out the default branch.
	- `cd SmartDecisions` to change into the repository.
2. As SSO is enabled, the application needs to be changed to run on port 5001.
Update the launchSettings.json (src/PharmaLex.SmartDecisions.Web/Properties/launchSettings.json) file to have an `applicationUrl` of `"https://localhost:5001;http://localhost:61424"`.
3. Update the "ConnectionStrings:default" setting to the connection string of the local SQL Server database.
4. If you want to enable AzureSearch locally, update the IsEnabled setting under the "AzureSearch" section. 

### Build and Test SmartDecisions Web
In order to run the web project directly from Visual Studio:
	- VPN should be connected
	- Ensure the startup project is set to PharmaLex.SmartDecisions.Web

### Authentication and Authorisation users
For local development you need to add your user in the `dbo.User` table and then add an entry in the `dbo.UserClaim` table with a claim (e.g., SuperAdmin) for the user.

### Build and Test PharmaLex.NewsletterApp
In order to run and debug the Newsletter application (Azure function):
- Set the startup project as PharmaLex.NewsletterApp
- Update the appsettings.json file in the PharmaLex.NewsletterApp project:
    - Change the "ConnectionStrings:default" setting to the connection string of the local SQL Server database
- Run the project in Visual Studio

By default, the Azure function project will use Azurite to emulate the Azure storage locally (you have to make sure Visual Studio comes with Azurite or install it separately). 
Alternatively, you can provide a connection string to a real Azure storage account in the local.settings.json file.
 
	
