﻿create trigger [dbo].[<PERSON><PERSON>m_Insert] on [dbo].[Claim]
for insert as
insert into [Audit].[<PERSON><PERSON><PERSON>_Audit]
select 'I', [Id], [Name], [ClaimType], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[Claim_Update] on [dbo].[Claim]
for update as
insert into [Audit].[Claim_Audit]
select 'U', [Id], [Name], [ClaimType], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[Claim_Delete] on [dbo].[Claim]
for delete as
insert into [Audit].[Claim_Audit]
select 'D', [Id], [Name], [ClaimType], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go

insert into [dbo].[<PERSON><PERSON><PERSON>] select 'SuperAdmin', 'admin', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[Claim] select 'UserAdmin', 'admin', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[Claim] select 'SystemAdmin', 'admin', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
go

create trigger [dbo].[UserClaim_Insert] on [dbo].[UserClaim]
for insert as
insert into [Audit].[UserClaim_Audit]
select 'I', [UserId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[UserClaim_Update] on [dbo].[UserClaim]
for update as
insert into [Audit].[UserClaim_Audit]
select 'U', [UserId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[UserClaim_Delete] on [dbo].[UserClaim]
for delete as
insert into [Audit].[UserClaim_Audit]
select 'D', [UserId], [ClaimId], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go

insert into [dbo].[UserClaim] select 1, 1, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[UserClaim] select 2, 1, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[UserClaim] select 3, 1, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[UserClaim] select 9, 2, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[UserClaim] select 10, 2, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[UserClaim] select 9, 3, getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[UserClaim] select 10, 3, getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[UserClaim] select 17, 1, getdate(), '<EMAIL>', getdate(), '<EMAIL>'