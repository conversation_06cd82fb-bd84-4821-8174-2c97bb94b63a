﻿<script type="text/x-template" id="contact-template">
    <div class="dialog-surface" v-if="!closed" v-on:click="close">
        <div class="dialog-container" v-on:click.stop>
            <div class="dialog-content">

                <div class="modal-header">
                    <h2>{{config.title || 'Contact us'}}</h2>
                    <i class="m-icon ml-auto" v-on:click="close">close</i>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input :value="currentSubject"
                               v-on:change="onChange('subject', $event.target)"
                               type="text"
                               required 
                               id="contactFormSubject"
                               :class="[{'input-validation-error': !subjectValid}]" />
                    </div>
                    <div class="form-group">
                        <label for="body">Text</label>
                        <textarea :value="currentBody"
                                  v-on:change="onChange('body', $event.target)"
                                  required 
                                  id="contactFormBody"
                                  :class="[{'input-validation-error': !bodyValid}]"></textarea>
                    </div>
                   
                    
                </div>
                
                          
                <div class="buttons mt-2">
                    <button v-on:click="close" class="button secondary">Cancel</button>
                    <button v-on:click="send" type="submit">Send</button>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueContactApp.component('contact', {
        template: '#contact-template',
        data() {
            return {
                closed: true,
                subject: null,
                subjectValid: true,
                body: null,
                bodyValid: true
            };
        },
        props: {
            config: {
                type: Object,
                default: {}
            }
        },
        computed: {
            currentSubject() {
                return this.subject !== null ? this.subject : this.config.subject;
            },
            currentBody() {
                return this.body !== null ? this.body : this.config.body;
            }
        },
        methods: {
            onChange(prop, el) {
                this[prop] = el.value;
                this[`${prop}Valid`] = el.checkValidity();
            },
            send() {
                this.checkValidity() && fetch(`/home/<USER>
                    method: 'POST',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ Body: this.currentBody, Subject: this.currentSubject })
                }).then(result => {
                    if (result.ok) {
                        this.close();
                        plx.toast.show('Done! Thanks for contacting us!', 2, 'confirm', null, 2500, { useIcons: true });
                    }
                    else
                        plx.toast.show('Something happened and your message was not sent!', 2, 'failed', null, 5000, { useIcons: true });
                });
            },
            close() {
                this.closed = true;
                this.body = this.config.body || null;
                this.subject = this.config.subject || null;
            },
            open() {
                this.closed = false;
            },
            checkValidity() {
                this.bodyValid = !!this.currentBody;
                this.subjectValid = !!this.currentSubject;

                return this.bodyValid && this.subjectValid;
            }
        },
        mounted() {
            let link = document.getElementById('contactLink');

            if (link) {
                link.addEventListener('click', this.open);
            }
        },
        unmounted() {
            let link = document.getElementById('contactLink');

            if (link) {
                link.removeEventListener('click', this.open);
            }
        }
    });
</script>
