﻿@model CompanyUsersViewModel
@{
    ViewData["Title"] = ls.Localise("(news).users.manage");
}

<div id="users" v-cloak>
    <div class="sub-header">
        <h2>@ls.Localise("(news).users.manage")</h2>
        <div class="controls">
            <a v-if="!model.totalLicenseLimitReached" class="button" href="/users/new">@ls.Localise("(news).users.add")</a>
        </div>
    </div>
    <div v-if="model.activeLicenseLimitReached || model.totalLicenseLimitReached" class="flex-item flex flex-cols tile flex-x1">
        <div class="flex flex-nowrap flex-align-center" v-if="model.totalLicenseLimitReached">
            <i class="m-icon warning-color">warning</i>
            <p class="lead p-0 m-0 pl-2"> @ls.LocaliseInterpolate("(news).users.maximum-users-limit-format", Model.TotalUsersCount.ToString()) </p>
        </div>
        <div class="flex flex-nowrap flex-align-center" v-if="model.activeLicenseLimitReached">
            <i class="m-icon warning-color">warning</i>
            <p class="lead p-0 m-0 pl-2"> @ls.LocaliseInterpolate("(news).users.maximum-active-users-limit-format", Model.ActiveUsersCount.ToString()) </p>
        </div>
    </div>

    <filtered-table :items="users"
                    :columns="columns"
                    :filters="filters"
                    :link="link"
                    :resources="resources">
    </filtered-table>

</div>

@section Scripts {
    <script type="text/javascript">
    var pageConfig = {
        appElement: '#users',
        data: function () {
            let model = @Html.Raw(Json.Serialize(Model));
            return {
                link: '/users/edit/',
                model,
                users: model.users.map(x => {
                    x.activeF = [x.active ? 1 : -1];
                    return x;
                }),
                 resources: {
                    noRecordsMessage: '@ls.LocaliseSafe("[[table]].no-results-message")',
                    sortByFormat: '@ls.LocaliseSafe("[[table]].sort-by-format")',
                    filterByFormat: '@ls.LocaliseSafe("[[table]].filter-by-format")',
                    searchInFormat: '@ls.LocaliseSafe("[[table]].search-in-format")',
                    clearFilters: '@ls.LocaliseSafe("[[table]].clear-filters")',
                    addItem: '@ls.LocaliseSafe("[[table]].add-item")',
                    noValue: '@ls.LocaliseSafe("[[table]].no-value")',
                    pager: {
                        showingFormat: '@ls.LocaliseSafe("[[table]].pager.showing-format")',
                        showingFilteredFormat: '@ls.LocaliseSafe("[[table]].pager.showing-filtered-format")',
                        pageSize: '@ls.LocaliseSafe("[[table]].pager.page-size")',
                        first: '@ls.LocaliseSafe("[[table]].pager.first")',
                        previous: '@ls.LocaliseSafe("[[table]].pager.previous")',
                        next: '@ls.LocaliseSafe("[[table]].pager.next")',
                        last: '@ls.LocaliseSafe("[[table]].pager.last")',
                    }
                },
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'displayFullName',
                            sortKey: 'displayFullName',
                            header: '@ls.LocaliseSafe("(news).users.column-name")',
                            type: 'text',
                            style: 'width: 45%;'
                        },
                        {
                            dataKey: 'email',
                            sortKey: 'email',
                            header: 'ID',
                            type: 'text',
                            style: 'width: 45%;'
                        },
                        {
                            dataKey: 'active',
                            sortKey: 'active',
                            header: '@ls.LocaliseSafe("(news).users.column-active")',
                            type: 'bool',
                            style: 'width: 50px;'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'displayFullName',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.displayFullName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'email',
                        options: [],
                        type: 'search',
                        header: 'Search ID',
                        fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'active',
                        options: [{ name: 'Enabled', value: 1 }, { name: 'Not enabled', value: -1 }],
                        filterCollection: 'activeF',
                        display: 'name',
                        dataKey: 'value',
                        type: 'select',
                        header: 'Filter By Active',
                        fn: v => p => (p.active ? 1 : -1) === v,
                        convert: v => parseInt(v)
                    }
                ]
            };
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}
