﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System.Threading.Tasks;
using System;
using System.Text.RegularExpressions;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "OwnsRecord")]
    public class EditContentItemController : BaseContentItemController
    {
        public EditContentItemController(IDistributedCacheServiceFactory cacheFactory, IMapper mapper) : base(cacheFactory, mapper) { }

        [HttpGet("/data/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.<PERSON>Value));
            var cim = await cic.FirstOrDefaultAsync(x => x.Id == id);

            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == cim.ContentTypeId);
            cim.ContentType = ctm;

            return View("/Views/ContentItem/EditContentItem.cshtml", new EditContentItemViewModel
            {
                ContentType = ctm,
                Item = cim
            });
        }

        [HttpPost("/data/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveEdit(int id, string button)
        {
            var model = this.Request.Form;

            // the validation of the Name field is done here and not in the ContentItemModel because more complex refactoring is needed to change the code to use the ContentItemModel
            if (Regex.IsMatch(model["name"].ToString(), new GeneratedRegexAttribute(@"<.*?>|<|>").Pattern, RegexOptions.NonBacktracking))
            {
                TempData.Add("ErrorMessage", "Invalid characters in the Name field.");
                return Redirect($"/data/edit/{id}");
            }
            var cic = cacheFactory.CreateTrackedEntity<ContentItem>().Configure(x => x.Include(y => y.FieldValue));
            var ci = await cic.FirstOrDefaultAsync(x => x.Id == int.Parse(model["id"]));
            
            var ctc = cacheFactory.CreateEntity<ContentType>().Configure(o => o.Include(x => x.Field));
            var ct = await ctc.FirstOrDefaultAsync(x => x.Id == ci.ContentTypeId);

            if (!ct.AutoManageName)
            {
                ci.Name = model["name"];
            }
            ci.Owner = model["owner"];
            ci.VerifiedDate = button == "save" ? DateTime.Parse(model["verifieddate"]) : DateTime.Now;

            var fvc = cacheFactory.CreateTrackedEntity<FieldValue>().Configure(o => o.Include(x => x.ContentItem));
            this.MapFieldValues(model, ci, ct, fvc);

            await cic.SaveChangesAsync();
            await fvc.SaveChangesAsync();

            return Redirect(this.IsSystemAdmin ? $"/data/{ci.ContentTypeId}" : "/records");
        }
    }
}
