{"name": "SmartDecisions", "version": "1.0.0", "scripts": {"build": "webpack --config webpack.dev.js", "build-prod": "webpack --config webpack.prod.js", "test": "echo \"Error: no test specified\" && exit 1"}, "directories": {"lib": "lib"}, "devDependencies": {"babel-loader": "^10.0.0", "css-loader": "^7.1.2", "eslint": "^9.4.0", "eslint-webpack-plugin": "^4.2.0", "gulp": "^5.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "mini-css-extract-plugin": "^1.6.2", "postcss-loader": "^8.1.1", "sass": "^1.77.2", "sass-loader": "^16.0.0", "vue": "^3.2.9", "vue-loader": "^17.4.2", "vue-style-loader": "^4.1.3", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-remove-empty-scripts": "^1.0.4"}, "overrides": {"@babel/helpers": "^7.27.0"}, "engines": {"node": ">=22"}}