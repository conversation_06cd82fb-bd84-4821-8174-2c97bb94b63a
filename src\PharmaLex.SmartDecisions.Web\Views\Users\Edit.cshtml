﻿@model PharmaLex.SmartDecisions.Web.Models.UserModel
@using PharmaLex.SmartDecisions.Entities
@using System.Web
@using PharmaLex.Caching.Data
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@inject IDistributedCacheServiceFactory cache
@{
    ViewData["Title"] = "Manage Admins";

    IEnumerable<Claim> _claims = 
        (await cache.CreateEntity<Claim>().WhereAsync(c => c.ClaimType == "admin" || c.ClaimType == "news"))
            .Where(c => 
                this.User.HasClaim(x => x.Type == "news:NewsAuthoringLead" && c.ClaimType == "news") ||
                this.User.HasClaim(x => x.Type == $"{c.ClaimType}:{c.Name}" || x.Type == "admin:SuperAdmin"));

    var claims = _claims.Select(c => new { Id = c.Id, Name = c.Name });
}

<div id="user">

    <div class="sub-header">
        <h2>@(String.IsNullOrEmpty(Model.DisplayNameAndEmail) ? "Add admin" : Model.DisplayNameAndEmail)</h2>
        <div class="controls">
            <a href="/manage/users" class="button secondary">Back to User list</a>
        </div>
    </div>

        <form method="post">
            <div class="flex flex-nowrap gapped-2">
                <div id="details-column" v-bind:class="['flex-x2 flex-item tile', {'hidden': !user}]" v-if="user !== null">
                    <h5>Details</h5>
                    <div class="form-group">
                        <label>Given name</label>
                        <input asp-for="GivenName" :value="user.givenName" type="text" readonly="readonly" />
                    </div>
                    <div class="form-group">
                        <label>Family name</label>
                        <input asp-for="FamilyName" :value="user.familyName" type="text" readonly="readonly" />
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                    <input asp-for="Email" :value="userEmail" type="text" readonly="readonly" />
                    </div>
                </div>

                <div id="search-column" v-bind:class="['flex-x3 flex-item tile', {'hidden': user}]">
                    <h5>Find user</h5>
                    <label>Start typing the user's name or email address</label>
                    <autocomplete v-if="active" :config="config" v-on:selected="selectedItem"></autocomplete>
                </div>
                <div id="claims-column" v-bind:class="['flex-x3 flex-item tile', {'form-col-disabled': !user}]">
                    <h5 class="mb-3">Manage roles</h5>
                    <div v-for="claim in claims" :key="claim.id" class="checkbox-list-item">
                        <input v-bind="bindClaim(claim)" />
                        <label :for="claim.id">{{claim.name}}</label>
                    </div>

                </div>
            </div>
           
            <div class="buttons no-border mt-2 p-2">
                <a class="button secondary" href="/manage/users">Cancel</a>
                <button type="submit">Save</button>
            </div>
        </form>

</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#user',
            data: function () {
                return {
                    config: {
                        canAddNew: false,
                        searchUrl: '/manage/users/find?term={term}'
                    },
                    user: null,
                    active: true,
                    isModelSet: false,
                    claims: @Html.Raw(Json.Serialize(claims))
                };
            },
            computed: {
                userEmail: function () {
                    return this.user.email ? this.user.email : this.user.value;
                },
            },
            methods: {
                validateForm: function (event) {
                    this.attemptSubmit = true;
                    if (this.missingGivenName || this.missingFamilyName || this.missingEmail) {
                        event.preventDefault();
                    }
                },
                selectedItem(selectedUser) {
                    this.user = selectedUser;
                    this.isModelSet = true;
                    /* Id has a value 0, when the selected user does not exist
                     * in the db of the application, but comes from the active directory only*/
                    if (this.user.id > 0) {
                        document.location.href = '/manage/users/edit/' + this.user.id;
                    }
                },
                bindClaim(claim) {
                    let binding = {
                        id: `${claim.id}`,
                        value: `${claim.id}`,
                        name: 'Claims',
                        type: 'checkbox'
                    }

                    if (this.user && this.user.claims && this.user.claims.includes(claim.id)) {
                        binding.checked = 'checked';
                    }

                    return binding;
                }
            },
            created() {
                if (@Model.Id !== 0) {
                    var userJson = '@Html.Raw(HttpUtility.JavaScriptStringEncode(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })))';
                    this.user = JSON.parse(userJson);
                    this.isModelSet = true;
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/AutoCompleteList" />
}