﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using PharmaLex.Caching.Data;
using PharmaLex.Office;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface IContentItemImportReader : IExcelReader
    {
        IList<ContentItemModel> Read(ContentType ct, Stream stream, string defaultOwner);
    }

    public class ContentItemImportReader : ExcelReader, IContentItemImportReader
    {
        private IDistributedCacheServiceFactory cache;

        public ContentItemImportReader(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public IList<ContentItemModel> Read(ContentType ct, Stream stream, string defaultOwner)
        {
            IWorkbook wb = new XSSFWorkbook(stream);
            ISheet s = wb.GetSheet(ct.PluralName) ?? wb.GetSheetAt(0);
            IEnumerator rows = s.GetRowEnumerator();
            DataFormatter df = new DataFormatter();

            rows.MoveNext();
            XSSFRow header = (XSSFRow)rows.Current;
            var fields = header.AsEnumerable<ICell>().Select(x => df.FormatCellValue(x).TrimEnd('*')).ToArray();

            IEnumerable<PicklistModel> picklists = ct.Field.Where(x => x.FieldType == FieldType.Picklist || x.FieldType == FieldType.Relationship).Select(x => new PicklistModel(x.RelatedContentTypeId.Value, cache.CreateMappedEntity<ContentItem, PicklistItemModel>().Where(y => y.ContentTypeId == x.RelatedContentTypeId)));

            List<ContentItemModel> items = new List<ContentItemModel>();
            while (rows.MoveNext())
            {
                IRow r = (XSSFRow)rows.Current;
                ContentItemModel cim = new ContentItemModel()
                {
                    ContentTypeId = ct.Id
                };

                List<FieldValueModel> values = new List<FieldValueModel>();
                for(int i = 0; i < fields.Count(); i++)
                {
                    switch(fields[i])
                    {
                        case "Id":
                            if (Int32.TryParse(df.FormatCellValue(r.GetCell(i)), out int id))
                            {
                                cim.Id = id;
                            }
                            break;
                        case "Name":
                            if (!ct.AutoManageName)
                            {
                                cim.Name = df.FormatCellValue(r.GetCell(i));
                            }
                            break;
                        case "Owner":
                            string owner = df.FormatCellValue(r.GetCell(i));
                            cim.Owner = String.IsNullOrWhiteSpace(owner) ? defaultOwner : owner;
                            break;
                        case "Verified on":
                            cim.VerifiedDate = DateTime.TryParseExact(df.FormatCellValue(r.GetCell(i)), "yyyy-MM-dd", null, new DateTimeStyles(), out DateTime vd) ? vd : DateTime.Now;
                            break;
                        default:
                            Field f = ct.Field.FirstOrDefault(x => x.Name == fields[i]);
                            if(f != null)
                            {
                                string v = this.GetFieldValue(f, df.FormatCellValue(r.GetCell(i)), picklists);
                                if (!String.IsNullOrEmpty(v))
                                {
                                    values.Add(new FieldValueModel
                                    {
                                        FieldId = f.Id,
                                        ContentItemId = cim.Id,
                                        Value = v,
                                        Name = $"{ct.Name} - {f.Name}"
                                    });
                                }
                            }
                            break;
                    }
                    // This could be way more efficient e.g. don't do the field exists checks on every row!
                }
                if(cim.Id < 1)
                {
                    if (ct.AutoManageName)
                    {
                        cim.Name = $"{ct.Name}-{DateTime.Now.Ticks}";
                    }
                    if(!cim.VerifiedDate.HasValue)
                    {
                        cim.VerifiedDate = DateTime.Now;
                    }
                    if(String.IsNullOrWhiteSpace(cim.Owner))
                    {
                        cim.Owner = defaultOwner;
                    }
                }
                cim.Values = values;
                items.Add(cim);
            }
            return items;
        }

        private string GetFieldValue(Field f, string v, IEnumerable<PicklistModel> picklists)
        {
            switch (f.FieldType)
            {
                case FieldType.Picklist:
                    var pl = picklists.First(x => x.ContentTypeId == f.RelatedContentTypeId);
                    if(f.MultiSelect)
                    {
                        return String.Join('|', pl.Items.Where(x => v.Split('|').Select(x => x.Trim()).Contains(x.Name)).Select(x => x.Id.ToString()));
                    }
                    return pl.Items.FirstOrDefault(x => x.Name == v.Trim())?.Id.ToString();
                case FieldType.Relationship: goto case FieldType.Picklist;
                default: return v;
            }
        }
    }
}
