﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Authentication.B2C;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers.Authorization
{
    public static class AuthorizationHelper
    {
        public static IServiceCollection AddPolicies(this IServiceCollection services)
        {
            var serviceProvider = services.BuildServiceProvider();
            var repoFactory = serviceProvider.GetRequiredService<IRepositoryFactory>();
            var httpContext = serviceProvider.GetRequiredService<IHttpContextAccessor>();
            List<Claim> claims = repoFactory.Create<Claim>().Configure().ToList();

            Action<AuthorizationOptions> configure = o =>
            {
                foreach (Claim c in claims.Where(x => x.ClaimType == "admin" && x.Key != "admin:UserAdmin"))
                {
                    o.AddPolicy(c.Name, p => p.RequireAssertion(x => x.User.HasClaim(y => y.Type == $"{c.ClaimType}:{c.Name}" || y.Type == "admin:SuperAdmin")));
                }

                o.AddPolicy("Admin", p => p.RequireAssertion(c => c.User.HasClaim(x => x.Type.StartsWith("admin:") || x.Type == "news:NewsAuthoringLead")));
                o.AddPolicy("RecordOwner", p => p.RequireAssertion(c => c.User.HasClaim(x => x.Type == "plx:RecordOwner")));
                o.AddPolicy("OwnsRecord", p => p.Requirements.Add(new OwnsRecordRequirement(httpContext, repoFactory)));
                o.AddPolicy("DecisionsSubscriber", p => p.Requirements.Add(new DecisionsSubscriberRequirement(repoFactory)));
                o.AddPolicy("CompanyManager", p => p.Requirements.Add(new CompanyManagerRequirement()));

                o.AddPolicy("UserAdmin", p => p.RequireAssertion(c => c.User.HasClaim(x => x.Type == "admin:UserAdmin" || x.Type == "news:NewsAuthoringLead" || x.Type == "admin:SuperAdmin")));

                o.AddPolicy("NewsAuthor", p => p.RequireAssertion(c => c.User.HasClaim(x => x.Type.StartsWith("news:") || x.Type == "admin:SuperAdmin")));
                o.AddPolicy("NewsAuthoringLead", p => p.RequireAssertion(c => c.User.HasClaim(x => x.Type == "news:NewsAuthoringLead" || x.Type == "admin:SuperAdmin")));
                o.AddPolicy("NewsSubscriber", p => p.Requirements.Add(new NewsSubscriberRequirement(repoFactory)));
            };

            return services.Configure(configure);
        }
    }

    public class OwnsRecordRequirement : AuthorizationHandler<OwnsRecordRequirement>, IAuthorizationRequirement
    {
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IRepositoryFactory repoFactory;

        public OwnsRecordRequirement(IHttpContextAccessor httpContextAccessor, IRepositoryFactory repoFactory)
        {
            this.httpContextAccessor = httpContextAccessor;
            this.repoFactory = repoFactory;
        }

        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, OwnsRecordRequirement requirement)
        {
            if (context.User.HasClaim(x => x.Type == "admin:SystemAdmin" || x.Type == "admin:SuperAdmin"))
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            var id = httpContextAccessor.HttpContext?.GetRouteData().Values["id"];
            if (id != null && int.TryParse(id.ToString(), out int recordId))
            {
                IRepository<ContentItem> cir = repoFactory.Create<ContentItem>();
                var ci = cir.Configure().FirstOrDefault(x => x.Id == recordId);
                if (ci != null && ci.Owner.ToLowerInvariant().Equals(context.User.Identity?.Name?.ToLowerInvariant(), StringComparison.InvariantCultureIgnoreCase))
                {
                    context.Succeed(requirement);
                }
            }
            return Task.CompletedTask;
        }
    }

    public class DecisionsSubscriberRequirement : AuthorizationHandler<DecisionsSubscriberRequirement>, IAuthorizationRequirement
    {
        private readonly IRepositoryFactory repoFactory;

        public DecisionsSubscriberRequirement(IRepositoryFactory repoFactory)
        {
            this.repoFactory = repoFactory;
        }

        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, DecisionsSubscriberRequirement requirement)
        {
            if (context.User.IsPharmaLexUser())
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            int userId = context.User.GetClaimValue<int>("plx:userid");
            var cur = repoFactory.Create<CompanyUser>();
            var cu = cur.Configure(o => o
                .Include(x => x.Company)
                    .ThenInclude(x => x.CompanyContentType)
                ).FirstOrDefault(x => x.UserId == userId && x.Active);

            if (cu?.Company.CompanyContentType.Count > 0)
            {
                context.Succeed(requirement);
            }

            return Task.CompletedTask;
        }
    }

    public class NewsSubscriberRequirement : AuthorizationHandler<NewsSubscriberRequirement>, IAuthorizationRequirement
    {
        private readonly IRepositoryFactory repoFactory;

        public NewsSubscriberRequirement(IRepositoryFactory repoFactory)
        {
            this.repoFactory = repoFactory;
        }

        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, NewsSubscriberRequirement requirement)
        {
            if (context.User.IsPharmaLexUser())
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            int userId = context.User.GetClaimValue<int>("plx:userid");
            var cur = repoFactory.Create<CompanyUser>();
            var cu = cur.Configure(o => o
                .Include(x => x.Company)
                    .ThenInclude(x => x.CompanyNewsCategory)
                ).FirstOrDefault(x => x.UserId == userId && x.Active);

            if (cu?.Company.CompanyNewsCategory.Count > 0)
            {
                context.Succeed(requirement);
            }

            return Task.CompletedTask;
        }
    }

    public abstract class CompanyRequirement<T> : AuthorizationHandler<T>, IAuthorizationRequirement where T : IAuthorizationRequirement
    {
        protected bool HasCompanyAccess(System.Security.Claims.ClaimsPrincipal user)
        {
            int userCompanyId = user.GetClaimValue<int>("plx:companyid");
            bool userIsActive = user.GetClaimValue<bool>("plx:userisactive");
            return userCompanyId > 0 && userIsActive;
        }
    }

    public class CompanyManagerRequirement : CompanyRequirement<CompanyManagerRequirement>, IAuthorizationRequirement
    {
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, CompanyManagerRequirement requirement)
        {
            if (HasCompanyAccess(context.User))
            {
                if (context.User.HasClaim(c => c.Type == "company:CompanyManager"))
                {
                    context.Succeed(requirement);
                    return Task.CompletedTask;
                }
            }

            context.Fail();
            return Task.CompletedTask;
        }
    }
}
