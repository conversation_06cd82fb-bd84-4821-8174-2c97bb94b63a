!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Uppy=t()}}(function(){var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},r={limit:1,onStart:function(){},onProgress:function(){},onPartComplete:function(){},onSuccess:function(){},onError:function(t){throw t}};function n(t,e){var r=t.indexOf(e);-1!==r&&t.splice(r,1)}var o=function(){function o(t,n){!function(t,e){if(!(t instanceof o))throw new TypeError("Cannot call a class as a function")}(this),this.options=e({},r,n),this.file=t,this.key=this.options.key||null,this.uploadId=this.options.uploadId||null,this.parts=this.options.parts||[],this.isPaused=!1,this.chunks=null,this.chunkState=null,this.uploading=[],this._initChunks()}return o.prototype._initChunks=function(){for(var t=[],e=Math.max(Math.ceil(this.file.size/1e4),5242880),r=0;r<this.file.size;r+=e){var n=Math.min(this.file.size,r+e);t.push(this.file.slice(r,n))}this.chunks=t,this.chunkState=t.map(function(){return{uploaded:0,busy:!1,done:!1}})},o.prototype._createUpload=function(){var e=this;return Promise.resolve().then(function(){return e.options.createMultipartUpload()}).then(function(e){if("object"!==(void 0===e?"undefined":t(e))||!e||"string"!=typeof e.uploadId||"string"!=typeof e.key)throw new TypeError("AwsS3/Multipart: Got incorrect result from 'createMultipartUpload()', expected an object '{ uploadId, key }'.");return e}).then(function(t){e.key=t.key,e.uploadId=t.uploadId,e.options.onStart(t)}).then(function(){e._uploadParts()}).catch(function(t){e._onError(t)})},o.prototype._resumeUpload=function(){var t=this;return Promise.resolve().then(function(){return t.options.listParts({uploadId:t.uploadId,key:t.key})}).then(function(e){e.forEach(function(e){var r=e.PartNumber-1;t.chunkState[r]={uploaded:e.Size,etag:e.ETag,done:!0},t.parts.some(function(t){return t.PartNumber===e.PartNumber})||t.parts.push({PartNumber:e.PartNumber,ETag:e.ETag})}),t._uploadParts()}).catch(function(e){t._onError(e)})},o.prototype._uploadParts=function(){var t=this;if(!this.isPaused){var e=this.options.limit-this.uploading.length;if(0!==e)if(this.chunkState.every(function(t){return t.done}))this._completeUpload();else{for(var r=[],n=0;n<this.chunkState.length;n++){var o=this.chunkState[n];if(!o.done&&!o.busy&&(r.push(n),r.length>=e))break}r.forEach(function(e){t._uploadPart(e)})}}},o.prototype._uploadPart=function(e){var r=this,n=this.chunks[e];return this.chunkState[e].busy=!0,Promise.resolve().then(function(){return r.options.prepareUploadPart({key:r.key,uploadId:r.uploadId,body:n,number:e+1})}).then(function(e){if("object"!==(void 0===e?"undefined":t(e))||!e||"string"!=typeof e.url)throw new TypeError("AwsS3/Multipart: Got incorrect result from 'prepareUploadPart()', expected an object '{ url }'.");return e}).then(function(t){var n=t.url;r._uploadPartBytes(e,n)},function(t){r._onError(t)})},o.prototype._onPartProgress=function(t,e,r){this.chunkState[t].uploaded=e;var n=this.chunkState.reduce(function(t,e){return t+e.uploaded},0);this.options.onProgress(n,this.file.size)},o.prototype._onPartComplete=function(t,e){this.chunkState[t].etag=e,this.chunkState[t].done=!0;var r={PartNumber:t+1,ETag:e};this.parts.push(r),this.options.onPartComplete(r),this._uploadParts()},o.prototype._uploadPartBytes=function(t,e){var r=this,o=this.chunks[t],i=new XMLHttpRequest;i.open("PUT",e,!0),i.responseType="text",this.uploading.push(i),i.upload.addEventListener("progress",function(e){e.lengthComputable&&r._onPartProgress(t,e.loaded,e.total)}),i.addEventListener("abort",function(e){n(r.uploading,e.target),r.chunkState[t].busy=!1}),i.addEventListener("load",function(e){if(n(r.uploading,e.target),r.chunkState[t].busy=!1,e.target.status<200||e.target.status>=300)r._onError(new Error("Non 2xx"));else{r._onPartProgress(t,o.size,o.size);var i=e.target.getResponseHeader("ETag");null!==i?r._onPartComplete(t,i):r._onError(new Error("AwsS3/Multipart: Could not read the ETag header. This likely means CORS is not configured correctly on the S3 Bucket. Seee https://uppy.io/docs/aws-s3-multipart#S3-Bucket-Configuration for instructions."))}}),i.addEventListener("error",function(e){n(r.uploading,e.target),r.chunkState[t].busy=!1;var o=new Error("Unknown error");o.source=e.target,r._onError(o)}),i.send(o)},o.prototype._completeUpload=function(){var t=this;return this.parts.sort(function(t,e){return t.PartNumber-e.PartNumber}),Promise.resolve().then(function(){return t.options.completeMultipartUpload({key:t.key,uploadId:t.uploadId,parts:t.parts})}).then(function(e){t.options.onSuccess(e)},function(e){t._onError(e)})},o.prototype._abortUpload=function(){this.uploading.slice().forEach(function(t){t.abort()}),this.options.abortMultipartUpload({key:this.key,uploadId:this.uploadId}),this.uploading=[]},o.prototype._onError=function(t){this.options.onError(t)},o.prototype.start=function(){this.isPaused=!1,this.uploadId?this._resumeUpload():this._createUpload()},o.prototype.pause=function(){this.uploading.slice().forEach(function(t){t.abort()}),this.isPaused=!0},o.prototype.abort=function(){if(!(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).really)return this.pause();this._abortUpload()},o}(),i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},s=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),a=function(){function t(e,r){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.uppy=e,this.opts=r,this.onReceiveResponse=this.onReceiveResponse.bind(this)}return t.prototype.onReceiveResponse=function(t){var e,r=this.uppy.getState().companion||{},n=this.opts.serverUrl,o=t.headers;return o.has("i-am")&&o.get("i-am")!==r[n]&&this.uppy.setState({companion:i({},r,(e={},e[n]=o.get("i-am"),e))}),t},t.prototype._getUrl=function(t){return/^(https?:|)\/\//.test(t)?t:this.hostname+"/"+t},t.prototype.get=function(t){var e=this;return fetch(this._getUrl(t),{method:"get",headers:this.headers,credentials:"same-origin"}).then(this.onReceiveResponse).then(function(t){return t.json()}).catch(function(r){throw new Error("Could not get "+e._getUrl(t)+". "+r)})},t.prototype.post=function(t,e){var r=this;return fetch(this._getUrl(t),{method:"post",headers:this.headers,credentials:"same-origin",body:JSON.stringify(e)}).then(this.onReceiveResponse).then(function(e){if(e.status<200||e.status>300)throw new Error("Could not post "+r._getUrl(t)+". "+e.statusText);return e.json()}).catch(function(e){throw new Error("Could not post "+r._getUrl(t)+". "+e)})},t.prototype.delete=function(t,e){var r=this;return fetch(this.hostname+"/"+t,{method:"delete",headers:this.headers,credentials:"same-origin",body:e?JSON.stringify(e):null}).then(this.onReceiveResponse).then(function(t){return t.json()}).catch(function(e){throw new Error("Could not delete "+r._getUrl(t)+". "+e)})},s(t,[{key:"hostname",get:function(){var t=this.uppy.getState().companion,e=this.opts.serverUrl;return(t&&t[e]?t[e]:e).replace(/\/$/,"")}},{key:"defaultHeaders",get:function(){return{Accept:"application/json","Content-Type":"application/json"}}},{key:"headers",get:function(){return i({},this.defaultHeaders,this.opts.serverHeaders||{})}}]),t}(),l=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),p=function(t){return t.split("-").map(function(t){return t.charAt(0).toUpperCase()+t.slice(1)}).join(" ")},c=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.provider=n.provider,o.id=o.provider,o.authProvider=n.authProvider||o.provider,o.name=o.opts.name||p(o.id),o.tokenKey="companion-"+o.id+"-auth-token",o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.setAuthToken=function(t){localStorage.setItem(this.tokenKey,t)},e.prototype.checkAuth=function(){return this.get(this.id+"/authorized").then(function(t){return t.authenticated})},e.prototype.authUrl=function(){return this.hostname+"/"+this.id+"/connect"},e.prototype.fileUrl=function(t){return this.hostname+"/"+this.id+"/get/"+t},e.prototype.list=function(t){return this.get(this.id+"/list/"+(t||""))},e.prototype.logout=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:location.href;return this.get(this.id+"/logout?redirect="+e).then(function(e){return localStorage.removeItem(t.tokenKey),e})},e.initPlugin=function(t,e,r){if(t.type="acquirer",t.files=[],r&&(t.opts=l({},r,e)),e.serverPattern){var n=e.serverPattern;if(!("string"==typeof n||Array.isArray(n)||n instanceof RegExp))throw new TypeError(t.id+': the option "serverPattern" must be one of string, Array, RegExp');t.opts.serverPattern=n}else/^(?!https?:\/\/).*$/.test(e.serverUrl)?t.opts.serverPattern=location.protocol+"//"+e.serverUrl.replace(/^\/\//,""):t.opts.serverPattern=e.serverUrl},u(e,[{key:"defaultHeaders",get:function(){return l({},t.prototype.defaultHeaders,{"uppy-auth-token":localStorage.getItem(this.tokenKey)})}}]),e}(a),h=function(){var t={},e=t._fns={};return t.emit=function(t,r,n,o,i,s,a){var l=function(t){for(var r=e[t]?e[t]:[],n=t.indexOf(":"),o=-1===n?[t]:[t.substring(0,n),t.substring(n+1)],i=Object.keys(e),s=0,a=i.length;s<a;s++){var l=i[s];if("*"===l&&(r=r.concat(e[l])),2===o.length&&o[0]===l){r=r.concat(e[l]);break}}return r}(t);l.length&&function(t,e,r){for(var n=0,o=e.length;n<o&&e[n];n++)e[n].event=t,e[n].apply(e[n],r)}(t,l,[r,n,o,i,s,a])},t.on=function(t,r){e[t]||(e[t]=[]),e[t].push(r)},t.once=function(e,r){this.on(e,function n(){r.apply(this,arguments),t.off(e,n)})},t.off=function(t,e){var r=[];if(t&&e)for(var n=this._fns[t],o=0,i=n?n.length:0;o<i;o++)n[o]!==e&&r.push(n[o]);r.length?this._fns[t]=r:delete this._fns[t]},t},d={RequestClient:a,Provider:c,Socket:function(){function t(e){var r=this;!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.queued=[],this.isOpen=!1,this.socket=new WebSocket(e.target),this.emitter=h(),this.socket.onopen=function(t){for(r.isOpen=!0;r.queued.length>0&&r.isOpen;){var e=r.queued[0];r.send(e.action,e.payload),r.queued=r.queued.slice(1)}},this.socket.onclose=function(t){r.isOpen=!1},this._handleMessage=this._handleMessage.bind(this),this.socket.onmessage=this._handleMessage,this.close=this.close.bind(this),this.emit=this.emit.bind(this),this.on=this.on.bind(this),this.once=this.once.bind(this),this.send=this.send.bind(this)}return t.prototype.close=function(){return this.socket.close()},t.prototype.send=function(t,e){this.isOpen?this.socket.send(JSON.stringify({action:t,payload:e})):this.queued.push({action:t,payload:e})},t.prototype.on=function(t,e){this.emitter.on(t,e)},t.prototype.emit=function(t,e){this.emitter.emit(t,e)},t.prototype.once=function(t,e){this.emitter.once(t,e)},t.prototype._handleMessage=function(t){try{var e=JSON.parse(t.data);this.emit(e.action,e.payload)}catch(t){console.log(t)}},t}()},f=function(t,e){var r="000000000"+t;return r.substr(r.length-e)},y="object"==typeof window?window:self,g=Object.keys(y).length,m=f(((navigator.mimeTypes?navigator.mimeTypes.length:0)+navigator.userAgent.length).toString(36)+g.toString(36),4),v=function(){return m},b={},w=0,S=4,P=36,_=Math.pow(P,S);function E(){return f((Math.random()*_<<0).toString(P),S)}function k(){return w=w<_?w:0,++w-1}function C(){return"c"+(new Date).getTime().toString(P)+f(k().toString(P),S)+v()+(E()+E())}C.slug=function(){var t=(new Date).getTime().toString(36),e=k().toString(36).slice(-4),r=v().slice(0,1)+v().slice(-1),n=E().slice(-2);return t.slice(-2)+e+r+n},C.isCuid=function(t){return"string"==typeof t&&!!t.startsWith("c")},C.isSlug=function(t){if("string"!=typeof t)return!1;var e=t.length;return e>=7&&e<=10},C.fingerprint=v,b=C;var T={};function U(t,e){this.text=t=t||"",this.hasWild=~t.indexOf("*"),this.separator=e,this.parts=t.split(e)}U.prototype.match=function(t){var e,r,n=!0,o=this.parts,i=o.length;if("string"==typeof t||t instanceof String)if(this.hasWild||this.text==t){for(r=(t||"").split(this.separator),e=0;n&&e<i;e++)"*"!==o[e]&&(n=e<r.length&&o[e]===r[e]);n=n&&r}else n=!1;else if("function"==typeof t.splice)for(n=[],e=t.length;e--;)this.match(t[e])&&(n[n.length]=t[e]);else if("object"==typeof t)for(var s in n={},t)this.match(s)&&(n[s]=t[s]);return n},T=function(t,e,r){var n=new U(t,r||/[\/\.]/);return void 0!==e?n.match(e):n};var O=/[\/\+\.]/,F=function(t){if("number"!=typeof t||isNaN(t))throw new TypeError("Expected a number, got "+typeof t);var e=t<0,r=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];if(e&&(t=-t),t<1)return(e?"-":"")+t+" B";var n=Math.min(Math.floor(Math.log(t)/Math.log(1e3)),r.length-1);t=Number(t/Math.pow(1e3,n));var o=r[n];return t>=10||t%1==0?(e?"-":"")+t.toFixed(0)+" "+o:(e?"-":"")+t.toFixed(1)+" "+o},A={exports:{}};!function(){"use strict";function t(t,e){var r,n,o,i,s=P;for(i=arguments.length;i-- >2;)S.push(arguments[i]);for(e&&null!=e.children&&(S.length||S.push(e.children),delete e.children);S.length;)if((n=S.pop())&&void 0!==n.pop)for(i=n.length;i--;)S.push(n[i]);else"boolean"==typeof n&&(n=null),(o="function"!=typeof t)&&(null==n?n="":"number"==typeof n?n=String(n):"string"!=typeof n&&(o=!1)),o&&r?s[s.length-1]+=n:s===P?s=[n]:s.push(n),r=o;var a=new b;return a.nodeName=t,a.children=s,a.attributes=null==e?void 0:e,a.key=null==e?void 0:e.key,void 0!==w.vnode&&w.vnode(a),a}function e(t,e){for(var r in e)t[r]=e[r];return t}function r(t){!t.__d&&(t.__d=!0)&&1==k.push(t)&&(w.debounceRendering||_)(n)}function n(){var t,e=k;for(k=[];t=e.pop();)t.__d&&g(t)}function o(t,e){return t.__n===e||t.nodeName.toLowerCase()===e.toLowerCase()}function i(t){var r=e({},t.attributes);r.children=t.children;var n=t.nodeName.defaultProps;if(void 0!==n)for(var o in n)void 0===r[o]&&(r[o]=n[o]);return r}function s(t){var e=t.parentNode;e&&e.removeChild(t)}function a(t,e,r,n,o){if("className"===e&&(e="class"),"key"===e);else if("ref"===e)r&&r(null),n&&n(t);else if("class"!==e||o)if("style"===e){if(n&&"string"!=typeof n&&"string"!=typeof r||(t.style.cssText=n||""),n&&"object"==typeof n){if("string"!=typeof r)for(var i in r)i in n||(t.style[i]="");for(var i in n)t.style[i]="number"==typeof n[i]&&!1===E.test(i)?n[i]+"px":n[i]}}else if("dangerouslySetInnerHTML"===e)n&&(t.innerHTML=n.__html||"");else if("o"==e[0]&&"n"==e[1]){var s=e!==(e=e.replace(/Capture$/,""));e=e.toLowerCase().substring(2),n?r||t.addEventListener(e,l,s):t.removeEventListener(e,l,s),(t.__l||(t.__l={}))[e]=n}else if("list"!==e&&"type"!==e&&!o&&e in t){try{t[e]=null==n?"":n}catch(t){}null!=n&&!1!==n||"spellcheck"==e||t.removeAttribute(e)}else{var a=o&&e!==(e=e.replace(/^xlink:?/,""));null==n||!1===n?a?t.removeAttributeNS("http://www.w3.org/1999/xlink",e.toLowerCase()):t.removeAttribute(e):"function"!=typeof n&&(a?t.setAttributeNS("http://www.w3.org/1999/xlink",e.toLowerCase(),n):t.setAttribute(e,n))}else t.className=n||""}function l(t){return this.__l[t.type](w.event&&w.event(t)||t)}function u(){for(var t;t=C.pop();)w.afterMount&&w.afterMount(t),t.componentDidMount&&t.componentDidMount()}function p(t,e,r,n,l,p){T++||(U=null!=l&&void 0!==l.ownerSVGElement,O=null!=t&&!("__preactattr_"in t));var h=function t(e,r,n,l,u){var p=e,h=U;if(null!=r&&"boolean"!=typeof r||(r=""),"string"==typeof r||"number"==typeof r)return e&&void 0!==e.splitText&&e.parentNode&&(!e._component||u)?e.nodeValue!=r&&(e.nodeValue=r):(p=document.createTextNode(r),e&&(e.parentNode&&e.parentNode.replaceChild(p,e),c(e,!0))),p.__preactattr_=!0,p;var f,g,v=r.nodeName;if("function"==typeof v)return function(t,e,r,n){for(var o=t&&t._component,s=o,a=t,l=o&&t._componentConstructor===e.nodeName,u=l,p=i(e);o&&!u&&(o=o.__u);)u=o.constructor===e.nodeName;return o&&u&&(!n||o._component)?(y(o,p,3,r,n),t=o.base):(s&&!l&&(m(s),t=a=null),o=d(e.nodeName,p,r),t&&!o.__b&&(o.__b=t,a=null),y(o,p,1,r,n),t=o.base,a&&t!==a&&(a._component=null,c(a,!1))),t}(e,r,n,l);if(U="svg"===v||"foreignObject"!==v&&U,v=String(v),(!e||!o(e,v))&&(f=v,(g=U?document.createElementNS("http://www.w3.org/2000/svg",f):document.createElement(f)).__n=f,p=g,e)){for(;e.firstChild;)p.appendChild(e.firstChild);e.parentNode&&e.parentNode.replaceChild(p,e),c(e,!0)}var b=p.firstChild,w=p.__preactattr_,S=r.children;if(null==w){w=p.__preactattr_={};for(var P=p.attributes,_=P.length;_--;)w[P[_].name]=P[_].value}return!O&&S&&1===S.length&&"string"==typeof S[0]&&null!=b&&void 0!==b.splitText&&null==b.nextSibling?b.nodeValue!=S[0]&&(b.nodeValue=S[0]):(S&&S.length||null!=b)&&function(e,r,n,i,a){var l,u,p,h,d,f,y,g,m=e.childNodes,v=[],b={},w=0,S=0,P=m.length,_=0,E=r?r.length:0;if(0!==P)for(var k=0;k<P;k++){var C=m[k],T=C.__preactattr_,U=E&&T?C._component?C._component.__k:T.key:null;null!=U?(w++,b[U]=C):(T||(void 0!==C.splitText?!a||C.nodeValue.trim():a))&&(v[_++]=C)}if(0!==E)for(var k=0;k<E;k++){h=r[k],d=null;var U=h.key;if(null!=U)w&&void 0!==b[U]&&(d=b[U],b[U]=void 0,w--);else if(S<_)for(l=S;l<_;l++)if(void 0!==v[l]&&(f=u=v[l],g=a,"string"==typeof(y=h)||"number"==typeof y?void 0!==f.splitText:"string"==typeof y.nodeName?!f._componentConstructor&&o(f,y.nodeName):g||f._componentConstructor===y.nodeName)){d=u,v[l]=void 0,l===_-1&&_--,l===S&&S++;break}d=t(d,h,n,i),p=m[k],d&&d!==e&&d!==p&&(null==p?e.appendChild(d):d===p.nextSibling?s(p):e.insertBefore(d,p))}if(w)for(var k in b)void 0!==b[k]&&c(b[k],!1);for(;S<=_;)void 0!==(d=v[_--])&&c(d,!1)}(p,S,n,l,O||null!=w.dangerouslySetInnerHTML),function(t,e,r){var n;for(n in r)e&&null!=e[n]||null==r[n]||a(t,n,r[n],r[n]=void 0,U);for(n in e)"children"===n||"innerHTML"===n||n in r&&e[n]===("value"===n||"checked"===n?t[n]:r[n])||a(t,n,r[n],r[n]=e[n],U)}(p,r.attributes,w),U=h,p}(t,e,r,n,p);return l&&h.parentNode!==l&&l.appendChild(h),--T||(O=!1,p||u()),h}function c(t,e){var r=t._component;r?m(r):(null!=t.__preactattr_&&t.__preactattr_.ref&&t.__preactattr_.ref(null),!1!==e&&null!=t.__preactattr_||s(t),h(t))}function h(t){for(t=t.lastChild;t;){var e=t.previousSibling;c(t,!0),t=e}}function d(t,e,r){var n,o=F.length;for(t.prototype&&t.prototype.render?(n=new t(e,r),v.call(n,e,r)):((n=new v(e,r)).constructor=t,n.render=f);o--;)if(F[o].constructor===t)return n.__b=F[o].__b,F.splice(o,1),n;return n}function f(t,e,r){return this.constructor(t,r)}function y(t,e,n,o,i){t.__x||(t.__x=!0,t.__r=e.ref,t.__k=e.key,delete e.ref,delete e.key,void 0===t.constructor.getDerivedStateFromProps&&(!t.base||i?t.componentWillMount&&t.componentWillMount():t.componentWillReceiveProps&&t.componentWillReceiveProps(e,o)),o&&o!==t.context&&(t.__c||(t.__c=t.context),t.context=o),t.__p||(t.__p=t.props),t.props=e,t.__x=!1,0!==n&&(1!==n&&!1===w.syncComponentUpdates&&t.base?r(t):g(t,1,i)),t.__r&&t.__r(t))}function g(t,r,n,o){if(!t.__x){var s,a,l,h=t.props,f=t.state,v=t.context,b=t.__p||h,S=t.__s||f,P=t.__c||v,_=t.base,E=t.__b,k=_||E,U=t._component,O=!1,F=P;if(t.constructor.getDerivedStateFromProps&&(f=e(e({},f),t.constructor.getDerivedStateFromProps(h,f)),t.state=f),_&&(t.props=b,t.state=S,t.context=P,2!==r&&t.shouldComponentUpdate&&!1===t.shouldComponentUpdate(h,f,v)?O=!0:t.componentWillUpdate&&t.componentWillUpdate(h,f,v),t.props=h,t.state=f,t.context=v),t.__p=t.__s=t.__c=t.__b=null,t.__d=!1,!O){s=t.render(h,f,v),t.getChildContext&&(v=e(e({},v),t.getChildContext())),_&&t.getSnapshotBeforeUpdate&&(F=t.getSnapshotBeforeUpdate(b,S));var A,x,D=s&&s.nodeName;if("function"==typeof D){var R=i(s);(a=U)&&a.constructor===D&&R.key==a.__k?y(a,R,1,v,!1):(A=a,t._component=a=d(D,R,v),a.__b=a.__b||E,a.__u=t,y(a,R,0,v,!1),g(a,1,n,!0)),x=a.base}else l=k,(A=U)&&(l=t._component=null),(k||1===r)&&(l&&(l._component=null),x=p(l,s,v,n||!_,k&&k.parentNode,!0));if(k&&x!==k&&a!==U){var I=k.parentNode;I&&x!==I&&(I.replaceChild(x,k),A||(k._component=null,c(k,!1)))}if(A&&m(A),t.base=x,x&&!o){for(var B=t,j=t;j=j.__u;)(B=j).base=x;x._component=B,x._componentConstructor=B.constructor}}for(!_||n?C.unshift(t):O||(t.componentDidUpdate&&t.componentDidUpdate(b,S,F),w.afterUpdate&&w.afterUpdate(t));t.__h.length;)t.__h.pop().call(t);T||o||u()}}function m(t){w.beforeUnmount&&w.beforeUnmount(t);var e=t.base;t.__x=!0,t.componentWillUnmount&&t.componentWillUnmount(),t.base=null;var r=t._component;r?m(r):e&&(e.__preactattr_&&e.__preactattr_.ref&&e.__preactattr_.ref(null),t.__b=e,s(e),F.push(t),h(e)),t.__r&&t.__r(null)}function v(t,e){this.__d=!0,this.context=e,this.props=t,this.state=this.state||{},this.__h=[]}var b=function(){},w={},S=[],P=[],_="function"==typeof Promise?Promise.resolve().then.bind(Promise.resolve()):setTimeout,E=/acit|ex(?:s|g|n|p|$)|rph|ows|mnc|ntw|ine[ch]|zoo|^ord/i,k=[],C=[],T=0,U=!1,O=!1,F=[];e(v.prototype,{setState:function(t,n){this.__s||(this.__s=this.state),this.state=e(e({},this.state),"function"==typeof t?t(this.state,this.props):t),n&&this.__h.push(n),r(this)},forceUpdate:function(t){t&&this.__h.push(t),g(this,2)},render:function(){}});var x={h:t,createElement:t,cloneElement:function(r,n){return t(r.nodeName,e(e({},r.attributes),n),arguments.length>2?[].slice.call(arguments,2):r.children)},Component:v,render:function(t,e,r){return p(r,t,{},!1,e,!1)},rerender:n,options:w};A.exports=x}(),A=A.exports;var x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D=function(t){return t&&"object"===(void 0===t?"undefined":x(t))&&t.nodeType===Node.ELEMENT_NODE},R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},I=function(t){return"string"==typeof t?document.querySelector(t):"object"===(void 0===t?"undefined":R(t))&&D(t)?t:void 0},B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},L=function(){function t(e,r){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.uppy=e,this.opts=r||{},this.update=this.update.bind(this),this.mount=this.mount.bind(this),this.install=this.install.bind(this),this.uninstall=this.uninstall.bind(this)}return t.prototype.getPluginState=function(){return this.uppy.getState().plugins[this.id]||{}},t.prototype.setPluginState=function(t){var e,r=this.uppy.getState().plugins;this.uppy.setState({plugins:j({},r,(e={},e[this.id]=j({},r[this.id],t),e))})},t.prototype.update=function(t){void 0!==this.el&&this._updateUI&&this._updateUI(t)},t.prototype.mount=function(e,r){var n,o,i,s=this,a=r.id,l=I(e);if(l)return this.isTargetDOMEl=!0,this.rerender=function(t){s.uppy.getPlugin(s.id)&&(s.el=A.render(s.render(t),l,s.el))},this._updateUI=(n=this.rerender,o=null,i=null,function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return i=e,o||(o=Promise.resolve().then(function(){return o=null,n.apply(void 0,i)})),o}),this.uppy.log("Installing "+a+" to a DOM element"),this.opts.replaceTargetContent&&(l.innerHTML=""),this.el=A.render(this.render(this.uppy.getState()),l),this.el;var u=void 0;if("object"===(void 0===e?"undefined":B(e))&&e instanceof t)u=e;else if("function"==typeof e){var p=e;this.uppy.iteratePlugins(function(t){if(t instanceof p)return u=t,!1})}if(u){var c=u.id;return this.uppy.log("Installing "+a+" to "+c),this.el=u.addTarget(r),this.el}throw this.uppy.log("Not installing "+a),new Error("Invalid target option given to "+a)},t.prototype.render=function(t){throw new Error("Extend the render method to add your plugin to a DOM element")},t.prototype.addTarget=function(t){throw new Error("Extend the addTarget method to add your plugin to another plugin's target")},t.prototype.unmount=function(){this.isTargetDOMEl&&this.el&&this.el.parentNode&&this.el.parentNode.removeChild(this.el)},t.prototype.install=function(){},t.prototype.uninstall=function(){this.unmount()},t}(),M=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},N=function(){function t(){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.state={},this.callbacks=[]}return t.prototype.getState=function(){return this.state},t.prototype.setState=function(t){var e=M({},this.state),r=M({},this.state,t);this.state=r,this._publish(e,r,t)},t.prototype.subscribe=function(t){var e=this;return this.callbacks.push(t),function(){e.callbacks.splice(e.callbacks.indexOf(t),1)}},t.prototype._publish=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];this.callbacks.forEach(function(t){t.apply(void 0,e)})},t}(),z=function(){return new N},q=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},H=function(){function t(e){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this);var r={locale:{strings:{},pluralize:function(t){return 1===t?0:1}}};this.opts=q({},r,e),this.locale=q({},r.locale,e.locale)}return t.prototype.interpolate=function(t,e){var r=String.prototype,n=r.split,o=r.replace,i=/\$/g,s=[t];for(var a in e)if("_"!==a&&e.hasOwnProperty(a)){var l=e[a];"string"==typeof l&&(l=o.call(e[a],i,"$$$$")),s=u(s,new RegExp("%\\{"+a+"\\}","g"),l)}return s;function u(t,e,r){var o=[];return t.forEach(function(t){n.call(t,e).forEach(function(t,e,n){""!==t&&o.push(t),e<n.length-1&&o.push(r)})}),o}},t.prototype.translate=function(t,e){return this.translateArray(t,e).join("")},t.prototype.translateArray=function(t,e){if(e&&void 0!==e.smart_count){var r=this.locale.pluralize(e.smart_count);return this.interpolate(this.opts.locale.strings[t][r],e)}return this.interpolate(this.opts.locale.strings[t],e)},t}(),W=function(t){return["uppy",t.name?t.name.toLowerCase().replace(/[^A-Z0-9]/gi,""):"",t.type,t.data.size,t.data.lastModified].filter(function(t){return t}).join("-")},X=function(t){var e=/(?:\.([^.]+))?$/.exec(t)[1];return{name:t.replace("."+e,""),extension:e}},V={md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",gif:"image/gif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",doc:"application/msword",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel"},G=function(t){var e=t.name?X(t.name).extension:null;return t.isRemote?t.type?t.type:V[e]:t.type?t.type:e&&V[e]?V[e]:"application/octet-stream"};function K(t){return 2!==t.length?0+t:t}var Y,J=function(){var t=new Date;return K(t.getHours().toString())+":"+K(t.getMinutes().toString())+":"+K(t.getSeconds().toString())},$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Q=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Z=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),tt=function(){function t(e){var r=this;!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this);var n={strings:{youCanOnlyUploadX:{0:"You can only upload %{smart_count} file",1:"You can only upload %{smart_count} files"},youHaveToAtLeastSelectX:{0:"You have to select at least %{smart_count} file",1:"You have to select at least %{smart_count} files"},exceedsSize:"This file exceeds maximum allowed size of",youCanOnlyUploadFileTypes:"You can only upload:",companionError:"Connection with Companion failed",failedToUpload:"Failed to upload %{file}",noInternetConnection:"No Internet connection",connectedToInternet:"Connected to the Internet",noFilesFound:"You have no files or folders here",selectXFiles:{0:"Select %{smart_count} file",1:"Select %{smart_count} files"},cancel:"Cancel",logOut:"Log out"}},o={id:"uppy",autoProceed:!1,debug:!1,restrictions:{maxFileSize:null,maxNumberOfFiles:null,minNumberOfFiles:null,allowedFileTypes:null},meta:{},onBeforeFileAdded:function(t,e){return t},onBeforeUpload:function(t){return t},locale:n,store:z()};this.opts=Q({},o,e),this.opts.restrictions=Q({},o.restrictions,this.opts.restrictions),this.locale=Q({},n,this.opts.locale),this.locale.strings=Q({},n.strings,this.opts.locale.strings),this.translator=new H({locale:this.locale}),this.i18n=this.translator.translate.bind(this.translator),this.plugins={},this.getState=this.getState.bind(this),this.getPlugin=this.getPlugin.bind(this),this.setFileMeta=this.setFileMeta.bind(this),this.setFileState=this.setFileState.bind(this),this.log=this.log.bind(this),this.info=this.info.bind(this),this.hideInfo=this.hideInfo.bind(this),this.addFile=this.addFile.bind(this),this.removeFile=this.removeFile.bind(this),this.pauseResume=this.pauseResume.bind(this),this._calculateProgress=this._calculateProgress.bind(this),this.updateOnlineStatus=this.updateOnlineStatus.bind(this),this.resetProgress=this.resetProgress.bind(this),this.pauseAll=this.pauseAll.bind(this),this.resumeAll=this.resumeAll.bind(this),this.retryAll=this.retryAll.bind(this),this.cancelAll=this.cancelAll.bind(this),this.retryUpload=this.retryUpload.bind(this),this.upload=this.upload.bind(this),this.emitter=h(),this.on=this.on.bind(this),this.off=this.off.bind(this),this.once=this.emitter.once.bind(this.emitter),this.emit=this.emitter.emit.bind(this.emitter),this.preProcessors=[],this.uploaders=[],this.postProcessors=[],this.store=this.opts.store,this.setState({plugins:{},files:{},currentUploads:{},capabilities:{resumableUploads:!1},totalProgress:0,meta:Q({},this.opts.meta),info:{isHidden:!0,type:"info",message:""}}),this._storeUnsubscribe=this.store.subscribe(function(t,e,n){r.emit("state-update",t,e,n),r.updateAll(e)}),this.opts.debug&&"undefined"!=typeof window&&(window.uppyLog="",window[this.opts.id]=this),this._addListeners()}return t.prototype.on=function(t,e){return this.emitter.on(t,e),this},t.prototype.off=function(t,e){return this.emitter.off(t,e),this},t.prototype.updateAll=function(t){this.iteratePlugins(function(e){e.update(t)})},t.prototype.setState=function(t){this.store.setState(t)},t.prototype.getState=function(){return this.store.getState()},t.prototype.setFileState=function(t,e){var r;if(!this.getState().files[t])throw new Error("Can\u2019t set state for "+t+" (the file could have been removed)");this.setState({files:Q({},this.getState().files,(r={},r[t]=Q({},this.getState().files[t],e),r))})},t.prototype.resetProgress=function(){var t={percentage:0,bytesUploaded:0,uploadComplete:!1,uploadStarted:!1},e=Q({},this.getState().files),r={};Object.keys(e).forEach(function(n){var o=Q({},e[n]);o.progress=Q({},o.progress,t),r[n]=o}),this.setState({files:r,totalProgress:0}),this.emit("reset-progress")},t.prototype.addPreProcessor=function(t){this.preProcessors.push(t)},t.prototype.removePreProcessor=function(t){var e=this.preProcessors.indexOf(t);-1!==e&&this.preProcessors.splice(e,1)},t.prototype.addPostProcessor=function(t){this.postProcessors.push(t)},t.prototype.removePostProcessor=function(t){var e=this.postProcessors.indexOf(t);-1!==e&&this.postProcessors.splice(e,1)},t.prototype.addUploader=function(t){this.uploaders.push(t)},t.prototype.removeUploader=function(t){var e=this.uploaders.indexOf(t);-1!==e&&this.uploaders.splice(e,1)},t.prototype.setMeta=function(t){var e=Q({},this.getState().meta,t),r=Q({},this.getState().files);Object.keys(r).forEach(function(e){r[e]=Q({},r[e],{meta:Q({},r[e].meta,t)})}),this.log("Adding metadata:"),this.log(t),this.setState({meta:e,files:r})},t.prototype.setFileMeta=function(t,e){var r=Q({},this.getState().files);if(r[t]){var n=Q({},r[t].meta,e);r[t]=Q({},r[t],{meta:n}),this.setState({files:r})}else this.log("Was trying to set metadata for a file that\u2019s not with us anymore: ",t)},t.prototype.getFile=function(t){return this.getState().files[t]},t.prototype.getFiles=function(){var t=this.getState().files;return Object.keys(t).map(function(e){return t[e]})},t.prototype._checkMinNumberOfFiles=function(t){var e=this.opts.restrictions.minNumberOfFiles;if(Object.keys(t).length<e)throw new Error(""+this.i18n("youHaveToAtLeastSelectX",{smart_count:e}))},t.prototype._checkRestrictions=function(t){var e=this.opts.restrictions,r=e.maxFileSize,n=e.maxNumberOfFiles,o=e.allowedFileTypes;if(n&&Object.keys(this.getState().files).length+1>n)throw new Error(""+this.i18n("youCanOnlyUploadX",{smart_count:n}));if(o&&!(o.filter(function(e){return e.indexOf("/")>-1?!!t.type&&function(t,e){function r(e){var r=T(e,t,O);return r&&r.length>=2}return e?r(e.split(";")[0]):r}(t.type,e):"."===e[0]&&t.extension===e.substr(1)?t.extension:void 0}).length>0)){var i=o.join(", ");throw new Error(this.i18n("youCanOnlyUploadFileTypes")+" "+i)}if(r&&t.data.size>r)throw new Error(this.i18n("exceedsSize")+" "+F(r))},t.prototype.addFile=function(t){var e,r=this,n=this.getState().files,o=this.opts.onBeforeFileAdded(t,n);if(!1!==o){if("object"===(void 0===o?"undefined":$(o))&&o){if(o.then)throw new TypeError("onBeforeFileAdded() returned a Promise, but this is no longer supported. It must be synchronous.");t=o}var i,s=G(t);i=t.name?t.name:"image"===s.split("/")[0]?s.split("/")[0]+"."+s.split("/")[1]:"noname";var a=X(i).extension,l=t.isRemote||!1,u=W(t),p=t.meta||{};p.name=i,p.type=s;var c={source:t.source||"",id:u,name:i,extension:a||"",meta:Q({},this.getState().meta,p),type:s,data:t.data,progress:{percentage:0,bytesUploaded:0,bytesTotal:t.data.size||0,uploadComplete:!1,uploadStarted:!1},size:t.data.size||0,isRemote:l,remote:t.remote||"",preview:t.preview};try{this._checkRestrictions(c)}catch(t){!function(t){var e="object"===(void 0===t?"undefined":$(t))?t:new Error(t);throw r.log(e.message),r.info(e.message,"error",5e3),e}(t)}this.setState({files:Q({},n,(e={},e[u]=c,e))}),this.emit("file-added",c),this.log("Added file: "+i+", "+u+", mime type: "+s),this.opts.autoProceed&&!this.scheduledAutoProceed&&(this.scheduledAutoProceed=setTimeout(function(){r.scheduledAutoProceed=null,r.upload().catch(function(t){console.error(t.stack||t.message||t)})},4))}else this.log("Not adding file because onBeforeFileAdded returned false")},t.prototype.removeFile=function(t){var e=this,r=this.getState(),n=r.files,o=r.currentUploads,i=Q({},n),s=i[t];delete i[t];var a=Q({},o),l=[];Object.keys(a).forEach(function(e){var r=o[e].fileIDs.filter(function(e){return e!==t});0!==r.length?a[e]=Q({},o[e],{fileIDs:r}):l.push(e)}),this.setState({currentUploads:a,files:i}),l.forEach(function(t){e._removeUpload(t)}),this._calculateTotalProgress(),this.emit("file-removed",s),this.log("File removed: "+s.id),s.preview&&0===s.preview.indexOf("blob:")&&URL.revokeObjectURL(s.preview),this.log("Removed file: "+t)},t.prototype.pauseResume=function(t){if(!this.getFile(t).uploadComplete){var e=!this.getFile(t).isPaused;return this.setFileState(t,{isPaused:e}),this.emit("upload-pause",t,e),e}},t.prototype.pauseAll=function(){var t=Q({},this.getState().files);Object.keys(t).filter(function(e){return!t[e].progress.uploadComplete&&t[e].progress.uploadStarted}).forEach(function(e){var r=Q({},t[e],{isPaused:!0});t[e]=r}),this.setState({files:t}),this.emit("pause-all")},t.prototype.resumeAll=function(){var t=Q({},this.getState().files);Object.keys(t).filter(function(e){return!t[e].progress.uploadComplete&&t[e].progress.uploadStarted}).forEach(function(e){var r=Q({},t[e],{isPaused:!1,error:null});t[e]=r}),this.setState({files:t}),this.emit("resume-all")},t.prototype.retryAll=function(){var t=Q({},this.getState().files),e=Object.keys(t).filter(function(e){return t[e].error});e.forEach(function(e){var r=Q({},t[e],{isPaused:!1,error:null});t[e]=r}),this.setState({files:t,error:null}),this.emit("retry-all",e);var r=this._createUpload(e);return this._runUpload(r)},t.prototype.cancelAll=function(){var t=this;this.emit("cancel-all");var e=this.getState().currentUploads;Object.keys(e).forEach(function(e){t._removeUpload(e)}),this.setState({files:{},totalProgress:0,error:null})},t.prototype.retryUpload=function(t){var e=Q({},this.getState().files),r=Q({},e[t],{error:null,isPaused:!1});e[t]=r,this.setState({files:e}),this.emit("upload-retry",t);var n=this._createUpload([t]);return this._runUpload(n)},t.prototype.reset=function(){this.cancelAll()},t.prototype._calculateProgress=function(t,e){this.getFile(t.id)?(this.setFileState(t.id,{progress:Q({},this.getFile(t.id).progress,{bytesUploaded:e.bytesUploaded,bytesTotal:e.bytesTotal,percentage:Math.floor((e.bytesUploaded/e.bytesTotal*100).toFixed(2))})}),this._calculateTotalProgress()):this.log("Not setting progress for a file that has been removed: "+t.id)},t.prototype._calculateTotalProgress=function(){var t=Q({},this.getState().files),e=Object.keys(t).filter(function(e){return t[e].progress.uploadStarted}),r=100*e.length,n=0;e.forEach(function(e){n+=t[e].progress.percentage});var o=0===r?0:Math.floor((100*n/r).toFixed(2));this.setState({totalProgress:o})},t.prototype._addListeners=function(){var t=this;this.on("error",function(e){t.setState({error:e.message})}),this.on("upload-error",function(e,r){t.setFileState(e.id,{error:r.message}),t.setState({error:r.message});var n=t.i18n("failedToUpload",{file:e.name});"object"===(void 0===r?"undefined":$(r))&&r.message&&(n={message:n,details:r.message}),t.info(n,"error",5e3)}),this.on("upload",function(){t.setState({error:null})}),this.on("upload-started",function(e,r){t.getFile(e.id)?t.setFileState(e.id,{progress:{uploadStarted:Date.now(),uploadComplete:!1,percentage:0,bytesUploaded:0,bytesTotal:e.size}}):t.log("Not setting progress for a file that has been removed: "+e.id)}),this.on("upload-progress",this._calculateProgress),this.on("upload-success",function(e,r,n){var o=t.getFile(e.id).progress;t.setFileState(e.id,{progress:Q({},o,{uploadComplete:!0,percentage:100,bytesUploaded:o.bytesTotal}),uploadURL:n,isPaused:!1}),t._calculateTotalProgress()}),this.on("preprocess-progress",function(e,r){t.getFile(e.id)?t.setFileState(e.id,{progress:Q({},t.getFile(e.id).progress,{preprocess:r})}):t.log("Not setting progress for a file that has been removed: "+e.id)}),this.on("preprocess-complete",function(e){if(t.getFile(e.id)){var r=Q({},t.getState().files);r[e.id]=Q({},r[e.id],{progress:Q({},r[e.id].progress)}),delete r[e.id].progress.preprocess,t.setState({files:r})}else t.log("Not setting progress for a file that has been removed: "+e.id)}),this.on("postprocess-progress",function(e,r){t.getFile(e.id)?t.setFileState(e.id,{progress:Q({},t.getState().files[e.id].progress,{postprocess:r})}):t.log("Not setting progress for a file that has been removed: "+e.id)}),this.on("postprocess-complete",function(e){if(t.getFile(e.id)){var r=Q({},t.getState().files);r[e.id]=Q({},r[e.id],{progress:Q({},r[e.id].progress)}),delete r[e.id].progress.postprocess,t.setState({files:r})}else t.log("Not setting progress for a file that has been removed: "+e.id)}),this.on("restored",function(){t._calculateTotalProgress()}),"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("online",function(){return t.updateOnlineStatus()}),window.addEventListener("offline",function(){return t.updateOnlineStatus()}),setTimeout(function(){return t.updateOnlineStatus()},3e3))},t.prototype.updateOnlineStatus=function(){void 0===window.navigator.onLine||window.navigator.onLine?(this.emit("is-online"),this.wasOffline&&(this.emit("back-online"),this.info(this.i18n("connectedToInternet"),"success",3e3),this.wasOffline=!1)):(this.emit("is-offline"),this.info(this.i18n("noInternetConnection"),"error",0),this.wasOffline=!0)},t.prototype.getID=function(){return this.opts.id},t.prototype.use=function(t,e){if("function"!=typeof t){var r="Expected a plugin class, but got "+(null===t?"null":void 0===t?"undefined":$(t))+". Please verify that the plugin was imported and spelled correctly.";throw new TypeError(r)}var n=new t(this,e),o=n.id;if(this.plugins[n.type]=this.plugins[n.type]||[],!o)throw new Error("Your plugin must have an id");if(!n.type)throw new Error("Your plugin must have a type");var i=this.getPlugin(o);if(i){var s="Already found a plugin named '"+i.id+"'. Tried to use: '"+o+"'.\nUppy plugins must have unique 'id' options. See https://uppy.io/docs/plugins/#id.";throw new Error(s)}return this.plugins[n.type].push(n),n.install(),this},t.prototype.getPlugin=function(t){var e=null;return this.iteratePlugins(function(r){if(r.id===t)return e=r,!1}),e},t.prototype.iteratePlugins=function(t){var e=this;Object.keys(this.plugins).forEach(function(r){e.plugins[r].forEach(t)})},t.prototype.removePlugin=function(t){this.log("Removing plugin "+t.id),this.emit("plugin-remove",t),t.uninstall&&t.uninstall();var e=this.plugins[t.type].slice(),r=e.indexOf(t);-1!==r&&(e.splice(r,1),this.plugins[t.type]=e);var n=this.getState();delete n.plugins[t.id],this.setState(n)},t.prototype.close=function(){var t=this;this.log("Closing Uppy instance "+this.opts.id+": removing all files and uninstalling plugins"),this.reset(),this._storeUnsubscribe(),this.iteratePlugins(function(e){t.removePlugin(e)})},t.prototype.info=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3,n="object"===(void 0===t?"undefined":$(t));this.setState({info:{isHidden:!1,type:e,message:n?t.message:t,details:n?t.details:null}}),this.emit("info-visible"),clearTimeout(this.infoTimeoutID),this.infoTimeoutID=0!==r?setTimeout(this.hideInfo,r):void 0},t.prototype.hideInfo=function(){var t=Q({},this.getState().info,{isHidden:!0});this.setState({info:t}),this.emit("info-hidden")},t.prototype.log=function(t,e){if(this.opts.debug){var r="[Uppy] ["+J()+"] "+t;window.uppyLog=window.uppyLog+"\nDEBUG LOG: "+t,"error"!==e?"warning"!==e?t===""+t?console.log(r):(r="[Uppy] ["+J()+"]",console.log(r),console.dir(t)):console.warn(r):console.error(r)}},t.prototype.run=function(){return this.log("Calling run() is no longer necessary.","warning"),this},t.prototype.restore=function(t){return this.log('Core: attempting to restore upload "'+t+'"'),this.getState().currentUploads[t]?this._runUpload(t):(this._removeUpload(t),Promise.reject(new Error("Nonexistent upload")))},t.prototype._createUpload=function(t){var e,r=b();return this.emit("upload",{id:r,fileIDs:t}),this.setState({currentUploads:Q({},this.getState().currentUploads,(e={},e[r]={fileIDs:t,step:0,result:{}},e))}),r},t.prototype._getUpload=function(t){return this.getState().currentUploads[t]},t.prototype.addResultData=function(t,e){var r;if(this._getUpload(t)){var n=this.getState().currentUploads,o=Q({},n[t],{result:Q({},n[t].result,e)});this.setState({currentUploads:Q({},n,(r={},r[t]=o,r))})}else this.log("Not setting result for an upload that has been removed: "+t)},t.prototype._removeUpload=function(t){var e=Q({},this.getState().currentUploads);delete e[t],this.setState({currentUploads:e})},t.prototype._runUpload=function(t){var e=this,r=this.getState().currentUploads[t],n=r.fileIDs,o=r.step,i=[].concat(this.preProcessors,this.uploaders,this.postProcessors),s=Promise.resolve();return i.forEach(function(r,i){i<o||(s=s.then(function(){var o,s=e.getState().currentUploads,a=Q({},s[t],{step:i});return e.setState({currentUploads:Q({},s,(o={},o[t]=a,o))}),r(n,t)}).then(function(t){return null}))}),s.catch(function(r){e.emit("error",r,t),e._removeUpload(t)}),s.then(function(){var r=n.map(function(t){return e.getFile(t)}),o=r.filter(function(t){return t&&!t.error}),i=r.filter(function(t){return t&&t.error});e.addResultData(t,{successful:o,failed:i,uploadID:t});var s=e.getState().currentUploads;if(s[t]){var a=s[t].result;return e.emit("complete",a),e._removeUpload(t),a}e.log("Not setting result for an upload that has been removed: "+t)})},t.prototype.upload=function(){var t=this;this.plugins.uploader||this.log("No uploader type plugins are used","warning");var e=this.getState().files,r=this.opts.onBeforeUpload(e);if(!1===r)return Promise.reject(new Error("Not starting the upload because onBeforeUpload returned false"));if(r&&"object"===(void 0===r?"undefined":$(r))){if(r.then)throw new TypeError("onBeforeUpload() returned a Promise, but this is no longer supported. It must be synchronous.");e=r}return Promise.resolve().then(function(){return t._checkMinNumberOfFiles(e)}).then(function(){var r=t.getState().currentUploads,n=Object.keys(r).reduce(function(t,e){return t.concat(r[e].fileIDs)},[]),o=[];Object.keys(e).forEach(function(e){var r=t.getFile(e);r.progress.uploadStarted||-1!==n.indexOf(e)||o.push(r.id)});var i=t._createUpload(o);return t._runUpload(i)}).catch(function(e){var r="object"===(void 0===e?"undefined":$(e))?e.message:e,n="object"===(void 0===e?"undefined":$(e))?e.details:null;return t.log(r+" "+n),t.info({message:r,details:n},"error",4e3),Promise.reject("object"===(void 0===e?"undefined":$(e))?e:new Error(e))})},Z(t,[{key:"state",get:function(){return this.getState()}}]),t}();(Y=function(t){return new tt(t)}).Uppy=tt,Y.Plugin=L;var et={};(function(t){var e="Expected a function",r=NaN,n="[object Symbol]",o=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,a=/^0o[0-7]+$/i,l=parseInt,u="object"==typeof t&&t&&t.Object===Object&&t,p="object"==typeof self&&self&&self.Object===Object&&self,c=u||p||Function("return this")(),h=Object.prototype.toString,d=Math.max,f=Math.min,y=function(){return c.Date.now()};function g(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function m(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&h.call(t)==n}(t))return r;if(g(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=g(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(o,"");var u=s.test(t);return u||a.test(t)?l(t.slice(2),u?2:8):i.test(t)?r:+t}et=function(t,r,n){var o=!0,i=!0;if("function"!=typeof t)throw new TypeError(e);return g(n)&&(o="leading"in n?!!n.leading:o,i="trailing"in n?!!n.trailing:i),function(t,r,n){var o,i,s,a,l,u,p=0,c=!1,h=!1,v=!0;if("function"!=typeof t)throw new TypeError(e);function b(e){var r=o,n=i;return o=i=void 0,p=e,a=t.apply(n,r)}function w(t){var e=t-u;return void 0===u||e>=r||e<0||h&&t-p>=s}function S(){var t=y();if(w(t))return P(t);l=setTimeout(S,function(t){var e=r-(t-u);return h?f(e,s-(t-p)):e}(t))}function P(t){return l=void 0,v&&o?b(t):(o=i=void 0,a)}function _(){var t=y(),e=w(t);if(o=arguments,i=this,u=t,e){if(void 0===l)return function(t){return p=t,l=setTimeout(S,r),c?b(t):a}(u);if(h)return l=setTimeout(S,r),b(u)}return void 0===l&&(l=setTimeout(S,r)),a}return r=m(r)||0,g(n)&&(c=!!n.leading,s=(h="maxWait"in n)?d(m(n.maxWait)||0,r):s,v="trailing"in n?!!n.trailing:v),_.cancel=function(){void 0!==l&&clearTimeout(l),p=0,o=u=i=l=void 0},_.flush=function(){return void 0===l?a:P(y())},_}(t,r,{leading:o,maxWait:r,trailing:i})}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var rt=et(function(t,e,r){var n=e.progress,o=e.bytesUploaded,i=e.bytesTotal;n&&(t.uppy.log("Upload progress: "+n),t.uppy.emit("upload-progress",r,{uploader:t,bytesUploaded:o,bytesTotal:i}))},300,{leading:!0,trailing:!0}),nt=function(t){var e=/^(?:https?:\/\/|\/\/)?(?:[^@\n]+@)?(?:www\.)?([^\n]+)/.exec(t)[1];return("https:"===location.protocol?"wss":"ws")+"://"+e},ot=function(t){var e=0,r=[];return function(o){return function(){for(var i=arguments.length,s=Array(i),a=0;a<i;a++)s[a]=arguments[a];var l=function(){e++;var t=o.apply(void 0,s);return t.then(n,n),t};return e>=t?new Promise(function(t,e){r.push(function(){l().then(t,e)})}):l()}};function n(){e--;var t=r.shift();t&&t()}},it=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},st=d.Socket,at=d.RequestClient;function lt(t){var e=[];return{on:function(r,n){return e.push([r,n]),t.on(r,n)},remove:function(){e.forEach(function(e){var r=e[0],n=e[1];t.off(r,n)})}}}function ut(t){if(t&&t.error){var e=new Error(t.message);throw it(e,t.error),e}return t}var pt,ct=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.type="uploader",o.id="AwsS3Multipart",o.title="AWS S3 Multipart",o.client=new at(r,n);var i={timeout:3e4,limit:0,createMultipartUpload:o.createMultipartUpload.bind(o),listParts:o.listParts.bind(o),prepareUploadPart:o.prepareUploadPart.bind(o),abortMultipartUpload:o.abortMultipartUpload.bind(o),completeMultipartUpload:o.completeMultipartUpload.bind(o)};return o.opts=it({},i,n),o.upload=o.upload.bind(o),"number"==typeof o.opts.limit&&0!==o.opts.limit?o.limitRequests=ot(o.opts.limit):o.limitRequests=function(t){return t},o.uploaders=Object.create(null),o.uploaderEvents=Object.create(null),o.uploaderSockets=Object.create(null),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.resetUploaderReferences=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.uploaders[t]&&(this.uploaders[t].abort({really:e.abort||!1}),this.uploaders[t]=null),this.uploaderEvents[t]&&(this.uploaderEvents[t].remove(),this.uploaderEvents[t]=null),this.uploaderSockets[t]&&(this.uploaderSockets[t].close(),this.uploaderSockets[t]=null)},e.prototype.assertHost=function(){if(!this.opts.serverUrl)throw new Error("Expected a `serverUrl` option containing a Companion address.")},e.prototype.createMultipartUpload=function(t){return this.assertHost(),this.client.post("s3/multipart",{filename:t.name,type:t.type}).then(ut)},e.prototype.listParts=function(t,e){var r=e.key,n=e.uploadId;this.assertHost();var o=encodeURIComponent(r);return this.client.get("s3/multipart/"+n+"?key="+o).then(ut)},e.prototype.prepareUploadPart=function(t,e){var r=e.key,n=e.uploadId,o=e.number;this.assertHost();var i=encodeURIComponent(r);return this.client.get("s3/multipart/"+n+"/"+o+"?key="+i).then(ut)},e.prototype.completeMultipartUpload=function(t,e){var r=e.key,n=e.uploadId,o=e.parts;this.assertHost();var i=encodeURIComponent(r),s=encodeURIComponent(n);return this.client.post("s3/multipart/"+s+"/complete?key="+i,{parts:o}).then(ut)},e.prototype.abortMultipartUpload=function(t,e){var r=e.key,n=e.uploadId;this.assertHost();var o=encodeURIComponent(r),i=encodeURIComponent(n);return this.client.delete("s3/multipart/"+i+"?key="+o).then(ut)},e.prototype.uploadFile=function(t){var e=this;return new Promise(function(r,n){var i=new o(t.data,it({createMultipartUpload:e.limitRequests(e.opts.createMultipartUpload.bind(e,t)),listParts:e.limitRequests(e.opts.listParts.bind(e,t)),prepareUploadPart:e.opts.prepareUploadPart.bind(e,t),completeMultipartUpload:e.limitRequests(e.opts.completeMultipartUpload.bind(e,t)),abortMultipartUpload:e.limitRequests(e.opts.abortMultipartUpload.bind(e,t)),limit:e.opts.limit||5,onStart:function(r){var n=e.uppy.getFile(t.id);e.uppy.setFileState(t.id,{s3Multipart:it({},n.s3Multipart,{key:r.key,uploadId:r.uploadId,parts:[]})})},onProgress:function(r,n){e.uppy.emit("upload-progress",t,{uploader:e,bytesUploaded:r,bytesTotal:n})},onError:function(r){e.uppy.log(r),e.uppy.emit("upload-error",t,r),r.message="Failed because: "+r.message,e.resetUploaderReferences(t.id),n(r)},onSuccess:function(n){e.uppy.emit("upload-success",t,i,n.location),n.location&&e.uppy.log("Download "+i.file.name+" from "+n.location),e.resetUploaderReferences(t.id),r(i)},onPartComplete:function(r){var n=e.uppy.getFile(t.id);n&&(e.uppy.setFileState(t.id,{s3Multipart:it({},n.s3Multipart,{parts:[].concat(n.s3Multipart.parts,[r])})}),e.uppy.emit("s3-multipart:part-uploaded",n,r))}},t.s3Multipart));e.uploaders[t.id]=i,e.uploaderEvents[t.id]=lt(e.uppy),e.onFileRemove(t.id,function(n){e.resetUploaderReferences(t.id,{abort:!0}),r("upload "+n.id+" was removed")}),e.onFilePause(t.id,function(t){t?i.pause():i.start()}),e.onPauseAll(t.id,function(){i.pause()}),e.onResumeAll(t.id,function(){i.start()}),t.isPaused||i.start(),t.isRestored||e.uppy.emit("upload-started",t,i)})},e.prototype.uploadRemote=function(t){var e=this;return this.resetUploaderReferences(t.id),new Promise(function(r,n){if(t.serverToken)return e.connectToServerSocket(t).then(function(){return r()}).catch(n);e.uppy.emit("upload-started",t),fetch(t.remote.url,{method:"post",credentials:"include",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(it({},t.remote.body,{protocol:"s3-multipart",size:t.data.size,metadata:t.meta}))}).then(function(r){return r.status<200||r.status>300?n(r.statusText):r.json().then(function(r){return e.uppy.setFileState(t.id,{serverToken:r.token}),e.uppy.getFile(t.id)})}).then(function(t){return e.connectToServerSocket(t)}).then(function(){r()}).catch(function(t){n(new Error(t))})})},e.prototype.connectToServerSocket=function(t){var e=this;return new Promise(function(r,n){var o=t.serverToken,i=nt(t.remote.serverUrl),s=new st({target:i+"/api/"+o});e.uploaderSockets[s]=s,e.uploaderEvents[t.id]=lt(e.uppy),e.onFileRemove(t.id,function(n){e.resetUploaderReferences(t.id,{abort:!0}),r("upload "+t.id+" was removed")}),e.onFilePause(t.id,function(t){s.send(t?"pause":"resume",{})}),e.onPauseAll(t.id,function(){return s.send("pause",{})}),e.onResumeAll(t.id,function(){t.error&&s.send("pause",{}),s.send("resume",{})}),e.onRetry(t.id,function(){s.send("pause",{}),s.send("resume",{})}),e.onRetryAll(t.id,function(){s.send("pause",{}),s.send("resume",{})}),t.isPaused&&s.send("pause",{}),s.on("progress",function(r){return rt(e,r,t)}),s.on("error",function(r){e.uppy.emit("upload-error",t,new Error(r.error)),n(new Error(r.error))}),s.on("success",function(n){e.uppy.emit("upload-success",t,n,n.url),r()})})},e.prototype.upload=function(t){var e=this;if(0===t.length)return Promise.resolve();var r=t.map(function(t){var r=e.uppy.getFile(t);return r.isRemote?e.uploadRemote(r):e.uploadFile(r)});return Promise.all(r)},e.prototype.onFileRemove=function(t,e){this.uploaderEvents[t].on("file-removed",function(r){t===r.id&&e(r.id)})},e.prototype.onFilePause=function(t,e){this.uploaderEvents[t].on("upload-pause",function(r,n){t===r&&e(n)})},e.prototype.onRetry=function(t,e){this.uploaderEvents[t].on("upload-retry",function(r){t===r&&e()})},e.prototype.onRetryAll=function(t,e){var r=this;this.uploaderEvents[t].on("retry-all",function(n){r.uppy.getFile(t)&&e()})},e.prototype.onPauseAll=function(t,e){var r=this;this.uploaderEvents[t].on("pause-all",function(){r.uppy.getFile(t)&&e()})},e.prototype.onResumeAll=function(t,e){var r=this;this.uploaderEvents[t].on("resume-all",function(){r.uppy.getFile(t)&&e()})},e.prototype.install=function(){var t=this,e=this.uppy.getState().capabilities;this.uppy.setState({capabilities:it({},e,{resumableUploads:!0})}),this.uppy.addUploader(this.upload),this.uppy.on("cancel-all",function(){t.uppy.getFiles().forEach(function(e){t.resetUploaderReferences(e.id,{abort:!0})})})},e.prototype.uninstall=function(){this.uppy.setState({capabilities:it({},this.uppy.getState().capabilities,{resumableUploads:!1})}),this.uppy.removeUploader(this.upload)},e}(Y.Plugin),ht={};pt=function(){return function(){var t=arguments.length;if(0===t)throw new Error("resolveUrl requires at least one argument; got none.");var e=document.createElement("base");if(e.href=arguments[0],1===t)return e.href;var r=document.getElementsByTagName("head")[0];r.insertBefore(e,r.firstChild);for(var n,o=document.createElement("a"),i=1;i<t;i++)o.href=arguments[i],n=o.href,e.href=n;return r.removeChild(e),n}},"object"==typeof ht?ht=pt():this.resolveUrl=pt();var dt=function(t){var e=[],r=[];function n(t){e.push(t)}function o(t){r.push(t)}return Promise.all(t.map(function(t){return t.then(n,o)})).then(function(){return{successful:e,failed:r}})},ft=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yt=d.Provider,gt=d.Socket;function mt(t,e){return e||(e=new Error("Upload error")),"string"==typeof e&&(e=new Error(e)),e instanceof Error||(e=ft(new Error("Upload error"),{data:e})),e.request=t,e}var vt=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.type="uploader",o.id="XHRUpload",o.title="XHRUpload";var i={strings:{timedOut:"Upload stalled for %{seconds} seconds, aborting."}},s={formData:!0,fieldName:"files[]",method:"post",metaFields:null,responseUrlFieldName:"url",bundle:!1,headers:{},locale:i,timeout:3e4,limit:0,withCredentials:!1,getResponseData:function(t,e){var r={};try{r=JSON.parse(t)}catch(t){console.log(t)}return r},getResponseError:function(t,e){return new Error("Upload error")}};if(o.opts=ft({},s,n),o.locale=ft({},i,o.opts.locale),o.locale.strings=ft({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.handleUpload=o.handleUpload.bind(o),"number"==typeof o.opts.limit&&0!==o.opts.limit?o.limitUploads=ot(o.opts.limit):o.limitUploads=function(t){return t},o.opts.bundle&&!o.opts.formData)throw new Error("`opts.formData` must be true when `opts.bundle` is enabled.");return o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.getOptions=function(t){var e=this.uppy.getState().xhrUpload,r=ft({},this.opts,e||{},t.xhrUpload||{});return r.headers={},ft(r.headers,this.opts.headers),e&&ft(r.headers,e.headers),t.xhrUpload&&ft(r.headers,t.xhrUpload.headers),r},e.prototype.createProgressTimeout=function(t,e){var r=this.uppy,n=this,o=!1;function i(){r.log("[XHRUpload] timed out");var o=new Error(n.i18n("timedOut",{seconds:Math.ceil(t/1e3)}));e(o)}var s=null;return{progress:function(){o||t>0&&(s&&clearTimeout(s),s=setTimeout(i,t))},done:function(){r.log("[XHRUpload] timer done"),s&&(clearTimeout(s),s=null),o=!0}}},e.prototype.createFormDataUpload=function(t,e){var r=new FormData;return(Array.isArray(e.metaFields)?e.metaFields:Object.keys(t.meta)).forEach(function(e){r.append(e,t.meta[e])}),t.name?r.append(e.fieldName,t.data,t.name):r.append(e.fieldName,t.data),r},e.prototype.createBareUpload=function(t,e){return t.data},e.prototype.upload=function(t,e,r){var n=this,o=this.getOptions(t);return this.uppy.log("uploading "+e+" of "+r),new Promise(function(e,r){var i=o.formData?n.createFormDataUpload(t,o):n.createBareUpload(t,o),s=n.createProgressTimeout(o.timeout,function(e){a.abort(),n.uppy.emit("upload-error",t,e),r(e)}),a=new XMLHttpRequest,l=b();a.upload.addEventListener("loadstart",function(t){n.uppy.log("[XHRUpload] "+l+" started"),s.progress()}),a.upload.addEventListener("progress",function(e){n.uppy.log("[XHRUpload] "+l+" progress: "+e.loaded+" / "+e.total),s.progress(),e.lengthComputable&&n.uppy.emit("upload-progress",t,{uploader:n,bytesUploaded:e.loaded,bytesTotal:e.total})}),a.addEventListener("load",function(i){if(n.uppy.log("[XHRUpload] "+l+" finished"),s.done(),i.target.status>=200&&i.target.status<300){var u=o.getResponseData(a.responseText,a),p=u[o.responseUrlFieldName],c={status:i.target.status,body:u,uploadURL:p};return n.uppy.setFileState(t.id,{response:c}),n.uppy.emit("upload-success",t,u,p),p&&n.uppy.log("Download "+t.name+" from "+t.uploadURL),e(t)}var h=o.getResponseData(a.responseText,a),d=mt(a,o.getResponseError(a.responseText,a)),f={status:i.target.status,body:h};return n.uppy.setFileState(t.id,{response:f}),n.uppy.emit("upload-error",t,d),r(d)}),a.addEventListener("error",function(e){n.uppy.log("[XHRUpload] "+l+" errored"),s.done();var i=mt(a,o.getResponseError(a.responseText,a));return n.uppy.emit("upload-error",t,i),r(i)}),a.open(o.method.toUpperCase(),o.endpoint,!0),a.withCredentials=o.withCredentials,Object.keys(o.headers).forEach(function(t){a.setRequestHeader(t,o.headers[t])}),a.send(i),n.uppy.on("file-removed",function(e){e.id===t.id&&(s.done(),a.abort())}),n.uppy.on("upload-cancel",function(e){e===t.id&&(s.done(),a.abort())}),n.uppy.on("cancel-all",function(){s.done(),a.abort()})})},e.prototype.uploadRemote=function(t,e,r){var n=this,o=this.getOptions(t);return new Promise(function(e,r){var i={};(Array.isArray(o.metaFields)?o.metaFields:Object.keys(t.meta)).forEach(function(e){i[e]=t.meta[e]}),new yt(n.uppy,t.remote.providerOptions).post(t.remote.url,ft({},t.remote.body,{endpoint:o.endpoint,size:t.data.size,fieldname:o.fieldName,metadata:i,headers:o.headers})).then(function(i){var s=i.token,a=nt(t.remote.serverUrl),l=new gt({target:a+"/api/"+s});l.on("progress",function(e){return rt(n,e,t)}),l.on("success",function(r){var i=o.getResponseData(r.response.responseText,r.response),s=i[o.responseUrlFieldName];return n.uppy.emit("upload-success",t,i,s),l.close(),e()}),l.on("error",function(e){var i=e.response,s=i?o.getResponseError(i.responseText,i):ft(new Error(e.error.message),{cause:e.error});n.uppy.emit("upload-error",t,s),r(s)})})})},e.prototype.uploadBundle=function(t){var e=this;return new Promise(function(r,n){var o=e.opts.endpoint,i=e.opts.method,s=new FormData;t.forEach(function(t,r){var n=e.getOptions(t);s.append(n.fieldName,t.data)});var a=new XMLHttpRequest;a.withCredentials=e.opts.withCredentials;var l=e.createProgressTimeout(e.opts.timeout,function(t){a.abort(),u(t),n(t)}),u=function(r){t.forEach(function(t){e.uppy.emit("upload-error",t,r)})};a.upload.addEventListener("loadstart",function(t){e.uppy.log("[XHRUpload] started uploading bundle"),l.progress()}),a.upload.addEventListener("progress",function(r){l.progress(),r.lengthComputable&&t.forEach(function(t){e.uppy.emit("upload-progress",t,{uploader:e,bytesUploaded:r.loaded/r.total*t.size,bytesTotal:t.size})})}),a.addEventListener("load",function(o){if(l.done(),o.target.status>=200&&o.target.status<300){var i=e.opts.getResponseData(a.responseText,a);return t.forEach(function(t){e.uppy.emit("upload-success",t,i)}),r()}var s=e.opts.getResponseError(a.responseText,a)||new Error("Upload error");return s.request=a,u(s),n(s)}),a.addEventListener("error",function(t){l.done();var r=e.opts.getResponseError(a.responseText,a)||new Error("Upload error");return u(r),n(r)}),e.uppy.on("cancel-all",function(){l.done(),a.abort()}),a.open(i.toUpperCase(),o,!0),a.withCredentials=e.opts.withCredentials,Object.keys(e.opts.headers).forEach(function(t){a.setRequestHeader(t,e.opts.headers[t])}),a.send(s),t.forEach(function(t){e.uppy.emit("upload-started",t)})})},e.prototype.uploadFiles=function(t){var e=this,r=t.map(function(r,n){var o=parseInt(n,10)+1,i=t.length;return r.error?function(){return Promise.reject(new Error(r.error))}:r.isRemote?(e.uppy.emit("upload-started",r),e.uploadRemote.bind(e,r,o,i)):(e.uppy.emit("upload-started",r),e.upload.bind(e,r,o,i))}).map(function(t){return e.limitUploads(t)()});return dt(r)},e.prototype.handleUpload=function(t){var e=this;if(0===t.length)return this.uppy.log("[XHRUpload] No files to upload!"),Promise.resolve();this.uppy.log("[XHRUpload] Uploading...");var r=t.map(function(t){return e.uppy.getFile(t)});return this.opts.bundle?this.uploadBundle(r):this.uploadFiles(r).then(function(){return null})},e.prototype.install=function(){this.opts.bundle&&this.uppy.setState({capabilities:ft({},this.uppy.getState().capabilities,{bundled:!0})}),this.uppy.addUploader(this.handleUpload)},e.prototype.uninstall=function(){this.opts.bundle&&this.uppy.setState({capabilities:ft({},this.uppy.getState().capabilities,{bundled:!0})}),this.uppy.removeUploader(this.handleUpload)},e}(Y.Plugin),bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t};function St(t){var e=t.headers?t.headers["content-type"]:t.getResponseHeader("Content-Type");return"string"==typeof e&&"application/xml"===e.toLowerCase()}var Pt,_t,Et,kt=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.type="uploader",o.id="AwsS3",o.title="AWS S3";var i={strings:{preparingUpload:"Preparing upload..."}},s={timeout:3e4,limit:0,getUploadParameters:o.getUploadParameters.bind(o),locale:i};return o.opts=wt({},s,n),o.locale=wt({},i,o.opts.locale),o.locale.strings=wt({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.prepareUpload=o.prepareUpload.bind(o),"number"==typeof o.opts.limit&&0!==o.opts.limit?o.limitRequests=ot(o.opts.limit):o.limitRequests=function(t){return t},o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.getUploadParameters=function(t){if(!this.opts.serverUrl)throw new Error("Expected a `serverUrl` option containing a Companion address.");var e=encodeURIComponent(t.name),r=encodeURIComponent(t.type);return fetch(this.opts.serverUrl+"/s3/params?filename="+e+"&type="+r,{method:"get",headers:{accept:"application/json"}}).then(function(t){return t.json()})},e.prototype.validateParameters=function(t,e){if("object"!==(void 0===e?"undefined":bt(e))||!e||"string"!=typeof e.url||"object"!==bt(e.fields)&&null!=e.fields||null!=e.method&&!/^(put|post)$/i.test(e.method)){var r=new TypeError("AwsS3: got incorrect result from 'getUploadParameters()' for file '"+t.name+"', expected an object '{ url, method, fields, headers }'.\nSee https://uppy.io/docs/aws-s3/#getUploadParameters-file for more on the expected format.");throw console.error(r),r}return e},e.prototype.prepareUpload=function(t){var e=this;t.forEach(function(t){var r=e.uppy.getFile(t);e.uppy.emit("preprocess-progress",r,{mode:"determinate",message:e.i18n("preparingUpload"),value:0})});var r=this.limitRequests(this.opts.getUploadParameters);return Promise.all(t.map(function(t){var n=e.uppy.getFile(t);return Promise.resolve().then(function(){return r(n)}).then(function(t){return e.validateParameters(n,t)}).then(function(t){return e.uppy.emit("preprocess-progress",n,{mode:"determinate",message:e.i18n("preparingUpload"),value:1}),t}).catch(function(t){e.uppy.emit("upload-error",n,t)})})).then(function(r){var n={};t.forEach(function(t,o){var i=e.uppy.getFile(t);if(!i.error){var s=r[o],a=s.method,l=void 0===a?"post":a,u=s.url,p=s.fields,c=s.headers,h={method:l,formData:"post"===l.toLowerCase(),endpoint:u,metaFields:Object.keys(p)};c&&(h.headers=c);var d=wt({},i,{meta:wt({},i.meta,p),xhrUpload:h});n[t]=d}}),e.uppy.setState({files:wt({},e.uppy.getState().files,n)}),t.forEach(function(t){var r=e.uppy.getFile(t);e.uppy.emit("preprocess-complete",r)})})},e.prototype.install=function(){var t=this.uppy.log;this.uppy.addPreProcessor(this.prepareUpload);var e=!1;this.uppy.use(vt,{fieldName:"file",responseUrlFieldName:"location",timeout:this.opts.timeout,limit:this.opts.limit,getResponseData:function(r,n){if(!St(n))return"POST"===this.method.toUpperCase()?(e||(t("[AwsS3] No response data found, make sure to set the success_action_status AWS SDK option to 201. See https://uppy.io/docs/aws-s3/#POST-Uploads","warning"),e=!0),{location:null}):n.responseURL?{location:n.responseURL.replace(/\?.*$/,"")}:{location:null};var o=function(){return""};return n.responseXML&&(o=function(t){var e=n.responseXML.querySelector(t);return e?e.textContent:""}),n.responseText&&(o=function(t){var e=n.responseText.indexOf("<"+t+">"),r=n.responseText.indexOf("</"+t+">");return-1!==e&&-1!==r?n.responseText.slice(e+t.length+2,r):""}),{location:ht(n.responseURL,o("Location")),bucket:o("Bucket"),key:o("Key"),etag:o("ETag")}},getResponseError:function(t,e){if(St(e)){var r=e.responseXML.querySelector("Error > Message");return new Error(r.textContent)}}})},e.prototype.uninstall=function(){var t=this.uppy.getPlugin("XHRUpload");this.uppy.removePlugin(t),this.uppy.removePreProcessor(this.prepareUpload)},e}(Y.Plugin),Ct=function(t,e){return(e="number"==typeof e?e:1/0)?function t(r,n){return r.reduce(function(r,o){return Array.isArray(o)&&n<e?r.concat(t(o,n+1)):r.concat(o)},[])}(t,1):Array.isArray(t)?t.map(function(t){return t}):t},Tt=Pt={};function Ut(){throw new Error("setTimeout has not been defined")}function Ot(){throw new Error("clearTimeout has not been defined")}function Ft(t){if(_t===setTimeout)return setTimeout(t,0);if((_t===Ut||!_t)&&setTimeout)return _t=setTimeout,setTimeout(t,0);try{return _t(t,0)}catch(e){try{return _t.call(null,t,0)}catch(e){return _t.call(this,t,0)}}}!function(){try{_t="function"==typeof setTimeout?setTimeout:Ut}catch(t){_t=Ut}try{Et="function"==typeof clearTimeout?clearTimeout:Ot}catch(t){Et=Ot}}();var At,xt=[],Dt=!1,Rt=-1;function It(){Dt&&At&&(Dt=!1,At.length?xt=At.concat(xt):Rt=-1,xt.length&&Bt())}function Bt(){if(!Dt){var t=Ft(It);Dt=!0;for(var e=xt.length;e;){for(At=xt,xt=[];++Rt<e;)At&&At[Rt].run();Rt=-1,e=xt.length}At=null,Dt=!1,function(t){if(Et===clearTimeout)return clearTimeout(t);if((Et===Ot||!Et)&&clearTimeout)return Et=clearTimeout,clearTimeout(t);try{Et(t)}catch(e){try{return Et.call(null,t)}catch(e){return Et.call(this,t)}}}(t)}}function jt(t,e){this.fun=t,this.array=e}function Lt(){}Tt.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];xt.push(new jt(t,e)),1!==xt.length||Dt||Ft(Bt)},jt.prototype.run=function(){this.fun.apply(null,this.array)},Tt.title="browser",Tt.browser=!0,Tt.env={},Tt.argv=[],Tt.version="",Tt.versions={},Tt.on=Lt,Tt.addListener=Lt,Tt.once=Lt,Tt.off=Lt,Tt.removeListener=Lt,Tt.removeAllListeners=Lt,Tt.emit=Lt,Tt.prependListener=Lt,Tt.prependOnceListener=Lt,Tt.listeners=function(t){return[]},Tt.binding=function(t){throw new Error("process.binding is not supported")},Tt.cwd=function(){return"/"},Tt.chdir=function(t){throw new Error("process.chdir is not supported")},Tt.umask=function(){return 0};var Mt={};(function(t){Mt=function(e,r){var n,o,i,s=!0;function a(e){function o(){r&&r(e,n),r=null}s?t.nextTick(o):o()}function l(t,e,r){n[t]=r,(0==--o||e)&&a(e)}Array.isArray(e)?(n=[],o=e.length):(i=Object.keys(e),n={},o=i.length),o?i?i.forEach(function(t){e[t](function(e,r){l(t,e,r)})}):e.forEach(function(t,e){t(function(t,r){l(e,t,r)})}):a(null),s=!1}}).call(this,Pt);var Nt=function(t,e){if("string"==typeof t){var r=t;if(!(t=window.document.querySelector(t)))throw new Error('"'+r+'" does not match any HTML elements')}if(!t)throw new Error('"'+t+'" is not a valid HTML element');var n;return"function"==typeof e&&(e={onDrop:e}),t.addEventListener("dragenter",o,!1),t.addEventListener("dragover",i,!1),t.addEventListener("dragleave",s,!1),t.addEventListener("drop",a,!1),function(){l(),t.removeEventListener("dragenter",o,!1),t.removeEventListener("dragover",i,!1),t.removeEventListener("dragleave",s,!1),t.removeEventListener("drop",a,!1)};function o(t){return e.onDragEnter&&e.onDragEnter(t),t.stopPropagation(),t.preventDefault(),!1}function i(r){if(r.stopPropagation(),r.preventDefault(),r.dataTransfer.items){var o=qt(r.dataTransfer.items),i=o.filter(function(t){return"file"===t.kind}),s=o.filter(function(t){return"string"===t.kind});if(0===i.length&&!e.onDropText)return;if(0===s.length&&!e.onDrop)return;if(0===i.length&&0===s.length)return}return t.classList.add("drag"),clearTimeout(n),e.onDragOver&&e.onDragOver(r),r.dataTransfer.dropEffect="copy",!1}function s(t){return t.stopPropagation(),t.preventDefault(),e.onDragLeave&&e.onDragLeave(t),clearTimeout(n),n=setTimeout(l,50),!1}function a(t){t.stopPropagation(),t.preventDefault(),e.onDragLeave&&e.onDragLeave(t),clearTimeout(n),l();var r={x:t.clientX,y:t.clientY},o=t.dataTransfer.getData("text");if(o&&e.onDropText&&e.onDropText(o,r),t.dataTransfer.items){var i=qt(t.dataTransfer.items).filter(function(t){return"file"===t.kind});if(0===i.length)return;Mt(i.map(function(t){return function(e){!function(t,e){var r=[];if(t.isFile)t.file(function(r){r.fullPath=t.fullPath,e(null,r)},function(t){e(t)});else if(t.isDirectory){var n=t.createReader();!function t(){n.readEntries(function(n){n.length>0?(r=r.concat(qt(n)),t()):Mt(r.map(function(t){return function(e){!function(t,e){var r=[];if(t.isFile)t.file(function(r){r.fullPath=t.fullPath,e(null,r)},function(t){e(t)});else if(t.isDirectory){var n=t.createReader();!function t(){n.readEntries(function(n){n.length>0?(r=r.concat(qt(n)),t()):Mt(r.map(function(t){return function(e){zt(t,e)}}),e)})}()}}(t,e)}}),e)})}()}}(t.webkitGetAsEntry(),e)}}),function(t,n){if(t)throw t;e.onDrop&&e.onDrop(Ct(n),r)})}else{var s=qt(t.dataTransfer.files);if(0===s.length)return;s.forEach(function(t){t.fullPath="/"+t.name}),e.onDrop&&e.onDrop(s,r)}return!1}function l(){t.classList.remove("drag")}};function zt(t,e){var r=[];if(t.isFile){t.file(function(r){r.fullPath=t.fullPath;e(null,r)},function(t){e(t)})}else if(t.isDirectory){var n=t.createReader();o()}function o(){n.readEntries(function(t){if(t.length>0){r=r.concat(qt(t));o()}else{i()}})}function i(){Mt(r.map(function(t){return function(e){zt(t,e)}}),e)}}function qt(t){return Array.prototype.slice.call(t||[],0)}var Ht={exports:{}};!function(){"use strict";var t={}.hasOwnProperty;function e(){for(var r=[],n=0;n<arguments.length;n++){var o=arguments[n];if(o){var i=typeof o;if("string"===i||"number"===i)r.push(o);else if(Array.isArray(o)&&o.length){var s=e.apply(null,o);s&&r.push(s)}else if("object"===i)for(var a in o)t.call(o,a)&&o[a]&&r.push(a)}}return r.join(" ")}Ht.exports?(e.default=e,Ht.exports=e):window.classNames=e}(),Ht=Ht.exports;var Wt,Xt={exports:{}};Wt=function(t){"use strict";function e(t){return t.attributes&&t.attributes.key}function r(t){return t.base}function n(t){return t&&t.filter(function(t){return null!==t})}function o(t,e){for(var r=t.length;r--;)if(e(t[r]))return!0;return!1}function i(t,r){return o(t,function(t){return e(t)===r})}function s(t,r){return i(t,e(r))}function a(t,r,n){return o(t,function(t){return e(t)===r&&t.props[n]})}function l(t,r,n){return a(t,e(r),n)}var u=" ",p=/[\n\t\r]+/g,c=function(t){return(u+t+u).replace(p,u)};function h(t,e){var r;t.classList?(r=t.classList).add.apply(r,e.split(" ")):t.className+=" "+e}function d(t,e){if(e=e.trim(),t.classList){var r;(r=t.classList).remove.apply(r,e.split(" "))}else{var n=t.className.trim(),o=c(n);for(e=u+e+u;o.indexOf(e)>=0;)o=o.replace(e,u);t.className=o.trim()}}var f={transitionend:{transition:"transitionend",WebkitTransition:"webkitTransitionEnd",MozTransition:"mozTransitionEnd",OTransition:"oTransitionEnd",msTransition:"MSTransitionEnd"},animationend:{animation:"animationend",WebkitAnimation:"webkitAnimationEnd",MozAnimation:"mozAnimationEnd",OAnimation:"oAnimationEnd",msAnimation:"MSAnimationEnd"}},y=[];"undefined"!=typeof window&&function(){var t=document.createElement("div").style;for(var e in"AnimationEvent"in window||delete f.animationend.animation,"TransitionEvent"in window||delete f.transitionend.transition,f){var r=f[e];for(var n in r)if(n in t){y.push(r[n]);break}}}();var g=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},m=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},v=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},b=function(t){function e(){var n,o;g(this,e);for(var i=arguments.length,s=Array(i),a=0;a<i;a++)s[a]=arguments[a];return n=o=v(this,t.call.apply(t,[this].concat(s))),o.flushClassNameQueue=function(){r(o)&&h(r(o),o.classNameQueue.join(" ")),o.classNameQueue.length=0,o.timeout=null},v(o,n)}return m(e,t),e.prototype.transition=function(t,e,n){var o=this,i=r(this),s=this.props.name[t]||this.props.name+"-"+t,a=this.props.name[t+"Active"]||s+"-active",l=null;this.endListener&&this.endListener(),this.endListener=function(t){t&&t.target!==i||(clearTimeout(l),d(i,s),d(i,a),function(t,e){y.length&&y.forEach(function(r){t.removeEventListener(r,e,!1)})}(i,o.endListener),o.endListener=null,e&&e())},n?(l=setTimeout(this.endListener,n),this.transitionTimeouts.push(l)):function(t,e){if(!y.length)return window.setTimeout(e,0);y.forEach(function(r){t.addEventListener(r,e,!1)})}(i,this.endListener),h(i,s),this.queueClass(a)},e.prototype.queueClass=function(t){this.classNameQueue.push(t),this.timeout||(this.timeout=setTimeout(this.flushClassNameQueue,17))},e.prototype.stop=function(){this.timeout&&(clearTimeout(this.timeout),this.classNameQueue.length=0,this.timeout=null),this.endListener&&this.endListener()},e.prototype.componentWillMount=function(){this.classNameQueue=[],this.transitionTimeouts=[]},e.prototype.componentWillUnmount=function(){this.timeout&&clearTimeout(this.timeout),this.transitionTimeouts.forEach(function(t){clearTimeout(t)})},e.prototype.componentWillEnter=function(t){this.props.enter?this.transition("enter",t,this.props.enterTimeout):t()},e.prototype.componentWillLeave=function(t){this.props.leave?this.transition("leave",t,this.props.leaveTimeout):t()},e.prototype.render=function(){return(t=this.props.children)&&t[0];var t},e}(t.Component),w=function(r){function o(n){g(this,o);var i=v(this,r.call(this));return i.renderChild=function(r){var n=i.props,o=n.transitionName,s=n.transitionEnter,a=n.transitionLeave,l=n.transitionEnterTimeout,u=n.transitionLeaveTimeout,p=e(r);return t.h(b,{key:p,ref:function(t){(i.refs[p]=t)||(r=null)},name:o,enter:s,leave:a,enterTimeout:l,leaveTimeout:u},r)},i.refs={},i.state={children:(n.children||[]).slice()},i}return m(o,r),o.prototype.shouldComponentUpdate=function(t,e){return e.children!==this.state.children},o.prototype.componentWillMount=function(){this.currentlyTransitioningKeys={},this.keysToEnter=[],this.keysToLeave=[]},o.prototype.componentWillReceiveProps=function(r){var o,a,u,p,c=this,h=r.children,d=r.exclusive,f=r.showProp,y=n(h||[]).slice(),g=n(d?this.props.children:this.state.children),m=(o=y,a=[],u={},p=[],g.forEach(function(t){var r=e(t);i(o,r)?p.length&&(u[r]=p,p=[]):p.push(t)}),o.forEach(function(t){var r=e(t);u.hasOwnProperty(r)&&(a=a.concat(u[r])),a.push(t)}),a.concat(p));f&&(m=m.map(function(e){var r;return!e.props[f]&&l(g,e,f)&&(e=t.cloneElement(e,((r={})[f]=!0,r))),e})),d&&m.forEach(function(t){return c.stop(e(t))}),this.setState({children:m}),this.forceUpdate(),y.forEach(function(t){var e=t.key,r=g&&s(g,t);if(f){if(r){var n=l(g,t,f),o=t.props[f];n||!o||c.currentlyTransitioningKeys[e]||c.keysToEnter.push(e)}}else r||c.currentlyTransitioningKeys[e]||c.keysToEnter.push(e)}),g.forEach(function(t){var e=t.key,r=y&&s(y,t);if(f){if(r){var n=l(y,t,f),o=t.props[f];n||!o||c.currentlyTransitioningKeys[e]||c.keysToLeave.push(e)}}else r||c.currentlyTransitioningKeys[e]||c.keysToLeave.push(e)})},o.prototype.performEnter=function(t){var e=this;this.currentlyTransitioningKeys[t]=!0;var r=this.refs[t];r.componentWillEnter?r.componentWillEnter(function(){return e._handleDoneEntering(t)}):this._handleDoneEntering(t)},o.prototype._handleDoneEntering=function(t){delete this.currentlyTransitioningKeys[t];var e=n(this.props.children),r=this.props.showProp;!e||!r&&!i(e,t)||r&&!a(e,t,r)?this.performLeave(t):this.setState({children:e})},o.prototype.stop=function(t){delete this.currentlyTransitioningKeys[t];var e=this.refs[t];e&&e.stop()},o.prototype.performLeave=function(t){var e=this;this.currentlyTransitioningKeys[t]=!0;var r=this.refs[t];r&&r.componentWillLeave?r.componentWillLeave(function(){return e._handleDoneLeaving(t)}):this._handleDoneLeaving(t)},o.prototype._handleDoneLeaving=function(t){delete this.currentlyTransitioningKeys[t];var e=this.props.showProp,r=n(this.props.children);e&&r&&a(r,t,e)?this.performEnter(t):!e&&r&&i(r,t)?this.performEnter(t):this.setState({children:r})},o.prototype.componentDidUpdate=function(){var t=this,e=this.keysToEnter,r=this.keysToLeave;this.keysToEnter=[],e.forEach(function(e){return t.performEnter(e)}),this.keysToLeave=[],r.forEach(function(e){return t.performLeave(e)})},o.prototype.render=function(e,r){var o=e.component,i=(e.transitionName,e.transitionEnter,e.transitionLeave,e.transitionEnterTimeout,e.transitionLeaveTimeout,e.children,function(t,e){var r={};for(var n in t)e.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n]);return r}(e,["component","transitionName","transitionEnter","transitionLeave","transitionEnterTimeout","transitionLeaveTimeout","children"])),s=r.children;return t.h(o,i,n(s).map(this.renderChild))},o}(t.Component);return w.defaultProps={component:"span",transitionEnter:!0,transitionLeave:!0},w},"object"==typeof Xt.exports?Xt.exports=Wt(A):this.PreactCSSTransitionGroup=Wt(this.preact),Xt=Xt.exports;var Vt=A.h,Gt=function(t){function e(r){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r));return n.handleClick=n.handleClick.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleClick=function(t){this.input.click()},e.prototype.render=function(){var t=this,e=Vt("button",{type:"button",class:"uppy-Dashboard-browse",onclick:this.handleClick},this.props.i18n("browse"));return Vt("div",{class:"uppy-Dashboard-dropFilesTitle"},0===this.props.acquirers.length?this.props.i18nArray("dropPaste",{browse:e}):this.props.i18nArray("dropPasteImport",{browse:e}),Vt("input",{class:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabindex:-1,type:"file",name:"files[]",multiple:1!==this.props.maxNumberOfFiles,onchange:this.props.handleInputChange,accept:this.props.allowedFileTypes,value:"",ref:function(e){t.input=e}}))},e}(A.Component),Kt=A.h,Yt=function(){return Kt("svg",{"aria-hidden":"true",width:"30",height:"30",viewBox:"0 0 30 30"},Kt("path",{d:"M15 30c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15zm4.258-12.676v6.846h-8.426v-6.846H5.204l9.82-12.364 9.82 12.364H19.26z"}))},Jt=function(){return Kt("svg",{"aria-hidden":"true",class:"UppyIcon",width:"51",height:"51",viewBox:"0 0 51 51"},Kt("path",{d:"M17.21 45.765a5.394 5.394 0 0 1-7.62 0l-4.12-4.122a5.393 5.393 0 0 1 0-7.618l6.774-6.775-2.404-2.404-6.775 6.776c-3.424 3.427-3.424 9 0 12.426l4.12 4.123a8.766 8.766 0 0 0 6.216 2.57c2.25 0 4.5-.858 6.214-2.57l13.55-13.552a8.72 8.72 0 0 0 2.575-6.213 8.73 8.73 0 0 0-2.575-6.213l-4.123-4.12-2.404 2.404 4.123 4.12a5.352 5.352 0 0 1 1.58 3.81c0 1.438-.562 2.79-1.58 3.808l-13.55 13.55z"}),Kt("path",{d:"M44.256 2.858A8.728 8.728 0 0 0 38.043.283h-.002a8.73 8.73 0 0 0-6.212 2.574l-13.55 13.55a8.725 8.725 0 0 0-2.575 6.214 8.73 8.73 0 0 0 2.574 6.216l4.12 4.12 2.405-2.403-4.12-4.12a5.357 5.357 0 0 1-1.58-3.812c0-1.437.562-2.79 1.58-3.808l13.55-13.55a5.348 5.348 0 0 1 3.81-1.58c1.44 0 2.792.562 3.81 1.58l4.12 4.12c2.1 2.1 2.1 5.518 0 7.617L39.2 23.775l2.404 2.404 6.775-6.777c3.426-3.427 3.426-9 0-12.426l-4.12-4.12z"}))},$t=function(){return Kt("svg",{"aria-hidden":"true",class:"UppyIcon retry",width:"28",height:"31",viewBox:"0 0 16 19",xmlns:"http://www.w3.org/2000/svg"},Kt("path",{d:"M16 11a8 8 0 1 1-8-8v2a6 6 0 1 0 6 6h2z"}),Kt("path",{d:"M7.9 3H10v2H7.9z"}),Kt("path",{d:"M8.536.5l3.535 3.536-1.414 1.414L7.12 1.914z"}),Kt("path",{d:"M10.657 2.621l1.414 1.415L8.536 7.57 7.12 6.157z"}))},Qt=function(){return Kt("svg",{"aria-hidden":"true",class:"UppyIcon",width:"55",height:"55",viewBox:"0 0 55 55"},Kt("path",{d:"M52.66.25c-.216-.19-.5-.276-.79-.242l-31 4.01a1 1 0 0 0-.87.992V40.622C18.174 38.428 15.273 37 12 37c-5.514 0-10 4.037-10 9s4.486 9 10 9 10-4.037 10-9c0-.232-.02-.46-.04-.687.014-.065.04-.124.04-.192V16.12l29-3.753v18.257C49.174 28.428 46.273 27 43 27c-5.514 0-10 4.037-10 9s4.486 9 10 9c5.464 0 9.913-3.966 9.993-8.867 0-.013.007-.024.007-.037V1a.998.998 0 0 0-.34-.75zM12 53c-4.41 0-8-3.14-8-7s3.59-7 8-7 8 3.14 8 7-3.59 7-8 7zm31-10c-4.41 0-8-3.14-8-7s3.59-7 8-7 8 3.14 8 7-3.59 7-8 7zM22 14.1V5.89l29-3.753v8.21l-29 3.754z"}))},Zt=function(){return Kt("svg",{"aria-hidden":"true",class:"UppyIcon",viewBox:"0 0 58 58"},Kt("path",{d:"M36.537 28.156l-11-7a1.005 1.005 0 0 0-1.02-.033C24.2 21.3 24 21.635 24 22v14a1 1 0 0 0 1.537.844l11-7a1.002 1.002 0 0 0 0-1.688zM26 34.18V23.82L34.137 29 26 34.18z"}),Kt("path",{d:"M57 6H1a1 1 0 0 0-1 1v44a1 1 0 0 0 1 1h56a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zM10 28H2v-9h8v9zm-8 2h8v9H2v-9zm10 10V8h34v42H12V40zm44-12h-8v-9h8v9zm-8 2h8v9h-8v-9zm8-22v9h-8V8h8zM2 8h8v9H2V8zm0 42v-9h8v9H2zm54 0h-8v-9h8v9z"}))},te=function(){return Kt("svg",{"aria-hidden":"true",class:"UppyIcon",viewBox:"0 0 342 335"},Kt("path",{d:"M329.337 227.84c-2.1 1.3-8.1 2.1-11.9 2.1-12.4 0-27.6-5.7-49.1-14.9 8.3-.6 15.8-.9 22.6-.9 12.4 0 16 0 28.2 3.1 12.1 3 12.2 9.3 10.2 10.6zm-215.1 1.9c4.8-8.4 9.7-17.3 14.7-26.8 12.2-23.1 20-41.3 25.7-56.2 11.5 20.9 25.8 38.6 42.5 52.8 2.1 1.8 4.3 3.5 6.7 5.3-34.1 6.8-63.6 15-89.6 24.9zm39.8-218.9c6.8 0 10.7 17.06 11 33.16.3 16-3.4 27.2-8.1 35.6-3.9-12.4-5.7-31.8-5.7-44.5 0 0-.3-24.26 2.8-24.26zm-133.4 307.2c3.9-10.5 19.1-31.3 41.6-49.8 1.4-1.1 4.9-4.4 8.1-7.4-23.5 37.6-39.3 52.5-49.7 57.2zm315.2-112.3c-6.8-6.7-22-10.2-45-10.5-15.6-.2-34.3 1.2-54.1 3.9-8.8-5.1-17.9-10.6-25.1-17.3-19.2-18-35.2-42.9-45.2-70.3.6-2.6 1.2-4.8 1.7-7.1 0 0 10.8-61.5 7.9-82.3-.4-2.9-.6-3.7-1.4-5.9l-.9-2.5c-2.9-6.76-8.7-13.96-17.8-13.57l-5.3-.17h-.1c-10.1 0-18.4 5.17-20.5 12.84-6.6 24.3.2 60.5 12.5 107.4l-3.2 7.7c-8.8 21.4-19.8 43-29.5 62l-1.3 2.5c-10.2 20-19.5 37-27.9 51.4l-8.7 4.6c-.6.4-15.5 8.2-19 10.3-29.6 17.7-49.28 37.8-52.54 53.8-1.04 5-.26 11.5 5.01 14.6l8.4 4.2c3.63 1.8 7.53 2.7 11.43 2.7 21.1 0 45.6-26.2 79.3-85.1 39-12.7 83.4-23.3 122.3-29.1 29.6 16.7 66 28.3 89 28.3 4.1 0 7.6-.4 10.5-1.2 4.4-1.1 8.1-3.6 10.4-7.1 4.4-6.7 5.4-15.9 4.1-25.4-.3-2.8-2.6-6.3-5-8.7z"}))},ee=function(){return Kt("svg",{"aria-hidden":"true",class:"UppyIcon",width:"62",height:"62",viewBox:"0 0 62 62",xmlns:"http://www.w3.org/2000/svg"},Kt("path",{d:"M4.309 4.309h24.912v53.382h-6.525v3.559h16.608v-3.559h-6.525V4.309h24.912v10.676h3.559V.75H.75v14.235h3.559z","fill-rule":"nonzero",fill:"#000"}))},re=function(){return Kt("svg",{"aria-hidden":"true",fill:"#607d8b",width:"27",height:"25",viewBox:"0 0 27 25"},Kt("path",{d:"M5.586 9.288a.313.313 0 0 0 .282.176h4.84v3.922c0 1.514 1.25 2.24 2.792 2.24 1.54 0 2.79-.726 2.79-2.24V9.464h4.84c.122 0 .23-.068.284-.176a.304.304 0 0 0-.046-.324L13.735.106a.316.316 0 0 0-.472 0l-7.63 8.857a.302.302 0 0 0-.047.325z"}),Kt("path",{d:"M24.3 5.093c-.218-.76-.54-1.187-1.208-1.187h-4.856l1.018 1.18h3.948l2.043 11.038h-7.193v2.728H9.114v-2.725h-7.36l2.66-11.04h3.33l1.018-1.18H3.907c-.668 0-1.06.46-1.21 1.186L0 16.456v7.062C0 24.338.676 25 1.51 25h23.98c.833 0 1.51-.663 1.51-1.482v-7.062L24.3 5.093z"}))},ne=A.h,oe=A.Component,ie=function(t){function e(r){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r));return n.handleClick=n.handleClick.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleClick=function(t){this.input.click()},e.prototype.render=function(){var t=this,e=0!==this.props.acquirers.length;return ne("div",{class:"uppy-DashboarAddFiles"},e?ne("div",{class:"uppy-DashboardTabs"},ne(Gt,{acquirers:this.props.acquirers,handleInputChange:this.props.handleInputChange,i18n:this.props.i18n,i18nArray:this.props.i18nArray,allowedFileTypes:this.props.allowedFileTypes,maxNumberOfFiles:this.props.maxNumberOfFiles}),ne("div",{class:"uppy-DashboardTabs-list",role:"tablist"},ne("div",{class:"uppy-DashboardTab",role:"presentation"},ne("button",{type:"button",class:"uppy-DashboardTab-btn",role:"tab",tabindex:0,onclick:this.handleClick},re(),ne("div",{class:"uppy-DashboardTab-name"},this.props.i18n("myDevice"))),ne("input",{class:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabindex:-1,type:"file",name:"files[]",multiple:1!==this.props.maxNumberOfFiles,accept:this.props.allowedFileTypes,onchange:this.props.handleInputChange,value:"",ref:function(e){t.input=e}})),this.props.acquirers.map(function(e){return ne("div",{class:"uppy-DashboardTab",role:"presentation"},ne("button",{class:"uppy-DashboardTab-btn",type:"button",role:"tab",tabindex:0,"aria-controls":"uppy-DashboardContent-panel--"+e.id,"aria-selected":t.props.activePanel.id===e.id,onclick:function(){return t.props.showPanel(e.id)}},e.icon(),ne("div",{class:"uppy-DashboardTab-name"},e.name)))}))):ne("div",{class:"uppy-DashboardTabs"},ne(Gt,{acquirers:this.props.acquirers,handleInputChange:this.props.handleInputChange,i18n:this.props.i18n,i18nArray:this.props.i18nArray,allowedFileTypes:this.props.allowedFileTypes,maxNumberOfFiles:this.props.maxNumberOfFiles})),this.props.note&&ne("div",{class:"uppy-Dashboard-note"},this.props.note),this.props.proudlyDisplayPoweredByUppy&&(this.props,ne("a",{tabindex:"-1",href:"https://uppy.io",rel:"noreferrer noopener",target:"_blank",class:"uppy-Dashboard-poweredBy"},"Powered by ",ne("svg",{"aria-hidden":"true",class:"UppyIcon uppy-Dashboard-poweredByIcon",width:"11",height:"11",viewBox:"0 0 11 11",xmlns:"http://www.w3.org/2000/svg"},ne("path",{d:"M7.365 10.5l-.01-4.045h2.612L5.5.806l-4.467 5.65h2.604l.01 4.044h3.718z","fill-rule":"evenodd"})),ne("span",{class:"uppy-Dashboard-poweredByUppy"},"Uppy"))))},e}(oe),se=A.h,ae=function(t){return se("div",{class:"uppy-Dashboard-AddFilesPanel","aria-hidden":t.showAddFilesPanel},se("div",{class:"uppy-DashboardContent-bar"},se("div",{class:"uppy-DashboardContent-title",role:"heading","aria-level":"h1"},t.i18n("addingMoreFiles")),se("button",{class:"uppy-DashboardContent-back",type:"button",onclick:function(e){return t.toggleAddFilesPanel(!1)}},t.i18n("back"))),se(ie,t))},le=ee,ue=Qt,pe=Zt,ce=te,he=function(t){var e={color:"#cbcbcb",icon:""};if(!t)return e;var r=t.split("/")[0],n=t.split("/")[1];return"text"===r?{color:"#cbcbcb",icon:le()}:"audio"===r?{color:"#1abc9c",icon:ue()}:"video"===r?{color:"#2980b9",icon:pe()}:"application"===r&&"pdf"===n?{color:"#e74c3c",icon:ce()}:"image"===r?{color:"#f2f2f2",icon:""}:e},de=A.h,fe=function(t){var e=t.file;if(e.preview)return de("img",{class:"uppy-DashboardItem-previewImg",alt:e.name,src:e.preview});var r=he(e.type),n=r.color,o=r.icon;return de("div",{class:"uppy-DashboardItem-previewIconWrap"},de("span",{class:"uppy-DashboardItem-previewIcon",style:{color:n}},o),de("svg",{class:"uppy-DashboardItem-previewIconBg",width:"72",height:"93",viewBox:"0 0 72 93"},de("g",null,de("path",{d:"M24.08 5h38.922A2.997 2.997 0 0 1 66 8.003v74.994A2.997 2.997 0 0 1 63.004 86H8.996A2.998 2.998 0 0 1 6 83.01V22.234L24.08 5z",fill:"#FFF"}),de("path",{d:"M24 5L6 22.248h15.007A2.995 2.995 0 0 0 24 19.244V5z",fill:"#E4E4E4"}))))},ye=function(t){var e=t.target.tagName;"INPUT"!==e&&"TEXTAREA"!==e?(t.preventDefault(),t.stopPropagation()):t.stopPropagation()},ge=A.h,me=function(t){function e(r){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r));return n.meta={},n.tempStoreMetaOrSubmit=n.tempStoreMetaOrSubmit.bind(n),n.renderMetaFields=n.renderMetaFields.bind(n),n.handleSave=n.handleSave.bind(n),n.handleCancel=n.handleCancel.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.componentDidMount=function(){var t=this;setTimeout(function(){t.firstInput&&t.firstInput.focus({preventScroll:!0})},150)},e.prototype.tempStoreMetaOrSubmit=function(t){var e=this.props.files[this.props.fileCardFor];if(13===t.keyCode)return t.stopPropagation(),t.preventDefault(),void this.props.saveFileCard(this.meta,e.id);var r=t.target.value,n=t.target.dataset.name;this.meta[n]=r},e.prototype.renderMetaFields=function(t){var e=this;return(this.props.metaFields||[]).map(function(r,n){return ge("fieldset",{class:"uppy-DashboardFileCard-fieldset"},ge("label",{class:"uppy-DashboardFileCard-label"},r.name),ge("input",{class:"uppy-c-textInput uppy-DashboardFileCard-input",type:"text","data-name":r.id,value:t.meta[r.id],placeholder:r.placeholder,onkeyup:e.tempStoreMetaOrSubmit,onkeydown:e.tempStoreMetaOrSubmit,onkeypress:e.tempStoreMetaOrSubmit,ref:function(t){0===n&&(e.firstInput=t)}}))})},e.prototype.handleSave=function(t){var e=this.props.fileCardFor;this.props.saveFileCard(this.meta,e)},e.prototype.handleCancel=function(t){this.meta={},this.props.toggleFileCard()},e.prototype.render=function(){var t=this.props.files[this.props.fileCardFor];return ge("div",{class:"uppy-DashboardFileCard",onDragOver:ye,onDragLeave:ye,onDrop:ye,onPaste:ye},ge("div",{class:"uppy-DashboardContent-bar"},ge("div",{class:"uppy-DashboardContent-title",role:"heading","aria-level":"h1"},this.props.i18nArray("editing",{file:ge("span",{class:"uppy-DashboardContent-titleFile"},t.meta?t.meta.name:t.name)})),ge("button",{class:"uppy-DashboardContent-back",type:"button",title:this.props.i18n("finishEditingFile"),onclick:this.handleSave},this.props.i18n("done"))),ge("div",{class:"uppy-DashboardFileCard-inner"},ge("div",{class:"uppy-DashboardFileCard-preview",style:{backgroundColor:he(t.type).color}},ge(fe,{file:t})),ge("div",{class:"uppy-DashboardFileCard-info"},this.renderMetaFields(t)),ge("div",{class:"uppy-Dashboard-actions"},ge("button",{class:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Dashboard-actionsBtn",type:"button",onclick:this.handleSave},this.props.i18n("saveChanges")),ge("button",{class:"uppy-u-reset uppy-c-btn uppy-c-btn-link uppy-Dashboard-actionsBtn",type:"button",onclick:this.handleCancel},this.props.i18n("cancel")))))},e}(A.Component),ve=A.h,be=2*Math.PI*15,we=function(t){return ve("svg",{width:"70",height:"70",viewBox:"0 0 36 36",class:"UppyIcon UppyIcon-progressCircle"},ve("g",{class:"progress-group"},ve("circle",{class:"bg",r:"15",cx:"18",cy:"18","stroke-width":"2",fill:"none"}),ve("circle",{class:"progress",r:"15",cx:"18",cy:"18",transform:"rotate(-90, 18, 18)","stroke-width":"2",fill:"none","stroke-dasharray":be,"stroke-dashoffset":be-be/100*t.progress})),t.hidePauseResumeCancelButtons||t.bundled?null:ve("g",null,ve("polygon",{class:"play",transform:"translate(3, 3)",points:"12 20 12 10 20 15"}),ve("g",{class:"pause",transform:"translate(14.5, 13)"},ve("rect",{x:"0",y:"0",width:"2",height:"10",rx:"0"}),ve("rect",{x:"5",y:"0",width:"2",height:"10",rx:"0"})),ve("polygon",{class:"cancel",transform:"translate(2, 2)",points:"19.8856516 11.0625 16 14.9481516 12.1019737 11.0625 11.0625 12.1143484 14.9481516 16 11.0625 19.8980263 12.1019737 20.9375 16 17.0518484 19.8856516 20.9375 20.9375 19.8980263 17.0518484 16 20.9375 12"})),ve("polygon",{class:"check",transform:"translate(2, 3)",points:"14 22.5 7 15.2457065 8.99985857 13.1732815 14 18.3547104 22.9729883 9 25 11.1005634"}))},Se=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Pe=Jt,_e=$t,Ee=A.h,ke=function(t){if(!t.hideRetryButton||!t.error)return t.isUploaded||t.bundled||t.hidePauseResumeCancelButtons&&!t.error?Ee("div",{class:"uppy-DashboardItem-progressIndicator"},Ee(we,{progress:t.file.progress.percentage,fileID:t.file.id,hidePauseResumeCancelButtons:t.hidePauseResumeCancelButtons,bundled:t.bundled})):Ee("button",{class:"uppy-DashboardItem-progressIndicator",type:"button","aria-label":t.progressIndicatorTitle,title:t.progressIndicatorTitle,onclick:t.onPauseResumeCancelRetry},t.error?t.hideRetryButton?null:_e():Ee(we,{progress:t.file.progress.percentage,fileID:t.file.id,hidePauseResumeCancelButtons:t.hidePauseResumeCancelButtons}))},Ce=function(t){var e,r,n=t.file,o=t.acquirers,i=n.progress.preprocess||n.progress.postprocess,s=n.progress.uploadComplete&&!i&&!n.error,a=n.progress.uploadStarted||i,l=n.progress.uploadStarted&&!n.progress.uploadComplete||i,u=n.isPaused||!1,p=n.error||!1,c=X(n.meta.name).name,h=t.isWide?(r=30,(e=c).length>r?e.substr(0,r/2)+"..."+e.substr(e.length-r/4,e.length):e):c,d=Ht("uppy-DashboardItem",{"is-inprogress":l},{"is-processing":i},{"is-complete":s},{"is-paused":u},{"is-error":p},{"is-resumable":t.resumableUploads},{"is-bundled":t.bundledUpload}),f=s?t.i18n("uploadComplete"):t.resumableUploads?n.isPaused?t.i18n("resumeUpload"):t.i18n("pauseUpload"):p?t.i18n("retryUpload"):t.i18n("cancelUpload");return Ee("li",{class:d,id:"uppy_"+n.id,title:n.meta.name},Ee("div",{class:"uppy-DashboardItem-preview"},Ee("div",{class:"uppy-DashboardItem-previewInnerWrap",style:{backgroundColor:he(n.type).color}},t.showLinkToFileUploadResult&&n.uploadURL?Ee("a",{class:"uppy-DashboardItem-previewLink",href:n.uploadURL,rel:"noreferrer noopener",target:"_blank"}):null,Ee(fe,{file:n})),Ee("div",{class:"uppy-DashboardItem-progress"},Ee(ke,Se({progressIndicatorTitle:f,onPauseResumeCancelRetry:function(e){s||(!p||t.hideRetryButton?t.hidePauseResumeCancelButtons||(t.resumableUploads?t.pauseUpload(n.id):t.cancelUpload(n.id)):t.retryUpload(n.id))},file:n,error:p},t)))),Ee("div",{class:"uppy-DashboardItem-info"},Ee("div",{class:"uppy-DashboardItem-name",title:c},t.showLinkToFileUploadResult&&n.uploadURL?Ee("a",{href:n.uploadURL,rel:"noreferrer noopener",target:"_blank"},n.extension?h+"."+n.extension:h):n.extension?h+"."+n.extension:h),Ee("div",{class:"uppy-DashboardItem-status"},n.data.size?Ee("div",{class:"uppy-DashboardItem-statusSize"},F(n.data.size)):null,n.source&&n.source!==t.id&&Ee("div",{class:"uppy-DashboardItem-sourceIcon"},o.map(function(e){if(e.id===n.source)return Ee("span",{title:t.i18n("fileSource",{name:e.name})},e.icon())})),!a&&t.metaFields&&t.metaFields.length?Ee("button",{class:"uppy-DashboardItem-edit",type:"button","aria-label":t.i18n("editFile"),title:t.i18n("editFile"),onclick:function(e){return t.toggleFileCard(n.id)}},t.i18n("edit")):null,t.showLinkToFileUploadResult&&n.uploadURL?Ee("button",{class:"uppy-DashboardItem-copyLink",type:"button","aria-label":t.i18n("copyLink"),title:t.i18n("copyLink"),onclick:function(){var e,r;(e=n.uploadURL,r=t.i18n("copyLinkToClipboardFallback"),r=r||"Copy the URL below",new Promise(function(t){var n=document.createElement("textarea");n.setAttribute("style",{position:"fixed",top:0,left:0,width:"2em",height:"2em",padding:0,border:"none",outline:"none",boxShadow:"none",background:"transparent"}),n.value=e,document.body.appendChild(n),n.select();var o=function(){document.body.removeChild(n),window.prompt(r,e),t()};try{return document.execCommand("copy")?(document.body.removeChild(n),t()):o()}catch(t){return document.body.removeChild(n),o()}})).then(function(){t.log("Link copied to clipboard."),t.info(t.i18n("copyLinkToClipboardSuccess"),"info",3e3)}).catch(t.log)}},Pe()):"")),Ee("div",{class:"uppy-DashboardItem-action"},!s&&Ee("button",{class:"uppy-DashboardItem-remove",type:"button","aria-label":t.i18n("removeFile"),title:t.i18n("removeFile"),onclick:function(){return t.removeFile(n.id)}},Ee("svg",{"aria-hidden":"true",class:"UppyIcon",width:"60",height:"60",viewBox:"0 0 60 60",xmlns:"http://www.w3.org/2000/svg"},Ee("path",{stroke:"#FFF","stroke-width":"1","fill-rule":"nonzero","vector-effect":"non-scaling-stroke",d:"M30 1C14 1 1 14 1 30s13 29 29 29 29-13 29-29S46 1 30 1z"}),Ee("path",{fill:"#FFF","vector-effect":"non-scaling-stroke",d:"M42 39.667L39.667 42 30 32.333 20.333 42 18 39.667 27.667 30 18 20.333 20.333 18 30 27.667 39.667 18 42 20.333 32.333 30z"})))))},Te=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ue=A.h,Oe=function(t){var e=0===t.totalFileCount,r=Ht("uppy-Dashboard-files",{"uppy-Dashboard-files--noFiles":e});return Ue("ul",{class:r},Object.keys(t.files).map(function(e){return Ue(Ce,Te({},t,{acquirers:t.acquirers,file:t.files[e]}))}))},Fe=A.h,Ae=function(t){return Fe("div",{class:"uppy-DashboardContent-panel",role:"tabpanel",id:t.activePanel&&"uppy-DashboardContent-panel--"+t.activePanel.id,onDragOver:ye,onDragLeave:ye,onDrop:ye,onPaste:ye},Fe("div",{class:"uppy-DashboardContent-bar"},Fe("div",{class:"uppy-DashboardContent-title",role:"heading","aria-level":"h1"},t.i18n("importFrom",{name:t.activePanel.name})),Fe("button",{class:"uppy-DashboardContent-back",type:"button",onclick:t.hideAllPanels},t.i18n("done"))),Fe("div",{class:"uppy-DashboardContent-panelBody"},t.getPlugin(t.activePanel.id).render(t.state)))},xe=A.h;function De(t){if(t.newFiles.length)return t.i18n("xFilesSelected",{smart_count:t.newFiles.length})}var Re=function(t){return xe("div",{class:"uppy-DashboardContent-bar"},xe("button",{class:"uppy-DashboardContent-back",type:"button",onclick:t.cancelAll},t.i18n("cancel")),xe("div",{class:"uppy-DashboardContent-title",role:"heading","aria-level":"h1"},xe(De,t)),xe("button",{class:"uppy-DashboardContent-addMore",type:"button","aria-label":t.i18n("addMoreFiles"),title:t.i18n("addMoreFiles"),onclick:function(){return t.toggleAddFilesPanel(!0)}},xe("svg",{class:"UppyIcon",width:"15",height:"15",viewBox:"0 0 13 13",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},xe("path",{d:"M7,6 L13,6 L13,7 L7,7 L7,13 L6,13 L6,7 L0,7 L0,6 L6,6 L6,0 L7,0 L7,6 Z"}))))},Ie=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Be=A.h,je=function(t){var e=0===t.totalFileCount,r=Ht({"uppy-Root":t.isTargetDOMEl},"uppy-Dashboard",{"Uppy--isTouchDevice":"ontouchstart"in window||navigator.maxTouchPoints},{"uppy-Dashboard--animateOpenClose":t.animateOpenClose},{"uppy-Dashboard--isClosing":t.isClosing},{"uppy-Dashboard--modal":!t.inline},{"uppy-size--md":t.containerWidth>576},{"uppy-size--lg":t.containerWidth>700},{"uppy-Dashboard--isAddFilesPanelVisible":t.showAddFilesPanel});return Be("div",{class:r,"aria-hidden":t.inline?"false":t.modal.isHidden,"aria-label":t.inline?t.i18n("dashboardTitle"):t.i18n("dashboardWindowTitle"),onpaste:t.handlePaste},Be("div",{class:"uppy-Dashboard-overlay",tabindex:-1,onclick:t.handleClickOutside}),Be("div",{class:"uppy-Dashboard-inner","aria-modal":!t.inline&&"true",role:!t.inline&&"dialog",style:{width:t.inline&&t.width?t.width:"",height:t.inline&&t.height?t.height:""}},Be("button",{class:"uppy-Dashboard-close",type:"button","aria-label":t.i18n("closeModal"),title:t.i18n("closeModal"),onclick:t.closeModal},Be("span",{"aria-hidden":"true"},"\xd7")),Be("div",{class:"uppy-Dashboard-innerWrap"},!e&&Be(Re,t),Be(e?ie:Oe,t),Be(Xt,{transitionName:"uppy-transition-slideDownUp",transitionEnterTimeout:250,transitionLeaveTimeout:250},t.showAddFilesPanel?Be(ae,Ie({key:"AddFilesPanel"},t)):null),Be(Xt,{transitionName:"uppy-transition-slideDownUp",transitionEnterTimeout:250,transitionLeaveTimeout:250},t.fileCardFor?Be(me,Ie({key:"FileCard"},t)):null),Be(Xt,{transitionName:"uppy-transition-slideDownUp",transitionEnterTimeout:250,transitionLeaveTimeout:250},t.activePanel?Be(Ae,Ie({key:"PanelContent"},t)):null),Be("div",{class:"uppy-Dashboard-progressindicators"},t.progressindicators.map(function(e){return t.getPlugin(e.id).render(t.state)})))))},Le=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Me=Y.Plugin,Ne=A.h,ze=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.type="progressindicator",o.id=o.opts.id||"Informer",o.title="Informer",o.opts=Le({},{typeColors:{info:{text:"#fff",bg:"#000"},warning:{text:"#fff",bg:"#F6A623"},error:{text:"#fff",bg:"#D32F2F"},success:{text:"#fff",bg:"#1BB240"}}},n),o.render=o.render.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.render=function(t){var e=t.info,r=e.isHidden,n=e.message,o=e.details;return Ne("div",{class:"uppy uppy-Informer","aria-hidden":r},Ne("p",{role:"alert"},n," ",o&&Ne("span",{"aria-label":o,"data-microtip-position":"top","data-microtip-size":"large",role:"tooltip"},"?")))},e.prototype.install=function(){var t=this.opts.target;t&&this.mount(t,this)},e}(Me),qe="error",He="waiting",We="preprocessing",Xe="uploading",Ve="postprocessing",Ge="complete",Ke=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ye=A.h,Je=function(t){var e=Ht("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--upload",{"uppy-c-btn-primary":t.uploadState===He});return Ye("button",{type:"button",class:e,"aria-label":t.i18n("uploadXFiles",{smart_count:t.newFiles}),onclick:t.startUpload},t.newFiles&&t.uploadStarted?t.i18n("uploadXNewFiles",{smart_count:t.newFiles}):t.i18n("uploadXFiles",{smart_count:t.newFiles}))},$e=function(t){return Ye("button",{type:"button",class:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--retry","aria-label":t.i18n("retryUpload"),onclick:t.retryAll},t.i18n("retry"))},Qe=function(t){return Ye("button",{type:"button",class:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--cancel","aria-label":t.i18n("cancel"),onclick:t.cancelAll},t.i18n("cancel"))},Ze=function(t){var e=t.resumableUploads,r=t.isAllPaused,n=(0,t.i18n)(e?r?"resumeUpload":"pauseUpload":"cancelUpload");return Ye("button",{title:n,class:"uppy-u-reset uppy-StatusBar-statusIndicator",type:"button",onclick:function(){return function(t){if(!t.isAllComplete)return t.resumableUploads?t.isAllPaused?t.resumeAll():t.pauseAll():t.cancelAll()}(t)}},e?r?Ye("svg",{"aria-hidden":"true",class:"UppyIcon",width:"15",height:"17",viewBox:"0 0 11 13"},Ye("path",{d:"M1.26 12.534a.67.67 0 0 1-.674.012.67.67 0 0 1-.336-.583v-11C.25.724.38.5.586.382a.658.658 0 0 1 .673.012l9.165 5.5a.66.66 0 0 1 .325.57.66.66 0 0 1-.325.573l-9.166 5.5z"})):Ye("svg",{"aria-hidden":"true",class:"UppyIcon",width:"16",height:"17",viewBox:"0 0 12 13"},Ye("path",{d:"M4.888.81v11.38c0 .446-.324.81-.722.81H2.722C2.324 13 2 12.636 2 12.19V.81c0-.446.324-.81.722-.81h1.444c.398 0 .722.364.722.81zM9.888.81v11.38c0 .446-.324.81-.722.81H7.722C7.324 13 7 12.636 7 12.19V.81c0-.446.324-.81.722-.81h1.444c.398 0 .722.364.722.81z"})):Ye("svg",{"aria-hidden":"true",class:"UppyIcon",width:"16px",height:"16px",viewBox:"0 0 19 19"},Ye("path",{d:"M17.318 17.232L9.94 9.854 9.586 9.5l-.354.354-7.378 7.378h.707l-.62-.62v.706L9.318 9.94l.354-.354-.354-.354L1.94 1.854v.707l.62-.62h-.706l7.378 7.378.354.354.354-.354 7.378-7.378h-.707l.622.62v-.706L9.854 9.232l-.354.354.354.354 7.378 7.378.708-.707-7.38-7.378v.708l7.38-7.38.353-.353-.353-.353-.622-.622-.353-.353-.354.352-7.378 7.38h.708L2.56 1.23 2.208.88l-.353.353-.622.62-.353.355.352.353 7.38 7.38v-.708l-7.38 7.38-.353.353.352.353.622.622.353.353.354-.353 7.38-7.38h-.708l7.38 7.38z"})))},tr=function(t){var e=Math.round(100*t.value);return Ye("div",{class:"uppy-StatusBar-content"},"determinate"===t.mode?e+"% \xb7 ":"",t.message)},er=et(function(t){return Ye("div",{class:"uppy-StatusBar-statusSecondary"},t.numUploads>1&&t.i18n("filesUploadedOfTotal",{complete:t.complete,smart_count:t.numUploads})+" \xb7 ",t.i18n("dataUploadedOfTotal",{complete:t.totalUploadedSize,total:t.totalSize})+" \xb7 ",t.i18n("xTimeLeft",{time:t.totalETA}))},500,{leading:!0,trailing:!0}),rr=function(t){if(!t.isUploadStarted||t.isAllComplete)return null;var e=t.isAllPaused?t.i18n("paused"):t.i18n("uploading");return Ye("div",{class:"uppy-StatusBar-content","aria-label":e,title:e},!t.hidePauseResumeCancelButtons&&Ye(Ze,t),Ye("div",{class:"uppy-StatusBar-status"},Ye("div",{class:"uppy-StatusBar-statusPrimary"},e,": ",t.totalProgress,"%"),!t.isAllPaused&&Ye(er,t)))},nr=function(t){t.totalProgress;var e=t.i18n;return Ye("div",{class:"uppy-StatusBar-content",role:"status",title:e("complete")},Ye("svg",{"aria-hidden":"true",class:"uppy-StatusBar-statusIndicator UppyIcon",width:"18",height:"17",viewBox:"0 0 23 17"},Ye("path",{d:"M8.944 17L0 7.865l2.555-2.61 6.39 6.525L20.41 0 23 2.645z"})),e("complete"))},or=function(t){var e=t.error,r=(t.retryAll,t.hideRetryButton),n=t.i18n;return Ye("div",{class:"uppy-StatusBar-content",role:"alert"},Ye("span",{class:"uppy-StatusBar-contentPadding"},n("uploadFailed"),"."),!r&&Ye("span",{class:"uppy-StatusBar-contentPadding"},n("pleasePressRetry")),Ye("span",{class:"uppy-StatusBar-details","aria-label":e,"data-microtip-position":"top","data-microtip-size":"large",role:"tooltip"},"?"))},ir=function(t){var e,r=(e=t,{hours:Math.floor(e/3600)%24,minutes:Math.floor(e/60)%60,seconds:Math.floor(e%60)}),n=r.hours?r.hours+"h ":"",o=r.hours?("0"+r.minutes).substr(-2):r.minutes;return n+(o?o+"m ":"")+(o?("0"+r.seconds).substr(-2):r.seconds)+"s"},sr=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ar=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.id=o.opts.id||"StatusBar",o.title="StatusBar",o.type="progressindicator";var i={strings:{uploading:"Uploading",complete:"Complete",uploadFailed:"Upload failed",pleasePressRetry:"Please press Retry to upload again",paused:"Paused",error:"Error",retry:"Retry",cancel:"Cancel",pressToRetry:"Press to retry",retryUpload:"Retry upload",resumeUpload:"Resume upload",cancelUpload:"Cancel upload",pauseUpload:"Pause upload",filesUploadedOfTotal:{0:"%{complete} of %{smart_count} file uploaded",1:"%{complete} of %{smart_count} files uploaded"},dataUploadedOfTotal:"%{complete} of %{total}",xTimeLeft:"%{time} left",uploadXFiles:{0:"Upload %{smart_count} file",1:"Upload %{smart_count} files"},uploadXNewFiles:{0:"Upload +%{smart_count} file",1:"Upload +%{smart_count} files"}}},s={target:"body",hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeCancelButtons:!1,showProgressDetails:!1,locale:i,hideAfterFinish:!0};return o.opts=sr({},s,n),o.locale=sr({},i,o.opts.locale),o.locale.strings=sr({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.startUpload=o.startUpload.bind(o),o.render=o.render.bind(o),o.install=o.install.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.getTotalSpeed=function(t){var e=0;return t.forEach(function(t){e+=function(t){if(!t.bytesUploaded)return 0;var e=new Date-t.uploadStarted;return t.bytesUploaded/(e/1e3)}(t.progress)}),e},e.prototype.getTotalETA=function(t){var e=this.getTotalSpeed(t);if(0===e)return 0;var r=t.reduce(function(t,e){return t+((r=e.progress).bytesTotal-r.bytesUploaded);var r},0);return Math.round(r/e*10)/10},e.prototype.startUpload=function(){var t=this;return this.uppy.upload().catch(function(e){t.uppy.log(e.stack||e.message||e)})},e.prototype.getUploadingState=function(t,e,r){if(t)return qe;if(e)return Ge;for(var n=He,o=Object.keys(r),i=0;i<o.length;i++){var s=r[o[i]].progress;if(s.uploadStarted&&!s.uploadComplete)return Xe;s.preprocess&&n!==Xe&&(n=We),s.postprocess&&n!==Xe&&n!==We&&(n=Ve)}return n},e.prototype.render=function(t){var e=t.files,r=Object.keys(e).filter(function(t){return e[t].progress.uploadStarted}),n=Object.keys(e).filter(function(t){return!e[t].progress.uploadStarted&&!e[t].progress.preprocess&&!e[t].progress.postprocess}),o=Object.keys(e).filter(function(t){return e[t].progress.uploadComplete}),i=Object.keys(e).filter(function(t){return e[t].error}),s=Object.keys(e).filter(function(t){return!e[t].progress.uploadComplete&&e[t].progress.uploadStarted&&!e[t].isPaused}),a=Object.keys(e).filter(function(t){return e[t].progress.uploadStarted||e[t].progress.preprocess||e[t].progress.postprocess}),l=Object.keys(e).filter(function(t){return e[t].progress.preprocess||e[t].progress.postprocess}),u=s.map(function(t){return e[t]}),p=F(this.getTotalSpeed(u)),c=ir(this.getTotalETA(u)),h=0,d=0;u.forEach(function(t){h+=t.progress.bytesTotal||0,d+=t.progress.bytesUploaded||0}),h=F(h),d=F(d);var f=r.length>0,y=100===t.totalProgress&&o.length===Object.keys(e).length&&0===l.length,g=f&&i.length===r.length,m=0===s.length&&!y&&!g&&r.length>0,v=t.capabilities.resumableUploads||!1;return function(t){var e=(t=t||{}).uploadState,r=t.totalProgress,n=void 0,o=void 0;if(e===We||e===Ve){var i=function(t){var e=[];Object.keys(t).forEach(function(r){var n=t[r].progress;n.preprocess&&e.push(n.preprocess),n.postprocess&&e.push(n.postprocess)});var r=e[0];return{mode:r.mode,message:r.message,value:e.filter(function(t){return"determinate"===t.mode}).reduce(function(t,e,r,n){return t+e.value/n.length},0)}}(t.files);"determinate"===(n=i.mode)&&(r=100*i.value),o=tr(i)}else e===Ge?o=nr(t):e===Xe?o=rr(t):e===qe&&(r=void 0,o=or(t));var s="number"==typeof r?r:100,a=e===He&&t.hideUploadButton||e===He&&!t.newFiles>0||e===Ge&&t.hideAfterFinish,l="uppy-StatusBar-progress\n                           "+(n?"is-"+n:""),u=Ht("uppy","uppy-StatusBar","is-"+e,{"uppy-StatusBar--detailedProgress":t.showProgressDetails});return Ye("div",{class:u,"aria-hidden":a},Ye("div",{class:l,style:{width:s+"%"},role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":r}),o,Ye("div",{class:"uppy-StatusBar-actions"},t.newFiles&&!t.hideUploadButton?Ye(Je,Ke({},t,{uploadState:e})):null,t.error&&!t.hideRetryButton?Ye($e,t):null,t.hidePauseResumeCancelButtons||e===He||e===Ge?null:Ye(Qe,t)))}({error:t.error,uploadState:this.getUploadingState(g,y,t.files||{}),totalProgress:t.totalProgress,totalSize:h,totalUploadedSize:d,uploadStarted:r.length,isAllComplete:y,isAllPaused:m,isAllErrored:g,isUploadStarted:f,complete:o.length,newFiles:n.length,numUploads:a.length,totalSpeed:p,totalETA:c,files:t.files,i18n:this.i18n,pauseAll:this.uppy.pauseAll,resumeAll:this.uppy.resumeAll,retryAll:this.uppy.retryAll,cancelAll:this.uppy.cancelAll,startUpload:this.startUpload,resumableUploads:v,showProgressDetails:this.opts.showProgressDetails,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeCancelButtons:this.opts.hidePauseResumeCancelButtons,hideAfterFinish:this.opts.hideAfterFinish})},e.prototype.install=function(){var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.unmount()},e}(Y.Plugin),lr=function(t,e,r){var n=t.split(",")[1],o=e.mimeType||t.split(",")[0].split(":")[1].split(";")[0];null==o&&(o="plain/text");for(var i=atob(n),s=[],a=0;a<i.length;a++)s.push(i.charCodeAt(a));return r?new File([new Uint8Array(s)],e.name||"",{type:o}):new Blob([new Uint8Array(s)],{type:o})},ur=function(t){if(!t)return!1;var e=t.split("/")[1];return!!/^(jpeg|gif|png|svg|svg\+xml|bmp)$/.test(e)},pr=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},cr=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.type="thumbnail",o.id=o.opts.id||"ThumbnailGenerator",o.title="Thumbnail Generator",o.queue=[],o.queueProcessing=!1,o.opts=pr({},{thumbnailWidth:200},n),o.addToQueue=o.addToQueue.bind(o),o.onRestored=o.onRestored.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.createThumbnail=function(t,e){var r=this,n=URL.createObjectURL(t.data);return new Promise(function(t,e){var r=new Image;r.src=n,r.onload=function(){URL.revokeObjectURL(n),t(r)},r.onerror=function(){URL.revokeObjectURL(n),e(new Error("Could not create thumbnail"))}}).then(function(t){var n=r.getProportionalHeight(t,e),o=r.resizeImage(t,e,n);return r.canvasToBlob(o,"image/png")}).then(function(t){return URL.createObjectURL(t)})},e.prototype.protect=function(t){var e=t.width/t.height,r=Math.floor(Math.sqrt(5e6*e)),n=Math.floor(5e6/Math.sqrt(5e6*e));if(r>4096&&(r=4096,n=Math.round(r/e)),n>4096&&(n=4096,r=Math.round(e*n)),t.width>r){var o=document.createElement("canvas");o.width=r,o.height=n,o.getContext("2d").drawImage(t,0,0,r,n),t=o}return t},e.prototype.resizeImage=function(t,e,r){t=this.protect(t);var n=Math.ceil(Math.log(t.width/e)*Math.LOG2E);n<1&&(n=1);for(var o=e*Math.pow(2,n-1),i=r*Math.pow(2,n-1);n--;){var s=document.createElement("canvas");s.width=o,s.height=i,s.getContext("2d").drawImage(t,0,0,o,i),t=s,o=Math.round(o/2),i=Math.round(i/2)}return t},e.prototype.canvasToBlob=function(t,e,r){return t.toBlob?new Promise(function(n){t.toBlob(n,e,r)}):Promise.resolve().then(function(){return lr(t.toDataURL(e,r),{})})},e.prototype.getProportionalHeight=function(t,e){var r=t.width/t.height;return Math.round(e/r)},e.prototype.setPreviewURL=function(t,e){this.uppy.setFileState(t,{preview:e})},e.prototype.addToQueue=function(t){this.queue.push(t),!1===this.queueProcessing&&this.processQueue()},e.prototype.processQueue=function(){var t=this;if(this.queueProcessing=!0,this.queue.length>0){var e=this.queue.shift();return this.requestThumbnail(e).catch(function(t){}).then(function(){return t.processQueue()})}this.queueProcessing=!1},e.prototype.requestThumbnail=function(t){var e=this;return ur(t.type)&&!t.isRemote?this.createThumbnail(t,this.opts.thumbnailWidth).then(function(r){e.setPreviewURL(t.id,r)}).catch(function(t){console.warn(t.stack||t.message)}):Promise.resolve()},e.prototype.onRestored=function(){var t=this;Object.keys(this.uppy.getState().files).forEach(function(e){var r=t.uppy.getFile(e);r.isRestored&&(r.preview&&!/^blob:/.test(r.preview)||t.addToQueue(r))})},e.prototype.install=function(){this.uppy.on("file-added",this.addToQueue),this.uppy.on("restored",this.onRestored)},e.prototype.uninstall=function(){this.uppy.off("file-added",this.addToQueue),this.uppy.off("restored",this.onRestored)},e}(Y.Plugin),hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dr=function(t){if("string"==typeof t){var e=[].slice.call(document.querySelectorAll(t));return e.length>0?e:null}if("object"===(void 0===t?"undefined":hr(t))&&D(t))return[t]},fr=function(t){return Array.prototype.slice.call(t||[],0)},yr=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},gr=Yt,mr=['a[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','area[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])',"input:not([disabled]):not([inert]):not([aria-hidden])","select:not([disabled]):not([inert]):not([aria-hidden])","textarea:not([disabled]):not([inert]):not([aria-hidden])","button:not([disabled]):not([inert]):not([aria-hidden])",'iframe:not([tabindex^="-"]):not([inert]):not([aria-hidden])','object:not([tabindex^="-"]):not([inert]):not([aria-hidden])','embed:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[contenteditable]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[tabindex]:not([tabindex^="-"]):not([inert]):not([aria-hidden])'],vr=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.id=o.opts.id||"Dashboard",o.title="Dashboard",o.type="orchestrator",o.modalName="uppy-Dashboard";var i={strings:{selectToUpload:"Select files to upload",closeModal:"Close Modal",upload:"Upload",importFrom:"Import from %{name}",addingMoreFiles:"Adding more files",addMoreFiles:"Add more files",dashboardWindowTitle:"Uppy Dashboard Window (Press escape to close)",dashboardTitle:"Uppy Dashboard",copyLinkToClipboardSuccess:"Link copied to clipboard",copyLinkToClipboardFallback:"Copy the URL below",copyLink:"Copy link",fileSource:"File source: %{name}",done:"Done",back:"Back",name:"Name",removeFile:"Remove file",editFile:"Edit file",editing:"Editing %{file}",edit:"Edit",finishEditingFile:"Finish editing file",saveChanges:"Save changes",cancel:"Cancel",localDisk:"Local Disk",myDevice:"My Device",dropPasteImport:"Drop files here, paste, %{browse} or import from",dropPaste:"Drop files here, paste or %{browse}",browse:"browse",fileProgress:"File progress: upload speed and ETA",numberOfSelectedFiles:"Number of selected files",uploadAllNewFiles:"Upload all new files",emptyFolderAdded:"No files were added from empty folder",uploadComplete:"Upload complete",resumeUpload:"Resume upload",pauseUpload:"Pause upload",retryUpload:"Retry upload",xFilesSelected:{0:"%{smart_count} file selected",1:"%{smart_count} files selected"},uploadXFiles:{0:"Upload %{smart_count} file",1:"Upload %{smart_count} files"},uploadXNewFiles:{0:"Upload +%{smart_count} file",1:"Upload +%{smart_count} files"},folderAdded:{0:"Added %{smart_count} file from %{folder}",1:"Added %{smart_count} files from %{folder}"}}},s={target:"body",metaFields:[],trigger:"#uppy-select-files",inline:!1,width:750,height:550,thumbnailWidth:280,defaultTabIcon:gr,showLinkToFileUploadResult:!0,showProgressDetails:!1,hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeCancelButtons:!1,hideProgressAfterFinish:!1,note:null,closeModalOnClickOutside:!1,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,proudlyDisplayPoweredByUppy:!0,onRequestCloseModal:function(){return o.closeModal()},locale:i,browserBackButtonClose:!1};return o.opts=yr({},s,n),o.locale=yr({},i,o.opts.locale),o.locale.strings=yr({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.i18nArray=o.translator.translateArray.bind(o.translator),o.openModal=o.openModal.bind(o),o.closeModal=o.closeModal.bind(o),o.requestCloseModal=o.requestCloseModal.bind(o),o.isModalOpen=o.isModalOpen.bind(o),o.addTarget=o.addTarget.bind(o),o.removeTarget=o.removeTarget.bind(o),o.hideAllPanels=o.hideAllPanels.bind(o),o.showPanel=o.showPanel.bind(o),o.getFocusableNodes=o.getFocusableNodes.bind(o),o.setFocusToFirstNode=o.setFocusToFirstNode.bind(o),o.handlePopState=o.handlePopState.bind(o),o.maintainFocus=o.maintainFocus.bind(o),o.initEvents=o.initEvents.bind(o),o.onKeydown=o.onKeydown.bind(o),o.handleClickOutside=o.handleClickOutside.bind(o),o.toggleFileCard=o.toggleFileCard.bind(o),o.toggleAddFilesPanel=o.toggleAddFilesPanel.bind(o),o.handleDrop=o.handleDrop.bind(o),o.handlePaste=o.handlePaste.bind(o),o.handleInputChange=o.handleInputChange.bind(o),o.updateDashboardElWidth=o.updateDashboardElWidth.bind(o),o.throttledUpdateDashboardElWidth=et(o.updateDashboardElWidth,500,{leading:!0,trailing:!0}),o.render=o.render.bind(o),o.install=o.install.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.removeTarget=function(t){var e=this.getPluginState().targets.filter(function(e){return e.id!==t.id});this.setPluginState({targets:e})},e.prototype.addTarget=function(t){var e=t.id||t.constructor.name,r=t.title||e,n=t.type;if("acquirer"===n||"progressindicator"===n||"presenter"===n){var o={id:e,name:r,type:n},i=this.getPluginState().targets.slice();return i.push(o),this.setPluginState({targets:i}),this.el}this.uppy.log("Dashboard: Modal can only be used by plugins of types: acquirer, progressindicator, presenter")},e.prototype.hideAllPanels=function(){this.setPluginState({activePanel:!1,showAddFilesPanel:!1})},e.prototype.showPanel=function(t){var e=this.getPluginState().targets.filter(function(e){return"acquirer"===e.type&&e.id===t})[0];this.setPluginState({activePanel:e})},e.prototype.requestCloseModal=function(){if(this.opts.onRequestCloseModal)return this.opts.onRequestCloseModal();this.closeModal()},e.prototype.getFocusableNodes=function(){var t=this.el.querySelectorAll(mr);return console.log(Object.keys(t).map(function(e){return t[e]})),Object.keys(t).map(function(e){return t[e]})},e.prototype.setFocusToFirstNode=function(){var t=this.getFocusableNodes();t.length&&t[0].focus()},e.prototype.updateBrowserHistory=function(){var t;history.state&&history.state[this.modalName]||history.pushState(((t={})[this.modalName]=!0,t),""),window.addEventListener("popstate",this.handlePopState,!1)},e.prototype.handlePopState=function(t){t.state&&t.state[this.modalName]||this.closeModal({manualClose:!1}),!this.isModalOpen()&&t.state&&t.state[this.modalName]&&history.go(-1)},e.prototype.setFocusToBrowse=function(){var t=this.el.querySelector(".uppy-Dashboard-browse");t&&t.focus()},e.prototype.maintainFocus=function(t){var e=this.getFocusableNodes(),r=e.indexOf(document.activeElement);t.shiftKey&&0===r&&(e[e.length-1].focus(),t.preventDefault()),t.shiftKey||r!==e.length-1||(e[0].focus(),t.preventDefault())},e.prototype.openModal=function(){var t=this;this.savedScrollPosition=window.scrollY,this.savedActiveElement=document.activeElement,this.opts.disablePageScrollWhenModalOpen&&document.body.classList.add("uppy-Dashboard-isFixed"),this.opts.animateOpenClose&&this.getPluginState().isClosing?this.el.addEventListener("animationend",function e(){t.setPluginState({isHidden:!1}),t.el.removeEventListener("animationend",e,!1)},!1):this.setPluginState({isHidden:!1}),this.opts.browserBackButtonClose&&this.updateBrowserHistory(),document.addEventListener("keydown",this.onKeydown),this.rerender(this.uppy.getState()),this.updateDashboardElWidth(),this.setFocusToBrowse()},e.prototype.closeModal=function(){var t=this,e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).manualClose,r=void 0===e||e;this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.opts.animateOpenClose?(this.setPluginState({isClosing:!0}),this.el.addEventListener("animationend",function e(){t.setPluginState({isHidden:!0,isClosing:!1}),t.el.removeEventListener("animationend",e,!1)},!1)):this.setPluginState({isHidden:!0}),document.removeEventListener("keydown",this.onKeydown),this.savedActiveElement.focus(),r&&this.opts.browserBackButtonClose&&history.state&&history.state[this.modalName]&&history.go(-1)},e.prototype.isModalOpen=function(){return!this.getPluginState().isHidden||!1},e.prototype.onKeydown=function(t){27===t.keyCode&&this.requestCloseModal(t),9===t.keyCode&&this.maintainFocus(t)},e.prototype.handleClickOutside=function(){this.opts.closeModalOnClickOutside&&this.requestCloseModal()},e.prototype.handlePaste=function(t){var e=this;fr(t.clipboardData.items).forEach(function(t){if("file"===t.kind){var r=t.getAsFile();if(!r)return e.uppy.log("[Dashboard] File pasted, but the file blob is empty"),void e.uppy.info("Error pasting file","error");e.uppy.log("[Dashboard] File pasted");try{e.uppy.addFile({source:e.id,name:t.name,type:t.type,data:r})}catch(t){}}})},e.prototype.handleInputChange=function(t){var e=this;t.preventDefault(),fr(t.target.files).forEach(function(t){try{e.uppy.addFile({source:e.id,name:t.name,type:t.type,data:t})}catch(t){}})},e.prototype.initEvents=function(){var t=this,e=dr(this.opts.trigger);!this.opts.inline&&e&&e.forEach(function(e){return e.addEventListener("click",t.openModal)}),this.opts.inline||e||this.uppy.log("Dashboard modal trigger not found. Make sure `trigger` is set in Dashboard options unless you are planning to call openModal() method yourself"),this.removeDragDropListener=Nt(this.el,function(e){t.handleDrop(e)}),this.updateDashboardElWidth(),window.addEventListener("resize",this.throttledUpdateDashboardElWidth),this.uppy.on("plugin-remove",this.removeTarget),this.uppy.on("file-added",function(e){return t.toggleAddFilesPanel(!1)})},e.prototype.removeEvents=function(){var t=this,e=dr(this.opts.trigger);!this.opts.inline&&e&&e.forEach(function(e){return e.removeEventListener("click",t.openModal)}),this.removeDragDropListener(),window.removeEventListener("resize",this.throttledUpdateDashboardElWidth),window.removeEventListener("popstate",this.handlePopState,!1),this.uppy.off("plugin-remove",this.removeTarget),this.uppy.off("file-added",function(e){return t.toggleAddFilesPanel(!1)})},e.prototype.updateDashboardElWidth=function(){var t=this.el.querySelector(".uppy-Dashboard-inner");t&&(this.uppy.log("Dashboard width: "+t.offsetWidth),this.setPluginState({containerWidth:t.offsetWidth}))},e.prototype.toggleFileCard=function(t){this.setPluginState({fileCardFor:t||!1})},e.prototype.toggleAddFilesPanel=function(t){this.setPluginState({showAddFilesPanel:t})},e.prototype.handleDrop=function(t){var e=this;this.uppy.log("[Dashboard] Files were dropped"),t.forEach(function(t){try{e.uppy.addFile({source:e.id,name:t.name,type:t.type,data:t})}catch(t){}})},e.prototype.render=function(t){var e=this,r=this.getPluginState(),n=t.files,o=t.capabilities,i=Object.keys(n).filter(function(t){return!n[t].progress.uploadStarted}),s=Object.keys(n).filter(function(t){return!n[t].progress.uploadComplete&&n[t].progress.uploadStarted&&!n[t].isPaused}),a=[];s.forEach(function(t){a.push(n[t])});var l=0,u=0;a.forEach(function(t){l+=t.progress.bytesTotal||0,u+=t.progress.bytesUploaded||0}),l=F(l),u=F(u);var p=function(t){var r=e.uppy.getPlugin(t.id);return yr({},t,{icon:r.icon||e.opts.defaultTabIcon,render:r.render})},c=r.targets.filter(function(t){return"acquirer"===t.type&&function(t){var r=e.uppy.getPlugin(t.id);return"function"!=typeof r.isSupported||r.isSupported()}(t)}).map(p),h=r.targets.filter(function(t){return"progressindicator"===t.type}).map(p);return je({state:t,modal:r,newFiles:i,files:n,totalFileCount:Object.keys(n).length,totalProgress:t.totalProgress,acquirers:c,activePanel:r.activePanel,animateOpenClose:this.opts.animateOpenClose,isClosing:r.isClosing,getPlugin:this.uppy.getPlugin,progressindicators:h,autoProceed:this.uppy.opts.autoProceed,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeCancelButtons:this.opts.hidePauseResumeCancelButtons,id:this.id,closeModal:this.requestCloseModal,handleClickOutside:this.handleClickOutside,handleInputChange:this.handleInputChange,handlePaste:this.handlePaste,inline:this.opts.inline,showPanel:this.showPanel,hideAllPanels:this.hideAllPanels,log:this.uppy.log,i18n:this.i18n,i18nArray:this.i18nArray,addFile:this.uppy.addFile,removeFile:this.uppy.removeFile,info:this.uppy.info,note:this.opts.note,metaFields:r.metaFields,resumableUploads:o.resumableUploads||!1,bundled:o.bundled||!1,startUpload:function(t){e.uppy.upload().catch(function(t){e.uppy.log(t.stack||t.message||t)})},pauseUpload:this.uppy.pauseResume,retryUpload:this.uppy.retryUpload,cancelUpload:function(t){e.uppy.emit("upload-cancel",t),e.uppy.removeFile(t)},cancelAll:this.uppy.cancelAll,fileCardFor:r.fileCardFor,toggleFileCard:this.toggleFileCard,toggleAddFilesPanel:this.toggleAddFilesPanel,showAddFilesPanel:r.showAddFilesPanel,saveFileCard:function(t,r){e.uppy.setFileMeta(r,t),e.toggleFileCard()},updateDashboardElWidth:this.updateDashboardElWidth,width:this.opts.width,height:this.opts.height,showLinkToFileUploadResult:this.opts.showLinkToFileUploadResult,proudlyDisplayPoweredByUppy:this.opts.proudlyDisplayPoweredByUppy,currentWidth:r.containerWidth,isWide:r.containerWidth>400,containerWidth:r.containerWidth,isTargetDOMEl:this.isTargetDOMEl,allowedFileTypes:this.uppy.opts.restrictions.allowedFileTypes,maxNumberOfFiles:this.uppy.opts.restrictions.maxNumberOfFiles})},e.prototype.discoverProviderPlugins=function(){var t=this;this.uppy.iteratePlugins(function(e){e&&!e.target&&e.opts&&e.opts.target===t.constructor&&t.addTarget(e)})},e.prototype.install=function(){var t=this;this.setPluginState({isHidden:!0,showFileCard:!1,showAddFilesPanel:!1,activePanel:!1,metaFields:this.opts.metaFields,targets:[]});var e=this.opts.target;e&&this.mount(e,this),(this.opts.plugins||[]).forEach(function(e){var r=t.uppy.getPlugin(e);r&&r.mount(t,r)}),this.opts.disableStatusBar||this.uppy.use(ar,{id:this.id+":StatusBar",target:this,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeCancelButtons:this.opts.hidePauseResumeCancelButtons,showProgressDetails:this.opts.showProgressDetails,hideAfterFinish:this.opts.hideProgressAfterFinish,locale:this.opts.locale}),this.opts.disableInformer||this.uppy.use(ze,{id:this.id+":Informer",target:this}),this.opts.disableThumbnailGenerator||this.uppy.use(cr,{id:this.id+":ThumbnailGenerator",thumbnailWidth:this.opts.thumbnailWidth}),this.discoverProviderPlugins(),this.initEvents()},e.prototype.uninstall=function(){var t=this;if(!this.opts.disableInformer){var e=this.uppy.getPlugin(this.id+":Informer");e&&this.uppy.removePlugin(e)}if(!this.opts.disableStatusBar){var r=this.uppy.getPlugin(this.id+":StatusBar");r&&this.uppy.removePlugin(r)}if(!this.opts.disableThumbnailGenerator){var n=this.uppy.getPlugin(this.id+":ThumbnailGenerator");n&&this.uppy.removePlugin(n)}(this.opts.plugins||[]).forEach(function(e){var r=t.uppy.getPlugin(e);r&&r.unmount()}),this.unmount(),this.removeEvents()},e}(Y.Plugin),br=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wr=Y.Plugin,Sr=A.h,Pr=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.type="acquirer",o.id=o.opts.id||"DragDrop",o.title="Drag & Drop";var i={strings:{dropHereOr:"Drop files here or %{browse}",browse:"browse"}},s={target:null,inputName:"files[]",width:"100%",height:"100%",note:null,locale:i};return o.opts=br({},s,n),o.isDragDropSupported=o.checkDragDropSupport(),o.locale=br({},i,o.opts.locale),o.locale.strings=br({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.i18nArray=o.translator.translateArray.bind(o.translator),o.handleDrop=o.handleDrop.bind(o),o.handleInputChange=o.handleInputChange.bind(o),o.checkDragDropSupport=o.checkDragDropSupport.bind(o),o.render=o.render.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.checkDragDropSupport=function(){var t=document.createElement("div");return"draggable"in t&&"ondragstart"in t&&"ondrop"in t&&"FormData"in window&&"FileReader"in window},e.prototype.handleDrop=function(t){var e=this;this.uppy.log("[DragDrop] Files dropped"),t.forEach(function(t){try{e.uppy.addFile({source:e.id,name:t.name,type:t.type,data:t})}catch(t){}})},e.prototype.handleInputChange=function(t){var e=this;this.uppy.log("[DragDrop] Files selected through input"),fr(t.target.files).forEach(function(t){try{e.uppy.addFile({source:e.id,name:t.name,type:t.type,data:t})}catch(t){}})},e.prototype.render=function(t){var e=this,r="uppy-Root uppy-DragDrop-container "+(this.isDragDropSupported?"uppy-DragDrop--is-dragdrop-supported":""),n={width:this.opts.width,height:this.opts.height},o=this.uppy.opts.restrictions;return Sr("div",{class:r,style:n},Sr("div",{class:"uppy-DragDrop-inner"},Sr("svg",{"aria-hidden":"true",class:"UppyIcon uppy-DragDrop-arrow",width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},Sr("path",{d:"M11 10V0H5v10H2l6 6 6-6h-3zm0 0","fill-rule":"evenodd"})),Sr("label",{class:"uppy-DragDrop-label"},Sr("input",{style:{width:"0.1px",height:"0.1px",opacity:0,overflow:"hidden",position:"absolute",zIndex:-1},class:"uppy-DragDrop-input",type:"file",name:this.opts.inputName,multiple:1!==o.maxNumberOfFiles,accept:o.allowedFileTypes,ref:function(t){e.input=t},onchange:this.handleInputChange,value:""}),this.i18nArray("dropHereOr",{browse:Sr("span",{class:"uppy-DragDrop-dragText"},this.i18n("browse"))})),Sr("span",{class:"uppy-DragDrop-note"},this.opts.note)))},e.prototype.install=function(){var t=this,e=this.opts.target;e&&this.mount(e,this),this.removeDragDropListener=Nt(this.el,function(e){t.handleDrop(e),t.uppy.log(e)})},e.prototype.uninstall=function(){this.unmount(),this.removeDragDropListener()},e}(wr),_r=A.h,Er=function(t){return _r("div",{class:"uppy-Provider-loading"},_r("span",null,"Loading..."))};function kr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Cr(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function Tr(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Ur=A.h,Or=A.Component,Fr=function(t){function e(){return kr(this,e),Cr(this,t.apply(this,arguments))}return Tr(e,t),e.prototype.componentDidMount=function(){var t=this;setTimeout(function(){t.connectButton&&t.connectButton.focus({preventScroll:!0})},150)},e.prototype.render=function(){var t=this;return Ur("div",{class:"uppy-Provider-auth"},Ur("div",{class:"uppy-Provider-authIcon"},this.props.pluginIcon()),Ur("h1",{class:"uppy-Provider-authTitle"},"Please authenticate with ",Ur("span",{class:"uppy-Provider-authTitleName"},this.props.pluginName),Ur("br",null)," to select files"),Ur("button",{type:"button",class:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Provider-authBtn",onclick:this.props.handleAuth,ref:function(e){t.connectButton=e}},"Connect to ",this.props.pluginName),this.props.demo&&Ur("button",{class:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Provider-authBtn",onclick:this.props.handleDemoAuth},"Proceed with Demo Account"))},e}(Or),Ar=function(t){function e(){return kr(this,e),Cr(this,t.apply(this,arguments))}return Tr(e,t),e.prototype.componentDidMount=function(){this.props.checkAuth()},e.prototype.render=function(){return this.props.checkAuthInProgress?Ur(Er,null):Ur(Fr,this.props)},e}(Or),xr=A.h,Dr=A.h,Rr=function(t){function e(r){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r));return n.isEmpty=!0,n.handleKeyPress=n.handleKeyPress.bind(n),n.handleClear=n.handleClear.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleKeyPress=function(t){if(13===t.keyCode)return t.stopPropagation(),void t.preventDefault();this.isEmpty=!this.input.value.length>0,this.props.filterQuery(t)},e.prototype.handleClear=function(t){this.input.value="",this.props.filterQuery()},e.prototype.render=function(){var t=this;return Dr("div",{class:"uppy-u-reset uppy-ProviderBrowser-search"},Dr("svg",{class:"UppyIcon uppy-ProviderBrowser-searchIcon",viewBox:"0 0 100 100"},Dr("path",{d:"M87.533 80.03L62.942 55.439c3.324-4.587 5.312-10.207 5.312-16.295 0-.312-.043-.611-.092-.908.05-.301.093-.605.093-.922 0-15.36-12.497-27.857-27.857-27.857-.273 0-.536.043-.799.08-.265-.037-.526-.08-.799-.08-15.361 0-27.858 12.497-27.858 27.857 0 .312.042.611.092.909a5.466 5.466 0 0 0-.093.921c0 15.36 12.496 27.858 27.857 27.858.273 0 .535-.043.8-.081.263.038.524.081.798.081 5.208 0 10.071-1.464 14.245-3.963L79.582 87.98a5.603 5.603 0 0 0 3.976 1.647 5.621 5.621 0 0 0 3.975-9.597zM39.598 55.838c-.265-.038-.526-.081-.8-.081-9.16 0-16.612-7.452-16.612-16.612 0-.312-.042-.611-.092-.908.051-.301.093-.605.093-.922 0-9.16 7.453-16.612 16.613-16.612.272 0 .534-.042.799-.079.263.037.525.079.799.079 9.16 0 16.612 7.452 16.612 16.612 0 .312.043.611.092.909-.05.301-.094.604-.094.921 0 9.16-7.452 16.612-16.612 16.612-.274 0-.536.043-.798.081z"})),Dr("input",{class:"uppy-u-reset uppy-ProviderBrowser-searchInput",type:"text",placeholder:"Filter","aria-label":"Filter",onkeyup:this.handleKeyPress,onkeydown:this.handleKeyPress,onkeypress:this.handleKeyPress,value:this.props.filterInput,ref:function(e){t.input=e}}),!this.isEmpty&&Dr("button",{class:"uppy-u-reset uppy-ProviderBrowser-searchClose",type:"button",onclick:this.handleClear},Dr("svg",{class:"UppyIcon",viewBox:"0 0 19 19"},Dr("path",{d:"M17.318 17.232L9.94 9.854 9.586 9.5l-.354.354-7.378 7.378h.707l-.62-.62v.706L9.318 9.94l.354-.354-.354-.354L1.94 1.854v.707l.62-.62h-.706l7.378 7.378.354.354.354-.354 7.378-7.378h-.707l.622.62v-.706L9.854 9.232l-.354.354.354.354 7.378 7.378.708-.707-7.38-7.378v.708l7.38-7.38.353-.353-.353-.353-.622-.622-.353-.353-.354.352-7.378 7.38h.708L2.56 1.23 2.208.88l-.353.353-.622.62-.353.355.352.353 7.38 7.38v-.708l-7.38 7.38-.353.353.352.353.622.622.353.353.354-.353 7.38-7.38h-.708l7.38 7.38z"}))))},e}(A.Component),Ir=A.h,Br=function(t){return Ir("div",{class:"uppy-ProviderBrowser-footer"},Ir("button",{class:"uppy-u-reset uppy-c-btn uppy-c-btn-primary",onclick:t.done},t.i18n("selectXFiles",{smart_count:t.selected})),Ir("button",{class:"uppy-u-reset uppy-c-btn uppy-c-btn-link",onclick:t.cancel},t.i18n("cancel")))},jr=A.h,Lr=function(t){var e=function(t){13===t.keyCode&&(t.stopPropagation(),t.preventDefault())},r=t.getItemIcon();return jr("li",{class:"uppy-ProviderBrowserItem"+(t.isChecked?" uppy-ProviderBrowserItem--selected":"")+("video"===r?" uppy-ProviderBrowserItem--noPreview":"")},jr("div",{class:"uppy-ProviderBrowserItem-checkbox"},jr("input",{type:"checkbox",role:"option",tabindex:0,"aria-label":"Select "+t.title,id:t.id,checked:t.isChecked,disabled:t.isDisabled,onchange:t.handleClick,onkeyup:e,onkeydown:e,onkeypress:e}),jr("label",{for:t.id,onclick:t.handleClick})),jr("button",{type:"button",class:"uppy-ProviderBrowserItem-inner","aria-label":"Select "+t.title,tabindex:0,onclick:function(e){if(e.preventDefault(),"folder"===t.type)return t.handleFolderClick(e);t.handleClick(e)}},function(t){if(null!==t)switch(t){case"file":return jr("svg",{"aria-hidden":"true",class:"UppyIcon",width:11,height:14.5,viewBox:"0 0 44 58"},jr("path",{d:"M27.437.517a1 1 0 0 0-.094.03H4.25C2.037.548.217 2.368.217 4.58v48.405c0 2.212 1.82 4.03 4.03 4.03H39.03c2.21 0 4.03-1.818 4.03-4.03V15.61a1 1 0 0 0-.03-.28 1 1 0 0 0 0-.093 1 1 0 0 0-.03-.032 1 1 0 0 0 0-.03 1 1 0 0 0-.032-.063 1 1 0 0 0-.03-.063 1 1 0 0 0-.032 0 1 1 0 0 0-.03-.063 1 1 0 0 0-.032-.03 1 1 0 0 0-.03-.063 1 1 0 0 0-.063-.062l-14.593-14a1 1 0 0 0-.062-.062A1 1 0 0 0 28 .708a1 1 0 0 0-.374-.157 1 1 0 0 0-.156 0 1 1 0 0 0-.03-.03l-.003-.003zM4.25 2.547h22.218v9.97c0 2.21 1.82 4.03 4.03 4.03h10.564v36.438a2.02 2.02 0 0 1-2.032 2.032H4.25c-1.13 0-2.032-.9-2.032-2.032V4.58c0-1.13.902-2.032 2.03-2.032zm24.218 1.345l10.375 9.937.75.718H30.5c-1.13 0-2.032-.9-2.032-2.03V3.89z"}));case"folder":return jr("svg",{"aria-hidden":"true",class:"UppyIcon",style:{width:16,marginRight:3},viewBox:"0 0 276.157 276.157"},jr("path",{d:"M273.08 101.378c-3.3-4.65-8.86-7.32-15.254-7.32h-24.34V67.59c0-10.2-8.3-18.5-18.5-18.5h-85.322c-3.63 0-9.295-2.875-11.436-5.805l-6.386-8.735c-4.982-6.814-15.104-11.954-23.546-11.954H58.73c-9.292 0-18.638 6.608-21.737 15.372l-2.033 5.752c-.958 2.71-4.72 5.37-7.596 5.37H18.5C8.3 49.09 0 57.39 0 67.59v167.07c0 .886.16 1.73.443 2.52.152 3.306 1.18 6.424 3.053 9.064 3.3 4.652 8.86 7.32 15.255 7.32h188.487c11.395 0 23.27-8.425 27.035-19.18l40.677-116.188c2.11-6.035 1.43-12.164-1.87-16.816zM18.5 64.088h8.864c9.295 0 18.64-6.607 21.738-15.37l2.032-5.75c.96-2.712 4.722-5.373 7.597-5.373h29.565c3.63 0 9.295 2.876 11.437 5.806l6.386 8.735c4.982 6.815 15.104 11.954 23.546 11.954h85.322c1.898 0 3.5 1.602 3.5 3.5v26.47H69.34c-11.395 0-23.27 8.423-27.035 19.178L15 191.23V67.59c0-1.898 1.603-3.5 3.5-3.5zm242.29 49.15l-40.676 116.188c-1.674 4.78-7.812 9.135-12.877 9.135H18.75c-1.447 0-2.576-.372-3.02-.997-.442-.625-.422-1.814.057-3.18l40.677-116.19c1.674-4.78 7.812-9.134 12.877-9.134h188.487c1.448 0 2.577.372 3.02.997.443.625.423 1.814-.056 3.18z"}));case"video":return jr("svg",{"aria-hidden":"true",viewBox:"0 0 58 58"},jr("path",{d:"M36.537 28.156l-11-7a1.005 1.005 0 0 0-1.02-.033C24.2 21.3 24 21.635 24 22v14a1 1 0 0 0 1.537.844l11-7a1.002 1.002 0 0 0 0-1.688zM26 34.18V23.82L34.137 29 26 34.18z"}),jr("path",{d:"M57 6H1a1 1 0 0 0-1 1v44a1 1 0 0 0 1 1h56a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1zM10 28H2v-9h8v9zm-8 2h8v9H2v-9zm10 10V8h34v42H12V40zm44-12h-8v-9h8v9zm-8 2h8v9h-8v-9zm8-22v9h-8V8h8zM2 8h8v9H2V8zm0 42v-9h8v9H2zm54 0h-8v-9h8v9z"}));default:return jr("img",{src:t})}}(t.getItemIcon()),t.showTitles&&t.title))},Mr=A.h,Nr=function(t){return t.folders.length||t.files.length?Mr("div",{class:"uppy-ProviderBrowser-body"},Mr("ul",{class:"uppy-ProviderBrowser-list",onscroll:t.handleScroll,role:"listbox","aria-label":"List of files from "+t.title},t.folders.map(function(e){var r=!1,n=t.isChecked(e);return n&&(r=n.loading),Lr({title:t.getItemName(e),id:t.getItemId(e),type:"folder",getItemIcon:function(){return t.getItemIcon(e)},isDisabled:r,isChecked:n,handleFolderClick:function(){return t.handleFolderClick(e)},handleClick:function(r){return t.toggleCheckbox(r,e)},columns:t.columns,showTitles:t.showTitles})}),t.files.map(function(e){return Lr({title:t.getItemName(e),id:t.getItemId(e),type:"file",getItemIcon:function(){return t.getItemIcon(e)},isDisabled:!1,isChecked:t.isChecked(e),handleClick:function(r){return t.toggleCheckbox(r,e)},columns:t.columns,showTitles:t.showTitles})}))):Mr("div",{class:"uppy-Provider-empty"},t.i18n("noFilesFound"))},zr=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},qr=A.h,Hr=function(t){var e=t.folders,r=t.files;""!==t.filterInput&&(e=t.filterItems(t.folders),r=t.filterItems(t.files));var n=t.currentSelection.length;return qr("div",{class:Ht("uppy-ProviderBrowser","uppy-ProviderBrowser-viewType--"+t.viewType)},qr("div",{class:"uppy-ProviderBrowser-header"},qr("div",{class:Ht("uppy-ProviderBrowser-headerBar",!t.showBreadcrumbs&&"uppy-ProviderBrowser-headerBar--simple")},qr("div",{class:"uppy-Provider-breadcrumbsWrap"},qr("div",{class:"uppy-Provider-breadcrumbsIcon"},t.pluginIcon&&t.pluginIcon()),t.showBreadcrumbs&&function(t){return xr("div",{class:"uppy-Provider-breadcrumbs"},t.directories.map(function(e,r){return function(t){return xr("button",{type:"button",onclick:t.getFolder},t.title)}({getFolder:function(){return t.getFolder(e.id)},title:0===r?t.title:e.title})}))}({getFolder:t.getFolder,directories:t.directories,title:t.title})),qr("span",{class:"uppy-ProviderBrowser-user"},t.username),qr("button",{type:"button",onclick:t.logout,class:"uppy-ProviderBrowser-userLogout"},t.i18n("logOut")))),t.showFilter&&qr(Rr,t),qr(Nr,{columns:[{name:"Name",key:"title"}],folders:e,files:r,activeRow:t.isActiveRow,sortByTitle:t.sortByTitle,sortByDate:t.sortByDate,isChecked:t.isChecked,handleFolderClick:t.getNextFolder,toggleCheckbox:t.toggleCheckbox,getItemName:t.getItemName,getItemIcon:t.getItemIcon,handleScroll:t.handleScroll,title:t.title,showTitles:t.showTitles,getItemId:t.getItemId,i18n:t.i18n}),n>0&&qr(Br,zr({selected:n},t)))},Wr=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t};function Xr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var Vr=A.h,Gr=function(t){function e(){return Xr(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.componentWillUnmount=function(){this.props.onUnmount()},e.prototype.render=function(){return this.props.children[0]},e}(A.Component),Kr=function(){function t(e,r){Xr(this,t),this.plugin=e,this.Provider=e[e.id],this.opts=Wr({},{viewType:"list",showTitles:!0,showFilter:!0,showBreadcrumbs:!0},r),this.addFile=this.addFile.bind(this),this.filterItems=this.filterItems.bind(this),this.filterQuery=this.filterQuery.bind(this),this.toggleSearch=this.toggleSearch.bind(this),this.getFolder=this.getFolder.bind(this),this.getNextFolder=this.getNextFolder.bind(this),this.logout=this.logout.bind(this),this.checkAuth=this.checkAuth.bind(this),this.handleAuth=this.handleAuth.bind(this),this.handleDemoAuth=this.handleDemoAuth.bind(this),this.sortByTitle=this.sortByTitle.bind(this),this.sortByDate=this.sortByDate.bind(this),this.isActiveRow=this.isActiveRow.bind(this),this.isChecked=this.isChecked.bind(this),this.toggleCheckbox=this.toggleCheckbox.bind(this),this.handleError=this.handleError.bind(this),this.handleScroll=this.handleScroll.bind(this),this.donePicking=this.donePicking.bind(this),this.cancelPicking=this.cancelPicking.bind(this),this.clearSelection=this.clearSelection.bind(this),this.render=this.render.bind(this),this.clearSelection()}return t.prototype.tearDown=function(){},t.prototype._updateFilesAndFolders=function(t,e,r){var n=this;this.plugin.getItemSubList(t).forEach(function(t){n.plugin.isFolder(t)?r.push(t):e.push(t)}),this.plugin.setPluginState({folders:r,files:e})},t.prototype.checkAuth=function(){var t=this;this.plugin.setPluginState({checkAuthInProgress:!0}),this.Provider.checkAuth().then(function(e){t.plugin.setPluginState({checkAuthInProgress:!1}),t.plugin.onAuth(e)}).catch(function(e){t.plugin.setPluginState({checkAuthInProgress:!1}),t.handleError(e)})},t.prototype.getFolder=function(t,e){var r=this;return this._loaderWrapper(this.Provider.list(t),function(n){var o,i=r.plugin.getPluginState(),s=function(e,r){for(var n=0;n<e.length;n++)if(o=e[n],t===o.id)return n;var o;return-1}(i.directories);o=-1!==s?i.directories.slice(0,s+1):i.directories.concat([{id:t,title:e||r.plugin.getItemName(n)}]),r.username=r.username?r.username:r.plugin.getUsername(n),r._updateFilesAndFolders(n,[],[]),r.plugin.setPluginState({directories:o})},this.handleError)},t.prototype.getNextFolder=function(t){var e=this.plugin.getItemRequestPath(t);this.getFolder(e,this.plugin.getItemName(t)),this.lastCheckbox=void 0},t.prototype.addFile=function(t){var e={id:this.providerFileToId(t),source:this.plugin.id,data:this.plugin.getItemData(t),name:this.plugin.getItemName(t)||this.plugin.getItemId(t),type:this.plugin.getMimeType(t),isRemote:!0,body:{fileId:this.plugin.getItemId(t)},remote:{serverUrl:this.plugin.opts.serverUrl,url:""+this.Provider.fileUrl(this.plugin.getItemRequestPath(t)),body:{fileId:this.plugin.getItemId(t)},providerOptions:this.Provider.opts}},r=G(e);r&&ur(r)&&(e.preview=this.plugin.getItemThumbnailUrl(t)),this.plugin.uppy.log("Adding remote file");try{this.plugin.uppy.addFile(e)}catch(t){}},t.prototype.removeFile=function(t){var e=this.plugin.getPluginState().currentSelection;this.plugin.setPluginState({currentSelection:e.filter(function(e){return e.id!==t})})},t.prototype.logout=function(){var t=this;this.Provider.logout(location.href).then(function(e){e.ok&&t.plugin.setPluginState({authenticated:!1,files:[],folders:[],directories:[]})}).catch(this.handleError)},t.prototype.filterQuery=function(t){var e=this.plugin.getPluginState();this.plugin.setPluginState(Wr({},e,{filterInput:t?t.target.value:""}))},t.prototype.toggleSearch=function(t){var e=this.plugin.getPluginState();this.plugin.setPluginState({isSearchVisible:!e.isSearchVisible,filterInput:""})},t.prototype.filterItems=function(t){var e=this,r=this.plugin.getPluginState();return""===r.filterInput?t:t.filter(function(t){return-1!==e.plugin.getItemName(t).toLowerCase().indexOf(r.filterInput.toLowerCase())})},t.prototype.sortByTitle=function(){var t=this,e=Wr({},this.plugin.getPluginState()),r=e.files,n=e.folders,o=e.sorting,i=r.sort(function(e,r){return"titleDescending"===o?t.plugin.getItemName(r).localeCompare(t.plugin.getItemName(e)):t.plugin.getItemName(e).localeCompare(t.plugin.getItemName(r))}),s=n.sort(function(e,r){return"titleDescending"===o?t.plugin.getItemName(r).localeCompare(t.plugin.getItemName(e)):t.plugin.getItemName(e).localeCompare(t.plugin.getItemName(r))});this.plugin.setPluginState(Wr({},e,{files:i,folders:s,sorting:"titleDescending"===o?"titleAscending":"titleDescending"}))},t.prototype.sortByDate=function(){var t=this,e=Wr({},this.plugin.getPluginState()),r=e.files,n=e.folders,o=e.sorting,i=r.sort(function(e,r){var n=new Date(t.plugin.getItemModifiedDate(e)),i=new Date(t.plugin.getItemModifiedDate(r));return"dateDescending"===o?n>i?-1:n<i?1:0:n>i?1:n<i?-1:0}),s=n.sort(function(e,r){var n=new Date(t.plugin.getItemModifiedDate(e)),i=new Date(t.plugin.getItemModifiedDate(r));return"dateDescending"===o?n>i?-1:n<i?1:0:n>i?1:n<i?-1:0});this.plugin.setPluginState(Wr({},e,{files:i,folders:s,sorting:"dateDescending"===o?"dateAscending":"dateDescending"}))},t.prototype.sortBySize=function(){var t=this,e=Wr({},this.plugin.getPluginState()),r=e.files,n=e.sorting;if(r.length&&this.plugin.getItemData(r[0]).size){var o=r.sort(function(e,r){var o=t.plugin.getItemData(e).size,i=t.plugin.getItemData(r).size;return"sizeDescending"===n?o>i?-1:o<i?1:0:o>i?1:o<i?-1:0});this.plugin.setPluginState(Wr({},e,{files:o,sorting:"sizeDescending"===n?"sizeAscending":"sizeDescending"}))}},t.prototype.isActiveRow=function(t){return this.plugin.getPluginState().activeRow===this.plugin.getItemId(t)},t.prototype.isChecked=function(t){return this.plugin.getPluginState().currentSelection.some(function(e){return e===t})},t.prototype.addFolder=function(t){var e=this,r=this.providerFileToId(t),n=this.plugin.getPluginState(),o=n.selectedFolders||{};if(!(r in o&&o[r].loading))return o[r]={loading:!0,files:[]},this.plugin.setPluginState({selectedFolders:o}),this.Provider.list(this.plugin.getItemRequestPath(t)).then(function(i){var s=[];e.plugin.getItemSubList(i).forEach(function(t){e.plugin.isFolder(t)||(e.addFile(t),s.push(e.providerFileToId(t)))}),(n=e.plugin.getPluginState()).selectedFolders[r]={loading:!1,files:s},e.plugin.setPluginState({selectedFolders:o});var a,l=e.plugin.uppy.getPlugin("Dashboard");a=s.length?l.i18n("folderAdded",{smart_count:s.length,folder:e.plugin.getItemName(t)}):l.i18n("emptyFolderAdded"),e.plugin.uppy.info(a)}).catch(function(t){delete(n=e.plugin.getPluginState()).selectedFolders[r],e.plugin.setPluginState({selectedFolders:n.selectedFolders}),e.handleError(t)})},t.prototype.toggleCheckbox=function(t,e){t.stopPropagation(),t.preventDefault();var r=this.plugin.getPluginState(),n=r.folders,o=r.files,i=this.filterItems(n.concat(o));if(this.lastCheckbox&&t.shiftKey){var s,a=i.indexOf(this.lastCheckbox),l=i.indexOf(e);return s=a<l?i.slice(a,l+1):i.slice(l,a+1),void this.plugin.setPluginState({currentSelection:s})}this.lastCheckbox=e;var u=this.plugin.getPluginState().currentSelection;this.isChecked(e)?this.plugin.setPluginState({currentSelection:u.filter(function(t){return t!==e})}):this.plugin.setPluginState({currentSelection:u.concat([e])})},t.prototype.providerFileToId=function(t){return W({data:this.plugin.getItemData(t),name:this.plugin.getItemName(t)||this.plugin.getItemId(t),type:this.plugin.getMimeType(t)})},t.prototype.handleDemoAuth=function(){var t=this.plugin.getPluginState();this.plugin.setPluginState({},t,{authenticated:!0})},t.prototype.handleAuth=function(){var t=this,e=btoa(JSON.stringify({origin:location.origin})),r=this.Provider.authUrl()+"?state="+e,n=window.open(r,"_blank");window.addEventListener("message",function e(r){t._isOriginAllowed(r.origin,t.plugin.opts.serverPattern)&&r.source===n?(n.close(),window.removeEventListener("message",e),t.Provider.setAuthToken(r.data.token),t._loaderWrapper(t.Provider.checkAuth(),t.plugin.onAuth,t.handleError)):t.plugin.uppy.log("rejecting event from "+r.origin+" vs allowed pattern "+t.plugin.opts.serverPattern)})},t.prototype._isOriginAllowed=function(t,e){var r=function(t){return"string"==typeof t?new RegExp("^"+t+"$"):t instanceof RegExp?t:void 0};return(Array.isArray(e)?e.map(r):[r(e)]).filter(function(t){return null!==t}).some(function(e){return e.test(t)})},t.prototype.handleError=function(t){var e=this.plugin.uppy,r=e.i18n("companionError");e.log(t.toString()),e.info({message:r,details:t.toString()},"error",5e3)},t.prototype.handleScroll=function(t){var e=this,r=t.target.scrollHeight-(t.target.scrollTop+t.target.offsetHeight),n=this.plugin.getNextPagePath?this.plugin.getNextPagePath():null;r<50&&n&&!this._isHandlingScroll&&(this.Provider.list(n).then(function(t){var r=e.plugin.getPluginState(),n=r.files,o=r.folders;e._updateFilesAndFolders(t,n,o)}).catch(this.handleError).then(function(){e._isHandlingScroll=!1}),this._isHandlingScroll=!0)},t.prototype.donePicking=function(){var t=this,e=this.plugin.getPluginState().currentSelection.map(function(e){return t.plugin.isFolder(e)?t.addFolder(e):t.addFile(e)});this._loaderWrapper(Promise.all(e),function(){t.clearSelection();var e=t.plugin.uppy.getPlugin("Dashboard");e&&e.hideAllPanels()},function(){})},t.prototype.cancelPicking=function(){this.clearSelection();var t=this.plugin.uppy.getPlugin("Dashboard");t&&t.hideAllPanels()},t.prototype.clearSelection=function(){this.plugin.setPluginState({currentSelection:[]})},t.prototype._loaderWrapper=function(t,e,r){var n=this;t.then(function(t){n.plugin.setPluginState({loading:!1}),e(t)}).catch(function(t){n.plugin.setPluginState({loading:!1}),r(t)}),this.plugin.setPluginState({loading:!0})},t.prototype.render=function(t){var e=this.plugin.getPluginState(),r=e.authenticated,n=e.checkAuthInProgress;if(e.loading)return Vr(Gr,{onUnmount:this.clearSelection},Vr(Er,null));if(!r)return Vr(Gr,{onUnmount:this.clearSelection},Vr(Ar,{pluginName:this.plugin.title,pluginIcon:this.plugin.icon,demo:this.plugin.opts.demo,checkAuth:this.checkAuth,handleAuth:this.handleAuth,handleDemoAuth:this.handleDemoAuth,checkAuthInProgress:n}));var o=Wr({},this.plugin.getPluginState(),{username:this.username,getNextFolder:this.getNextFolder,getFolder:this.getFolder,filterItems:this.filterItems,filterQuery:this.filterQuery,toggleSearch:this.toggleSearch,sortByTitle:this.sortByTitle,sortByDate:this.sortByDate,logout:this.logout,demo:this.plugin.opts.demo,isActiveRow:this.isActiveRow,isChecked:this.isChecked,toggleCheckbox:this.toggleCheckbox,getItemId:this.plugin.getItemId,getItemName:this.plugin.getItemName,getItemIcon:this.plugin.getItemIcon,handleScroll:this.handleScroll,done:this.donePicking,cancel:this.cancelPicking,title:this.plugin.title,viewType:this.opts.viewType,showTitles:this.opts.showTitles,showFilter:this.opts.showFilter,showBreadcrumbs:this.opts.showBreadcrumbs,pluginIcon:this.plugin.icon,i18n:this.plugin.uppy.i18n});return Vr(Gr,{onUnmount:this.clearSelection},Vr(Hr,o))},t}(),Yr=Y.Plugin,Jr=d.Provider,$r=A.h,Qr=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.id=o.opts.id||"Dropbox",Jr.initPlugin(o,n),o.title=o.opts.title||"Dropbox",o.icon=function(){return $r("svg",{"aria-hidden":"true",fill:"#0060ff",width:"128",height:"118",viewBox:"0 0 128 118"},$r("path",{d:"M38.145.777L1.108 24.96l25.608 20.507 37.344-23.06z"}),$r("path",{d:"M1.108 65.975l37.037 24.183L64.06 68.525l-37.343-23.06zM64.06 68.525l25.917 21.633 37.036-24.183-25.61-20.51z"}),$r("path",{d:"M127.014 24.96L89.977.776 64.06 22.407l37.345 23.06zM64.136 73.18l-25.99 21.567-11.122-7.262v8.142l37.112 22.256 37.114-22.256v-8.142l-11.12 7.262z"}))},o[o.id]=new Jr(r,{serverUrl:o.opts.serverUrl,serverHeaders:o.opts.serverHeaders,provider:"dropbox"}),o.onAuth=o.onAuth.bind(o),o.render=o.render.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.install=function(){this.view=new Kr(this),this.setPluginState({authenticated:!1,files:[],folders:[],directories:[],activeRow:-1,filterInput:"",isSearchVisible:!1});var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.view.tearDown(),this.unmount()},e.prototype.onAuth=function(t){this.setPluginState({authenticated:t}),t&&this.view.getFolder()},e.prototype.getUsername=function(t){return t.user_email},e.prototype.isFolder=function(t){return"folder"===t[".tag"]},e.prototype.getItemData=function(t){return t},e.prototype.getItemIcon=function(t){return t[".tag"]},e.prototype.getItemSubList=function(t){return t.entries},e.prototype.getItemName=function(t){return t.name||""},e.prototype.getMimeType=function(t){return null},e.prototype.getItemId=function(t){return t.id},e.prototype.getItemRequestPath=function(t){return encodeURIComponent(t.path_lower)},e.prototype.getItemModifiedDate=function(t){return t.server_modified},e.prototype.getItemThumbnailUrl=function(t){return this.opts.serverUrl+"/"+this.Dropbox.id+"/thumbnail/"+this.getItemRequestPath(t)},e.prototype.render=function(t){return this.view.render(t)},e}(Yr),Zr=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},tn=Y.Plugin,en=A.h,rn=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.id=o.opts.id||"FileInput",o.title="File Input",o.type="acquirer";var i={strings:{chooseFiles:"Choose files"}},s={target:null,pretty:!0,inputName:"files[]",locale:i};return o.opts=Zr({},s,n),o.locale=Zr({},i,o.opts.locale),o.locale.strings=Zr({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.render=o.render.bind(o),o.handleInputChange=o.handleInputChange.bind(o),o.handleClick=o.handleClick.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleInputChange=function(t){var e=this;this.uppy.log("[FileInput] Something selected through input..."),fr(t.target.files).forEach(function(t){try{e.uppy.addFile({source:e.id,name:t.name,type:t.type,data:t})}catch(t){}})},e.prototype.handleClick=function(t){this.input.click()},e.prototype.render=function(t){var e=this,r=this.uppy.opts.restrictions;return en("div",{class:"uppy-Root uppy-FileInput-container"},en("input",{class:"uppy-FileInput-input",style:this.opts.pretty&&{width:"0.1px",height:"0.1px",opacity:0,overflow:"hidden",position:"absolute",zIndex:-1},type:"file",name:this.opts.inputName,onchange:this.handleInputChange,multiple:1!==r.maxNumberOfFiles,accept:r.allowedFileTypes,ref:function(t){e.input=t},value:""}),this.opts.pretty&&en("button",{class:"uppy-FileInput-btn",type:"button",onclick:this.handleClick},this.i18n("chooseFiles")))},e.prototype.install=function(){var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.unmount()},e}(tn),nn={__esModule:!0};nn.default=cn,nn.getFieldData=hn;var on={"[object HTMLCollection]":!0,"[object NodeList]":!0,"[object RadioNodeList]":!0},sn={button:!0,fieldset:!0,reset:!0,submit:!0},an={checkbox:!0,radio:!0},ln=/^\s+|\s+$/g,un=Array.prototype.slice,pn=Object.prototype.toString;function cn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{trim:!1};if(!t)throw new Error("A form is required by getFormData, was given form="+t);for(var r={},n=void 0,o=[],i={},s=0,a=t.elements.length;s<a;s++){var l=t.elements[s];sn[l.type]||l.disabled||(n=l.name||l.id)&&!i[n]&&(o.push(n),i[n]=!0)}for(var u=0,p=o.length;u<p;u++){var c=hn(t,n=o[u],e);null!=c&&(r[n]=c)}return r}function hn(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{trim:!1};if(!t)throw new Error("A form is required by getFieldData, was given form="+t);if(!e&&"[object String]"!==pn.call(e))throw new Error("A field name is required by getFieldData, was given fieldName="+e);var n=t.elements[e];if(!n||n.disabled)return null;if(!on[pn.call(n)])return dn(n,r.trim);for(var o=[],i=!0,s=0,a=n.length;s<a;s++)if(!n[s].disabled){i&&"radio"!==n[s].type&&(i=!1);var l=dn(n[s],r.trim);null!=l&&(o=o.concat(l))}return i&&1===o.length?o[0]:o.length>0?o:null}function dn(t,e){var r=null,n=t.type;if("select-one"===n)return t.options.length&&(r=t.options[t.selectedIndex].value),r;if("select-multiple"===n){r=[];for(var o=0,i=t.options.length;o<i;o++)t.options[o].selected&&r.push(t.options[o].value);return 0===r.length&&(r=null),r}return"file"===n&&"files"in t?(t.multiple?0===(r=un.call(t.files)).length&&(r=null):r=t.files[0],r):(an[n]?t.checked&&(r=t.value):r=e?t.value.replace(ln,""):t.value,r)}cn.getFieldData=hn;var fn=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yn=Y.Plugin,gn=nn.default||nn,mn=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.type="acquirer",o.id="Form",o.title="Form",o.opts=fn({},{target:null,resultName:"uppyResult",getMetaFromForm:!0,addResultToForm:!0,submitOnSuccess:!1,triggerUploadOnSubmit:!1},n),o.handleFormSubmit=o.handleFormSubmit.bind(o),o.handleUploadStart=o.handleUploadStart.bind(o),o.handleSuccess=o.handleSuccess.bind(o),o.addResultToForm=o.addResultToForm.bind(o),o.getMetaFromForm=o.getMetaFromForm.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleUploadStart=function(){this.opts.getMetaFromForm&&this.getMetaFromForm()},e.prototype.handleSuccess=function(t){this.opts.addResultToForm&&this.addResultToForm(t),this.opts.submitOnSuccess&&this.form.submit()},e.prototype.handleFormSubmit=function(t){var e=this;this.opts.triggerUploadOnSubmit&&(t.preventDefault(),this.uppy.upload().catch(function(t){e.uppy.log(t.stack||t.message||t)}))},e.prototype.addResultToForm=function(t){this.uppy.log("[Form] Adding result to the original form:"),this.uppy.log(t);var e=this.form.querySelector('[name="'+this.opts.resultName+'"]');e?e.value=JSON.stringify(t):((e=document.createElement("input")).name=this.opts.resultName,e.type="hidden",e.value=JSON.stringify(t),this.form.appendChild(e))},e.prototype.getMetaFromForm=function(){var t=gn(this.form);this.uppy.setMeta(t)},e.prototype.install=function(){this.form=I(this.opts.target),this.form&&"FORM"!==!this.form.nodeName?(this.form.addEventListener("submit",this.handleFormSubmit),this.uppy.on("upload",this.handleUploadStart),this.uppy.on("complete",this.handleSuccess)):console.error("Form plugin requires a <form> target element passed in options to operate, none was found","error")},e.prototype.uninstall=function(){this.form.removeEventListener("submit",this.handleFormSubmit),this.uppy.off("upload",this.handleUploadStart),this.uppy.off("complete",this.handleSuccess)},e}(yn),vn={},bn=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wn="undefined"!=typeof window&&(window.indexedDB||window.webkitIndexedDB||window.mozIndexedDB||window.OIndexedDB||window.msIndexedDB),Sn=!!wn,Pn="uppy-blobs",_n="files",En=864e5,kn=3;function Cn(t){var e=wn.open(t,kn);return new Promise(function(t,r){e.onupgradeneeded=function(e){var r=e.target.result,n=e.currentTarget.transaction;if(e.oldVersion<2&&r.createObjectStore(_n,{keyPath:"id"}).createIndex("store","store",{unique:!1}),e.oldVersion<3){var o=n.objectStore(_n);o.createIndex("expires","expires",{unique:!1}),o.openCursor().onsuccess=function(t){var e=t.target.result;if(e){var r=e.value;r.expires=Date.now()+En,e.update(r)}}}n.oncomplete=function(){t(r)}},e.onsuccess=function(e){t(e.target.result)},e.onerror=r})}function Tn(t){return new Promise(function(e,r){t.onsuccess=function(t){e(t.target.result)},t.onerror=r})}var Un=!1,On=function(){function t(e){var r=this;!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.opts=bn({dbName:Pn,storeName:"default",expires:En,maxFileSize:10485760,maxTotalSize:314572800},e),this.name=this.opts.storeName;var n=function(){return Cn(r.opts.dbName)};Un?this.ready=n():(Un=!0,this.ready=t.cleanup().then(n,n))}return t.prototype.key=function(t){return this.name+"!"+t},t.prototype.list=function(){var t=this;return this.ready.then(function(e){return Tn(e.transaction([_n],"readonly").objectStore(_n).index("store").getAll(IDBKeyRange.only(t.name)))}).then(function(t){var e={};return t.forEach(function(t){e[t.fileID]=t.data}),e})},t.prototype.get=function(t){var e=this;return this.ready.then(function(r){return Tn(r.transaction([_n],"readonly").objectStore(_n).get(e.key(t)))}).then(function(t){return{id:t.data.fileID,data:t.data.data}})},t.prototype.getSize=function(){var t=this;return this.ready.then(function(e){var r=e.transaction([_n],"readonly").objectStore(_n).index("store").openCursor(IDBKeyRange.only(t.name));return new Promise(function(t,e){var n=0;r.onsuccess=function(e){var r=e.target.result;r?(n+=r.value.data.size,r.continue()):t(n)},r.onerror=function(){e(new Error("Could not retrieve stored blobs size"))}})})},t.prototype.put=function(t){var e=this;return t.data.size>this.opts.maxFileSize?Promise.reject(new Error("File is too big to store.")):this.getSize().then(function(t){return t>e.opts.maxTotalSize?Promise.reject(new Error("No space left")):e.ready}).then(function(r){return Tn(r.transaction([_n],"readwrite").objectStore(_n).add({id:e.key(t.id),fileID:t.id,store:e.name,expires:Date.now()+e.opts.expires,data:t.data}))})},t.prototype.delete=function(t){var e=this;return this.ready.then(function(r){return Tn(r.transaction([_n],"readwrite").objectStore(_n).delete(e.key(t)))})},t.cleanup=function(){return Cn(Pn).then(function(t){var e=t.transaction([_n],"readwrite").objectStore(_n).index("expires").openCursor(IDBKeyRange.upperBound(Date.now()));return new Promise(function(r,n){e.onsuccess=function(e){var n=e.target.result;if(n){var o=n.value;console.log("[IndexedDBStore] Deleting record",o.fileID,"of size",F(o.data.size),"- expired on",new Date(o.expires)),n.delete(),n.continue()}else r(t)},e.onerror=n})}).then(function(t){t.close()})},t}();On.isSupported=Sn,vn=On;var Fn=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t};function An(t){try{return JSON.parse(t)}catch(t){return null}}var xn=!1,Dn=function(){function t(e){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.opts=Fn({expires:864e5},e),this.name="uppyState:"+e.storeName,xn||(xn=!0,t.cleanup())}return t.prototype.load=function(){var t=localStorage.getItem(this.name);if(!t)return null;var e=An(t);return e?e.metadata?e.metadata:(this.save(e),e):null},t.prototype.save=function(t){var e=Date.now()+this.opts.expires,r=JSON.stringify({metadata:t,expires:e});localStorage.setItem(this.name,r)},t.cleanup=function(){var t=function(){for(var t=[],e=0;e<localStorage.length;e++){var r=localStorage.key(e);/^uppyState:/.test(r)&&t.push(r.slice("uppyState:".length))}return t}(),e=Date.now();t.forEach(function(t){var r=localStorage.getItem("uppyState:"+t);if(!r)return null;var n=An(r);if(!n)return null;n.expires&&n.expires<e&&localStorage.removeItem("uppyState:"+t)})},t}(),Rn={},In="undefined"!=typeof navigator&&"serviceWorker"in navigator,Bn=function(){function t(e){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.ready=new Promise(function(t,e){In?navigator.serviceWorker.controller?t():navigator.serviceWorker.addEventListener("controllerchange",function(){t()}):e(new Error("Unsupported"))}),this.name=e.storeName}return t.prototype.list=function(){var t=this,e={},r=new Promise(function(t,r){e.resolve=t,e.reject=r});console.log("Loading stored blobs from Service Worker");var n=function r(n){if(n.data.store===t.name)switch(n.data.type){case"uppy/ALL_FILES":e.resolve(n.data.files),navigator.serviceWorker.removeEventListener("message",r)}};return this.ready.then(function(){navigator.serviceWorker.addEventListener("message",n),navigator.serviceWorker.controller.postMessage({type:"uppy/GET_FILES",store:t.name})}),r},t.prototype.put=function(t){var e=this;return this.ready.then(function(){navigator.serviceWorker.controller.postMessage({type:"uppy/ADD_FILE",store:e.name,file:t})})},t.prototype.delete=function(t){var e=this;return this.ready.then(function(){navigator.serviceWorker.controller.postMessage({type:"uppy/REMOVE_FILE",store:e.name,fileID:t})})},t}();Bn.isSupported=In,Rn=Bn;var jn,Ln=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mn=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.type="debugger",o.id="GoldenRetriever",o.title="Golden Retriever",o.opts=Ln({},{expires:864e5,serviceWorker:!1},n),o.MetaDataStore=new Dn({expires:o.opts.expires,storeName:r.getID()}),o.ServiceWorkerStore=null,o.opts.serviceWorker&&(o.ServiceWorkerStore=new Rn({storeName:r.getID()})),o.IndexedDBStore=new vn(Ln({expires:o.opts.expires},n.indexedDB||{},{storeName:r.getID()})),o.saveFilesStateToLocalStorage=o.saveFilesStateToLocalStorage.bind(o),o.loadFilesStateFromLocalStorage=o.loadFilesStateFromLocalStorage.bind(o),o.loadFileBlobsFromServiceWorker=o.loadFileBlobsFromServiceWorker.bind(o),o.loadFileBlobsFromIndexedDB=o.loadFileBlobsFromIndexedDB.bind(o),o.onBlobsLoaded=o.onBlobsLoaded.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.loadFilesStateFromLocalStorage=function(){var t=this.MetaDataStore.load();t&&(this.uppy.log("[GoldenRetriever] Recovered some state from Local Storage"),this.uppy.setState({currentUploads:t.currentUploads||{},files:t.files||{}}),this.savedPluginData=t.pluginData)},e.prototype.getWaitingFiles=function(){var t={};return this.uppy.getFiles().forEach(function(e){e.progress&&e.progress.uploadStarted||(t[e.id]=e)}),t},e.prototype.getUploadingFiles=function(){var t=this,e={},r=this.uppy.getState().currentUploads;return r&&Object.keys(r).forEach(function(n){r[n].fileIDs.forEach(function(r){e[r]=t.uppy.getFile(r)})}),e},e.prototype.saveFilesStateToLocalStorage=function(){var t=Ln(this.getWaitingFiles(),this.getUploadingFiles()),e={};this.uppy.emit("restore:get-data",function(t){Ln(e,t)});var r=this.uppy.getState().currentUploads;this.MetaDataStore.save({currentUploads:r,files:t,pluginData:e})},e.prototype.loadFileBlobsFromServiceWorker=function(){var t=this;this.ServiceWorkerStore.list().then(function(e){var r=Object.keys(e).length;return r===t.uppy.getFiles().length?(t.uppy.log("[GoldenRetriever] Successfully recovered "+r+" blobs from Service Worker!"),t.uppy.info("Successfully recovered "+r+" files","success",3e3),t.onBlobsLoaded(e)):(t.uppy.log("[GoldenRetriever] No blobs found in Service Worker, trying IndexedDB now..."),t.loadFileBlobsFromIndexedDB())}).catch(function(e){t.uppy.log("[GoldenRetriever] Failed to recover blobs from Service Worker","warning"),t.uppy.log(e)})},e.prototype.loadFileBlobsFromIndexedDB=function(){var t=this;this.IndexedDBStore.list().then(function(e){var r=Object.keys(e).length;if(r>0)return t.uppy.log("[GoldenRetriever] Successfully recovered "+r+" blobs from IndexedDB!"),t.uppy.info("Successfully recovered "+r+" files","success",3e3),t.onBlobsLoaded(e);t.uppy.log("[GoldenRetriever] No blobs found in IndexedDB")}).catch(function(e){t.uppy.log("[GoldenRetriever] Failed to recover blobs from IndexedDB","warning"),t.uppy.log(e)})},e.prototype.onBlobsLoaded=function(t){var e=this,r=[],n=Ln({},this.uppy.getState().files);Object.keys(t).forEach(function(o){var i=e.uppy.getFile(o);if(i){var s=t[o],a=Ln({},i,{data:s,isRestored:!0});n[o]=a}else r.push(o)}),this.uppy.setState({files:n}),this.uppy.emit("restored",this.savedPluginData),r.length&&this.deleteBlobs(r).then(function(){e.uppy.log("[GoldenRetriever] Cleaned up "+r.length+" old files")}).catch(function(t){e.uppy.log("[GoldenRetriever] Could not clean up "+r.length+" old files","warning"),e.uppy.log(t)})},e.prototype.deleteBlobs=function(t){var e=this,r=[];return t.forEach(function(t){e.ServiceWorkerStore&&r.push(e.ServiceWorkerStore.delete(t)),e.IndexedDBStore&&r.push(e.IndexedDBStore.delete(t))}),Promise.all(r)},e.prototype.install=function(){var t=this;this.loadFilesStateFromLocalStorage(),this.uppy.getFiles().length>0?this.ServiceWorkerStore?(this.uppy.log("[GoldenRetriever] Attempting to load files from Service Worker..."),this.loadFileBlobsFromServiceWorker()):(this.uppy.log("[GoldenRetriever] Attempting to load files from Indexed DB..."),this.loadFileBlobsFromIndexedDB()):(this.uppy.log("[GoldenRetriever] No files need to be loaded, only restoring processing state..."),this.onBlobsLoaded([])),this.uppy.on("file-added",function(e){e.isRemote||(t.ServiceWorkerStore&&t.ServiceWorkerStore.put(e).catch(function(e){t.uppy.log("[GoldenRetriever] Could not store file","warning"),t.uppy.log(e)}),t.IndexedDBStore.put(e).catch(function(e){t.uppy.log("[GoldenRetriever] Could not store file","warning"),t.uppy.log(e)}))}),this.uppy.on("file-removed",function(e){t.ServiceWorkerStore&&t.ServiceWorkerStore.delete(e.id).catch(function(e){t.uppy.log("[GoldenRetriever] Failed to remove file","warning"),t.uppy.log(e)}),t.IndexedDBStore.delete(e.id).catch(function(e){t.uppy.log("[GoldenRetriever] Failed to remove file","warning"),t.uppy.log(e)})}),this.uppy.on("complete",function(e){var r=e.successful,n=r.map(function(t){return t.id});t.deleteBlobs(n).then(function(){t.uppy.log("[GoldenRetriever] Removed "+r.length+" files that finished uploading")}).catch(function(e){t.uppy.log("[GoldenRetriever] Could not remove "+r.length+" files that finished uploading","warning"),t.uppy.log(e)})}),this.uppy.on("state-update",this.saveFilesStateToLocalStorage),this.uppy.on("restored",function(){var e=t.uppy.getState().currentUploads;e&&Object.keys(e).forEach(function(r){t.uppy.restore(r,e[r])})})},e}(Y.Plugin),Nn=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zn=Y.Plugin,qn=d.Provider,Hn=A.h,Wn=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.id=o.opts.id||"GoogleDrive",o.title=o.opts.title||"Google Drive",qn.initPlugin(o,n),o.title=o.opts.title||"Google Drive",o.icon=function(){return Hn("svg",{"aria-hidden":"true",width:"18px",height:"16px",viewBox:"0 0 18 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},Hn("g",{"fill-rule":"evenodd"},Hn("polygon",{fill:"#3089FC",points:"6.32475 10.2 18 10.2 14.999625 15.3 3.324375 15.3"}),Hn("polygon",{fill:"#00A85D",points:"3.000375 15.3 0 10.2 5.83875 0.275974026 8.838 5.37597403 5.999625 10.2"}),Hn("polygon",{fill:"#FFD024",points:"11.838375 9.92402597 5.999625 0 12.000375 0 17.839125 9.92402597"})))},o[o.id]=new qn(r,{serverUrl:o.opts.serverUrl,serverHeaders:o.opts.serverHeaders,provider:"drive",authProvider:"google"}),o.onAuth=o.onAuth.bind(o),o.render=o.render.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.install=function(){this.view=new Kr(this),this.setPluginState({authenticated:!1,files:[],folders:[],directories:[],activeRow:-1,filterInput:"",isSearchVisible:!1});var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.view.tearDown(),this.unmount()},e.prototype.onAuth=function(t){this.setPluginState({authenticated:t}),t&&this.view.getFolder("root")},e.prototype.getUsername=function(t){var e=t.files,r=Array.isArray(e),n=0;for(e=r?e:e[Symbol.iterator]();;){var o;if(r){if(n>=e.length)break;o=e[n++]}else{if((n=e.next()).done)break;o=n.value}var i=o;if(i.ownedByMe){var s=i.permissions,a=Array.isArray(s),l=0;for(s=a?s:s[Symbol.iterator]();;){var u;if(a){if(l>=s.length)break;u=s[l++]}else{if((l=s.next()).done)break;u=l.value}var p=u;if("owner"===p.role)return p.emailAddress}}}},e.prototype.isFolder=function(t){return"application/vnd.google-apps.folder"===t.mimeType},e.prototype.getItemData=function(t){return Nn({},t,{size:parseFloat(t.size)})},e.prototype.getItemIcon=function(t){return t.iconLink},e.prototype.getItemSubList=function(t){var e=this;return t.files.filter(function(t){return e.isFolder(t)||!t.mimeType.startsWith("application/vnd.google")})},e.prototype.getItemName=function(t){return t.name?t.name:"/"},e.prototype.getMimeType=function(t){return t.mimeType},e.prototype.getItemId=function(t){return t.id},e.prototype.getItemRequestPath=function(t){return this.getItemId(t)},e.prototype.getItemModifiedDate=function(t){return t.modifiedTime},e.prototype.getItemThumbnailUrl=function(t){return this.opts.serverUrl+"/"+this.GoogleDrive.id+"/thumbnail/"+this.getItemRequestPath(t)},e.prototype.render=function(t){return this.view.render(t)},e}(zn),Xn=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Vn=Y.Plugin,Gn=d.Provider,Kn=A.h,Yn=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.id=o.opts.id||"Instagram",Gn.initPlugin(o,n),o.title=o.opts.title||"Instagram",o.icon=function(){return Kn("svg",{"aria-hidden":"true",fill:"#DE3573",width:"28",height:"28",viewBox:"0 0 512 512"},Kn("path",{d:"M256,49.471c67.266,0,75.233.257,101.8,1.469,24.562,1.121,37.9,5.224,46.778,8.674a78.052,78.052,0,0,1,28.966,18.845,78.052,78.052,0,0,1,18.845,28.966c3.45,8.877,7.554,22.216,8.674,46.778,1.212,26.565,1.469,34.532,1.469,101.8s-0.257,75.233-1.469,101.8c-1.121,24.562-5.225,37.9-8.674,46.778a83.427,83.427,0,0,1-47.811,47.811c-8.877,3.45-22.216,7.554-46.778,8.674-26.56,1.212-34.527,1.469-101.8,1.469s-75.237-.257-101.8-1.469c-24.562-1.121-37.9-5.225-46.778-8.674a78.051,78.051,0,0,1-28.966-18.845,78.053,78.053,0,0,1-18.845-28.966c-3.45-8.877-7.554-22.216-8.674-46.778-1.212-26.564-1.469-34.532-1.469-101.8s0.257-75.233,1.469-101.8c1.121-24.562,5.224-37.9,8.674-46.778A78.052,78.052,0,0,1,78.458,78.458a78.053,78.053,0,0,1,28.966-18.845c8.877-3.45,22.216-7.554,46.778-8.674,26.565-1.212,34.532-1.469,101.8-1.469m0-45.391c-68.418,0-77,.29-103.866,1.516-26.815,1.224-45.127,5.482-61.151,11.71a123.488,123.488,0,0,0-44.62,29.057A123.488,123.488,0,0,0,17.3,90.982C11.077,107.007,6.819,125.319,5.6,152.134,4.369,179,4.079,187.582,4.079,256S4.369,333,5.6,359.866c1.224,26.815,5.482,45.127,11.71,61.151a123.489,123.489,0,0,0,29.057,44.62,123.486,123.486,0,0,0,44.62,29.057c16.025,6.228,34.337,10.486,61.151,11.71,26.87,1.226,35.449,1.516,103.866,1.516s77-.29,103.866-1.516c26.815-1.224,45.127-5.482,61.151-11.71a128.817,128.817,0,0,0,73.677-73.677c6.228-16.025,10.486-34.337,11.71-61.151,1.226-26.87,1.516-35.449,1.516-103.866s-0.29-77-1.516-103.866c-1.224-26.815-5.482-45.127-11.71-61.151a123.486,123.486,0,0,0-29.057-44.62A123.487,123.487,0,0,0,421.018,17.3C404.993,11.077,386.681,6.819,359.866,5.6,333,4.369,324.418,4.079,256,4.079h0Z"}),Kn("path",{d:"M256,126.635A129.365,129.365,0,1,0,385.365,256,129.365,129.365,0,0,0,256,126.635Zm0,213.338A83.973,83.973,0,1,1,339.974,256,83.974,83.974,0,0,1,256,339.973Z"}),Kn("circle",{cx:"390.476",cy:"121.524",r:"30.23"}))},o[o.id]=new Gn(r,{serverUrl:o.opts.serverUrl,serverHeaders:o.opts.serverHeaders,provider:"instagram",authProvider:"instagram"}),o.onAuth=o.onAuth.bind(o),o.render=o.render.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.install=function(){this.view=new Kr(this,{viewType:"grid",showTitles:!1,showFilter:!1,showBreadcrumbs:!1}),this.setPluginState({authenticated:!1,files:[],folders:[],directories:[],activeRow:-1,filterInput:"",isSearchVisible:!1});var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.view.tearDown(),this.unmount()},e.prototype.onAuth=function(t){this.setPluginState({authenticated:t}),t&&this.view.getFolder("recent")},e.prototype.getUsername=function(t){return t.data[0].user.username},e.prototype.isFolder=function(t){return!1},e.prototype.getItemData=function(t){return t},e.prototype.getItemIcon=function(t){return t.images?t.images.low_resolution.url:"video"},e.prototype.getItemSubList=function(t){var e=[];return t.data.forEach(function(t){t.carousel_media?t.carousel_media.forEach(function(r,n){var o=t.id,i=t.created_time,s=Xn({},r,{id:o,created_time:i});s.carousel_id=n,e.push(s)}):e.push(t)}),e},e.prototype.getItemName=function(t){if(t&&t.created_time){var e="video"===t.type?"mp4":"jpeg",r=new Date(1e3*t.created_time);return"Instagram "+(r=r.toLocaleDateString([],{year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric"}))+(t.carousel_id?" "+t.carousel_id:"")+"."+e}return""},e.prototype.getMimeType=function(t){return"video"===t.type?"video/mp4":"image/jpeg"},e.prototype.getItemId=function(t){return""+t.id+(t.carousel_id||"")},e.prototype.getItemRequestPath=function(t){var e=isNaN(t.carousel_id)?"":"?carousel_id="+t.carousel_id;return""+t.id+e},e.prototype.getItemModifiedDate=function(t){return t.created_time},e.prototype.getItemThumbnailUrl=function(t){return t.images.thumbnail.url},e.prototype.getNextPagePath=function(){var t=this.getPluginState().files;return"recent?max_id="+this.getItemId(t[t.length-1])},e.prototype.render=function(t){return this.view.render(t)},e}(Vn),Jn=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$n=Y.Plugin,Qn=A.h,Zn=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.id=o.opts.id||"ProgressBar",o.title="Progress Bar",o.type="progressindicator",o.opts=Jn({},{target:"body",replaceTargetContent:!1,fixed:!1,hideAfterFinish:!0},n),o.render=o.render.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.render=function(t){var e=t.totalProgress||0,r=100===e&&this.opts.hideAfterFinish;return Qn("div",{class:"uppy uppy-ProgressBar",style:{position:this.opts.fixed?"fixed":"initial"},"aria-hidden":r},Qn("div",{class:"uppy-ProgressBar-inner",style:{width:e+"%"}}),Qn("div",{class:"uppy-ProgressBar-percentage"},e))},e.prototype.install=function(){var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.unmount()},e}($n),to=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},eo=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.type="debugger",o.id="ReduxDevTools",o.title="Redux DevTools",o.opts=to({},{},n),o.handleStateChange=o.handleStateChange.bind(o),o.initDevTools=o.initDevTools.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleStateChange=function(t,e,r){this.devTools.send("UPPY_STATE_UPDATE",e)},e.prototype.initDevTools=function(){var t=this;this.devTools=window.devToolsExtension.connect(),this.devToolsUnsubscribe=this.devTools.subscribe(function(e){if("DISPATCH"===e.type)switch(console.log(e.payload.type),e.payload.type){case"RESET":return void t.uppy.reset();case"IMPORT_STATE":var r=e.payload.nextLiftedState.computedStates;return t.uppy.store.state=to({},t.uppy.getState(),r[r.length-1].state),void t.uppy.updateAll(t.uppy.getState());case"JUMP_TO_STATE":case"JUMP_TO_ACTION":t.uppy.store.state=to({},t.uppy.getState(),JSON.parse(e.state)),t.uppy.updateAll(t.uppy.getState())}})},e.prototype.install=function(){this.withDevTools="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__,this.withDevTools&&(this.initDevTools(),this.uppy.on("state-update",this.handleStateChange))},e.prototype.uninstall=function(){this.withDevTools&&(this.devToolsUnsubscribe(),this.uppy.off("state-update",this.handleStateUpdate))},e}(Y.Plugin),ro=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},no=function(t){return function(e){return e.uppy[t]}},oo=function(){function t(e){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this._store=e.store,this._id=e.id||b(),this._selector=e.selector||no(this._id),this.setState({})}return t.prototype.setState=function(t){this._store.dispatch({type:"uppy/STATE_UPDATE",id:this._id,payload:t})},t.prototype.getState=function(){return this._selector(this._store.getState())},t.prototype.subscribe=function(t){var e=this,r=this.getState();return this._store.subscribe(function(){var n=e.getState();if(r!==n){var o=function(t,e){var r=Object.keys(e),n={};return r.forEach(function(r){t[r]!==e[r]&&(n[r]=e[r])}),n}(r,n);t(r,n,o),r=n}})},t}();(jn=function(t){return new oo(t)}).STATE_UPDATE="uppy/STATE_UPDATE",jn.reducer=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments[1];if("uppy/STATE_UPDATE"===e.type){var r,n=ro({},t[e.id],e.payload);return ro({},t,((r={})[e.id]=n,r))}return t},jn.middleware=function(){return function(){return function(t){return function(e){t(e)}}}};var io={exports:{}};function so(t){if(t)return function(t){for(var e in so.prototype)t[e]=so.prototype[e];return t}(t)}io.exports=so,so.prototype.on=so.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},so.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},so.prototype.off=so.prototype.removeListener=so.prototype.removeAllListeners=so.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+t];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var o=0;o<n.length;o++)if((r=n[o])===e||r.fn===e){n.splice(o,1);break}return this},so.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),r=this._callbacks["$"+t];if(r)for(var n=0,o=(r=r.slice(0)).length;n<o;++n)r[n].apply(this,e);return this},so.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},so.prototype.hasListeners=function(t){return!!this.listeners(t).length},io=io.exports;var ao={};function lo(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}ao=lo,lo.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),r=Math.floor(e*this.jitter*t);t=0==(1&Math.floor(10*e))?t-r:t+r}return 0|Math.min(t,this.max)},lo.prototype.reset=function(){this.attempts=0},lo.prototype.setMin=function(t){this.ms=t},lo.prototype.setMax=function(t){this.max=t},lo.prototype.setJitter=function(t){this.jitter=t};for(var uo=[].slice,po=function(t,e){if("string"==typeof e&&(e=t[e]),"function"!=typeof e)throw new Error("bind() requires a function");var r=uo.call(arguments,2);return function(){return e.apply(t,r.concat(uo.call(arguments)))}},co=[].indexOf,ho=function(t,e){if(co)return t.indexOf(e);for(var r=0;r<t.length;++r)if(t[r]===e)return r;return-1},fo=function(t,e,r){return t.on(e,r),{destroy:function(){t.removeListener(e,r)}}},yo={toByteArray:function(t){var e,r,n,o,i,s,a=t.length;i=Po(t),s=new vo(3*a/4-i),n=i>0?a-4:a;var l=0;for(e=0,r=0;e<n;e+=4,r+=3)o=mo[t.charCodeAt(e)]<<18|mo[t.charCodeAt(e+1)]<<12|mo[t.charCodeAt(e+2)]<<6|mo[t.charCodeAt(e+3)],s[l++]=o>>16&255,s[l++]=o>>8&255,s[l++]=255&o;return 2===i?(o=mo[t.charCodeAt(e)]<<2|mo[t.charCodeAt(e+1)]>>4,s[l++]=255&o):1===i&&(o=mo[t.charCodeAt(e)]<<10|mo[t.charCodeAt(e+1)]<<4|mo[t.charCodeAt(e+2)]>>2,s[l++]=o>>8&255,s[l++]=255&o),s},fromByteArray:function(t){for(var e,r=t.length,n=r%3,o="",i=[],s=0,a=r-n;s<a;s+=16383)i.push(_o(t,s,s+16383>a?a:s+16383));return 1===n?(e=t[r-1],o+=go[e>>2],o+=go[e<<4&63],o+="=="):2===n&&(e=(t[r-2]<<8)+t[r-1],o+=go[e>>10],o+=go[e>>4&63],o+=go[e<<2&63],o+="="),i.push(o),i.join("")}},go=[],mo=[],vo="undefined"!=typeof Uint8Array?Uint8Array:Array,bo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",wo=0,So=bo.length;wo<So;++wo)go[wo]=bo[wo],mo[bo.charCodeAt(wo)]=wo;function Po(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===t[e-2]?2:"="===t[e-1]?1:0}function _o(t,e,r){for(var n,o,i=[],s=e;s<r;s+=3)n=(t[s]<<16)+(t[s+1]<<8)+t[s+2],i.push(go[(o=n)>>18&63]+go[o>>12&63]+go[o>>6&63]+go[63&o]);return i.join("")}mo["-".charCodeAt(0)]=62,mo["_".charCodeAt(0)]=63;var Eo={read:function(t,e,r,n,o){var i,s,a=8*o-n-1,l=(1<<a)-1,u=l>>1,p=-7,c=r?o-1:0,h=r?-1:1,d=t[e+c];for(c+=h,i=d&(1<<-p)-1,d>>=-p,p+=a;p>0;i=256*i+t[e+c],c+=h,p-=8);for(s=i&(1<<-p)-1,i>>=-p,p+=n;p>0;s=256*s+t[e+c],c+=h,p-=8);if(0===i)i=1-u;else{if(i===l)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,n),i-=u}return(d?-1:1)*s*Math.pow(2,i-n)},write:function(t,e,r,n,o,i){var s,a,l,u=8*i-o-1,p=(1<<u)-1,c=p>>1,h=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:i-1,f=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=p):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),(e+=s+c>=1?h/l:h*Math.pow(2,1-c))*l>=2&&(s++,l/=2),s+c>=p?(a=0,s=p):s+c>=1?(a=(e*l-1)*Math.pow(2,o),s+=c):(a=e*Math.pow(2,c-1)*Math.pow(2,o),s=0));o>=8;t[r+d]=255&a,d+=f,a/=256,o-=8);for(s=s<<o|a,u+=o;u>0;t[r+d]=255&s,d+=f,s/=256,u-=8);t[r+d-f]|=128*y}},ko={};ko.Buffer=Uo,ko.INSPECT_MAX_BYTES=50;var Co=2147483647;function To(t){if(t>Co)throw new RangeError("Invalid typed array length");var e=new Uint8Array(t);return e.__proto__=Uo.prototype,e}function Uo(t,e,r){if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return Ao(t)}return Oo(t,e,r)}function Oo(t,e,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return si(t)||t&&si(t.buffer)?function(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;return(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r)).__proto__=Uo.prototype,n}(t,e,r):"string"==typeof t?function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!Uo.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var r=0|Ro(t,e),n=To(r),o=n.write(t,e);return o!==r&&(n=n.slice(0,o)),n}(t,e):function(t){if(Uo.isBuffer(t)){var e=0|Do(t.length),r=To(e);return 0===r.length?r:(t.copy(r,0,0,e),r)}if(t){if(ArrayBuffer.isView(t)||"length"in t)return"number"!=typeof t.length||ai(t.length)?To(0):xo(t);if("Buffer"===t.type&&Array.isArray(t.data))return xo(t.data)}throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object.")}(t)}function Fo(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('"size" argument must not be negative')}function Ao(t){return Fo(t),To(t<0?0:0|Do(t))}function xo(t){for(var e=t.length<0?0:0|Do(t.length),r=To(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function Do(t){if(t>=Co)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Co.toString(16)+" bytes");return 0|t}function Ro(t,e){if(Uo.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||si(t))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return ni(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return oi(t).length;default:if(n)return ni(t).length;e=(""+e).toLowerCase(),n=!0}}function Io(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function Bo(t,e,r,n,o){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),ai(r=+r)&&(r=o?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(o)return-1;r=t.length-1}else if(r<0){if(!o)return-1;r=0}if("string"==typeof e&&(e=Uo.from(e,n)),Uo.isBuffer(e))return 0===e.length?-1:jo(t,e,r,n,o);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):jo(t,[e],r,n,o);throw new TypeError("val must be string, number or Buffer")}function jo(t,e,r,n,o){var i,s=1,a=t.length,l=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s=2,a/=2,l/=2,r/=2}function u(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(o){var p=-1;for(i=r;i<a;i++)if(u(t,i)===u(e,-1===p?0:i-p)){if(-1===p&&(p=i),i-p+1===l)return p*s}else-1!==p&&(i-=i-p),p=-1}else for(r+l>a&&(r=a-l),i=r;i>=0;i--){for(var c=!0,h=0;h<l;h++)if(u(t,i+h)!==u(e,h)){c=!1;break}if(c)return i}return-1}function Lo(t,e,r,n){r=Number(r)||0;var o=t.length-r;n?(n=Number(n))>o&&(n=o):n=o;var i=e.length;n>i/2&&(n=i/2);for(var s=0;s<n;++s){var a=parseInt(e.substr(2*s,2),16);if(ai(a))return s;t[r+s]=a}return s}function Mo(t,e,r,n){return ii(ni(e,t.length-r),t,r,n)}function No(t,e,r,n){return ii(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function zo(t,e,r,n){return No(t,e,r,n)}function qo(t,e,r,n){return ii(oi(e),t,r,n)}function Ho(t,e,r,n){return ii(function(t,e){for(var r,n,o,i=[],s=0;s<t.length&&!((e-=2)<0);++s)n=(r=t.charCodeAt(s))>>8,o=r%256,i.push(o),i.push(n);return i}(e,t.length-r),t,r,n)}function Wo(t,e,r){return 0===e&&r===t.length?yo.fromByteArray(t):yo.fromByteArray(t.slice(e,r))}function Xo(t,e,r){r=Math.min(t.length,r);for(var n=[],o=e;o<r;){var i,s,a,l,u=t[o],p=null,c=u>239?4:u>223?3:u>191?2:1;if(o+c<=r)switch(c){case 1:u<128&&(p=u);break;case 2:128==(192&(i=t[o+1]))&&(l=(31&u)<<6|63&i)>127&&(p=l);break;case 3:i=t[o+1],s=t[o+2],128==(192&i)&&128==(192&s)&&(l=(15&u)<<12|(63&i)<<6|63&s)>2047&&(l<55296||l>57343)&&(p=l);break;case 4:i=t[o+1],s=t[o+2],a=t[o+3],128==(192&i)&&128==(192&s)&&128==(192&a)&&(l=(15&u)<<18|(63&i)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(p=l)}null===p?(p=65533,c=1):p>65535&&(p-=65536,n.push(p>>>10&1023|55296),p=56320|1023&p),n.push(p),o+=c}return function(t){var e=t.length;if(e<=Vo)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=Vo));return r}(n)}Uo.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(t){return!1}}(),Uo.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(Uo.prototype,"parent",{get:function(){if(this instanceof Uo)return this.buffer}}),Object.defineProperty(Uo.prototype,"offset",{get:function(){if(this instanceof Uo)return this.byteOffset}}),"undefined"!=typeof Symbol&&Symbol.species&&Uo[Symbol.species]===Uo&&Object.defineProperty(Uo,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),Uo.poolSize=8192,Uo.from=function(t,e,r){return Oo(t,e,r)},Uo.prototype.__proto__=Uint8Array.prototype,Uo.__proto__=Uint8Array,Uo.alloc=function(t,e,r){return function(t,e,r){return Fo(t),t<=0?To(t):void 0!==e?"string"==typeof r?To(t).fill(e,r):To(t).fill(e):To(t)}(t,e,r)},Uo.allocUnsafe=function(t){return Ao(t)},Uo.allocUnsafeSlow=function(t){return Ao(t)},Uo.isBuffer=function(t){return null!=t&&!0===t._isBuffer},Uo.compare=function(t,e){if(!Uo.isBuffer(t)||!Uo.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,o=0,i=Math.min(r,n);o<i;++o)if(t[o]!==e[o]){r=t[o],n=e[o];break}return r<n?-1:n<r?1:0},Uo.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Uo.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return Uo.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=Uo.allocUnsafe(e),o=0;for(r=0;r<t.length;++r){var i=t[r];if(ArrayBuffer.isView(i)&&(i=Uo.from(i)),!Uo.isBuffer(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(n,o),o+=i.length}return n},Uo.byteLength=Ro,Uo.prototype._isBuffer=!0,Uo.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)Io(this,e,e+1);return this},Uo.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)Io(this,e,e+3),Io(this,e+1,e+2);return this},Uo.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)Io(this,e,e+7),Io(this,e+1,e+6),Io(this,e+2,e+5),Io(this,e+3,e+4);return this},Uo.prototype.toString=function(){var t=this.length;return 0===t?"":0===arguments.length?Xo(this,0,t):function(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return Yo(this,e,r);case"utf8":case"utf-8":return Xo(this,e,r);case"ascii":return Go(this,e,r);case"latin1":case"binary":return Ko(this,e,r);case"base64":return Wo(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Jo(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}.apply(this,arguments)},Uo.prototype.toLocaleString=Uo.prototype.toString,Uo.prototype.equals=function(t){if(!Uo.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===Uo.compare(this,t)},Uo.prototype.inspect=function(){var t="",e=ko.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,e).match(/.{2}/g).join(" "),this.length>e&&(t+=" ... ")),"<Buffer "+t+">"},Uo.prototype.compare=function(t,e,r,n,o){if(!Uo.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),e<0||r>t.length||n<0||o>this.length)throw new RangeError("out of range index");if(n>=o&&e>=r)return 0;if(n>=o)return-1;if(e>=r)return 1;if(this===t)return 0;for(var i=(o>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0),a=Math.min(i,s),l=this.slice(n,o),u=t.slice(e,r),p=0;p<a;++p)if(l[p]!==u[p]){i=l[p],s=u[p];break}return i<s?-1:s<i?1:0},Uo.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},Uo.prototype.indexOf=function(t,e,r){return Bo(this,t,e,r,!0)},Uo.prototype.lastIndexOf=function(t,e,r){return Bo(this,t,e,r,!1)},Uo.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var o=this.length-e;if((void 0===r||r>o)&&(r=o),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var i=!1;;)switch(n){case"hex":return Lo(this,t,e,r);case"utf8":case"utf-8":return Mo(this,t,e,r);case"ascii":return No(this,t,e,r);case"latin1":case"binary":return zo(this,t,e,r);case"base64":return qo(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Ho(this,t,e,r);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},Uo.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Vo=4096;function Go(t,e,r){var n="";r=Math.min(t.length,r);for(var o=e;o<r;++o)n+=String.fromCharCode(127&t[o]);return n}function Ko(t,e,r){var n="";r=Math.min(t.length,r);for(var o=e;o<r;++o)n+=String.fromCharCode(t[o]);return n}function Yo(t,e,r){var n,o=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>o)&&(r=o);for(var i="",s=e;s<r;++s)i+=(n=t[s])<16?"0"+n.toString(16):n.toString(16);return i}function Jo(t,e,r){for(var n=t.slice(e,r),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}function $o(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function Qo(t,e,r,n,o,i){if(!Uo.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function Zo(t,e,r,n,o,i){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function ti(t,e,r,n,o){return e=+e,r>>>=0,o||Zo(t,0,r,4),Eo.write(t,e,r,n,23,4),r+4}function ei(t,e,r,n,o){return e=+e,r>>>=0,o||Zo(t,0,r,8),Eo.write(t,e,r,n,52,8),r+8}Uo.prototype.slice=function(t,e){var r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return n.__proto__=Uo.prototype,n},Uo.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||$o(t,e,this.length);for(var n=this[t],o=1,i=0;++i<e&&(o*=256);)n+=this[t+i]*o;return n},Uo.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||$o(t,e,this.length);for(var n=this[t+--e],o=1;e>0&&(o*=256);)n+=this[t+--e]*o;return n},Uo.prototype.readUInt8=function(t,e){return t>>>=0,e||$o(t,1,this.length),this[t]},Uo.prototype.readUInt16LE=function(t,e){return t>>>=0,e||$o(t,2,this.length),this[t]|this[t+1]<<8},Uo.prototype.readUInt16BE=function(t,e){return t>>>=0,e||$o(t,2,this.length),this[t]<<8|this[t+1]},Uo.prototype.readUInt32LE=function(t,e){return t>>>=0,e||$o(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},Uo.prototype.readUInt32BE=function(t,e){return t>>>=0,e||$o(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},Uo.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||$o(t,e,this.length);for(var n=this[t],o=1,i=0;++i<e&&(o*=256);)n+=this[t+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*e)),n},Uo.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||$o(t,e,this.length);for(var n=e,o=1,i=this[t+--n];n>0&&(o*=256);)i+=this[t+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*e)),i},Uo.prototype.readInt8=function(t,e){return t>>>=0,e||$o(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},Uo.prototype.readInt16LE=function(t,e){t>>>=0,e||$o(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},Uo.prototype.readInt16BE=function(t,e){t>>>=0,e||$o(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},Uo.prototype.readInt32LE=function(t,e){return t>>>=0,e||$o(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},Uo.prototype.readInt32BE=function(t,e){return t>>>=0,e||$o(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},Uo.prototype.readFloatLE=function(t,e){return t>>>=0,e||$o(t,4,this.length),Eo.read(this,t,!0,23,4)},Uo.prototype.readFloatBE=function(t,e){return t>>>=0,e||$o(t,4,this.length),Eo.read(this,t,!1,23,4)},Uo.prototype.readDoubleLE=function(t,e){return t>>>=0,e||$o(t,8,this.length),Eo.read(this,t,!0,52,8)},Uo.prototype.readDoubleBE=function(t,e){return t>>>=0,e||$o(t,8,this.length),Eo.read(this,t,!1,52,8)},Uo.prototype.writeUIntLE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||Qo(this,t,e,r,Math.pow(2,8*r)-1,0);var o=1,i=0;for(this[e]=255&t;++i<r&&(o*=256);)this[e+i]=t/o&255;return e+r},Uo.prototype.writeUIntBE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||Qo(this,t,e,r,Math.pow(2,8*r)-1,0);var o=r-1,i=1;for(this[e+o]=255&t;--o>=0&&(i*=256);)this[e+o]=t/i&255;return e+r},Uo.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,1,255,0),this[e]=255&t,e+1},Uo.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},Uo.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},Uo.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},Uo.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},Uo.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var o=Math.pow(2,8*r-1);Qo(this,t,e,r,o-1,-o)}var i=0,s=1,a=0;for(this[e]=255&t;++i<r&&(s*=256);)t<0&&0===a&&0!==this[e+i-1]&&(a=1),this[e+i]=(t/s>>0)-a&255;return e+r},Uo.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var o=Math.pow(2,8*r-1);Qo(this,t,e,r,o-1,-o)}var i=r-1,s=1,a=0;for(this[e+i]=255&t;--i>=0&&(s*=256);)t<0&&0===a&&0!==this[e+i+1]&&(a=1),this[e+i]=(t/s>>0)-a&255;return e+r},Uo.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},Uo.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},Uo.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},Uo.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},Uo.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||Qo(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},Uo.prototype.writeFloatLE=function(t,e,r){return ti(this,t,e,!0,r)},Uo.prototype.writeFloatBE=function(t,e,r){return ti(this,t,e,!1,r)},Uo.prototype.writeDoubleLE=function(t,e,r){return ei(this,t,e,!0,r)},Uo.prototype.writeDoubleBE=function(t,e,r){return ei(this,t,e,!1,r)},Uo.prototype.copy=function(t,e,r,n){if(!Uo.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var o=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var i=o-1;i>=0;--i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return o},Uo.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!Uo.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){var o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var i;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=Uo.isBuffer(t)?t:new Uo(t,n),a=s.length;if(0===a)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%a]}return this};var ri=/[^+\/0-9A-Za-z-_]/g;function ni(t,e){var r;e=e||1/0;for(var n=t.length,o=null,i=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!o){if(r>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(s+1===n){(e-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(e-=3)>-1&&i.push(239,191,189),o=r;continue}r=65536+(o-55296<<10|r-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((e-=1)<0)break;i.push(r)}else if(r<2048){if((e-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function oi(t){return yo.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(ri,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function ii(t,e,r,n){for(var o=0;o<n&&!(o+r>=e.length||o>=t.length);++o)e[o+r]=t[o];return o}function si(t){return t instanceof ArrayBuffer||null!=t&&null!=t.constructor&&"ArrayBuffer"===t.constructor.name&&"number"==typeof t.byteLength}function ai(t){return t!=t}var li={}.toString,ui=Array.isArray||function(t){return"[object Array]"==li.call(t)},pi={};(function(t){var e=Object.prototype.toString,r="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===e.call(Blob),n="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===e.call(File);pi=function e(o){if(!o||"object"!=typeof o)return!1;if(ui(o)){for(var i=0,s=o.length;i<s;i++)if(e(o[i]))return!0;return!1}if("function"==typeof t&&t.isBuffer&&t.isBuffer(o)||"function"==typeof ArrayBuffer&&o instanceof ArrayBuffer||r&&o instanceof Blob||n&&o instanceof File)return!0;if(o.toJSON&&"function"==typeof o.toJSON&&1===arguments.length)return e(o.toJSON(),!0);for(var a in o)if(Object.prototype.hasOwnProperty.call(o,a)&&e(o[a]))return!0;return!1}}).call(this,ko.Buffer);var ci={encode:function(t){var e="";for(var r in t)t.hasOwnProperty(r)&&(e.length&&(e+="&"),e+=encodeURIComponent(r)+"="+encodeURIComponent(t[r]));return e},decode:function(t){for(var e={},r=t.split("&"),n=0,o=r.length;n<o;n++){var i=r[n].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}},hi=1e3,di=6e4,fi=864e5;function yi(t,e,r){if(!(t<e))return t<1.5*e?Math.floor(t/e)+" "+r:Math.ceil(t/e)+" "+r+"s"}var gi={};function mi(t){var e;function r(){if(r.enabled){var t=r,n=+new Date,o=n-(e||n);t.diff=o,t.prev=e,t.curr=n,e=n;for(var i=new Array(arguments.length),s=0;s<i.length;s++)i[s]=arguments[s];i[0]=gi.coerce(i[0]),"string"!=typeof i[0]&&i.unshift("%O");var a=0;i[0]=i[0].replace(/%([a-zA-Z%])/g,function(e,r){if("%%"===e)return e;a++;var n=gi.formatters[r];if("function"==typeof n){var o=i[a];e=n.call(t,o),i.splice(a,1),a--}return e}),gi.formatArgs.call(t,i),(r.log||gi.log||console.log.bind(console)).apply(t,i)}}return r.namespace=t,r.enabled=gi.enabled(t),r.useColors=gi.useColors(),r.color=function(t){var e,r=0;for(e in t)r=(r<<5)-r+t.charCodeAt(e),r|=0;return gi.colors[Math.abs(r)%gi.colors.length]}(t),r.destroy=vi,"function"==typeof gi.init&&gi.init(r),gi.instances.push(r),r}function vi(){var t=gi.instances.indexOf(this);return-1!==t&&(gi.instances.splice(t,1),!0)}(gi=gi=mi.debug=mi.default=mi).coerce=function(t){return t instanceof Error?t.stack||t.message:t},gi.disable=function(){gi.enable("")},gi.enable=function(t){var e;gi.save(t),gi.names=[],gi.skips=[];var r=("string"==typeof t?t:"").split(/[\s,]+/),n=r.length;for(e=0;e<n;e++)r[e]&&("-"===(t=r[e].replace(/\*/g,".*?"))[0]?gi.skips.push(new RegExp("^"+t.substr(1)+"$")):gi.names.push(new RegExp("^"+t+"$")));for(e=0;e<gi.instances.length;e++){var o=gi.instances[e];o.enabled=gi.enabled(o.namespace)}},gi.enabled=function(t){if("*"===t[t.length-1])return!0;var e,r;for(e=0,r=gi.skips.length;e<r;e++)if(gi.skips[e].test(t))return!1;for(e=0,r=gi.names.length;e<r;e++)if(gi.names[e].test(t))return!0;return!1},gi.humanize=function(t,e){e=e||{};var r,n=typeof t;if("string"===n&&t.length>0)return function(t){if(!((t=String(t)).length>100)){var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t);if(e){var r=parseFloat(e[1]);switch((e[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"days":case"day":case"d":return r*fi;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return r*di;case"seconds":case"second":case"secs":case"sec":case"s":return r*hi;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(t);if("number"===n&&!1===isNaN(t))return e.long?yi(r=t,fi,"day")||yi(r,36e5,"hour")||yi(r,di,"minute")||yi(r,hi,"second")||r+" ms":function(t){return t>=fi?Math.round(t/fi)+"d":t>=36e5?Math.round(t/36e5)+"h":t>=di?Math.round(t/di)+"m":t>=hi?Math.round(t/hi)+"s":t+"ms"}(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))},gi.instances=[],gi.names=[],gi.skips=[],gi.formatters={};var bi={};(function(t){function e(){var e;try{e=bi.storage.debug}catch(t){}return!e&&void 0!==t&&"env"in t&&(e=t.env.DEBUG),e}(bi=bi=gi).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},bi.formatArgs=function(t){var e=this.useColors;if(t[0]=(e?"%c":"")+this.namespace+(e?" %c":" ")+t[0]+(e?"%c ":" ")+"+"+bi.humanize(this.diff),e){var r="color: "+this.color;t.splice(1,0,r,"color: inherit");var n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,function(t){"%%"!==t&&(n++,"%c"===t&&(o=n))}),t.splice(o,0,r)}},bi.save=function(t){try{null==t?bi.storage.removeItem("debug"):bi.storage.debug=t}catch(t){}},bi.load=e,bi.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},bi.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(t){}}(),bi.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],bi.formatters.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}},bi.enable(e())}).call(this,Pt);var wi={};(function(t){wi=function(o){return e&&t.Buffer.isBuffer(o)||r&&(o instanceof t.ArrayBuffer||n(o))};var e="function"==typeof t.Buffer&&"function"==typeof t.Buffer.isBuffer,r="function"==typeof t.ArrayBuffer,n=r&&"function"==typeof t.ArrayBuffer.isView?t.ArrayBuffer.isView:function(e){return e.buffer instanceof t.ArrayBuffer}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var Si={};(function(t){var e=Object.prototype.toString,r="function"==typeof t.Blob||"[object BlobConstructor]"===e.call(t.Blob),n="function"==typeof t.File||"[object FileConstructor]"===e.call(t.File);Si.deconstructPacket=function(t){var e=[],r=t.data,n=t;return n.data=function t(e,r){if(!e)return e;if(wi(e)){var n={_placeholder:!0,num:r.length};return r.push(e),n}if(ui(e)){for(var o=new Array(e.length),i=0;i<e.length;i++)o[i]=t(e[i],r);return o}if("object"==typeof e&&!(e instanceof Date)){o={};for(var s in e)o[s]=t(e[s],r);return o}return e}(r,e),n.attachments=e.length,{packet:n,buffers:e}},Si.reconstructPacket=function(t,e){return t.data=function t(e,r){if(!e)return e;if(e&&e._placeholder)return r[e.num];if(ui(e))for(var n=0;n<e.length;n++)e[n]=t(e[n],r);else if("object"==typeof e)for(var o in e)e[o]=t(e[o],r);return e}(t.data,e),t.attachments=void 0,t},Si.removeBlobs=function(t,e){var o=0,i=t;!function t(s,a,l){if(!s)return s;if(r&&s instanceof Blob||n&&s instanceof File){o++;var u=new FileReader;u.onload=function(){l?l[a]=this.result:i=this.result,--o||e(i)},u.readAsArrayBuffer(s)}else if(ui(s))for(var p=0;p<s.length;p++)t(s[p],p,s);else if("object"==typeof s&&!wi(s))for(var c in s)t(s[c],c,s)}(i),o||e(i)}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var Pi={},_i=bi("socket.io-parser");function Ei(){}Pi.protocol=4,Pi.types=["CONNECT","DISCONNECT","EVENT","ACK","ERROR","BINARY_EVENT","BINARY_ACK"],Pi.CONNECT=0,Pi.DISCONNECT=1,Pi.EVENT=2,Pi.ACK=3,Pi.ERROR=4,Pi.BINARY_EVENT=5,Pi.BINARY_ACK=6,Pi.Encoder=Ei,Pi.Decoder=Ti;var ki=Pi.ERROR+'"encode error"';function Ci(t){var e=""+t.type;if(Pi.BINARY_EVENT!==t.type&&Pi.BINARY_ACK!==t.type||(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data){var r=function(t){try{return JSON.stringify(t)}catch(t){return!1}}(t.data);if(!1===r)return ki;e+=r}return _i("encoded %j as %s",t,e),e}function Ti(){this.reconstructor=null}function Ui(t){this.reconPack=t,this.buffers=[]}function Oi(t){return{type:Pi.ERROR,data:"parser error: "+t}}Ei.prototype.encode=function(t,e){_i("encoding packet %j",t),Pi.BINARY_EVENT===t.type||Pi.BINARY_ACK===t.type?function(t,e){Si.removeBlobs(t,function(t){var r=Si.deconstructPacket(t),n=Ci(r.packet),o=r.buffers;o.unshift(n),e(o)})}(t,e):e([Ci(t)])},io(Ti.prototype),Ti.prototype.add=function(t){var e;if("string"==typeof t)e=function(t){var e=0,r={type:Number(t.charAt(0))};if(null==Pi.types[r.type])return Oi("unknown packet type "+r.type);if(Pi.BINARY_EVENT===r.type||Pi.BINARY_ACK===r.type){for(var n="";"-"!==t.charAt(++e)&&(n+=t.charAt(e),e!=t.length););if(n!=Number(n)||"-"!==t.charAt(e))throw new Error("Illegal attachments");r.attachments=Number(n)}if("/"===t.charAt(e+1))for(r.nsp="";++e;){if(","===(i=t.charAt(e)))break;if(r.nsp+=i,e===t.length)break}else r.nsp="/";var o=t.charAt(e+1);if(""!==o&&Number(o)==o){for(r.id="";++e;){var i;if(null==(i=t.charAt(e))||Number(i)!=i){--e;break}if(r.id+=t.charAt(e),e===t.length)break}r.id=Number(r.id)}if(t.charAt(++e)){var s=function(t){try{return JSON.parse(t)}catch(t){return!1}}(t.substr(e));if(!(!1!==s&&(r.type===Pi.ERROR||ui(s))))return Oi("invalid payload");r.data=s}return _i("decoded %s as %j",t,r),r}(t),Pi.BINARY_EVENT===e.type||Pi.BINARY_ACK===e.type?(this.reconstructor=new Ui(e),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",e)):this.emit("decoded",e);else{if(!wi(t)&&!t.base64)throw new Error("Unknown type: "+t);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,this.emit("decoded",e))}},Ti.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},Ui.prototype.takeBinaryData=function(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e=Si.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null},Ui.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]};var Fi=function(t,e){for(var r=[],n=(e=e||0)||0;n<t.length;n++)r[n-e]=t[n];return r},Ai={},xi=bi("socket.io-client:socket");Ai=Ai=Ii;var Di={connect:1,connect_error:1,connect_timeout:1,connecting:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1,ping:1,pong:1},Ri=io.prototype.emit;function Ii(t,e,r){this.io=t,this.nsp=e,this.json=this,this.ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,this.flags={},r&&r.query&&(this.query=r.query),this.io.autoConnect&&this.open()}function Bi(){}io(Ii.prototype),Ii.prototype.subEvents=function(){if(!this.subs){var t=this.io;this.subs=[fo(t,"open",po(this,"onopen")),fo(t,"packet",po(this,"onpacket")),fo(t,"close",po(this,"onclose"))]}},Ii.prototype.open=Ii.prototype.connect=function(){return this.connected?this:(this.subEvents(),this.io.open(),"open"===this.io.readyState&&this.onopen(),this.emit("connecting"),this)},Ii.prototype.send=function(){var t=Fi(arguments);return t.unshift("message"),this.emit.apply(this,t),this},Ii.prototype.emit=function(t){if(Di.hasOwnProperty(t))return Ri.apply(this,arguments),this;var e=Fi(arguments),r={type:(void 0!==this.flags.binary?this.flags.binary:pi(e))?Pi.BINARY_EVENT:Pi.EVENT,data:e,options:{}};return r.options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof e[e.length-1]&&(xi("emitting packet with ack id %d",this.ids),this.acks[this.ids]=e.pop(),r.id=this.ids++),this.connected?this.packet(r):this.sendBuffer.push(r),this.flags={},this},Ii.prototype.packet=function(t){t.nsp=this.nsp,this.io.packet(t)},Ii.prototype.onopen=function(){if(xi("transport is open - connecting"),"/"!==this.nsp)if(this.query){var t="object"==typeof this.query?ci.encode(this.query):this.query;xi("sending connect packet with query %s",t),this.packet({type:Pi.CONNECT,query:t})}else this.packet({type:Pi.CONNECT})},Ii.prototype.onclose=function(t){xi("close (%s)",t),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",t)},Ii.prototype.onpacket=function(t){var e=t.nsp===this.nsp,r=t.type===Pi.ERROR&&"/"===t.nsp;if(e||r)switch(t.type){case Pi.CONNECT:this.onconnect();break;case Pi.EVENT:case Pi.BINARY_EVENT:this.onevent(t);break;case Pi.ACK:case Pi.BINARY_ACK:this.onack(t);break;case Pi.DISCONNECT:this.ondisconnect();break;case Pi.ERROR:this.emit("error",t.data)}},Ii.prototype.onevent=function(t){var e=t.data||[];xi("emitting event %j",e),null!=t.id&&(xi("attaching ack callback to event"),e.push(this.ack(t.id))),this.connected?Ri.apply(this,e):this.receiveBuffer.push(e)},Ii.prototype.ack=function(t){var e=this,r=!1;return function(){if(!r){r=!0;var n=Fi(arguments);xi("sending ack %j",n),e.packet({type:pi(n)?Pi.BINARY_ACK:Pi.ACK,id:t,data:n})}}},Ii.prototype.onack=function(t){var e=this.acks[t.id];"function"==typeof e?(xi("calling ack %s with %j",t.id,t.data),e.apply(this,t.data),delete this.acks[t.id]):xi("bad ack %s",t.id)},Ii.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},Ii.prototype.emitBuffered=function(){var t;for(t=0;t<this.receiveBuffer.length;t++)Ri.apply(this,this.receiveBuffer[t]);for(this.receiveBuffer=[],t=0;t<this.sendBuffer.length;t++)this.packet(this.sendBuffer[t]);this.sendBuffer=[]},Ii.prototype.ondisconnect=function(){xi("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},Ii.prototype.destroy=function(){if(this.subs){for(var t=0;t<this.subs.length;t++)this.subs[t].destroy();this.subs=null}this.io.destroy(this)},Ii.prototype.close=Ii.prototype.disconnect=function(){return this.connected&&(xi("performing disconnect (%s)",this.nsp),this.packet({type:Pi.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},Ii.prototype.compress=function(t){return this.flags.compress=t,this},Ii.prototype.binary=function(t){return this.flags.binary=t,this};var ji=function(t,e,r){var n=!1;return r=r||Bi,o.count=t,0===t?e():o;function o(t,i){if(o.count<=0)throw new Error("after called too many times");--o.count,t?(n=!0,e(t),e=r):0!==o.count||n||e(null,i)}},Li=function(t,e,r){var n=t.byteLength;if(e=e||0,r=r||n,t.slice)return t.slice(e,r);if(e<0&&(e+=n),r<0&&(r+=n),r>n&&(r=n),e>=n||e>=r||0===n)return new ArrayBuffer(0);for(var o=new Uint8Array(t),i=new Uint8Array(r-e),s=e,a=0;s<r;s++,a++)i[a]=o[s];return i.buffer},Mi={};!function(){"use strict";for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",e=new Uint8Array(256),r=0;r<t.length;r++)e[t.charCodeAt(r)]=r;Mi.encode=function(e){var r,n=new Uint8Array(e),o=n.length,i="";for(r=0;r<o;r+=3)i+=t[n[r]>>2],i+=t[(3&n[r])<<4|n[r+1]>>4],i+=t[(15&n[r+1])<<2|n[r+2]>>6],i+=t[63&n[r+2]];return o%3==2?i=i.substring(0,i.length-1)+"=":o%3==1&&(i=i.substring(0,i.length-2)+"=="),i},Mi.decode=function(t){var r,n,o,i,s,a=.75*t.length,l=t.length,u=0;"="===t[t.length-1]&&(a--,"="===t[t.length-2]&&a--);var p=new ArrayBuffer(a),c=new Uint8Array(p);for(r=0;r<l;r+=4)n=e[t.charCodeAt(r)],o=e[t.charCodeAt(r+1)],i=e[t.charCodeAt(r+2)],s=e[t.charCodeAt(r+3)],c[u++]=n<<2|o>>4,c[u++]=(15&o)<<4|i>>2,c[u++]=(3&i)<<6|63&s;return p}}();var Ni={};(function(t){var e=t.BlobBuilder||t.WebKitBlobBuilder||t.MSBlobBuilder||t.MozBlobBuilder,r=function(){try{return 2===new Blob(["hi"]).size}catch(t){return!1}}(),n=r&&function(){try{return 2===new Blob([new Uint8Array([1,2])]).size}catch(t){return!1}}(),o=e&&e.prototype.append&&e.prototype.getBlob;function i(t){for(var e=0;e<t.length;e++){var r=t[e];if(r.buffer instanceof ArrayBuffer){var n=r.buffer;if(r.byteLength!==n.byteLength){var o=new Uint8Array(r.byteLength);o.set(new Uint8Array(n,r.byteOffset,r.byteLength)),n=o.buffer}t[e]=n}}}Ni=r?n?t.Blob:function(t,e){return i(t),new Blob(t,e||{})}:o?function(t,r){r=r||{};var n=new e;i(t);for(var o=0;o<t.length;o++)n.append(t[o]);return r.type?n.getBlob(r.type):n.getBlob()}:void 0}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var zi=Object.keys||function(t){var e=[],r=Object.prototype.hasOwnProperty;for(var n in t)r.call(t,n)&&e.push(n);return e},qi={exports:{}};(function(t){!function(e){var r="object"==typeof qi.exports&&qi.exports,n=qi&&qi.exports==r&&qi,o="object"==typeof t&&t;o.global!==o&&o.window!==o||(e=o);var i,s,a,l=String.fromCharCode;function u(t){for(var e,r,n=[],o=0,i=t.length;o<i;)(e=t.charCodeAt(o++))>=55296&&e<=56319&&o<i?56320==(64512&(r=t.charCodeAt(o++)))?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),o--):n.push(e);return n}function p(t,e){if(t>=55296&&t<=57343){if(e)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value");return!1}return!0}function c(t,e){return l(t>>e&63|128)}function h(t,e){if(0==(4294967168&t))return l(t);var r="";return 0==(4294965248&t)?r=l(t>>6&31|192):0==(4294901760&t)?(p(t,e)||(t=65533),r=l(t>>12&15|224),r+=c(t,6)):0==(4292870144&t)&&(r=l(t>>18&7|240),r+=c(t,12),r+=c(t,6)),r+l(63&t|128)}function d(){if(a>=s)throw Error("Invalid byte index");var t=255&i[a];if(a++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}function f(t){var e,r;if(a>s)throw Error("Invalid byte index");if(a==s)return!1;if(e=255&i[a],a++,0==(128&e))return e;if(192==(224&e)){if((r=(31&e)<<6|d())>=128)return r;throw Error("Invalid continuation byte")}if(224==(240&e)){if((r=(15&e)<<12|d()<<6|d())>=2048)return p(r,t)?r:65533;throw Error("Invalid continuation byte")}if(240==(248&e)&&(r=(7&e)<<18|d()<<12|d()<<6|d())>=65536&&r<=1114111)return r;throw Error("Invalid UTF-8 detected")}var y={version:"2.1.2",encode:function(t,e){for(var r=!1!==(e=e||{}).strict,n=u(t),o=n.length,i=-1,s="";++i<o;)s+=h(n[i],r);return s},decode:function(t,e){var r=!1!==(e=e||{}).strict;i=u(t),s=i.length,a=0;for(var n,o=[];!1!==(n=f(r));)o.push(n);return function(t){for(var e,r=t.length,n=-1,o="";++n<r;)(e=t[n])>65535&&(o+=l((e-=65536)>>>10&1023|55296),e=56320|1023&e),o+=l(e);return o}(o)}};if(r&&!r.nodeType)if(n)n.exports=y;else{var g={}.hasOwnProperty;for(var m in y)g.call(y,m)&&(r[m]=y[m])}else e.utf8=y}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{}),qi=qi.exports;var Hi={};(function(t){var e;t&&t.ArrayBuffer&&(e=Mi);var r="undefined"!=typeof navigator&&/Android/i.test(navigator.userAgent),n="undefined"!=typeof navigator&&/PhantomJS/i.test(navigator.userAgent),o=r||n;Hi.protocol=3;var i=Hi.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6},s=zi(i),a={type:"error",data:"parser error"};function l(t,e,r){for(var n=new Array(t.length),o=ji(t.length,r),i=function(t,r,o){e(r,function(e,r){n[t]=r,o(e,n)})},s=0;s<t.length;s++)i(s,t[s],o)}Hi.encodePacket=function(e,r,n,s){"function"==typeof r&&(s=r,r=!1),"function"==typeof n&&(s=n,n=null);var a=void 0===e.data?void 0:e.data.buffer||e.data;if(t.ArrayBuffer&&a instanceof ArrayBuffer)return function(t,e,r){if(!e)return Hi.encodeBase64Packet(t,r);var n=t.data,o=new Uint8Array(n),s=new Uint8Array(1+n.byteLength);s[0]=i[t.type];for(var a=0;a<o.length;a++)s[a+1]=o[a];return r(s.buffer)}(e,r,s);if(Ni&&a instanceof t.Blob)return function(t,e,r){if(!e)return Hi.encodeBase64Packet(t,r);if(o)return function(t,e,r){if(!e)return Hi.encodeBase64Packet(t,r);var n=new FileReader;return n.onload=function(){t.data=n.result,Hi.encodePacket(t,e,!0,r)},n.readAsArrayBuffer(t.data)}(t,e,r);var n=new Uint8Array(1);return n[0]=i[t.type],r(new Ni([n.buffer,t.data]))}(e,r,s);if(a&&a.base64)return function(t,e){return e("b"+Hi.packets[t.type]+t.data.data)}(e,s);var l=i[e.type];return void 0!==e.data&&(l+=n?qi.encode(String(e.data),{strict:!1}):String(e.data)),s(""+l)},Hi.encodeBase64Packet=function(e,r){var n,o="b"+Hi.packets[e.type];if(Ni&&e.data instanceof t.Blob){var i=new FileReader;return i.onload=function(){var t=i.result.split(",")[1];r(o+t)},i.readAsDataURL(e.data)}try{n=String.fromCharCode.apply(null,new Uint8Array(e.data))}catch(t){for(var s=new Uint8Array(e.data),a=new Array(s.length),l=0;l<s.length;l++)a[l]=s[l];n=String.fromCharCode.apply(null,a)}return o+=t.btoa(n),r(o)},Hi.decodePacket=function(t,e,r){if(void 0===t)return a;if("string"==typeof t){if("b"===t.charAt(0))return Hi.decodeBase64Packet(t.substr(1),e);if(r&&!1===(t=function(t){try{t=qi.decode(t,{strict:!1})}catch(t){return!1}return t}(t)))return a;var n=t.charAt(0);return Number(n)==n&&s[n]?t.length>1?{type:s[n],data:t.substring(1)}:{type:s[n]}:a}n=new Uint8Array(t)[0];var o=Li(t,1);return Ni&&"blob"===e&&(o=new Ni([o])),{type:s[n],data:o}},Hi.decodeBase64Packet=function(t,r){var n=s[t.charAt(0)];if(!e)return{type:n,data:{base64:!0,data:t.substr(1)}};var o=e.decode(t.substr(1));return"blob"===r&&Ni&&(o=new Ni([o])),{type:n,data:o}},Hi.encodePayload=function(t,e,r){"function"==typeof e&&(r=e,e=null);var n=pi(t);return e&&n?Ni&&!o?Hi.encodePayloadAsBlob(t,r):Hi.encodePayloadAsArrayBuffer(t,r):t.length?void l(t,function(t,r){Hi.encodePacket(t,!!n&&e,!1,function(t){r(null,function(t){return t.length+":"+t}(t))})},function(t,e){return r(e.join(""))}):r("0:")},Hi.decodePayload=function(t,e,r){if("string"!=typeof t)return Hi.decodePayloadAsBinary(t,e,r);var n;if("function"==typeof e&&(r=e,e=null),""===t)return r(a,0,1);for(var o,i,s="",l=0,u=t.length;l<u;l++){var p=t.charAt(l);if(":"===p){if(""===s||s!=(o=Number(s)))return r(a,0,1);if(s!=(i=t.substr(l+1,o)).length)return r(a,0,1);if(i.length){if(n=Hi.decodePacket(i,e,!1),a.type===n.type&&a.data===n.data)return r(a,0,1);if(!1===r(n,l+o,u))return}l+=o,s=""}else s+=p}return""!==s?r(a,0,1):void 0},Hi.encodePayloadAsArrayBuffer=function(t,e){if(!t.length)return e(new ArrayBuffer(0));l(t,function(t,e){Hi.encodePacket(t,!0,!0,function(t){return e(null,t)})},function(t,r){var n=r.reduce(function(t,e){var r;return t+(r="string"==typeof e?e.length:e.byteLength).toString().length+r+2},0),o=new Uint8Array(n),i=0;return r.forEach(function(t){var e="string"==typeof t,r=t;if(e){for(var n=new Uint8Array(t.length),s=0;s<t.length;s++)n[s]=t.charCodeAt(s);r=n.buffer}o[i++]=e?0:1;var a=r.byteLength.toString();for(s=0;s<a.length;s++)o[i++]=parseInt(a[s]);for(o[i++]=255,n=new Uint8Array(r),s=0;s<n.length;s++)o[i++]=n[s]}),e(o.buffer)})},Hi.encodePayloadAsBlob=function(t,e){l(t,function(t,e){Hi.encodePacket(t,!0,!0,function(t){var r=new Uint8Array(1);if(r[0]=1,"string"==typeof t){for(var n=new Uint8Array(t.length),o=0;o<t.length;o++)n[o]=t.charCodeAt(o);t=n.buffer,r[0]=0}var i=(t instanceof ArrayBuffer?t.byteLength:t.size).toString(),s=new Uint8Array(i.length+1);for(o=0;o<i.length;o++)s[o]=parseInt(i[o]);if(s[i.length]=255,Ni){var a=new Ni([r.buffer,s.buffer,t]);e(null,a)}})},function(t,r){return e(new Ni(r))})},Hi.decodePayloadAsBinary=function(t,e,r){"function"==typeof e&&(r=e,e=null);for(var n=t,o=[];n.byteLength>0;){for(var i=new Uint8Array(n),s=0===i[0],l="",u=1;255!==i[u];u++){if(l.length>310)return r(a,0,1);l+=i[u]}n=Li(n,2+l.length),l=parseInt(l);var p=Li(n,0,l);if(s)try{p=String.fromCharCode.apply(null,new Uint8Array(p))}catch(t){var c=new Uint8Array(p);for(p="",u=0;u<c.length;u++)p+=String.fromCharCode(c[u])}o.push(p),n=Li(n,l)}var h=o.length;o.forEach(function(t,n){r(Hi.decodePacket(t,e,!0),n,h)})}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var Wi=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Xi=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],Vi=function(t){var e=t,r=t.indexOf("["),n=t.indexOf("]");-1!=r&&-1!=n&&(t=t.substring(0,r)+t.substring(r,n).replace(/:/g,";")+t.substring(n,t.length));for(var o=Wi.exec(t||""),i={},s=14;s--;)i[Xi[s]]=o[s]||"";return-1!=r&&-1!=n&&(i.source=e,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i},Gi={};function Ki(t){this.path=t.path,this.hostname=t.hostname,this.port=t.port,this.secure=t.secure,this.query=t.query,this.timestampParam=t.timestampParam,this.timestampRequests=t.timestampRequests,this.readyState="",this.agent=t.agent||!1,this.socket=t.socket,this.enablesXDR=t.enablesXDR,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.forceNode=t.forceNode,this.extraHeaders=t.extraHeaders,this.localAddress=t.localAddress}Gi=Ki,io(Ki.prototype),Ki.prototype.onError=function(t,e){var r=new Error(t);return r.type="TransportError",r.description=e,this.emit("error",r),this},Ki.prototype.open=function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this},Ki.prototype.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},Ki.prototype.send=function(t){if("open"!==this.readyState)throw new Error("Transport not open");this.write(t)},Ki.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},Ki.prototype.onData=function(t){var e=Hi.decodePacket(t,this.socket.binaryType);this.onPacket(e)},Ki.prototype.onPacket=function(t){this.emit("packet",t)},Ki.prototype.onClose=function(){this.readyState="closed",this.emit("close")};var Yi=function(t,e){var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t},Ji={};try{Ji="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){Ji=!1}var $i={};(function(t){$i=function(e){var r=e.xdomain,n=e.xscheme,o=e.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!r||Ji))return new XMLHttpRequest}catch(t){}try{if("undefined"!=typeof XDomainRequest&&!n&&o)return new XDomainRequest}catch(t){}if(!r)try{return new(t[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var Qi,Zi={},ts="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),es=64,rs={},ns=0,os=0;function is(t){var e="";do{e=ts[t%es]+e,t=Math.floor(t/es)}while(t>0);return e}function ss(){var t=is(+new Date);return t!==Qi?(ns=0,Qi=t):t+"."+is(ns++)}for(;os<es;os++)rs[ts[os]]=os;ss.encode=is,ss.decode=function(t){var e=0;for(os=0;os<t.length;os++)e=e*es+rs[t.charAt(os)];return e},Zi=ss;var as=bi("engine.io-client:polling"),ls=ps,us=null!=new $i({xdomain:!1}).responseType;function ps(t){var e=t&&t.forceBase64;us&&!e||(this.supportsBinary=!1),Gi.call(this,t)}Yi(ps,Gi),ps.prototype.name="polling",ps.prototype.doOpen=function(){this.poll()},ps.prototype.pause=function(t){var e=this;function r(){as("paused"),e.readyState="paused",t()}if(this.readyState="pausing",this.polling||!this.writable){var n=0;this.polling&&(as("we are currently polling - waiting to pause"),n++,this.once("pollComplete",function(){as("pre-pause polling complete"),--n||r()})),this.writable||(as("we are currently writing - waiting to pause"),n++,this.once("drain",function(){as("pre-pause writing complete"),--n||r()}))}else r()},ps.prototype.poll=function(){as("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},ps.prototype.onData=function(t){var e=this;as("polling got data %s",t),Hi.decodePayload(t,this.socket.binaryType,function(t,r,n){if("opening"===e.readyState&&e.onOpen(),"close"===t.type)return e.onClose(),!1;e.onPacket(t)}),"closed"!==this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"===this.readyState?this.poll():as('ignoring poll - transport state "%s"',this.readyState))},ps.prototype.doClose=function(){var t=this;function e(){as("writing close packet"),t.write([{type:"close"}])}"open"===this.readyState?(as("transport open - closing"),e()):(as("transport not open - deferring close"),this.once("open",e))},ps.prototype.write=function(t){var e=this;this.writable=!1;var r=function(){e.writable=!0,e.emit("drain")};Hi.encodePayload(t,this.supportsBinary,function(t){e.doWrite(t,r)})},ps.prototype.uri=function(){var t=this.query||{},e=this.secure?"https":"http",r="";return!1!==this.timestampRequests&&(t[this.timestampParam]=Zi()),this.supportsBinary||t.sid||(t.b64=1),t=ci.encode(t),this.port&&("https"===e&&443!==Number(this.port)||"http"===e&&80!==Number(this.port))&&(r=":"+this.port),t.length&&(t="?"+t),e+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+r+this.path+t};var cs={};(function(t){cs=i;var e,r=/\n/g,n=/\\n/g;function o(){}function i(r){ls.call(this,r),this.query=this.query||{},e||(t.___eio||(t.___eio=[]),e=t.___eio),this.index=e.length;var n=this;e.push(function(t){n.onData(t)}),this.query.j=this.index,t.document&&t.addEventListener&&t.addEventListener("beforeunload",function(){n.script&&(n.script.onerror=o)},!1)}Yi(i,ls),i.prototype.supportsBinary=!1,i.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),ls.prototype.doClose.call(this)},i.prototype.doPoll=function(){var t=this,e=document.createElement("script");this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),e.async=!0,e.src=this.uri(),e.onerror=function(e){t.onError("jsonp poll error",e)};var r=document.getElementsByTagName("script")[0];r?r.parentNode.insertBefore(e,r):(document.head||document.body).appendChild(e),this.script=e,"undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent)&&setTimeout(function(){var t=document.createElement("iframe");document.body.appendChild(t),document.body.removeChild(t)},100)},i.prototype.doWrite=function(t,e){var o=this;if(!this.form){var i,s=document.createElement("form"),a=document.createElement("textarea"),l=this.iframeId="eio_iframe_"+this.index;s.className="socketio",s.style.position="absolute",s.style.top="-1000px",s.style.left="-1000px",s.target=l,s.method="POST",s.setAttribute("accept-charset","utf-8"),a.name="d",s.appendChild(a),document.body.appendChild(s),this.form=s,this.area=a}function u(){p(),e()}function p(){if(o.iframe)try{o.form.removeChild(o.iframe)}catch(t){o.onError("jsonp polling iframe removal error",t)}try{var t='<iframe src="javascript:0" name="'+o.iframeId+'">';i=document.createElement(t)}catch(t){(i=document.createElement("iframe")).name=o.iframeId,i.src="javascript:0"}i.id=o.iframeId,o.form.appendChild(i),o.iframe=i}this.form.action=this.uri(),p(),t=t.replace(n,"\\\n"),this.area.value=t.replace(r,"\\n");try{this.form.submit()}catch(t){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"===o.iframe.readyState&&u()}:this.iframe.onload=u}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var hs={};(function(t){var e=bi("engine.io-client:polling-xhr");function r(){}function n(e){if(ls.call(this,e),this.requestTimeout=e.requestTimeout,this.extraHeaders=e.extraHeaders,t.location){var r="https:"===location.protocol,n=location.port;n||(n=r?443:80),this.xd=e.hostname!==t.location.hostname||n!==e.port,this.xs=e.secure!==r}}function o(t){this.method=t.method||"GET",this.uri=t.uri,this.xd=!!t.xd,this.xs=!!t.xs,this.async=!1!==t.async,this.data=void 0!==t.data?t.data:null,this.agent=t.agent,this.isBinary=t.isBinary,this.supportsBinary=t.supportsBinary,this.enablesXDR=t.enablesXDR,this.requestTimeout=t.requestTimeout,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.extraHeaders=t.extraHeaders,this.create()}function i(){for(var t in o.requests)o.requests.hasOwnProperty(t)&&o.requests[t].abort()}(hs=n).Request=o,Yi(n,ls),n.prototype.supportsBinary=!0,n.prototype.request=function(t){return(t=t||{}).uri=this.uri(),t.xd=this.xd,t.xs=this.xs,t.agent=this.agent||!1,t.supportsBinary=this.supportsBinary,t.enablesXDR=this.enablesXDR,t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized,t.requestTimeout=this.requestTimeout,t.extraHeaders=this.extraHeaders,new o(t)},n.prototype.doWrite=function(t,e){var r="string"!=typeof t&&void 0!==t,n=this.request({method:"POST",data:t,isBinary:r}),o=this;n.on("success",e),n.on("error",function(t){o.onError("xhr post error",t)}),this.sendXhr=n},n.prototype.doPoll=function(){e("xhr poll");var t=this.request(),r=this;t.on("data",function(t){r.onData(t)}),t.on("error",function(t){r.onError("xhr poll error",t)}),this.pollXhr=t},io(o.prototype),o.prototype.create=function(){var r={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR};r.pfx=this.pfx,r.key=this.key,r.passphrase=this.passphrase,r.cert=this.cert,r.ca=this.ca,r.ciphers=this.ciphers,r.rejectUnauthorized=this.rejectUnauthorized;var n=this.xhr=new $i(r),i=this;try{e("xhr open %s: %s",this.method,this.uri),n.open(this.method,this.uri,this.async);try{if(this.extraHeaders)for(var s in n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0),this.extraHeaders)this.extraHeaders.hasOwnProperty(s)&&n.setRequestHeader(s,this.extraHeaders[s])}catch(t){}if("POST"===this.method)try{this.isBinary?n.setRequestHeader("Content-type","application/octet-stream"):n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{n.setRequestHeader("Accept","*/*")}catch(t){}"withCredentials"in n&&(n.withCredentials=!0),this.requestTimeout&&(n.timeout=this.requestTimeout),this.hasXDR()?(n.onload=function(){i.onLoad()},n.onerror=function(){i.onError(n.responseText)}):n.onreadystatechange=function(){if(2===n.readyState)try{var t=n.getResponseHeader("Content-Type");i.supportsBinary&&"application/octet-stream"===t&&(n.responseType="arraybuffer")}catch(t){}4===n.readyState&&(200===n.status||1223===n.status?i.onLoad():setTimeout(function(){i.onError(n.status)},0))},e("xhr data %s",this.data),n.send(this.data)}catch(t){return void setTimeout(function(){i.onError(t)},0)}t.document&&(this.index=o.requestsCount++,o.requests[this.index]=this)},o.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},o.prototype.onData=function(t){this.emit("data",t),this.onSuccess()},o.prototype.onError=function(t){this.emit("error",t),this.cleanup(!0)},o.prototype.cleanup=function(e){if(void 0!==this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=r:this.xhr.onreadystatechange=r,e)try{this.xhr.abort()}catch(t){}t.document&&delete o.requests[this.index],this.xhr=null}},o.prototype.onLoad=function(){var t;try{var e;try{e=this.xhr.getResponseHeader("Content-Type")}catch(t){}t="application/octet-stream"===e&&this.xhr.response||this.xhr.responseText}catch(t){this.onError(t)}null!=t&&this.onData(t)},o.prototype.hasXDR=function(){return void 0!==t.XDomainRequest&&!this.xs&&this.enablesXDR},o.prototype.abort=function(){this.cleanup()},o.requestsCount=0,o.requests={},t.document&&(t.attachEvent?t.attachEvent("onunload",i):t.addEventListener&&t.addEventListener("beforeunload",i,!1))}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var ds={},fs={};(function(t){var e,r=bi("engine.io-client:websocket"),n=t.WebSocket||t.MozWebSocket;if("undefined"==typeof window)try{e=ds}catch(t){}var o=n;function i(t){t&&t.forceBase64&&(this.supportsBinary=!1),this.perMessageDeflate=t.perMessageDeflate,this.usingBrowserWebSocket=n&&!t.forceNode,this.protocols=t.protocols,this.usingBrowserWebSocket||(o=e),Gi.call(this,t)}o||"undefined"!=typeof window||(o=e),fs=i,Yi(i,Gi),i.prototype.name="websocket",i.prototype.supportsBinary=!0,i.prototype.doOpen=function(){if(this.check()){var t=this.uri(),e=this.protocols,r={agent:this.agent,perMessageDeflate:this.perMessageDeflate};r.pfx=this.pfx,r.key=this.key,r.passphrase=this.passphrase,r.cert=this.cert,r.ca=this.ca,r.ciphers=this.ciphers,r.rejectUnauthorized=this.rejectUnauthorized,this.extraHeaders&&(r.headers=this.extraHeaders),this.localAddress&&(r.localAddress=this.localAddress);try{this.ws=this.usingBrowserWebSocket?e?new o(t,e):new o(t):new o(t,e,r)}catch(t){return this.emit("error",t)}void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="nodebuffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},i.prototype.addEventListeners=function(){var t=this;this.ws.onopen=function(){t.onOpen()},this.ws.onclose=function(){t.onClose()},this.ws.onmessage=function(e){t.onData(e.data)},this.ws.onerror=function(e){t.onError("websocket error",e)}},i.prototype.write=function(e){var n=this;this.writable=!1;for(var o=e.length,i=0,s=o;i<s;i++)!function(e){Hi.encodePacket(e,n.supportsBinary,function(i){if(!n.usingBrowserWebSocket){var s={};e.options&&(s.compress=e.options.compress),n.perMessageDeflate&&("string"==typeof i?t.Buffer.byteLength(i):i.length)<n.perMessageDeflate.threshold&&(s.compress=!1)}try{n.usingBrowserWebSocket?n.ws.send(i):n.ws.send(i,s)}catch(t){r("websocket closed before onclose event")}--o||(n.emit("flush"),setTimeout(function(){n.writable=!0,n.emit("drain")},0))})}(e[i])},i.prototype.onClose=function(){Gi.prototype.onClose.call(this)},i.prototype.doClose=function(){void 0!==this.ws&&this.ws.close()},i.prototype.uri=function(){var t=this.query||{},e=this.secure?"wss":"ws",r="";return this.port&&("wss"===e&&443!==Number(this.port)||"ws"===e&&80!==Number(this.port))&&(r=":"+this.port),this.timestampRequests&&(t[this.timestampParam]=Zi()),this.supportsBinary||(t.b64=1),(t=ci.encode(t)).length&&(t="?"+t),e+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+r+this.path+t},i.prototype.check=function(){return!(!o||"__initialize"in o&&this.name===i.prototype.name)}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var ys={};(function(t){ys.polling=function(e){var r=!1,n=!1,o=!1!==e.jsonp;if(t.location){var i="https:"===location.protocol,s=location.port;s||(s=i?443:80),r=e.hostname!==location.hostname||s!==e.port,n=e.secure!==i}if(e.xdomain=r,e.xscheme=n,"open"in new $i(e)&&!e.forceJSONP)return new hs(e);if(!o)throw new Error("JSONP disabled");return new cs(e)},ys.websocket=fs}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var gs={};(function(t){var e=bi("engine.io-client:socket");function r(e,n){if(!(this instanceof r))return new r(e,n);n=n||{},e&&"object"==typeof e&&(n=e,e=null),e?(e=Vi(e),n.hostname=e.host,n.secure="https"===e.protocol||"wss"===e.protocol,n.port=e.port,e.query&&(n.query=e.query)):n.host&&(n.hostname=Vi(n.host).host),this.secure=null!=n.secure?n.secure:t.location&&"https:"===location.protocol,n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.agent=n.agent||!1,this.hostname=n.hostname||(t.location?location.hostname:"localhost"),this.port=n.port||(t.location&&location.port?location.port:this.secure?443:80),this.query=n.query||{},"string"==typeof this.query&&(this.query=ci.decode(this.query)),this.upgrade=!1!==n.upgrade,this.path=(n.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!n.forceJSONP,this.jsonp=!1!==n.jsonp,this.forceBase64=!!n.forceBase64,this.enablesXDR=!!n.enablesXDR,this.timestampParam=n.timestampParam||"t",this.timestampRequests=n.timestampRequests,this.transports=n.transports||["polling","websocket"],this.transportOptions=n.transportOptions||{},this.readyState="",this.writeBuffer=[],this.prevBufferLen=0,this.policyPort=n.policyPort||843,this.rememberUpgrade=n.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=n.onlyBinaryUpgrades,this.perMessageDeflate=!1!==n.perMessageDeflate&&(n.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=n.pfx||null,this.key=n.key||null,this.passphrase=n.passphrase||null,this.cert=n.cert||null,this.ca=n.ca||null,this.ciphers=n.ciphers||null,this.rejectUnauthorized=void 0===n.rejectUnauthorized||n.rejectUnauthorized,this.forceNode=!!n.forceNode;var o="object"==typeof t&&t;o.global===o&&(n.extraHeaders&&Object.keys(n.extraHeaders).length>0&&(this.extraHeaders=n.extraHeaders),n.localAddress&&(this.localAddress=n.localAddress)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingIntervalTimer=null,this.pingTimeoutTimer=null,this.open()}gs=r,r.priorWebsocketSuccess=!1,io(r.prototype),r.protocol=Hi.protocol,r.Socket=r,r.Transport=Gi,r.transports=ys,r.parser=Hi,r.prototype.createTransport=function(t){e('creating transport "%s"',t);var r=function(t){var e={};for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);return e}(this.query);r.EIO=Hi.protocol,r.transport=t;var n=this.transportOptions[t]||{};return this.id&&(r.sid=this.id),new ys[t]({query:r,socket:this,agent:n.agent||this.agent,hostname:n.hostname||this.hostname,port:n.port||this.port,secure:n.secure||this.secure,path:n.path||this.path,forceJSONP:n.forceJSONP||this.forceJSONP,jsonp:n.jsonp||this.jsonp,forceBase64:n.forceBase64||this.forceBase64,enablesXDR:n.enablesXDR||this.enablesXDR,timestampRequests:n.timestampRequests||this.timestampRequests,timestampParam:n.timestampParam||this.timestampParam,policyPort:n.policyPort||this.policyPort,pfx:n.pfx||this.pfx,key:n.key||this.key,passphrase:n.passphrase||this.passphrase,cert:n.cert||this.cert,ca:n.ca||this.ca,ciphers:n.ciphers||this.ciphers,rejectUnauthorized:n.rejectUnauthorized||this.rejectUnauthorized,perMessageDeflate:n.perMessageDeflate||this.perMessageDeflate,extraHeaders:n.extraHeaders||this.extraHeaders,forceNode:n.forceNode||this.forceNode,localAddress:n.localAddress||this.localAddress,requestTimeout:n.requestTimeout||this.requestTimeout,protocols:n.protocols||void 0})},r.prototype.open=function(){var t;if(this.rememberUpgrade&&r.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))t="websocket";else{if(0===this.transports.length){var e=this;return void setTimeout(function(){e.emit("error","No transports available")},0)}t=this.transports[0]}this.readyState="opening";try{t=this.createTransport(t)}catch(t){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)},r.prototype.setTransport=function(t){e("setting transport %s",t.name);var r=this;this.transport&&(e("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=t,t.on("drain",function(){r.onDrain()}).on("packet",function(t){r.onPacket(t)}).on("error",function(t){r.onError(t)}).on("close",function(){r.onClose("transport close")})},r.prototype.probe=function(t){e('probing transport "%s"',t);var n=this.createTransport(t,{probe:1}),o=!1,i=this;function s(){if(i.onlyBinaryUpgrades){var s=!this.supportsBinary&&i.transport.supportsBinary;o=o||s}o||(e('probe transport "%s" opened',t),n.send([{type:"ping",data:"probe"}]),n.once("packet",function(s){if(!o)if("pong"===s.type&&"probe"===s.data){if(e('probe transport "%s" pong',t),i.upgrading=!0,i.emit("upgrading",n),!n)return;r.priorWebsocketSuccess="websocket"===n.name,e('pausing current transport "%s"',i.transport.name),i.transport.pause(function(){o||"closed"!==i.readyState&&(e("changing transport and sending upgrade packet"),h(),i.setTransport(n),n.send([{type:"upgrade"}]),i.emit("upgrade",n),n=null,i.upgrading=!1,i.flush())})}else{e('probe transport "%s" failed',t);var a=new Error("probe error");a.transport=n.name,i.emit("upgradeError",a)}}))}function a(){o||(o=!0,h(),n.close(),n=null)}function l(r){var o=new Error("probe error: "+r);o.transport=n.name,a(),e('probe transport "%s" failed because of error: %s',t,r),i.emit("upgradeError",o)}function u(){l("transport closed")}function p(){l("socket closed")}function c(t){n&&t.name!==n.name&&(e('"%s" works - aborting "%s"',t.name,n.name),a())}function h(){n.removeListener("open",s),n.removeListener("error",l),n.removeListener("close",u),i.removeListener("close",p),i.removeListener("upgrading",c)}r.priorWebsocketSuccess=!1,n.once("open",s),n.once("error",l),n.once("close",u),this.once("close",p),this.once("upgrading",c),n.open()},r.prototype.onOpen=function(){if(e("socket open"),this.readyState="open",r.priorWebsocketSuccess="websocket"===this.transport.name,this.emit("open"),this.flush(),"open"===this.readyState&&this.upgrade&&this.transport.pause){e("starting upgrade probes");for(var t=0,n=this.upgrades.length;t<n;t++)this.probe(this.upgrades[t])}},r.prototype.onPacket=function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(e('socket receive: type "%s", data "%s"',t.type,t.data),this.emit("packet",t),this.emit("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var r=new Error("server error");r.code=t.data,this.onError(r);break;case"message":this.emit("data",t.data),this.emit("message",t.data)}else e('packet received with socket readyState "%s"',this.readyState)},r.prototype.onHandshake=function(t){this.emit("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.onOpen(),"closed"!==this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},r.prototype.onHeartbeat=function(t){clearTimeout(this.pingTimeoutTimer);var e=this;e.pingTimeoutTimer=setTimeout(function(){"closed"!==e.readyState&&e.onClose("ping timeout")},t||e.pingInterval+e.pingTimeout)},r.prototype.setPing=function(){var t=this;clearTimeout(t.pingIntervalTimer),t.pingIntervalTimer=setTimeout(function(){e("writing ping packet - expecting pong within %sms",t.pingTimeout),t.ping(),t.onHeartbeat(t.pingTimeout)},t.pingInterval)},r.prototype.ping=function(){var t=this;this.sendPacket("ping",function(){t.emit("ping")})},r.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emit("drain"):this.flush()},r.prototype.flush=function(){"closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(e("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},r.prototype.write=r.prototype.send=function(t,e,r){return this.sendPacket("message",t,e,r),this},r.prototype.sendPacket=function(t,e,r,n){if("function"==typeof e&&(n=e,e=void 0),"function"==typeof r&&(n=r,r=null),"closing"!==this.readyState&&"closed"!==this.readyState){(r=r||{}).compress=!1!==r.compress;var o={type:t,data:e,options:r};this.emit("packetCreate",o),this.writeBuffer.push(o),n&&this.once("flush",n),this.flush()}},r.prototype.close=function(){if("opening"===this.readyState||"open"===this.readyState){this.readyState="closing";var t=this;this.writeBuffer.length?this.once("drain",function(){this.upgrading?o():r()}):this.upgrading?o():r()}function r(){t.onClose("forced close"),e("socket closing - telling transport to close"),t.transport.close()}function n(){t.removeListener("upgrade",n),t.removeListener("upgradeError",n),r()}function o(){t.once("upgrade",n),t.once("upgradeError",n)}return this},r.prototype.onError=function(t){e("socket error %j",t),r.priorWebsocketSuccess=!1,this.emit("error",t),this.onClose("transport error",t)},r.prototype.onClose=function(t,r){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(e('socket close with reason: "%s"',t),clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",t,r),this.writeBuffer=[],this.prevBufferLen=0)},r.prototype.filterUpgrades=function(t){for(var e=[],r=0,n=t.length;r<n;r++)~ho(this.transports,t[r])&&e.push(t[r]);return e}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var ms={};(ms=gs).parser=Hi;var vs={},bs=bi("socket.io-client:manager"),ws=Object.prototype.hasOwnProperty;function Ss(t,e){if(!(this instanceof Ss))return new Ss(t,e);t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.nsps={},this.subs=[],this.opts=e,this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(e.randomizationFactor||.5),this.backoff=new ao({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this.readyState="closed",this.uri=t,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[];var r=e.parser||Pi;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this.autoConnect=!1!==e.autoConnect,this.autoConnect&&this.open()}vs=Ss,Ss.prototype.emitAll=function(){for(var t in this.emit.apply(this,arguments),this.nsps)ws.call(this.nsps,t)&&this.nsps[t].emit.apply(this.nsps[t],arguments)},Ss.prototype.updateSocketIds=function(){for(var t in this.nsps)ws.call(this.nsps,t)&&(this.nsps[t].id=this.generateId(t))},Ss.prototype.generateId=function(t){return("/"===t?"":t+"#")+this.engine.id},io(Ss.prototype),Ss.prototype.reconnection=function(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection},Ss.prototype.reconnectionAttempts=function(t){return arguments.length?(this._reconnectionAttempts=t,this):this._reconnectionAttempts},Ss.prototype.reconnectionDelay=function(t){return arguments.length?(this._reconnectionDelay=t,this.backoff&&this.backoff.setMin(t),this):this._reconnectionDelay},Ss.prototype.randomizationFactor=function(t){return arguments.length?(this._randomizationFactor=t,this.backoff&&this.backoff.setJitter(t),this):this._randomizationFactor},Ss.prototype.reconnectionDelayMax=function(t){return arguments.length?(this._reconnectionDelayMax=t,this.backoff&&this.backoff.setMax(t),this):this._reconnectionDelayMax},Ss.prototype.timeout=function(t){return arguments.length?(this._timeout=t,this):this._timeout},Ss.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},Ss.prototype.open=Ss.prototype.connect=function(t,e){if(bs("readyState %s",this.readyState),~this.readyState.indexOf("open"))return this;bs("opening %s",this.uri),this.engine=ms(this.uri,this.opts);var r=this.engine,n=this;this.readyState="opening",this.skipReconnect=!1;var o=fo(r,"open",function(){n.onopen(),t&&t()}),i=fo(r,"error",function(e){if(bs("connect_error"),n.cleanup(),n.readyState="closed",n.emitAll("connect_error",e),t){var r=new Error("Connection error");r.data=e,t(r)}else n.maybeReconnectOnOpen()});if(!1!==this._timeout){var s=this._timeout;bs("connect attempt will timeout after %d",s);var a=setTimeout(function(){bs("connect attempt timed out after %d",s),o.destroy(),r.close(),r.emit("error","timeout"),n.emitAll("connect_timeout",s)},s);this.subs.push({destroy:function(){clearTimeout(a)}})}return this.subs.push(o),this.subs.push(i),this},Ss.prototype.onopen=function(){bs("open"),this.cleanup(),this.readyState="open",this.emit("open");var t=this.engine;this.subs.push(fo(t,"data",po(this,"ondata"))),this.subs.push(fo(t,"ping",po(this,"onping"))),this.subs.push(fo(t,"pong",po(this,"onpong"))),this.subs.push(fo(t,"error",po(this,"onerror"))),this.subs.push(fo(t,"close",po(this,"onclose"))),this.subs.push(fo(this.decoder,"decoded",po(this,"ondecoded")))},Ss.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},Ss.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},Ss.prototype.ondata=function(t){this.decoder.add(t)},Ss.prototype.ondecoded=function(t){this.emit("packet",t)},Ss.prototype.onerror=function(t){bs("error",t),this.emitAll("error",t)},Ss.prototype.socket=function(t,e){var r=this.nsps[t];if(!r){r=new Ai(this,t,e),this.nsps[t]=r;var n=this;r.on("connecting",o),r.on("connect",function(){r.id=n.generateId(t)}),this.autoConnect&&o()}function o(){~ho(n.connecting,r)||n.connecting.push(r)}return r},Ss.prototype.destroy=function(t){var e=ho(this.connecting,t);~e&&this.connecting.splice(e,1),this.connecting.length||this.close()},Ss.prototype.packet=function(t){bs("writing packet %j",t);var e=this;t.query&&0===t.type&&(t.nsp+="?"+t.query),e.encoding?e.packetBuffer.push(t):(e.encoding=!0,this.encoder.encode(t,function(r){for(var n=0;n<r.length;n++)e.engine.write(r[n],t.options);e.encoding=!1,e.processPacketQueue()}))},Ss.prototype.processPacketQueue=function(){if(this.packetBuffer.length>0&&!this.encoding){var t=this.packetBuffer.shift();this.packet(t)}},Ss.prototype.cleanup=function(){bs("cleanup");for(var t=this.subs.length,e=0;e<t;e++)this.subs.shift().destroy();this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},Ss.prototype.close=Ss.prototype.disconnect=function(){bs("disconnect"),this.skipReconnect=!0,this.reconnecting=!1,"opening"===this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},Ss.prototype.onclose=function(t){bs("onclose"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",t),this._reconnection&&!this.skipReconnect&&this.reconnect()},Ss.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var t=this;if(this.backoff.attempts>=this._reconnectionAttempts)bs("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1;else{var e=this.backoff.duration();bs("will wait %dms before reconnect attempt",e),this.reconnecting=!0;var r=setTimeout(function(){t.skipReconnect||(bs("attempting reconnect"),t.emitAll("reconnect_attempt",t.backoff.attempts),t.emitAll("reconnecting",t.backoff.attempts),t.skipReconnect||t.open(function(e){e?(bs("reconnect attempt error"),t.reconnecting=!1,t.reconnect(),t.emitAll("reconnect_error",e.data)):(bs("reconnect success"),t.onreconnect())}))},e);this.subs.push({destroy:function(){clearTimeout(r)}})}},Ss.prototype.onreconnect=function(){var t=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",t)};var Ps={};(function(t){var e=bi("socket.io-client:url");Ps=function(r,n){var o=r;n=n||t.location,null==r&&(r=n.protocol+"//"+n.host),"string"==typeof r&&("/"===r.charAt(0)&&(r="/"===r.charAt(1)?n.protocol+r:n.host+r),/^(https?|wss?):\/\//.test(r)||(e("protocol-less url %s",r),r=void 0!==n?n.protocol+"//"+r:"https://"+r),e("parse %s",r),o=Vi(r)),o.port||(/^(http|ws)$/.test(o.protocol)?o.port="80":/^(http|ws)s$/.test(o.protocol)&&(o.port="443")),o.path=o.path||"/";var i=-1!==o.host.indexOf(":")?"["+o.host+"]":o.host;return o.id=o.protocol+"://"+i+":"+o.port,o.href=o.protocol+"://"+i+(n&&n.port===o.port?"":":"+o.port),o}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var _s={},Es=bi("socket.io-client"),ks=(_s=_s=Cs).managers={};function Cs(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var r,n=Ps(t),o=n.source,i=n.id,s=n.path,a=ks[i]&&s in ks[i].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?(Es("ignoring socket cache for %s",o),r=vs(o,e)):(ks[i]||(Es("new io instance for %s",o),ks[i]=vs(o,e)),r=ks[i]),n.query&&!e.query&&(e.query=n.query),r.socket(n.path,e)}_s.protocol=Pi.protocol,_s.connect=Cs,_s.Manager=vs,_s.Socket=Ai;var Ts=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Us=void 0,Os=["ASSEMBLY_UPLOADING","ASSEMBLY_EXECUTING","ASSEMBLY_COMPLETED"];function Fs(t,e){return Os.indexOf(t)>=Os.indexOf(e)}var As=function(t){function e(r){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this));return n.status=r,n.socket=null,n.pollInterval=null,n.closed=!1,n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.connect=function(){this._connectSocket(),this._beginPolling()},e.prototype._onFinished=function(){this.emit("finished"),this.close()},e.prototype._connectSocket=function(){var t=this,e=function(t){var e=/^\w+:\/\//.exec(t),r=0;e&&(r=e[0].length+1);var n=t.indexOf("/",r);return-1===n?{origin:t,pathname:"/"}:{origin:t.slice(0,n),pathname:t.slice(n)}}(this.status.websocket_url),r=(Us||(Us=_s),Us).connect(e.origin,{transports:["websocket"],path:e.pathname});r.on("connect",function(){r.emit("assembly_connect",{id:t.status.assembly_id}),t.emit("connect")}),r.on("error",function(){r.disconnect(),t.socket=null}),r.on("assembly_finished",function(){t._onFinished()}),r.on("assembly_upload_finished",function(e){t.emit("upload",e),t._fetchStatus({diff:!1})}),r.on("assembly_uploading_finished",function(){t.emit("executing"),t._fetchStatus({diff:!1})}),r.on("assembly_upload_meta_data_extracted",function(){t.emit("metadata"),t._fetchStatus({diff:!1})}),r.on("assembly_result_finished",function(e,r){t.emit("result",e,r),t._fetchStatus({diff:!1})}),r.on("assembly_error",function(e){t._onError(e),t._fetchStatus({diff:!1})}),this.socket=r},e.prototype._onError=function(t){this.emit("error",Ts(new Error(t.message),t))},e.prototype._beginPolling=function(){var t=this;this.pollInterval=setInterval(function(){t.socket&&t.socket.connected||t._fetchStatus()},2e3)},e.prototype._fetchStatus=function(){var t=this,e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).diff,r=void 0===e||e;return fetch(this.status.assembly_ssl_url).then(function(t){return t.json()}).then(function(e){t.closed||(t.emit("status",e),r?t.updateStatus(e):t.status=e)})},e.prototype.update=function(){return this._fetchStatus({diff:!0})},e.prototype.updateStatus=function(t){this._diffStatus(this.status,t),this.status=t},e.prototype._diffStatus=function(t,e){var r=this,n=t.ok,o=e.ok;if(e.error&&!t.error)return this._onError(e);var i=Fs(o,"ASSEMBLY_EXECUTING")&&!Fs(n,"ASSEMBLY_EXECUTING");i&&this.emit("executing"),Object.keys(e.uploads).filter(function(e){return!t.uploads.hasOwnProperty(e)}).map(function(t){return e.uploads[t]}).forEach(function(t){r.emit("upload",t)}),i&&this.emit("metadata"),Object.keys(e.results).forEach(function(n){var o=e.results[n],i=t.results[n];o.filter(function(t){return!i||!i.some(function(e){return e.id===t.id})}).forEach(function(t){r.emit("result",n,t)})}),Fs(o,"ASSEMBLY_COMPLETED")&&!Fs(n,"ASSEMBLY_COMPLETED")&&this.emit("finished")},e.prototype.close=function(){this.closed=!0,this.socket&&(this.socket.disconnect(),this.socket=null),clearInterval(this.pollInterval)},e}(io);function xs(t){if(!t)throw new Error("Transloadit: The `params` option is required.");if("string"==typeof t)try{t=JSON.parse(t)}catch(t){throw t.message="Transloadit: The `params` option is a malformed JSON string: "+t.message,t}if(!t.auth||!t.auth.key)throw new Error("Transloadit: The `params.auth.key` option is required. You can find your Transloadit API key at https://transloadit.com/accounts/credentials.")}var Ds=(Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t})(function(){function t(e,r){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.files=e,this.opts=r}return t.prototype._normalizeAssemblyOptions=function(t,e){if(Array.isArray(e.fields)){var r=e.fields;e.fields={},r.forEach(function(r){e.fields[r]=t.meta[r]})}return e.fields||(e.fields={}),e},t.prototype._getAssemblyOptions=function(t){var e=this,r=this.opts;return Promise.resolve().then(function(){return r.getAssemblyOptions(t,r)}).then(function(r){return e._normalizeAssemblyOptions(t,r)}).then(function(e){return xs(e.params),{fileIDs:[t.id],options:e}})},t.prototype._dedupe=function(t){var e=Object.create(null);return t.forEach(function(t){var r,n=t.fileIDs,o=t.options,i=JSON.stringify(o);e[i]?(r=e[i].fileIDs).push.apply(r,n):e[i]={options:o,fileIDs:[].concat(n)}}),Object.keys(e).map(function(t){return e[t]})},t.prototype.build=function(){var t=this,e=this.opts;return this.files.length>0?Promise.all(this.files.map(function(e){return t._getAssemblyOptions(e)})).then(function(e){return t._dedupe(e)}):e.alwaysRunAssembly?Promise.resolve(e.getAssemblyOptions(null,e)).then(function(e){return xs(e.params),[{fileIDs:t.files.map(function(t){return t.id}),options:e}]}):Promise.resolve([])},t}(),{validateParams:xs}),Rs=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this));return o._uppy=r,o._assemblyIDs=n,o._remaining=n.length,o.promise=new Promise(function(t,e){o._resolve=t,o._reject=e}),o._onAssemblyComplete=o._onAssemblyComplete.bind(o),o._onAssemblyError=o._onAssemblyError.bind(o),o._onImportError=o._onImportError.bind(o),o._addListeners(),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype._watching=function(t){return-1!==this._assemblyIDs.indexOf(t)},e.prototype._onAssemblyComplete=function(t){this._watching(t.assembly_id)&&(this._uppy.log("[Transloadit] AssemblyWatcher: Got Assembly finish "+t.assembly_id),this.emit("assembly-complete",t.assembly_id),this._checkAllComplete())},e.prototype._onAssemblyError=function(t,e){this._watching(t.assembly_id)&&(this._uppy.log("[Transloadit] AssemblyWatcher: Got Assembly error "+t.assembly_id),this._uppy.log(e),this.emit("assembly-error",t.assembly_id,e),this._checkAllComplete())},e.prototype._onImportError=function(t,e,r){this._watching(t.assembly_id)&&this._onAssemblyError(t,r)},e.prototype._checkAllComplete=function(){this._remaining-=1,0===this._remaining&&(this._removeListeners(),this._resolve())},e.prototype._removeListeners=function(){this._uppy.off("transloadit:complete",this._onAssemblyComplete),this._uppy.off("transloadit:assembly-error",this._onAssemblyError),this._uppy.off("transloadit:import-error",this._onImportError)},e.prototype._addListeners=function(){this._uppy.on("transloadit:complete",this._onAssemblyComplete),this._uppy.on("transloadit:assembly-error",this._onAssemblyError),this._uppy.on("transloadit:import-error",this._onImportError)},e}(io),Is=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.opts=e}return t.prototype.createAssembly=function(t){t.templateId;var e=t.params,r=t.fields,n=t.signature,o=t.expectedFiles,i=new FormData;return i.append("params","string"==typeof e?e:JSON.stringify(e)),n&&i.append("signature",n),Object.keys(r).forEach(function(t){i.append(t,r[t])}),i.append("num_expected_upload_files",o),fetch(this.opts.service+"/assemblies",{method:"post",body:i}).then(function(t){return t.json()}).then(function(t){if(t.error){var e=new Error(t.error);throw e.message=t.error,e.details=t.reason,e}return t})},t.prototype.reserveFile=function(t,e){var r=encodeURIComponent(e.size);return fetch(t.assembly_ssl_url+"/reserve_file?size="+r,{method:"post"}).then(function(t){return t.json()})},t.prototype.addFile=function(t,e){if(!e.uploadURL)return Promise.reject(new Error("File does not have an `uploadURL`."));var r=encodeURIComponent(e.size),n=encodeURIComponent(e.uploadURL),o="size="+r+"&filename="+encodeURIComponent(e.name)+"&fieldname=file&s3Url="+n;return fetch(t.assembly_ssl_url+"/add_file?"+o,{method:"post"}).then(function(t){return t.json()})},t.prototype.getAssemblyStatus=function(t){return fetch(t).then(function(t){return t.json()})},t}(),Bs={};Object.defineProperty(Bs,"__esModule",{value:!0}),Bs.setItem=function(t,e){if(js)return localStorage.setItem(t,e)},Bs.getItem=function(t){if(js)return localStorage.getItem(t)},Bs.removeItem=function(t){if(js)return localStorage.removeItem(t)};var js=!1;try{js="localStorage"in window;var Ls="tusSupport";localStorage.setItem(Ls,localStorage.getItem(Ls))}catch(t){if(t.code!==t.SECURITY_ERR&&t.code!==t.QUOTA_EXCEEDED_ERR)throw t;js=!1}Bs.canStoreURLs=js;var Ms=Object.prototype.hasOwnProperty,Ns=Object.prototype.toString,zs=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===Ns.call(t)},qs=function(t){if(!t||"[object Object]"!==Ns.call(t))return!1;var e,r=Ms.call(t,"constructor"),n=t.constructor&&t.constructor.prototype&&Ms.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!r&&!n)return!1;for(e in t);return void 0===e||Ms.call(t,e)},Hs={};Object.defineProperty(Hs,"__esModule",{value:!0}),Hs.encode=function(t){return Ws(unescape(encodeURIComponent(t)))};var Ws=window.btoa;Hs.isSupported="btoa"in window;var Xs={};Object.defineProperty(Xs,"__esModule",{value:!0}),Xs.newRequest=function(){return new window.XMLHttpRequest},Xs.resolveUrl=function(t,e){return(0,Gs.default)(t,e)};var Vs,Gs=(Vs=ht)&&Vs.__esModule?Vs:{default:Vs},Ks={},Ys=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}();Object.defineProperty(Ks,"__esModule",{value:!0}),Ks.getSource=function(t){if("function"==typeof t.slice&&void 0!==t.size)return new Js(t);throw new Error("source object may only be an instance of File or Blob in this environment")};var Js=function(){function t(e){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this._file=e,this.size=e.size}return Ys(t,[{key:"slice",value:function(t,e){return this._file.slice(t,e)}},{key:"close",value:function(){}}]),t}(),$s={};Object.defineProperty($s,"__esModule",{value:!0});var Qs=function(t){function e(t){var r=arguments.length<=1||void 0===arguments[1]?null:arguments[1],n=arguments.length<=2||void 0===arguments[2]?null:arguments[2];!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,Object.getPrototypeOf(e).call(this,t.message));o.originalRequest=n,o.causingError=r;var i=t.message;return null!=r&&(i+=", caused by "+r.toString()),null!=n&&(i+=", originated from request (response code: "+n.status+", response text: "+n.responseText+")"),o.message=i,o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,Error),e}();$s.default=Qs;var Zs={};Object.defineProperty(Zs,"__esModule",{value:!0}),Zs.default=function(t,e){return["tus",t.name,t.type,t.size,t.lastModified,e.endpoint].join("-")};var ta={},ea=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}();Object.defineProperty(ta,"__esModule",{value:!0});var ra=la(Zs),na=la($s),oa=la(function t(){var e,r,n,o,i,s,a=arguments[0],l=1,u=arguments.length,p=!1;for("boolean"==typeof a&&(p=a,a=arguments[1]||{},l=2),(null==a||"object"!=typeof a&&"function"!=typeof a)&&(a={});l<u;++l)if(null!=(e=arguments[l]))for(r in e)n=a[r],a!==(o=e[r])&&(p&&o&&(qs(o)||(i=zs(o)))?(i?(i=!1,s=n&&zs(n)?n:[]):s=n&&qs(n)?n:{},a[r]=t(p,s,o)):void 0!==o&&(a[r]=o));return a}),ia=aa(Hs),sa=aa(Bs);function aa(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function la(t){return t&&t.__esModule?t:{default:t}}var ua={endpoint:null,fingerprint:ra.default,resume:!0,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,headers:{},chunkSize:1/0,withCredentials:!1,uploadUrl:null,uploadSize:null,overridePatchMethod:!1,retryDelays:null,removeFingerprintOnSuccess:!1},pa=function(){function t(e,r){!function(e,r){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),this.options=(0,oa.default)(!0,{},ua,r),this.file=e,this.url=null,this._xhr=null,this._fingerprint=null,this._offset=null,this._aborted=!1,this._size=null,this._source=null,this._retryAttempt=0,this._retryTimeout=null,this._offsetBeforeRetry=0}return ea(t,[{key:"start",value:function(){var t=this,e=this.file;if(e)if(this.options.endpoint||this.options.uploadUrl){var r=this._source=(0,Ks.getSource)(e,this.options.chunkSize);if(null!=this.options.uploadSize){var n=+this.options.uploadSize;if(isNaN(n))throw new Error("tus: cannot convert `uploadSize` option into a number");this._size=n}else{if(null==(n=r.size))throw new Error("tus: cannot automatically derive upload's size from input and must be specified manually using the `uploadSize` option");this._size=n}var o,i=this.options.retryDelays;if(null!=i){if("[object Array]"!==Object.prototype.toString.call(i))throw new Error("tus: the `retryDelays` option must either be an array or null");o=t.options.onError,t.options.onError=function(e){t.options.onError=o,null!=t._offset&&t._offset>t._offsetBeforeRetry&&(t._retryAttempt=0);var r=!0;if("undefined"!=typeof window&&"navigator"in window&&!1===window.navigator.onLine&&(r=!1),t._retryAttempt<i.length&&null!=e.originalRequest&&!ca(e.originalRequest.status,400)&&r){var n=i[t._retryAttempt++];t._offsetBeforeRetry=t._offset,t.options.uploadUrl=t.url,t._retryTimeout=setTimeout(function(){t.start()},n)}else t._emitError(e)}}if(this._aborted=!1,null==this.url){if(null!=this.options.uploadUrl)return this.url=this.options.uploadUrl,void this._resumeUpload();if(this.options.resume){this._fingerprint=this.options.fingerprint(e,this.options);var s=sa.getItem(this._fingerprint);if(null!=s)return this.url=s,void this._resumeUpload()}this._createUpload()}else this._resumeUpload()}else this._emitError(new Error("tus: neither an endpoint or an upload URL is provided"));else this._emitError(new Error("tus: no file or stream to upload provided"))}},{key:"abort",value:function(){null!==this._xhr&&(this._xhr.abort(),this._source.close(),this._aborted=!0),null!=this._retryTimeout&&(clearTimeout(this._retryTimeout),this._retryTimeout=null)}},{key:"_emitXhrError",value:function(t,e,r){this._emitError(new na.default(e,r,t))}},{key:"_emitError",value:function(t){if("function"!=typeof this.options.onError)throw t;this.options.onError(t)}},{key:"_emitSuccess",value:function(){"function"==typeof this.options.onSuccess&&this.options.onSuccess()}},{key:"_emitProgress",value:function(t,e){"function"==typeof this.options.onProgress&&this.options.onProgress(t,e)}},{key:"_emitChunkComplete",value:function(t,e,r){"function"==typeof this.options.onChunkComplete&&this.options.onChunkComplete(t,e,r)}},{key:"_setupXHR",value:function(t){this._xhr=t,t.setRequestHeader("Tus-Resumable","1.0.0");var e=this.options.headers;for(var r in e)t.setRequestHeader(r,e[r]);t.withCredentials=this.options.withCredentials}},{key:"_createUpload",value:function(){var t=this;if(this.options.endpoint){var e=(0,Xs.newRequest)();e.open("POST",this.options.endpoint,!0),e.onload=function(){if(ca(e.status,200)){var r=e.getResponseHeader("Location");if(null!=r){if(t.url=(0,Xs.resolveUrl)(t.options.endpoint,r),0===t._size)return t._emitSuccess(),void t._source.close();t.options.resume&&sa.setItem(t._fingerprint,t.url),t._offset=0,t._startUpload()}else t._emitXhrError(e,new Error("tus: invalid or missing Location header"))}else t._emitXhrError(e,new Error("tus: unexpected response while creating upload"))},e.onerror=function(r){t._emitXhrError(e,new Error("tus: failed to create upload"),r)},this._setupXHR(e),e.setRequestHeader("Upload-Length",this._size);var r=function(t){if(!ia.isSupported)return"";var e=[];for(var r in t)e.push(r+" "+ia.encode(t[r]));return e.join(",")}(this.options.metadata);""!==r&&e.setRequestHeader("Upload-Metadata",r),e.send(null)}else this._emitError(new Error("tus: unable to create upload because no endpoint is provided"))}},{key:"_resumeUpload",value:function(){var t=this,e=(0,Xs.newRequest)();e.open("HEAD",this.url,!0),e.onload=function(){if(!ca(e.status,200))return t.options.resume&&ca(e.status,400)&&sa.removeItem(t._fingerprint),423===e.status?void t._emitXhrError(e,new Error("tus: upload is currently locked; retry later")):t.options.endpoint?(t.url=null,void t._createUpload()):void t._emitXhrError(e,new Error("tus: unable to resume upload (new upload cannot be created without an endpoint)"));var r=parseInt(e.getResponseHeader("Upload-Offset"),10);if(isNaN(r))t._emitXhrError(e,new Error("tus: invalid or missing offset value"));else{var n=parseInt(e.getResponseHeader("Upload-Length"),10);if(isNaN(n))t._emitXhrError(e,new Error("tus: invalid or missing length value"));else{if(r===n)return t._emitProgress(n,n),void t._emitSuccess();t._offset=r,t._startUpload()}}},e.onerror=function(r){t._emitXhrError(e,new Error("tus: failed to resume upload"),r)},this._setupXHR(e),e.send(null)}},{key:"_startUpload",value:function(){var t=this;if(!this._aborted){var e=(0,Xs.newRequest)();this.options.overridePatchMethod?(e.open("POST",this.url,!0),e.setRequestHeader("X-HTTP-Method-Override","PATCH")):e.open("PATCH",this.url,!0),e.onload=function(){if(ca(e.status,200)){var r=parseInt(e.getResponseHeader("Upload-Offset"),10);if(isNaN(r))t._emitXhrError(e,new Error("tus: invalid or missing offset value"));else{if(t._emitProgress(r,t._size),t._emitChunkComplete(r-t._offset,r,t._size),t._offset=r,r==t._size)return t.options.removeFingerprintOnSuccess&&t.options.resume&&sa.removeItem(t._fingerprint),t._emitSuccess(),void t._source.close();t._startUpload()}}else t._emitXhrError(e,new Error("tus: unexpected response while uploading chunk"))},e.onerror=function(r){t._aborted||t._emitXhrError(e,new Error("tus: failed to upload chunk at offset "+t._offset),r)},"upload"in e&&(e.upload.onprogress=function(e){e.lengthComputable&&t._emitProgress(r+e.loaded,t._size)}),this._setupXHR(e),e.setRequestHeader("Upload-Offset",this._offset),e.setRequestHeader("Content-Type","application/offset+octet-stream");var r=this._offset,n=this._offset+this.options.chunkSize;(n===1/0||n>this._size)&&(n=this._size),e.send(this._source.slice(r,n)),this._emitProgress(this._offset,this._size)}}}]),t}();function ca(t,e){return t>=e&&t<e+100}pa.defaultOptions=ua,ta.default=pa;var ha,da=(ha=ta)&&ha.__esModule?ha:{default:ha};da.default.defaultOptions;if("undefined"!=typeof window){var fa=window,ya=fa.XMLHttpRequest,ga=fa.Blob;ya&&ga&&ga.prototype.slice}else!0;var ma={Upload:da.default},va=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ba=d.Provider,wa=d.RequestClient,Sa=d.Socket,Pa={endpoint:"",resume:!0,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,headers:{},chunkSize:1/0,withCredentials:!1,uploadUrl:null,uploadSize:null,overridePatchMethod:!1,retryDelays:null};function _a(t){var e=[];return{on:function(r,n){return e.push([r,n]),t.on(r,n)},remove:function(){e.forEach(function(e){var r=e[0],n=e[1];t.off(r,n)})}}}var Ea=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));return o.type="uploader",o.id="Tus",o.title="Tus",o.opts=va({},{resume:!0,autoRetry:!0,useFastRemoteRetry:!0,limit:0,retryDelays:[0,1e3,3e3,5e3]},n),"number"==typeof o.opts.limit&&0!==o.opts.limit?o.limitUploads=ot(o.opts.limit):o.limitUploads=function(t){return t},o.uploaders=Object.create(null),o.uploaderEvents=Object.create(null),o.uploaderSockets=Object.create(null),o.handleResetProgress=o.handleResetProgress.bind(o),o.handleUpload=o.handleUpload.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.handleResetProgress=function(){var t=va({},this.uppy.getState().files);Object.keys(t).forEach(function(e){if(t[e].tus&&t[e].tus.uploadUrl){var r=va({},t[e].tus);delete r.uploadUrl,t[e]=va({},t[e],{tus:r})}}),this.uppy.setState({files:t})},e.prototype.resetUploaderReferences=function(t){this.uploaders[t]&&(this.uploaders[t].abort(),this.uploaders[t]=null),this.uploaderEvents[t]&&(this.uploaderEvents[t].remove(),this.uploaderEvents[t]=null),this.uploaderSockets[t]&&(this.uploaderSockets[t].close(),this.uploaderSockets[t]=null)},e.prototype.upload=function(t,e,r){var n=this;return this.resetUploaderReferences(t.id),new Promise(function(e,r){var o=va({},Pa,n.opts,t.tus||{});o.onError=function(e){n.uppy.log(e),n.uppy.emit("upload-error",t,e),e.message="Failed because: "+e.message,n.resetUploaderReferences(t.id),r(e)},o.onProgress=function(e,r){n.onReceiveUploadUrl(t,a.url),n.uppy.emit("upload-progress",t,{uploader:n,bytesUploaded:e,bytesTotal:r})},o.onSuccess=function(){n.uppy.emit("upload-success",t,a,a.url),a.url&&n.uppy.log("Download "+a.file.name+" from "+a.url),n.resetUploaderReferences(t.id),e(a)};var i=function(t,e,r){Object.prototype.hasOwnProperty.call(t,e)&&!Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=t[e])},s=va({},t.meta);i(s,"type","filetype"),i(s,"name","filename"),o.metadata=s;var a=new ma.Upload(t.data,o);n.uploaders[t.id]=a,n.uploaderEvents[t.id]=_a(n.uppy),n.onFileRemove(t.id,function(r){n.resetUploaderReferences(t.id),e("upload "+r+" was removed")}),n.onPause(t.id,function(t){t?a.abort():a.start()}),n.onPauseAll(t.id,function(){a.abort()}),n.onCancelAll(t.id,function(){n.resetUploaderReferences(t.id)}),n.onResumeAll(t.id,function(){t.error&&a.abort(),a.start()}),t.isPaused||a.start()})},e.prototype.uploadRemote=function(t,e,r){var n=this;this.resetUploaderReferences(t.id);var o=va({},this.opts,t.tus||{});return new Promise(function(e,r){if(n.uppy.log(t.remote.url),t.serverToken)return n.connectToServerSocket(t).then(function(){return e()}).catch(r);n.uppy.emit("upload-started",t),new(t.remote.providerOptions.provider?ba:wa)(n.uppy,t.remote.providerOptions).post(t.remote.url,va({},t.remote.body,{endpoint:o.endpoint,uploadUrl:o.uploadUrl,protocol:"tus",size:t.data.size,metadata:t.meta})).then(function(e){return n.uppy.setFileState(t.id,{serverToken:e.token}),t=n.uppy.getFile(t.id)}).then(function(t){return n.connectToServerSocket(t)}).then(function(){e()}).catch(function(t){r(new Error(t))})})},e.prototype.connectToServerSocket=function(t){var e=this;return new Promise(function(r,n){var o=t.serverToken,i=nt(t.remote.serverUrl),s=new Sa({target:i+"/api/"+o});e.uploaderSockets[t.id]=s,e.uploaderEvents[t.id]=_a(e.uppy),e.onFileRemove(t.id,function(){s.send("pause",{}),r("upload "+t.id+" was removed")}),e.onPause(t.id,function(t){t?s.send("pause",{}):s.send("resume",{})}),e.onPauseAll(t.id,function(){return s.send("pause",{})}),e.onCancelAll(t.id,function(){return s.send("pause",{})}),e.onResumeAll(t.id,function(){t.error&&s.send("pause",{}),s.send("resume",{})}),e.onRetry(t.id,function(){s.send("pause",{}),s.send("resume",{})}),e.onRetryAll(t.id,function(){s.send("pause",{}),s.send("resume",{})}),t.isPaused&&s.send("pause",{}),s.on("progress",function(r){return rt(e,r,t)}),s.on("error",function(r){var o=r.error.message,i=va(new Error(o),{cause:r.error});e.opts.useFastRemoteRetry||(e.resetUploaderReferences(t.id),e.uppy.setFileState(t.id,{serverToken:null})),e.uppy.emit("upload-error",t,i),n(i)}),s.on("success",function(n){e.uppy.emit("upload-success",t,n,n.url),e.resetUploaderReferences(t.id),r()})})},e.prototype.onReceiveUploadUrl=function(t,e){var r=this.uppy.getFile(t.id);r&&(r.tus&&r.tus.uploadUrl===e||(this.uppy.log("[Tus] Storing upload url"),this.uppy.setFileState(r.id,{tus:va({},r.tus,{uploadUrl:e})})))},e.prototype.onFileRemove=function(t,e){this.uploaderEvents[t].on("file-removed",function(r){t===r.id&&e(r.id)})},e.prototype.onPause=function(t,e){this.uploaderEvents[t].on("upload-pause",function(r,n){t===r&&e(n)})},e.prototype.onRetry=function(t,e){this.uploaderEvents[t].on("upload-retry",function(r){t===r&&e()})},e.prototype.onRetryAll=function(t,e){var r=this;this.uploaderEvents[t].on("retry-all",function(n){r.uppy.getFile(t)&&e()})},e.prototype.onPauseAll=function(t,e){var r=this;this.uploaderEvents[t].on("pause-all",function(){r.uppy.getFile(t)&&e()})},e.prototype.onCancelAll=function(t,e){var r=this;this.uploaderEvents[t].on("cancel-all",function(){r.uppy.getFile(t)&&e()})},e.prototype.onResumeAll=function(t,e){var r=this;this.uploaderEvents[t].on("resume-all",function(){r.uppy.getFile(t)&&e()})},e.prototype.uploadFiles=function(t){var e=this,r=t.map(function(r,n){var o=parseInt(n,10)+1,i=t.length;return r.error?function(){return Promise.reject(new Error(r.error))}:r.isRemote?(e.uppy.emit("upload-started",r),e.uploadRemote.bind(e,r,o,i)):(e.uppy.emit("upload-started",r),e.upload.bind(e,r,o,i))}).map(function(t){return e.limitUploads(t)()});return dt(r)},e.prototype.handleUpload=function(t){var e=this;if(0===t.length)return this.uppy.log("Tus: no files to upload!"),Promise.resolve();this.uppy.log("Tus is uploading...");var r=t.map(function(t){return e.uppy.getFile(t)});return this.uploadFiles(r).then(function(){return null})},e.prototype.install=function(){this.uppy.setState({capabilities:va({},this.uppy.getState().capabilities,{resumableUploads:!0})}),this.uppy.addUploader(this.handleUpload),this.uppy.on("reset-progress",this.handleResetProgress),this.opts.autoRetry&&this.uppy.on("back-online",this.uppy.retryAll)},e.prototype.uninstall=function(){this.uppy.setState({capabilities:va({},this.uppy.getState().capabilities,{resumableUploads:!1})}),this.uppy.removeUploader(this.handleUpload),this.opts.autoRetry&&this.uppy.off("back-online",this.uppy.retryAll)},e}(Y.Plugin),ka={},Ca=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t};function Ta(t,e){return{params:e.params,signature:e.signature,fields:e.fields}}var Ua="https://api2.transloadit.com/companion",Oa=/https?:\/\/api2(?:-\w+)?\.transloadit\.com\/companion/,Fa=/https?:\/\/api2(?:-\w+)?\.transloadit\.com\/uppy-server/;(ka=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.type="uploader",o.id="Transloadit",o.title="Transloadit";var i={strings:{creatingAssembly:"Preparing upload...",creatingAssemblyFailed:"Transloadit: Could not create Assembly",encoding:"Encoding..."}},s={service:"https://api2.transloadit.com",waitForEncoding:!1,waitForMetadata:!1,alwaysRunAssembly:!1,importFromUploadURLs:!1,signature:null,params:null,fields:{},getAssemblyOptions:Ta,locale:i};return o.opts=Ca({},s,n),o.locale=Ca({},i,o.opts.locale),o.locale.strings=Ca({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o._prepareUpload=o._prepareUpload.bind(o),o._afterUpload=o._afterUpload.bind(o),o._handleError=o._handleError.bind(o),o._onFileUploadURLAvailable=o._onFileUploadURLAvailable.bind(o),o._onRestored=o._onRestored.bind(o),o._getPersistentData=o._getPersistentData.bind(o),o.opts.params&&Ds.validateParams(o.opts.params),o.client=new Is({service:o.opts.service}),o.activeAssemblies={},o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype._attachAssemblyMetadata=function(t,e){var r=Ca({},t.meta,{assembly_url:e.assembly_url,filename:t.name,fieldname:"file"}),n=Ca({},t.tus,{endpoint:e.tus_url}),o=t.remote;if(t.remote&&Fa.test(t.remote.serverUrl)){var i=new Error("The https://api2.transloadit.com/uppy-server endpoint was renamed to https://api2.transloadit.com/companion, please update your `serverUrl` options accordingly.");throw this.uppy.log(i),i}if(t.remote&&Oa.test(t.remote.serverUrl)){var s=e.companion_url.replace(/\/$/,""),a=t.remote.url.replace(t.remote.serverUrl,"").replace(/^\//,"");o=Ca({},t.remote,{serverUrl:s,url:s+"/"+a})}var l=Ca({},t,{transloadit:{assembly:e.assembly_id}});return this.opts.importFromUploadURLs||Ca(l,{meta:r,tus:n,remote:o}),l},e.prototype._createAssembly=function(t,e,r){var n=this;return this.uppy.log("[Transloadit] create Assembly"),this.client.createAssembly({params:r.params,fields:r.fields,expectedFiles:t.length,signature:r.signature}).then(function(r){var o,i,s=new As(r),a=s.status,l=n.getPluginState(),u=l.assemblies,p=l.uploadsAssemblies;n.setPluginState({assemblies:Ca({},u,(o={},o[a.assembly_id]=a,o)),uploadsAssemblies:Ca({},p,(i={},i[e]=[].concat(p[e],[a.assembly_id]),i))});var c=n.uppy.getState().files,h={};return t.forEach(function(t){h[t]=n._attachAssemblyMetadata(n.uppy.getFile(t),a)}),n.uppy.setState({files:Ca({},c,h)}),n.uppy.emit("transloadit:assembly-created",a,t),n._connectAssembly(s),n.uppy.log("[Transloadit] Created Assembly "+s.assembly_id),s}).catch(function(t){throw t.message=n.i18n("creatingAssemblyFailed")+": "+t.message,t})},e.prototype._shouldWaitAfterUpload=function(){return this.opts.waitForEncoding||this.opts.waitForMetadata},e.prototype._reserveFiles=function(t,e){var r=this;return Promise.all(e.map(function(e){var n=r.uppy.getFile(e);return r.client.reserveFile(t,n)}))},e.prototype._onFileUploadURLAvailable=function(t){var e=this;if(t&&t.transloadit&&t.transloadit.assembly){var r=this.getPluginState().assemblies[t.transloadit.assembly];this.client.addFile(r,t).catch(function(n){e.uppy.log(n),e.uppy.emit("transloadit:import-error",r,t.id,n)})}},e.prototype._findFile=function(t){for(var e=this.uppy.getFiles(),r=0;r<e.length;r++){var n=e[r];if(n.uploadURL===t.tus_upload_url)return n;if(n.tus&&n.tus.uploadUrl===t.tus_upload_url)return n;if(!t.is_tus_file&&n.name===t.name&&n.size===t.size)return n}},e.prototype._onFileUploadComplete=function(t,e){var r,n=this.getPluginState(),o=this._findFile(e);o?(this.setPluginState({files:Ca({},n.files,(r={},r[e.id]={assembly:t,id:o.id,uploadedFile:e},r))}),this.uppy.emit("transloadit:upload",e,this.getAssembly(t))):this.uppy.log("[Transloadit] Couldn\u2019t file the file, it was likely removed in the process")},e.prototype._onResult=function(t,e,r){var n=this.getPluginState(),o=n.files[r.original_id];r.localId=o?o.id:null;var i={result:r,stepName:e,id:r.id,assembly:t};this.setPluginState({results:[].concat(n.results,[i])}),this.uppy.emit("transloadit:result",e,r,this.getAssembly(t))},e.prototype._onAssemblyFinished=function(t){var e=this,r=t.assembly_ssl_url;this.client.getAssemblyStatus(r).then(function(t){var r,n=e.getPluginState();e.setPluginState({assemblies:Ca({},n.assemblies,(r={},r[t.assembly_id]=t,r))}),e.uppy.emit("transloadit:complete",t)})},e.prototype._getPersistentData=function(t){var e,r=this.getPluginState(),n=r.assemblies,o=r.uploadsAssemblies;t(((e={})[this.id]={assemblies:n,uploadsAssemblies:o},e))},e.prototype._onRestored=function(t){var e=this,r=t&&t[this.id]?t[this.id]:{},n=r.assemblies||{},o=r.uploadsAssemblies||{};0!==Object.keys(o).length&&(this.restored=Promise.resolve().then(function(){var t,r,i;return t=n,r={},i=[],Object.keys(t).forEach(function(n){var o=t[n];o.uploads.forEach(function(t){var o=e._findFile(t);r[t.id]={id:o.id,assembly:n,uploadedFile:t}});var s=e.getPluginState();Object.keys(o.results).forEach(function(t){o.results[t].forEach(function(e){var r=s.files[e.original_id];e.localId=r?r.id:null,i.push({id:e.id,result:e,stepName:t,assembly:n})})})}),e.setPluginState({assemblies:t,files:r,results:i,uploadsAssemblies:o}),function(){var t=e.getPluginState().assemblies;Object.keys(t).forEach(function(r){var n=new As(t[r]);e._connectAssembly(n)})}(),function(){var t=e.getPluginState().assemblies;return Promise.all(Object.keys(t).map(function(t){return e.activeAssemblies[t].update()}))}()}),this.restored.then(function(){e.restored=null}))},e.prototype._connectAssembly=function(t){var e=this,r=t.status.assembly_id;return this.activeAssemblies[r]=t,t.on("status",function(t){var n,o=e.getPluginState().assemblies;e.setPluginState({assemblies:Ca({},o,(n={},n[r]=t,n))})}),t.on("upload",function(t){e._onFileUploadComplete(r,t)}),t.on("error",function(r){e.uppy.emit("transloadit:assembly-error",t.status,r)}),t.on("executing",function(){e.uppy.emit("transloadit:assembly-executing",t.status)}),this.opts.waitForEncoding&&t.on("result",function(t,n){e._onResult(r,t,n)}),this.opts.waitForEncoding?t.on("finished",function(){e._onAssemblyFinished(t.status)}):this.opts.waitForMetadata&&t.on("metadata",function(){e._onAssemblyFinished(t.status)}),"ASSEMBLY_COMPLETE"===t.ok?t:(new Promise(function(e,r){t.once("connect",e),t.once("status",e),t.once("error",r)}).then(function(){e.uppy.log("[Transloadit] Socket is ready")}),t.connect(),t)},e.prototype._prepareUpload=function(t,e){var r,n=this;(t=t.filter(function(t){return!t.error})).forEach(function(t){var e=n.uppy.getFile(t);n.uppy.emit("preprocess-progress",e,{mode:"indeterminate",message:n.i18n("creatingAssembly")})});var o=function(t){var r=t.fileIDs,o=t.options;return n._createAssembly(r,e,o).then(function(t){if(n.opts.importFromUploadURLs)return n._reserveFiles(t,r)}).then(function(){r.forEach(function(t){var e=n.uppy.getFile(t);n.uppy.emit("preprocess-complete",e)})}).catch(function(t){throw r.forEach(function(e){var r=n.uppy.getFile(e);n.uppy.emit("preprocess-complete",r),n.uppy.emit("upload-error",r,t)}),t})},i=this.getPluginState().uploadsAssemblies;this.setPluginState({uploadsAssemblies:Ca({},i,(r={},r[e]=[],r))});var s=t.map(function(t){return n.uppy.getFile(t)});return new Ds(s,this.opts).build().then(function(t){return Promise.all(t.map(o))},function(e){throw t.forEach(function(t){var r=n.uppy.getFile(t);n.uppy.emit("preprocess-complete",r),n.uppy.emit("upload-error",r,e)}),e})},e.prototype._afterUpload=function(t,e){var r=this;t=t.filter(function(t){return!t.error});var n=this.getPluginState();if(this.restored)return this.restored.then(function(){return r._afterUpload(t,e)});var o=n.uploadsAssemblies[e];if(!this._shouldWaitAfterUpload()){o.forEach(function(t){r.activeAssemblies[t].close(),delete r.activeAssemblies[t]});var i=o.map(function(t){return r.getAssembly(t)});return this.uppy.addResultData(e,{transloadit:i}),Promise.resolve()}if(0===o.length)return this.uppy.addResultData(e,{transloadit:[]}),Promise.resolve();var s=new Rs(this.uppy,o);return t.forEach(function(t){var e=r.uppy.getFile(t);r.uppy.emit("postprocess-progress",e,{mode:"indeterminate",message:r.i18n("encoding")})}),s.on("assembly-complete",function(t){r.getAssemblyFiles(t).forEach(function(t){r.uppy.emit("postprocess-complete",t)})}),s.on("assembly-error",function(t,e){r.getAssemblyFiles(t).forEach(function(t){r.uppy.emit("upload-error",t,e),r.uppy.emit("postprocess-complete",t)})}),s.promise.then(function(){var t=o.map(function(t){return r.getAssembly(t)}),n=r.getPluginState(),i=Ca({},n.uploadsAssemblies);delete i[e],r.setPluginState({uploadsAssemblies:i}),r.uppy.addResultData(e,{transloadit:t})})},e.prototype._handleError=function(t,e){var r=this;this.uppy.log("[Transloadit] _handleError in upload "+e),this.uppy.log(t),this.getPluginState().uploadsAssemblies[e].forEach(function(t){r.activeAssemblies[t]&&r.activeAssemblies[t].close()})},e.prototype.install=function(){this.uppy.addPreProcessor(this._prepareUpload),this.uppy.addPostProcessor(this._afterUpload),this.uppy.on("error",this._handleError),this.opts.importFromUploadURLs?this.uppy.on("upload-success",this._onFileUploadURLAvailable):this.uppy.use(Ea,{resume:!1,useFastRemoteRetry:!1,metaFields:["assembly_url","filename","fieldname"]}),this.uppy.on("restore:get-data",this._getPersistentData),this.uppy.on("restored",this._onRestored),this.setPluginState({assemblies:{},uploadsAssemblies:{},files:{},results:[]})},e.prototype.uninstall=function(){this.uppy.removePreProcessor(this._prepareUpload),this.uppy.removePostProcessor(this._afterUpload),this.uppy.off("error",this._handleError),this.opts.importFromUploadURLs&&this.uppy.off("upload-success",this._onFileUploadURLAvailable)},e.prototype.getAssembly=function(t){return this.getPluginState().assemblies[t]},e.prototype.getAssemblyFiles=function(t){return this.uppy.getFiles().filter(function(e){return e&&e.transloadit&&e.transloadit.assembly===t})},e}(Y.Plugin)).COMPANION=Ua,ka.UPPY_SERVER=Ua;var Aa=A.h,xa=function(t){function e(r){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r));return n.handleKeyPress=n.handleKeyPress.bind(n),n.handleClick=n.handleClick.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.componentDidMount=function(){var t=this;this.input.value="",setTimeout(function(){t.input&&t.input.focus({preventScroll:!0})},150)},e.prototype.handleKeyPress=function(t){13===t.keyCode&&this.props.addFile(this.input.value)},e.prototype.handleClick=function(){this.props.addFile(this.input.value)},e.prototype.render=function(){var t=this;return Aa("div",{class:"uppy-Url"},Aa("input",{class:"uppy-c-textInput uppy-Url-input",type:"text",placeholder:this.props.i18n("enterUrlToImport"),onkeyup:this.handleKeyPress,ref:function(e){t.input=e}}),Aa("button",{class:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Url-importButton",type:"button","aria-label":this.props.i18n("import"),onclick:this.handleClick},this.props.i18n("import")))},e}(A.Component),Da=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ra=Y.Plugin,Ia=A.h,Ba=d.RequestClient,ja=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.id=o.opts.id||"Url",o.title=o.opts.title||"Link",o.type="acquirer",o.icon=function(){return Ia("svg",{"aria-hidden":"true",width:"23",height:"23",viewBox:"0 0 23 23",xmlns:"http://www.w3.org/2000/svg"},Ia("path",{d:"M20.485 11.236l-2.748 2.737c-.184.182-.367.365-.642.547-1.007.73-2.107 1.095-3.298 1.095-1.65 0-3.298-.73-4.398-2.19-.275-.365-.183-1.003.183-1.277.367-.273 1.008-.182 1.283.183 1.191 1.642 3.482 1.915 5.13.73a.714.714 0 0 0 .367-.365l2.75-2.737c1.373-1.46 1.373-3.74-.093-5.108a3.72 3.72 0 0 0-5.13 0L12.33 6.4a.888.888 0 0 1-1.283 0 .88.88 0 0 1 0-1.277l1.558-1.55a5.38 5.38 0 0 1 7.605 0c2.29 2.006 2.382 5.564.274 7.662zm-8.979 6.294L9.95 19.081a3.72 3.72 0 0 1-5.13 0c-1.467-1.368-1.467-3.74-.093-5.108l2.75-2.737.366-.365c.824-.547 1.74-.82 2.748-.73 1.008.183 1.833.639 2.382 1.46.275.365.917.456 1.283.182.367-.273.458-.912.183-1.277-.916-1.186-2.199-1.915-3.573-2.098-1.374-.273-2.84.091-4.031 1.004l-.55.547-2.749 2.737c-2.107 2.189-2.015 5.655.092 7.753C4.727 21.453 6.101 22 7.475 22c1.374 0 2.749-.547 3.848-1.55l1.558-1.551a.88.88 0 0 0 0-1.278c-.367-.364-1.008-.456-1.375-.09z",fill:"#FF814F","fill-rule":"nonzero"}))};var i={strings:{import:"Import",enterUrlToImport:"Enter URL to import a file",failedToFetch:"Companion failed to fetch this URL, please make sure it\u2019s correct",enterCorrectUrl:"Incorrect URL: Please make sure you are entering a direct link to a file"}},s={locale:i};if(o.opts=Da({},s,n),o.locale=Da({},i,o.opts.locale),o.locale.strings=Da({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.hostname=o.opts.serverUrl,!o.hostname)throw new Error("Companion hostname is required, please consult https://uppy.io/docs/companion");return o.getMeta=o.getMeta.bind(o),o.addFile=o.addFile.bind(o),o.handleDrop=o.handleDrop.bind(o),o.handleDragOver=o.handleDragOver.bind(o),o.handleDragLeave=o.handleDragLeave.bind(o),o.handlePaste=o.handlePaste.bind(o),o.client=new Ba(r,{serverUrl:o.opts.serverUrl,serverHeaders:o.opts.serverHeaders}),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.getFileNameFromUrl=function(t){return t.substring(t.lastIndexOf("/")+1)},e.prototype.checkIfCorrectURL=function(t){if(!t)return!1;var e=t.match(/^([a-z0-9]+):\/\//)[1];return"http"===e||"https"===e},e.prototype.addProtocolToURL=function(t){return/^[a-z0-9]+:\/\//.test(t)?t:"http://"+t},e.prototype.getMeta=function(t){var e=this;return this.client.post("url/meta",{url:t}).then(function(t){if(t.error)throw e.uppy.log("[URL] Error:"),e.uppy.log(t.error),new Error("Failed to fetch the file");return t})},e.prototype.addFile=function(t){var e=this;return t=this.addProtocolToURL(t),this.checkIfCorrectURL(t)?this.getMeta(t).then(function(r){return{source:e.id,name:e.getFileNameFromUrl(t),type:r.type,data:{size:r.size},isRemote:!0,body:{url:t},remote:{serverUrl:e.opts.serverUrl,url:e.hostname+"/url/get",body:{fileId:t,url:t},providerOptions:e.client.opts}}}).then(function(t){e.uppy.log("[Url] Adding remote file");try{e.uppy.addFile(t)}catch(t){}}).then(function(){var t=e.uppy.getPlugin("Dashboard");t&&t.hideAllPanels()}).catch(function(t){e.uppy.log(t),e.uppy.info({message:e.i18n("failedToFetch"),details:t},"error",4e3)}):(this.uppy.log("[URL] Incorrect URL entered: "+t),void this.uppy.info(this.i18n("enterCorrectUrl"),"error",4e3))},e.prototype.handleDrop=function(t){var e=this;t.preventDefault(),t.dataTransfer.items&&fr(t.dataTransfer.items).forEach(function(t){"string"===t.kind&&"text/uri-list"===t.type&&t.getAsString(function(t){e.uppy.log("[URL] Adding file from dropped url: "+t),e.addFile(t)})})},e.prototype.handleDragOver=function(t){t.preventDefault(),this.el.classList.add("drag")},e.prototype.handleDragLeave=function(t){t.preventDefault(),this.el.classList.remove("drag")},e.prototype.handlePaste=function(t){var e=this;if(t.clipboardData.items){var r=fr(t.clipboardData.items);r.filter(function(t){return"file"===t.kind}).length>0||r.forEach(function(t){"string"===t.kind&&"text/plain"===t.type&&t.getAsString(function(t){e.uppy.log("[URL] Adding file from pasted url: "+t),e.addFile(t)})})}},e.prototype.render=function(t){return Ia(xa,{i18n:this.i18n,addFile:this.addFile})},e.prototype.install=function(){var t=this.opts.target;t&&this.mount(t,this),this.el.addEventListener("drop",this.handleDrop),this.el.addEventListener("dragover",this.handleDragOver),this.el.addEventListener("dragleave",this.handleDragLeave),this.el.addEventListener("paste",this.handlePaste)},e.prototype.uninstall=function(){this.el.removeEventListener("drop",this.handleDrop),this.el.removeEventListener("dragover",this.handleDragOver),this.el.removeEventListener("dragleave",this.handleDragLeave),this.el.removeEventListener("paste",this.handlePaste),this.unmount()},e}(Ra),La={"video/ogg":"ogv","audio/ogg":"ogg","video/webm":"webm","audio/webm":"webm","video/mp4":"mp4","audio/mp3":"mp3"},Ma=A.h,Na=function(t){return Ma("svg",{"aria-hidden":"true",fill:"#0097DC",width:"66",height:"55",viewBox:"0 0 66 55",xmlns:"http://www.w3.org/2000/svg"},Ma("path",{d:"M57.3 8.433c4.59 0 8.1 3.51 8.1 8.1v29.7c0 4.59-3.51 8.1-8.1 8.1H8.7c-4.59 0-8.1-3.51-8.1-8.1v-29.7c0-4.59 3.51-8.1 8.1-8.1h9.45l4.59-7.02c.54-.54 1.35-1.08 2.16-1.08h16.2c.81 0 1.62.54 2.16 1.08l4.59 7.02h9.45zM33 14.64c-8.62 0-15.393 6.773-15.393 15.393 0 8.62 6.773 15.393 15.393 15.393 8.62 0 15.393-6.773 15.393-15.393 0-8.62-6.773-15.393-15.393-15.393zM33 40c-5.648 0-9.966-4.319-9.966-9.967 0-5.647 4.318-9.966 9.966-9.966s9.966 4.319 9.966 9.966C42.966 35.681 38.648 40 33 40z","fill-rule":"evenodd"}))},za=A.h,qa=A.h,Ha=A.h;function Wa(t,e){return-1!==t.indexOf(e)}var Xa=function(t){function e(){return function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.componentDidMount=function(){this.props.onFocus(),this.btnContainer.firstChild.focus()},e.prototype.componentWillUnmount=function(){this.props.onStop()},e.prototype.render=function(){var t,e,r,n=this,o=this.props.supportsRecording&&(Wa(this.props.modes,"video-only")||Wa(this.props.modes,"audio-only")||Wa(this.props.modes,"video-audio")),i=Wa(this.props.modes,"picture");return Ha("div",{class:"uppy uppy-Webcam-container"},Ha("div",{class:"uppy-Webcam-videoContainer"},Ha("video",{class:"uppy-Webcam-video  "+(this.props.mirror?"uppy-Webcam-video--mirrored":""),autoplay:!0,muted:!0,playsinline:!0,srcObject:this.props.src||""})),Ha("div",{class:"uppy-Webcam-buttonContainer",ref:function(t){n.btnContainer=t}},i?(t=this.props,e=t.onSnapshot,r=t.i18n,qa("button",{class:"uppy-u-reset uppy-c-btn uppy-Webcam-button uppy-Webcam-button--picture",type:"button",title:r("takePicture"),"aria-label":r("takePicture"),onclick:e},Na())):null," ",o?function(t){var e=t.recording,r=t.onStartRecording,n=t.onStopRecording,o=t.i18n;return console.log("is recording",e),e?za("button",{class:"uppy-u-reset uppy-c-btn uppy-Webcam-button uppy-Webcam-button--video",type:"button",title:o("stopRecording"),"aria-label":o("stopRecording"),onclick:n},za("svg",{"aria-hidden":"true",class:"UppyIcon",width:"100",height:"100",viewBox:"0 0 100 100"},za("rect",{x:"15",y:"15",width:"70",height:"70"}))):za("button",{class:"uppy-u-reset uppy-c-btn uppy-Webcam-button uppy-Webcam-button--video",type:"button",title:o("startRecording"),"aria-label":o("startRecording"),onclick:r},za("svg",{"aria-hidden":"true",class:"UppyIcon",width:"100",height:"100",viewBox:"0 0 100 100"},za("circle",{cx:"50",cy:"50",r:"40"})))}(this.props):null))},e}(A.Component),Va=A.h,Ga=function(t){return Va("div",{class:"uppy-Webcam-permissons"},Va("div",{class:"uppy-Webcam-permissonsIcon"},t.icon()),Va("h1",{class:"uppy-Webcam-title"},t.i18n("allowAccessTitle")),Va("p",null,t.i18n("allowAccessDescription")))},Ka="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ya=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ja=A.h,$a=function(t){function e(r,n){!function(t,r){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,t.call(this,r,n));o.mediaDevices=function(){if(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)return navigator.mediaDevices;var t=navigator.mozGetUserMedia||navigator.webkitGetUserMedia;return t?{getUserMedia:function(e){return new Promise(function(r,n){t.call(navigator,e,r,n)})}}:null}(),o.supportsUserMedia=!!o.mediaDevices,o.protocol=location.protocol.match(/https/i)?"https":"http",o.id=o.opts.id||"Webcam",o.title=o.opts.title||"Camera",o.type="acquirer",o.icon=Na;var i={strings:{smile:"Smile!",takePicture:"Take a picture",startRecording:"Begin video recording",stopRecording:"Stop video recording",allowAccessTitle:"Please allow access to your camera",allowAccessDescription:"In order to take pictures or record video with your camera, please allow camera access for this site."}},s={onBeforeSnapshot:function(){return Promise.resolve()},countdown:!1,locale:i,modes:["video-audio","video-only","audio-only","picture"],mirror:!0,facingMode:"user"};return o.opts=Ya({},s,n),o.locale=Ya({},i,o.opts.locale),o.locale.strings=Ya({},i.strings,o.opts.locale.strings),o.translator=new H({locale:o.locale}),o.i18n=o.translator.translate.bind(o.translator),o.install=o.install.bind(o),o.setPluginState=o.setPluginState.bind(o),o.render=o.render.bind(o),o.start=o.start.bind(o),o.stop=o.stop.bind(o),o.takeSnapshot=o.takeSnapshot.bind(o),o.startRecording=o.startRecording.bind(o),o.stopRecording=o.stopRecording.bind(o),o.oneTwoThreeSmile=o.oneTwoThreeSmile.bind(o),o.focus=o.focus.bind(o),o.webcamActive=!1,o.opts.countdown&&(o.opts.onBeforeSnapshot=o.oneTwoThreeSmile),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e.prototype.isSupported=function(){return!!this.mediaDevices},e.prototype.getConstraints=function(){return{audio:-1!==this.opts.modes.indexOf("video-audio")||-1!==this.opts.modes.indexOf("audio-only"),video:!(-1===this.opts.modes.indexOf("video-audio")&&-1===this.opts.modes.indexOf("video-only")&&-1===this.opts.modes.indexOf("picture"))&&{facingMode:this.opts.facingMode}}},e.prototype.start=function(){var t=this;if(!this.isSupported())return Promise.reject(new Error("Webcam access not supported"));this.webcamActive=!0;var e=this.getConstraints();return this.mediaDevices.getUserMedia(e).then(function(e){t.stream=e,t.setPluginState({cameraReady:!0})}).catch(function(e){t.setPluginState({cameraError:e})})},e.prototype.startRecording=function(){var t=this;this.recorder=new MediaRecorder(this.stream),this.recordingChunks=[],this.recorder.addEventListener("dataavailable",function(e){t.recordingChunks.push(e.data)}),this.recorder.start(),this.setPluginState({isRecording:!0})},e.prototype.stopRecording=function(){var t=this;return new Promise(function(e,r){t.recorder.addEventListener("stop",function(){e()}),t.recorder.stop()}).then(function(){return t.setPluginState({isRecording:!1}),t.getVideo()}).then(function(e){try{t.uppy.addFile(e)}catch(t){}}).then(function(){t.recordingChunks=null,t.recorder=null;var e=t.uppy.getPlugin("Dashboard");e&&e.hideAllPanels()},function(e){throw t.recordingChunks=null,t.recorder=null,e})},e.prototype.stop=function(){this.stream.getAudioTracks().forEach(function(t){t.stop()}),this.stream.getVideoTracks().forEach(function(t){t.stop()}),this.webcamActive=!1,this.stream=null},e.prototype.getVideoElement=function(){return this.el.querySelector(".uppy-Webcam-video")},e.prototype.oneTwoThreeSmile=function(){var t=this;return new Promise(function(e,r){var n=t.opts.countdown,o=setInterval(function(){if(!t.webcamActive)return clearInterval(o),t.captureInProgress=!1,r(new Error("Webcam is not active"));n>0?(t.uppy.info(n+"...","warning",800),n--):(clearInterval(o),t.uppy.info(t.i18n("smile"),"success",1500),setTimeout(function(){return e()},1500))},1e3)})},e.prototype.takeSnapshot=function(){var t=this;this.captureInProgress||(this.captureInProgress=!0,this.opts.onBeforeSnapshot().catch(function(e){var r="object"===(void 0===e?"undefined":Ka(e))?e.message:e;return t.uppy.info(r,"error",5e3),Promise.reject(new Error("onBeforeSnapshot: "+r))}).then(function(){return t.getImage()}).then(function(e){t.captureInProgress=!1;var r=t.uppy.getPlugin("Dashboard");r&&r.hideAllPanels();try{t.uppy.addFile(e)}catch(t){}},function(e){throw t.captureInProgress=!1,e}))},e.prototype.getImage=function(){var t=this,e=this.getVideoElement();if(!e)return Promise.reject(new Error("No video element found, likely due to the Webcam tab being closed."));var r="webcam-"+Date.now()+".jpg",n=e.videoWidth,o=e.videoHeight,i=document.createElement("canvas");return i.width=n,i.height=o,i.getContext("2d").drawImage(e,0,0),function(t,e,r){return t.toBlob?new Promise(function(n){t.toBlob(n,e,r)}):Promise.resolve().then(function(){return lr(t.toDataURL(e,r),{})})}(i,"image/jpeg").then(function(e){return{source:t.id,name:r,data:new Blob([e],{type:"image/jpeg"}),type:"image/jpeg"}})},e.prototype.getVideo=function(){var t=this.recordingChunks[0].type,e=function(t){return La[t]||null}(t);if(!e)return Promise.reject(new Error('Could not retrieve recording: Unsupported media type "'+t+'"'));var r="webcam-"+Date.now()+"."+e,n=new Blob(this.recordingChunks,{type:t}),o={source:this.id,name:r,data:new Blob([n],{type:t}),type:t};return Promise.resolve(o)},e.prototype.focus=function(){var t=this;this.opts.countdown||setTimeout(function(){t.uppy.info(t.i18n("smile"),"success",1500)},1e3)},e.prototype.render=function(t){this.webcamActive||this.start();var e=this.getPluginState();return e.cameraReady?Ja(Xa,Ya({},e,{onSnapshot:this.takeSnapshot,onStartRecording:this.startRecording,onStopRecording:this.stopRecording,onFocus:this.focus,onStop:this.stop,i18n:this.i18n,modes:this.opts.modes,supportsRecording:"function"==typeof MediaRecorder&&!!MediaRecorder.prototype&&"function"==typeof MediaRecorder.prototype.start,recording:e.isRecording,mirror:this.opts.mirror,src:this.stream})):Ja(Ga,{icon:Na,i18n:this.i18n})},e.prototype.install=function(){this.setPluginState({cameraReady:!1});var t=this.opts.target;t&&this.mount(t,this)},e.prototype.uninstall=function(){this.stream&&this.stop(),this.unmount()},e}(Y.Plugin),Qa={};return Qa.Core=Y,Qa.server=d,Qa.views={ProviderView:Kr},Qa.DefaultStore=z,Qa.ReduxStore=jn,Qa.Dashboard=vr,Qa.DragDrop=Pr,Qa.FileInput=rn,Qa.Informer=ze,Qa.ProgressBar=Zn,Qa.StatusBar=ar,Qa.Dropbox=Qr,Qa.GoogleDrive=Wn,Qa.Instagram=Yn,Qa.Url=ja,Qa.Webcam=$a,Qa.AwsS3=kt,Qa.AwsS3Multipart=ct,Qa.Transloadit=ka,Qa.Tus=Ea,Qa.XHRUpload=vt,Qa.Form=mn,Qa.GoldenRetriever=Mn,Qa.ReduxDevTools=eo,Qa.ThumbnailGenerator=cr,Qa});
//# sourceMappingURL=uppy.min.js.map