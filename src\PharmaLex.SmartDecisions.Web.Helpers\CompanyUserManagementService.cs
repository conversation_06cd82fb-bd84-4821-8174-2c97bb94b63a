﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface ICompanyUserManagementService
    {
        Task<bool> ProcessCompanyUser(CompanyUserModel companyUser, CompanyModel company, IEnumerable<int> sites);
        Task<string> ProcessSignupInvitationResend(CompanyUserModel companyUser, CompanyModel company);
        bool ValidateNewModelState(CompanyUserViewModel model, CompanyUserModel companyUser);
        bool ValidateEditModelState(CompanyUserViewModel model, User user);

        Task ProcessMigratedCompanyUser(CompanyUserModel companyUser, CompanyModel company);
    }

    public class CompanyUserManagementService : ICompanyUserManagementService
    {
        private readonly IAzureAdB2CGraphService graphService;
        private readonly IMapper mapper;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IConfiguration configuration;
        private readonly HttpContext context;
        private readonly IOidcService oidcService;
        public CompanyUserManagementService(
            IHttpContextAccessor context,
            IDistributedCacheServiceFactory cache,
            IMapper mapper,
            IOidcService oidcService,
            IConfiguration configuration,
            IAzureAdB2CGraphService graphService)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.configuration = configuration;
            this.graphService = graphService;
            this.context = context.HttpContext;
            this.oidcService = oidcService;
        }

        private System.Security.Claims.ClaimsPrincipal User => this.context.User;
        private HttpRequest Request => this.context.Request;


        public async Task ProcessMigratedCompanyUser(CompanyUserModel companyUser, CompanyModel company)
        {
            // TODO: DELETE THIS ROUTINE AFTER MIGRATION

            string signupInvitation = oidcService.GetSignupInvitationLink("signup-invitation",
                new Dictionary<string, string>
                {
                { "displayName", $"{companyUser.GivenName} {companyUser.FamilyName}" },
                { "givenName", companyUser.GivenName },
                { "surname", companyUser.FamilyName },
                { "email", companyUser.Email }
                });

            await this.NotifyUserMigrationOnly(companyUser, company.Name, signupInvitation);
        }

        public async Task<bool> ProcessCompanyUser(CompanyUserModel companyUser, CompanyModel company, IEnumerable<int> sites)
        {
            var users = cache.CreateTrackedEntity<User>("CompanyUser", "UserClaim", "UserClaim.Claim");
            User u = await users.FirstOrDefaultAsync(x => x.Email.ToLower() == companyUser.Email.ToLower());

            if (u != null && u.CompanyUser != null && u.CompanyUser.CompanyId != company.Id)
                return false;

            bool isNewDbUser = u is null;
            if (isNewDbUser)
            {
                u = new User();
                users.Add(u);
            }

            mapper.Map(companyUser, u);
            u.CompanyUser.CompanyId = company.Id;

            if (!await this.graphService.UserExists(companyUser.Email) && isNewDbUser)
            {
                u.InvitationEmailLink = await SendSignupEmail(companyUser, company);
            }
            else if (isNewDbUser)
            {
                await this.NotifyUser(companyUser, company.Name);
            }

            await users.SaveChangesAsync();

            return true;
        }

        public async Task<string> ProcessSignupInvitationResend(CompanyUserModel companyUser, CompanyModel company)
        {
            var users = cache.CreateTrackedEntity<User>();
            User u = await users.FirstOrDefaultAsync(x => x.Id == companyUser.Id);

            u.InvitationEmailLink = await SendSignupEmail(companyUser, company);
            await users.SaveChangesAsync();

            return u.InvitationEmailLink;
        }

        private async Task<string> SendSignupEmail(CompanyUserModel companyUser, CompanyModel company)
        {
            string signupInvitation = oidcService.GetSignupInvitationLink("signup-invitation",
                new Dictionary<string, string>
                {
                        { "displayName", $"{companyUser.GivenName} {companyUser.FamilyName}" },
                        { "givenName", companyUser.GivenName },
                        { "surname", companyUser.FamilyName },
                        { "email", companyUser.Email }
                });

            await this.NotifyUser(companyUser, company.Name, signupInvitation);

            return signupInvitation;
        }

        private async Task NotifyUser(CompanyUserModel user, string company, string signupInvitation = null)
        {
            string inviterEmail = this.User.GetEmail();
            string inviterName = this.User.GetClaimValue(ClaimConstants.Name);

            string apiKey = this.configuration.GetConnectionString("SendGrid");
            string senderEmail = this.configuration.GetValue<string>("AppSettings:SmartPHLEXAdminEmail");

            SendGridClient client = new SendGridClient(apiKey);
            var from = new EmailAddress(senderEmail, "SmartPHLEX admin");
            var to = new EmailAddress(user.Email);

            var model = new CompanyUserCreationEmail
            {
                FullName = $"{user.GivenName} {user.FamilyName}",
                InviterName = inviterName,
                InviterEmail = inviterEmail,
                Company = company,
                Email = user.Email,
                Url = signupInvitation ?? $"{this.Request.Scheme}://{this.Request.Host}",
                LoginUrl = $"{this.Request.Scheme}://{this.Request.Host}",
                ExpiryDays = this.configuration.GetValue<string>("AzureAdB2CPolicy:LinkExpiresAfterDays")
            };

            var templateId = this.configuration.GetValue<string>($"AppSettings:{(String.IsNullOrEmpty(signupInvitation) ? "CompanyUserLoginEmailTemplateId" : "CompanyUserSignUpEmailTemplateId")}");

            var msg = new SendGridMessage();
            msg.SetFrom(from);
            msg.AddTo(to);
            msg.SetTemplateId(templateId);
            msg.SetTemplateData(model);

            await client.SendEmailAsync(msg); 
        }

        private async Task NotifyUserMigrationOnly(CompanyUserModel user, string company, string signupInvitation = null)
        {
            // TODO: DELETE THIS ROUTINE AFTER MIGRATION

            string inviterEmail = this.User.GetEmail();
            string inviterName = this.User.GetClaimValue(ClaimConstants.Name);

            string apiKey = this.configuration.GetConnectionString("SendGrid");
            string senderEmail = this.configuration.GetValue<string>("AppSettings:SmartPHLEXAdminEmail");

            SendGridClient client = new SendGridClient(new SendGridClientOptions { ApiKey = apiKey, HttpErrorAsException = true });
            var from = new EmailAddress(senderEmail, "SmartPHLEX admin");
            var to = new EmailAddress(user.Email);

            var model = new CompanyUserCreationEmail
            {
                FullName = user.DisplayFullName,
                InviterName = inviterName,
                InviterEmail = inviterEmail,
                Company = company,
                Email = user.Email,
                Url = signupInvitation ?? $"{this.Request.Scheme}://{this.Request.Host}"
            };

            var templateId = "d-bcaa0c1ed91b4f7d8eb3771f54982236";

            var msg = new SendGridMessage();
            msg.SetFrom(from);
            msg.AddTo(to);
            msg.SetTemplateId(templateId);
            msg.SetTemplateData(model);

            await client.SendEmailAsync(msg);
        }

        public bool ValidateNewModelState(CompanyUserViewModel model, CompanyUserModel companyUser)
        {
            return !model.TotalLicenseLimitReached &&
                !(companyUser.Active && model.ActiveLicenseLimitReached);
        }

        public bool ValidateEditModelState(CompanyUserViewModel model, User user)
        {
            return !model.ActiveLicenseLimitReached || !model.CompanyUser.Active ||
                model.CompanyUser.Active == user.CompanyUser.Active;
        }
    }
}
