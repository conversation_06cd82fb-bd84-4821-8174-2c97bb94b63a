﻿create trigger [dbo].[MultilingualResource_Insert] on [dbo].[MultilingualResource]
for insert as
insert into [Audit].[MultilingualResource_Audit]
select 'I'
      ,[Id]
      ,[LocaleId]
      ,[Key]
      ,[Content]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[MultilingualResource_Update] on [dbo].[MultilingualResource]
for update as
insert into [Audit].[MultilingualResource_Audit]
select 'U'
      ,[Id]
      ,[LocaleId]
      ,[Key]
      ,[Content]
      ,[CreatedDate]
      ,[CreatedBy]
      ,[LastUpdatedDate]
      ,[LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[MultilingualResource_Delete] on [dbo].[MultilingualResource]
for delete as
insert into [Audit].[MultilingualResource_Audit]
select 'D'
      ,[Id]
      ,[LocaleId]
      ,[Key]
      ,[Content]
      ,[CreatedDate]
      ,[CreatedBy]
      ,getdate()
      ,coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
