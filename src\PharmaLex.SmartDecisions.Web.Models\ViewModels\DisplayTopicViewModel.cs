﻿using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class DisplayTopicViewModel
    {
        public ContentTypeModel ContentType { get; set; }
        public ViewContentTypeDisplayModel Display { get; set; }
        public List<TopicItemModel> Items { get; set; }

        public IEnumerable<RegulatoryAuthorityModel> RegulatoryAuthorities { get; set; }
        public IEnumerable<CountryModel> Markets { get; set; }
        public CountryModel Country { get; set; }

        public IEnumerable<PicklistModel> Picklists { get; set; }
        public TopicItemModel SelectedItem { get; set; }

        public int SelectedItemIndex
        {
            get { return this.SelectedItem != null ? this.Items.FindIndex(x => x.Id == this.SelectedItem.Id) : 0; }
        }

        public int? PreviousItemId
        {
            get
            {
                if (this.Items.Count <= 1)
                {
                    return this.SelectedItem?.Id;
                }
                int i = this.SelectedItemIndex;
                return i == 0 ? this.Items.Last().Id : this.Items[i - 1].Id;
            }
        }

        public int? NextItemId
        {
            get
            {
                if (this.Items.Count <= 1)
                {
                    return this.SelectedItem?.Id;
                }
                int i = this.SelectedItemIndex;
                return i == this.Items.Count - 1 ? this.Items.First().Id : this.Items[i + 1].Id;
            }
        }
    }
}
