﻿<link rel="stylesheet" href="/dist/css/mapsCss.css" asp-append-version="true" />

<script type="text/x-template" id="plx-map-template">
    <div>
        <select id="countrySelect" v-show="showList" v-bind:disabled="!editable" multiple v-model="selectedCountries" v-on:change="onCountryListSelectionChanged" :class="['company-countries-select']">
            <option v-for="country in countries" :value="country.id">{{country.name}}</option>
        </select>
        <div v-show="!showList" id="map-container" :class="['company-countries-map']"></div>

        <div class="buttons p-0 mt-1 no-border">
            <select class="on-map concise" v-if="regions.length && editable" v-model="copyFromRegion" v-on:change="onCopyFromRegionChange">
                <option value="-1">Select region</option>
                <option v-for="(r, i) in regions" :value="r.id" ref="regionSelector">{{r.name}}</option>
            </select>
            <a class="button" @@click="showList=!showList">{{toggleText}}</a>
            
            <a v-if="editable" class="button" @@click="onSelectAllCountries">Select all</a>
            <a v-if="editable" class="button" @@click="onClear">Clear all</a>
        </div>
        
    </div>
</script>

<environment include="Development">
    <script src="/data/countries.topo.js"></script>
    <script src="/lib/d3/d3.v4.js"></script>
    <script src="/lib/d3/topojson.js"></script>
    <script src="/js/map.js" asp-append-version="true"></script>
    <script src="/js/vue/v3/plx-map-v3.js"></script>
</environment>
<environment exclude="Development">
    <script src="@VersionCdn.GetUrl("js/vue/v3/plx-map-v3.js")"></script>
    <script src="@VersionCdn.GetUrl("js/map-bundle.min.js")"></script>
</environment>