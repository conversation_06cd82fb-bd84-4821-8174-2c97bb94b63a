﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class NewsArticleAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public int? NewsSourceId { get; set; }
        public int? ImportanceId { get; set; }
        public System.DateTime? SourcePublicationDate { get; set; }

    }
}
