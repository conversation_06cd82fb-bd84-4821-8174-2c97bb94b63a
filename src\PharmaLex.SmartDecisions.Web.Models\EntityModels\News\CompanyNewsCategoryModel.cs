﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class LicenseNewsCategoryModel : IModel
    {
        public int Id { get; set; }
        public int? ParentId { get; set; }
        public int GroupId { get; set; }
        public string Name { get; set; }
    }

    public class LicenseNewsCategoryModelMappingProfile : Profile
    {
        public LicenseNewsCategoryModelMappingProfile()
        {
            this.CreateMap<NewsCategory, LicenseNewsCategoryModel>();
        }
    }
}
