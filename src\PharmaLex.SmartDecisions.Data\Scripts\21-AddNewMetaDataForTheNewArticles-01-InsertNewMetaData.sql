﻿DECLARE @parentId int
SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Type of text')

INSERT INTO [dbo].[NewsCategory] SELECT N'Newsletter', @parentId, 4, 72, N'newsletter', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Rules of procedure', @parentId, 4, 73, N'rules-of-procedure', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Questions & Answers', @parentId, 4, 74, N'questions-and-answers', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Call for applications', @parentId, 4, 75, N'call-for-applications', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].newsletter', 'Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].newsletter', 'Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].rules-of-procedure', 'Rules of procedure', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].rules-of-procedure', 'Règlement intérieur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].questions-and-answers', 'Questions & Answers', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].questions-and-answers', 'Questions & réponses', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].call-for-applications', 'Call for applications', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].call-for-applications', 'Appel à candidatures', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Medicines')

INSERT INTO [dbo].[NewsCategory] SELECT N'GMOs', @parentId, 2, 76, N'gmo', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Labile blood products', @parentId, 2, 77, N'labile-blood-product', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].gmo', 'GMOs', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].gmo', 'OGM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].labile-blood-product', 'Labile blood products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].labile-blood-product', 'Produits sanguins labiles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[NewsSource] select 'CAMD (Competent Authorities for Medical Devices)', 'https://ich.org', 73, N'camd', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsSource].camd', 'CAMD (Competent Authorities for Medical Devices)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].camd', 'CAMD (Competent Authorities for Medical Devices)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

