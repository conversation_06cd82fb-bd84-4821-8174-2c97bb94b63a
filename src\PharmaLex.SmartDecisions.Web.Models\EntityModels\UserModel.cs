﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class UserModel : IModel
    {
        public UserModel()
        {
            this.Claims = new List<int>();
            this.DisplayClaims = new List<string>();
        }

        public int Id { get; set; }
        [Required, StringLength(256)]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Email field.")]
        public string Email { get; set; }
        [Required, StringLength(512)]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Given Name field.")]
        public string GivenName { get; set; }
        [Required, StringLength(512)]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Family Name field.")]
        public string FamilyName { get; set; }
        public List<int> Claims { get; set; }
        public List<string> DisplayClaims { get; set; }
        public string DisplayClaimsText { get; set; }
        public string DisplayFullName { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public string InvitationEmailLink { get; set; }
        public string CompanyName { get; set; }

        public string DisplayNameAndEmail
        {
            get { return this.Id > 0 ? $"{this.GivenName} {this.FamilyName} ({this.Email})" : ""; }
        }
    }

    public class CompanyUserModel : UserModel
    {
        public bool Active { get; set; }
        [Required]
        public int? ClaimId { get; set; }
    }

    public class UserFindResultModel
    {
        public int Id { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
        public string Email { get; set; }

        public bool Active { get; set; } = true;
        public List<int> Claims { get; set; }
    }
}
