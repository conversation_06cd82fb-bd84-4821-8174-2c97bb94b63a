﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "SystemAdmin")]
    public partial class ContentItemController : BaseContentItemController
    {
        protected readonly IContentItemExportWriter exportWriter;
        protected readonly IContentItemImportReader importReader;

        public ContentItemController(IDistributedCacheServiceFactory cacheFactory, IMapper mapper, IContentItemExportWriter exportWriter, IContentItemImportReader importReader) : base(cacheFactory, mapper)
        {
            this.exportWriter = exportWriter;
            this.importReader = importReader;
        }

        [HttpGet("/data/{id}")]
        public async Task<IActionResult> ContentItems(int id)
        {
            ListContentItemViewModel lcivm = await this.GetContentItems(id);

            return View(lcivm);
        }

        [HttpGet("/data/new/{contentTypeId}")]
        public async Task<IActionResult> New(int contentTypeId)
        {
            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == contentTypeId);
            return View("EditContentItem", new EditContentItemViewModel
            {
                ContentType = ctm,
                Item = new ContentItemModel()
                {
                    ContentTypeId = ctm.Id,
                    ContentType = ctm,
                    Owner = ((ClaimsIdentity)this.User.Identity).GetEmail(),
                    VerifiedDate = DateTime.Now
                }
            });
        }

        [HttpPost("/data/new/{contentTypeId}")]
        public async Task<IActionResult> SaveNew(int contentTypeId)
        {
            var model = this.Request.Form;

            // the validation of the Name field is done here and not in the ContentItemModel because more complex refactoring is needed to change the code to use the ContentItemModel
            if (Regex.IsMatch(model["name"].ToString(), new GeneratedRegexAttribute(@"<.*?>|<|>").Pattern, RegexOptions.NonBacktracking))
            {
                TempData.Add("ErrorMessage", "Invalid characters in the Name field.");
                return Redirect($"/data/new/{contentTypeId}");
            }
            var ctc = cacheFactory.CreateEntity<ContentType>().Configure(x => x.Include(y => y.Field));
            var ct = await ctc.FirstOrDefaultAsync(x => x.Id == contentTypeId);
            var cic = cacheFactory.CreateTrackedEntity<ContentItem>();
            var ci = new ContentItem
            {
                ContentTypeId = contentTypeId,
                Name = ct.AutoManageName ? $"{ct.Name}-{DateTime.Now.Ticks}" : model["name"].ToString(),
                Owner = model["owner"],
                VerifiedDate = DateTime.Parse(model["verifieddate"])
            };
            cic.Add(ci);
            await cic.SaveChangesAsync();
            if (ct.AutoManageName)
            {
                ci.Name = $"{ct.Name}-{ci.Id}";
                await cic.SaveChangesAsync();
            }
            var fvc = cacheFactory.CreateTrackedEntity<FieldValue>();
            this.MapFieldValues(model, ci, ct, fvc);
            await fvc.SaveChangesAsync();
    
            return Redirect($"/data/{contentTypeId}");
        }

        [HttpGet("/data/delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>();
            var cim = await cic.FirstOrDefaultAsync(x => x.Id == id);

            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>();
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == cim.ContentTypeId);

            return View("DeleteContentItem", new EditContentItemViewModel
            {
                ContentType = ctm,
                Item = cim
            });
        }

        [HttpPost("/data/delete/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            var cic = cacheFactory.CreateTrackedEntity<ContentItem>();
            ContentItem ci = await cic.FirstOrDefaultAsync(x => x.Id == id);
            cic.Remove(ci);
            await cic.SaveChangesAsync();
            return Redirect($"/data/{ci.ContentTypeId}");
        }

        [HttpGet("/data/export/{id}")]
        public async Task<IActionResult> Export(int id)
        {
            var ctc = cacheFactory.CreateEntity<ContentType>().Configure(x => x.Include(y => y.Field));
            var ct = await ctc.FirstOrDefaultAsync(x => x.Id == id);
            var cic = cacheFactory.CreateEntity<ContentItem>().Configure(x => x.Include(y => y.FieldValue));
            var items = await cic.WhereAsync(x => x.ContentTypeId == id);
            byte[] xl = exportWriter.Write(ct, items);
            return File(xl, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"SmartDECISIONS_{ct.PluralName}_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.xlsx");
        }

        [HttpPost("/data/import/{id}")]
        public async Task<IActionResult> Import(int id, IFormFile file)
        {
            var ctc = cacheFactory.CreateEntity<ContentType>().Configure(x => x.Include(y => y.Field));
            var ct = await ctc.FirstOrDefaultAsync(x => x.Id == id);
            var items = importReader.Read(ct, file.OpenReadStream(), this.User.Identity.Name);
            var ctm = mapper.Map<ContentTypeModel>(ct);
            var cic = cacheFactory.CreateTrackedEntity<ContentItem>().Configure(x => x.Include(y => y.FieldValue));
            var existing = await cic.WhereAsync(x => x.ContentTypeId == id);
            var errorMessages = new List<string>();
            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                if (item.Id > 0)
                {
                    var ci = existing.FirstOrDefault(x => x.Id == item.Id);
                    if (ci != null)
                    {
                        // Create a temporary model to merge the existing data and imported data for validation
                        var cim = mapper.Map<ContentItemModel>(ci);
                        mapper.Map(item, cim);
                        var validation = cim.Validate(ctm);
                        if (validation.Success)
                        {
                            mapper.Map(cim, ci);
                        }
                        else
                        {
                            errorMessages.Add($"Row {i + 2}: {validation.Message}");
                        }
                    }
                    else
                    {
                        errorMessages.Add($"Row {i + 2}: No existing {ct.Name} with the supplied ID of {item.Id} could be found to update");
                    }
                }
                else
                {
                    var validation = item.Validate(ctm);
                    if (validation.Success)
                    {
                        cic.Add(mapper.Map<ContentItem>(item));
                    }
                    else
                    {
                        errorMessages.Add($"Row {i + 2}: {validation.Message}");
                    }
                }
            }
            await cic.SaveChangesAsync();

            return Json(new 
            { 
                Count = items.Count,
                Errors = errorMessages
            });
        }
    }
}