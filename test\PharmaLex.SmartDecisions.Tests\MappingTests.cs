﻿using AutoMapper;
using PharmaLex.SmartDecisions.Web.Models.Mappings;
using Xunit;

namespace PharmaLex.SmartDecisions.Tests
{
    public class MappingTests
    {
        private readonly IMapper _sut;

        public MappingTests()
        {
            var assembly = typeof(CompanyModelMappingProfile).Assembly;
            _sut = new MapperConfiguration(cfg => cfg.AddMaps(assembly)).CreateMapper();
        }

        [Fact]
        public void All_mappings_should_be_setup_correctly() => _sut.ConfigurationProvider.AssertConfigurationIsValid();
    }
}
