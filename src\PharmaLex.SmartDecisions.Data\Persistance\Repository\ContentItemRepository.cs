﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Data.Persistance.Repository
{
    public class ContentItemRepository : TrackingGenericRepository<ContentItem>, IContentItemRepository 
    {
        public ContentItemRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
        {
        }

        public Task AddItemAsync(ContentItem item)
        {
            throw new NotImplementedException();
        }

        public Task DeleteItemAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<ContentItem>> GetAllItemsAsync()
        {
            throw new NotImplementedException();
        }

        public Task<ContentItem> GetItemAsync(int id)
        {
            throw new NotImplementedException();
        }

        public IQueryable<ContentItem> GetQueryableItems(Func<IQueryable<ContentItem>, IIncludableQueryable<ContentItem, object>> include = null)
        {
            var query = context.Set<ContentItem>().AsQueryable();
            if (include != null)
            {
                query = include(query);
            }

            return query;
        }

        public Task UpdateItemAsync(ContentItem item)
        {
            throw new NotImplementedException();
        }
    }
}
