﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class CompanyModel : IModel
    {
        public CompanyModel()
        {
            this.CompanyUsers = new List<CompanyUserModel>();
            this.LicensedTopics = new List<int>();
            this.LicensedNewsCategories = new List<int>();
        }

        public int Id { get; set; }
        [Required]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Name field.")]
        public string Name { get; set; }
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Contact name field.")]
        public string PrimaryContactName { get; set; }
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Contact email field.")]
        public string PrimaryContactEmail { get; set; }
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Contact address field.")]
        public string PrimaryContactAddress { get; set; }
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Contact phone field.")]
        public string PrimaryContactPhone { get; set; }
        [Required]
        public int? MaximumUsersCount { get; set; }
        [Required]
        public int? MaximumActiveUsersCount { get; set; }

        public ICollection<CompanyUserModel> CompanyUsers { get; set; }
        public ICollection<int> LicensedTopics { get; set; }
        public ICollection<int> LicensedNewsCategories { get; set; }
    }
}
