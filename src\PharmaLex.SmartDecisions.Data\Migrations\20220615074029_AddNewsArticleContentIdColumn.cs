﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.SmartDecisions.Data.Extensions;

#nullable disable

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class AddNewsArticleContentIdColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "NewsArticleContentIds",
                schema: "Audit",
                table: "Newsletter_Audit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NewsArticleContentIds",
                table: "Newsletter",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticleContent_PublishingStateId",
                table: "NewsArticleContent",
                column: "PublishingStateId");

            migrationBuilder.CreateIndex(
                name: "IX_NewsArticle_ImportanceId",
                table: "NewsArticle",
                column: "ImportanceId");

            migrationBuilder.SqlFileExec("15-AddNewsArticleContentIdColumn-01-UpdateNewsletterTriggers.sql");
            migrationBuilder.SqlFileExec("15-AddNewsArticleContentIdColumn-02-MigrateNewsArticleContentData.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_NewsArticleContent_PublishingStateId",
                table: "NewsArticleContent");

            migrationBuilder.DropIndex(
                name: "IX_NewsArticle_ImportanceId",
                table: "NewsArticle");

            migrationBuilder.DropColumn(
                name: "NewsArticleContentIds",
                schema: "Audit",
                table: "Newsletter_Audit");

            migrationBuilder.DropColumn(
                name: "NewsArticleContentIds",
                table: "Newsletter");
        }
    }
}
