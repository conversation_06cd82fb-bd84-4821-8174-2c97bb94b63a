﻿using PharmaLex.SmartDecisions.Entities.Enums;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class UserNotificationModel
    {
        public string Html { get; set; }
        public int Position { get; set; }
        public string Type { get; set; }
        public int? Number { get; set; }
        public int Duration { get; set; }
        public NotificationOptions Options { get; set; }

        public UserNotificationModel(string html, 
            UserNotificationPosition position = UserNotificationPosition.TopCenter, 
            UserNotificationType type = UserNotificationType.Info, 
            int duration = 2500, 
            int? number = null, 
            NotificationOptions options = null)
        {
            this.Html = html;
            this.Position = (int)position;
            this.Type = type.ToString().ToUpperInvariant();
            this.Duration = duration;
            this.Number = number;
            this.Options = options;
        }
    }
}
