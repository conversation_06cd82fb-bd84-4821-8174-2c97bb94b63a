﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PharmaLex.SmartDecisions.Entities;

#nullable disable

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    [DbContext(typeof(SmartDecisionsContext))]
    [Migration("20220615074029_AddNewsArticleContentIdColumn")]
    partial class AddNewsArticleContentIdColumn
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.AzureBlobAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("Container")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ContentType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Length")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("Uri")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("AzureBlob_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ClaimAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("ClaimType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("AuditId");

                    b.ToTable("Claim_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("MaximumActiveUsersCount")
                        .HasColumnType("int");

                    b.Property<int?>("MaximumUsersCount")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PrimaryContactAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PrimaryContactEmail")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("PrimaryContactName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PrimaryContactPhone")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("AuditId");

                    b.ToTable("Company_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyContentTypeAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int?>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("AuditId");

                    b.ToTable("CompanyContentType_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyNewsCategoryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("NewsCategoryId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("CompanyNewsCategory_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyUserAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<bool?>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("CompanyUser_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ContentItemAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Owner")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("VerifiedDate")
                        .HasColumnType("datetime");

                    b.HasKey("AuditId");

                    b.ToTable("ContentItem_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ContentTypeAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<bool?>("AutoManageName")
                        .HasColumnType("bit");

                    b.Property<int?>("ContentTypeCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Owner")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PluralName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ShortName")
                        .HasMaxLength(31)
                        .HasColumnType("nvarchar(31)");

                    b.Property<bool?>("System")
                        .HasColumnType("bit");

                    b.HasKey("AuditId");

                    b.ToTable("ContentType_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ContentTypeDisplayAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("ContentTypeDisplayTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Json")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("ContentTypeDisplay_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.FieldAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int?>("FieldTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Length")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("RelatedContentTypeId")
                        .HasColumnType("int");

                    b.Property<bool?>("Required")
                        .HasColumnType("bit");

                    b.Property<bool?>("System")
                        .HasColumnType("bit");

                    b.Property<bool?>("Unique")
                        .HasColumnType("bit");

                    b.HasKey("AuditId");

                    b.ToTable("Field_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.FieldValueAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("ContentItemId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("FieldId")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AuditId");

                    b.ToTable("FieldValue_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.LocaleAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<bool?>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<bool?>("Default")
                        .HasColumnType("bit");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("IsoCountryCode")
                        .HasColumnType("nchar(2)");

                    b.Property<string>("IsoLanguageCode")
                        .HasColumnType("nchar(2)");

                    b.Property<string>("IsoScriptCode")
                        .HasColumnType("nchar(4)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LocalisationKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LocalisedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("Locale_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.MultilingualResourceAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Key")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("LocaleId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("MultilingualResource_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.NewsArticleAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<int?>("ImportanceId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("NewsSourceId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SourcePublicationDate")
                        .HasColumnType("datetime2");

                    b.HasKey("AuditId");

                    b.ToTable("NewsArticle_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.NewsArticleCategoryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("NewsArticleId")
                        .HasColumnType("int");

                    b.Property<int?>("NewsCategoryId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("NewsArticleCategory_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.NewsArticleContentAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("AuthorId")
                        .HasColumnType("int");

                    b.Property<int?>("AzureBlobId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("FriendlyUrl")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("GoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("ImpactAssessmentSummary")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("LocaleId")
                        .HasColumnType("int");

                    b.Property<int?>("NewsArticleId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PublishingStateDateUtc")
                        .HasColumnType("datetime");

                    b.Property<int?>("PublishingStateId")
                        .HasColumnType("int");

                    b.Property<int?>("ReviewerId")
                        .HasColumnType("int");

                    b.Property<string>("SourceUrl")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("NewsArticleContent_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.NewsCategoryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("GroupId")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LocalisationKey")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("NewsCategory_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.NewsletterAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CreatedDateUtc")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<bool?>("IsMonthly")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("LocaleId")
                        .HasColumnType("int");

                    b.Property<string>("NewsArticleContentIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SubscriptionId")
                        .HasColumnType("int");

                    b.Property<string>("UniqueKey")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("Newsletter_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.NewsletterNewsArticleContentAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("NewsArticleContentId")
                        .HasColumnType("int");

                    b.Property<int?>("NewsletterId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TimeOpenUtc")
                        .HasColumnType("datetime2");

                    b.HasKey("AuditId");

                    b.ToTable("NewsletterNewsArticleContent_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.UserAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("FamilyName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("GivenName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("InvitationEmailLink")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("LocaleId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("User_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.UserClaimAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<int?>("ClaimId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("UserClaim_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.UserNewsCategoryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CreatedDateUtc")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("NewsCategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("UserNewsCategory_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.AzureBlob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Container")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("Length")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("Uri")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "Container")
                        .IsUnique()
                        .HasDatabaseName("UC_AzureBlob_Name_Container");

                    b.ToTable("AzureBlob");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Claim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("Claim");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("MaximumActiveUsersCount")
                        .HasColumnType("int");

                    b.Property<int>("MaximumUsersCount")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PrimaryContactAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PrimaryContactEmail")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("PrimaryContactName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PrimaryContactPhone")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("UC_Company_Name");

                    b.ToTable("Company");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyContentType", b =>
                {
                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("CompanyId", "ContentTypeId");

                    b.HasIndex("ContentTypeId");

                    b.ToTable("CompanyContentType");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyNewsCategory", b =>
                {
                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int>("NewsCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("CompanyId", "NewsCategoryId");

                    b.HasIndex("NewsCategoryId");

                    b.ToTable("CompanyNewsCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyUser", b =>
                {
                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("CompanyId", "UserId");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("UC_CompanyUser_UserId");

                    b.ToTable("CompanyUser");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Owner")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("VerifiedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ContentTypeId", "Name" }, "UC_ContentItem_ContentTypeId_Name")
                        .IsUnique();

                    b.ToTable("ContentItem");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<bool>("AutoManageName")
                        .HasColumnType("bit");

                    b.Property<int>("ContentTypeCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Owner")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PluralName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasMaxLength(31)
                        .HasColumnType("nvarchar(31)");

                    b.Property<bool>("System")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Name" }, "UC_ContentType_Name")
                        .IsUnique();

                    b.ToTable("ContentType");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentTypeDisplay", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("ContentTypeDisplayTypeId")
                        .HasColumnType("int");

                    b.Property<int>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Json")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("ContentTypeId");

                    b.ToTable("ContentTypeDisplay");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Field", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("ContentTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int>("FieldTypeId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Length")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("RelatedContentTypeId")
                        .HasColumnType("int");

                    b.Property<bool>("Required")
                        .HasColumnType("bit");

                    b.Property<bool>("System")
                        .HasColumnType("bit");

                    b.Property<bool>("Unique")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ContentTypeId", "Name" }, "UC_Field_ContentTypeId_Name")
                        .IsUnique();

                    b.ToTable("Field");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.FieldValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("ContentItemId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("FieldId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ContentItemId");

                    b.HasIndex("FieldId");

                    b.ToTable("FieldValue");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Locale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("Default")
                        .HasColumnType("bit");

                    b.Property<string>("IsoCountryCode")
                        .IsRequired()
                        .HasColumnType("nchar(2)");

                    b.Property<string>("IsoLanguageCode")
                        .IsRequired()
                        .HasColumnType("nchar(2)");

                    b.Property<string>("IsoScriptCode")
                        .HasColumnType("nchar(4)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LocalisationKey")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LocalisedName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("UC_Locale_Name");

                    b.HasIndex("IsoLanguageCode", "IsoScriptCode", "IsoCountryCode")
                        .IsUnique()
                        .HasDatabaseName("UC_Locale_Code")
                        .HasFilter("[IsoScriptCode] IS NOT NULL");

                    b.ToTable("Locale");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Logging", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("UserId")
                        .HasMaxLength(256)
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Logging");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.MultilingualResource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("LocaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LocaleId");

                    b.HasIndex("Key", "LocaleId")
                        .IsUnique()
                        .HasDatabaseName("UC_MultilingualResource_Key_Locale");

                    b.ToTable("MultilingualResource");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ImportanceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("NewsSourceId")
                        .HasColumnType("int");

                    b.Property<DateTime>("SourcePublicationDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("NewsSourceId");

                    b.HasIndex(new[] { "ImportanceId" }, "IX_NewsArticle_ImportanceId");

                    b.ToTable("NewsArticle");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticleCategory", b =>
                {
                    b.Property<int>("NewsArticleId")
                        .HasColumnType("int");

                    b.Property<int>("NewsCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("NewsArticleId", "NewsCategoryId")
                        .HasName("PK_NewsArticleCategory");

                    b.HasIndex("NewsCategoryId");

                    b.ToTable("NewsArticleCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticleContent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("AuthorId")
                        .HasColumnType("int");

                    b.Property<int?>("AzureBlobId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("FriendlyUrl")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("GoLiveDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ImpactAssessmentSummary")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("LocaleId")
                        .HasColumnType("int");

                    b.Property<int>("NewsArticleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("PublishingStateDateUtc")
                        .HasColumnType("datetime");

                    b.Property<int>("PublishingStateId")
                        .HasColumnType("int");

                    b.Property<int?>("ReviewerId")
                        .HasColumnType("int");

                    b.Property<string>("SourceUrl")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("AuthorId");

                    b.HasIndex("AzureBlobId");

                    b.HasIndex("FriendlyUrl")
                        .IsUnique()
                        .HasDatabaseName("UC_NewsArticleContent_FriendlyUrl");

                    b.HasIndex("NewsArticleId");

                    b.HasIndex("ReviewerId");

                    b.HasIndex(new[] { "PublishingStateId" }, "IX_NewsArticleContent_PublishingStateId");

                    b.HasIndex(new[] { "LocaleId", "NewsArticleId" }, "UC_NewsArticleContent_LocaleId_NewsArticleId")
                        .IsUnique();

                    b.ToTable("NewsArticleContent");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("GroupId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LocalisationKey")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("NewsCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Newsletter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreatedDateUtc")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsMonthly")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("LocaleId")
                        .HasColumnType("int");

                    b.Property<string>("NewsArticleContentIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("NewsletterSubscriptionId")
                        .HasColumnType("int");

                    b.Property<string>("UniqueKey")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LocaleId");

                    b.HasIndex("NewsletterSubscriptionId");

                    b.HasIndex("UniqueKey")
                        .IsUnique()
                        .HasDatabaseName("UC_Newsletter_UniqueKey");

                    b.HasIndex("UserId");

                    b.ToTable("Newsletter");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsletterNewsArticleContent", b =>
                {
                    b.Property<int>("NewsletterId")
                        .HasColumnType("int");

                    b.Property<int>("NewsArticleContentId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("TimeOpenUtc")
                        .HasColumnType("datetime");

                    b.HasKey("NewsletterId", "NewsArticleContentId");

                    b.HasIndex("NewsArticleContentId");

                    b.ToTable("NewsletterNewsArticleContent");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsletterSubscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreatedDateUtc")
                        .HasColumnType("datetime");

                    b.Property<int>("DeliveryLocalDay")
                        .HasColumnType("int");

                    b.Property<int>("DeliveryLocalHour")
                        .HasColumnType("int");

                    b.Property<int>("DeliveryUtcDay")
                        .HasColumnType("int");

                    b.Property<int>("DeliveryUtcHour")
                        .HasColumnType("int");

                    b.Property<bool>("IsInfoflash")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsMonthly")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Timezone")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("TimezoneOffset")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("NewsletterSubscription");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsletterSubscriptionAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<bool?>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CreatedDateUtc")
                        .HasColumnType("datetime");

                    b.Property<int?>("DeliveryLocalDay")
                        .HasColumnType("int");

                    b.Property<int?>("DeliveryLocalHour")
                        .HasColumnType("int");

                    b.Property<int?>("DeliveryUtcDay")
                        .HasColumnType("int");

                    b.Property<int?>("DeliveryUtcHour")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<bool?>("IsInfoflash")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsMonthly")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Timezone")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("TimezoneOffset")
                        .HasColumnType("int");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("NewsletterSubscription_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsSource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LocalisationKey")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("UC_NewsSource_Name");

                    b.ToTable("NewsSource");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsSourceAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AuditId"), 1L, 1);

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LocalisationKey")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("NewsSource_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("FamilyName")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("GivenName")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("InvitationEmailLink")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("LocaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LocaleId");

                    b.ToTable("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.UserClaim", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("ClaimId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("UserId", "ClaimId");

                    b.HasIndex("ClaimId");

                    b.ToTable("UserClaim");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.UserNewsCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreatedDateUtc")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("NewsCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("NewsCategoryId");

                    b.HasIndex("UserId", "NewsCategoryId")
                        .IsUnique()
                        .HasDatabaseName("UC_UserNewsCategory");

                    b.ToTable("UserNewsCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyContentType", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Company", "Company")
                        .WithMany("CompanyContentType")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CompanyContentType_Company");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                        .WithMany("CompanyContentType")
                        .HasForeignKey("ContentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CompanyContentType_ContentType");

                    b.Navigation("Company");

                    b.Navigation("ContentType");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyNewsCategory", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Company", "Company")
                        .WithMany("CompanyNewsCategory")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CompanyNewsCategory_Company");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsCategory", "NewsCategory")
                        .WithMany("CompanyNewsCategory")
                        .HasForeignKey("NewsCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CompanyNewsCategory_NewsCategory");

                    b.Navigation("Company");

                    b.Navigation("NewsCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyUser", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Company", "Company")
                        .WithMany("CompanyUser")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CompanyUser_Company");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                        .WithOne("CompanyUser")
                        .HasForeignKey("PharmaLex.SmartDecisions.Entities.CompanyUser", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_CompanyUser_User");

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentItem", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                        .WithMany("ContentItem")
                        .HasForeignKey("ContentTypeId")
                        .IsRequired()
                        .HasConstraintName("FK_ContentItem_ContentType");

                    b.Navigation("ContentType");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentTypeDisplay", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                        .WithMany("ContentTypeDisplay")
                        .HasForeignKey("ContentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ContentTypeDisplay_ContentType");

                    b.Navigation("ContentType");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Field", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                        .WithMany("Field")
                        .HasForeignKey("ContentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Field_ContentType");

                    b.Navigation("ContentType");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.FieldValue", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.ContentItem", "ContentItem")
                        .WithMany("FieldValue")
                        .HasForeignKey("ContentItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_FieldValue_ContentItem");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.Field", "Field")
                        .WithMany("FieldValue")
                        .HasForeignKey("FieldId")
                        .IsRequired()
                        .HasConstraintName("FK_FieldValue_Field");

                    b.Navigation("ContentItem");

                    b.Navigation("Field");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.MultilingualResource", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Locale", "Locale")
                        .WithMany("MultilingualResource")
                        .HasForeignKey("LocaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_MultilingualResource_Locale");

                    b.Navigation("Locale");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticle", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsSource", "NewsSource")
                        .WithMany("NewsArticle")
                        .HasForeignKey("NewsSourceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_NewsArticle_NewsSource");

                    b.Navigation("NewsSource");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticleCategory", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsArticle", "NewsArticle")
                        .WithMany("NewsArticleCategory")
                        .HasForeignKey("NewsArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_NewsArticleCategory_NewsArticle");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsCategory", "NewsCategory")
                        .WithMany("NewsArticleCategory")
                        .HasForeignKey("NewsCategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_NewsArticleCategory_NewsCategory");

                    b.Navigation("NewsArticle");

                    b.Navigation("NewsCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticleContent", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "Author")
                        .WithMany("NewsArticleContentAuthor")
                        .HasForeignKey("AuthorId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_NewsArticleContent_Author");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.AzureBlob", "AzureBlob")
                        .WithMany("NewsArticleContent")
                        .HasForeignKey("AzureBlobId")
                        .HasConstraintName("FK_NewsArticleContent_AzureBlob");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.Locale", "Locale")
                        .WithMany("NewsArticleContent")
                        .HasForeignKey("LocaleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_NewsArticleContent_Locale");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsArticle", "NewsArticle")
                        .WithMany("NewsArticleContent")
                        .HasForeignKey("NewsArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_NewsArticleContent_NewsArticle");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "Reviewer")
                        .WithMany("NewsArticleContentReviewer")
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_NewsArticleContent_Riviewer");

                    b.Navigation("Author");

                    b.Navigation("AzureBlob");

                    b.Navigation("Locale");

                    b.Navigation("NewsArticle");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsCategory", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsCategory", "ParentCategory")
                        .WithMany("ChildCategory")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("FK_NewsCategory_NewsCategory");

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Newsletter", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Locale", "Locale")
                        .WithMany("Newsletter")
                        .HasForeignKey("LocaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Newsletter_Locale");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsletterSubscription", "NewsletterSubscription")
                        .WithMany("Newsletter")
                        .HasForeignKey("NewsletterSubscriptionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_Newsletter_NewsletterSubscription");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                        .WithMany("Newsletter")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Newsletter_User");

                    b.Navigation("Locale");

                    b.Navigation("NewsletterSubscription");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsletterNewsArticleContent", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsArticleContent", "NewsArticleContent")
                        .WithMany("NewsletterNewsArticleContent")
                        .HasForeignKey("NewsArticleContentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_NewsletterNewsArticleContent_NewsArticleContent");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.Newsletter", "Newsletter")
                        .WithMany("NewsletterNewsArticleContent")
                        .HasForeignKey("NewsletterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_NewsletterNewsArticleContent_Newsletter");

                    b.Navigation("NewsArticleContent");

                    b.Navigation("Newsletter");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsletterSubscription", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                        .WithMany("NewsletterSubscription")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_NewsletterSubscription_User");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.User", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Locale", "Locale")
                        .WithMany("User")
                        .HasForeignKey("LocaleId")
                        .HasConstraintName("FK_User_Locale");

                    b.Navigation("Locale");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.UserClaim", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.Claim", "Claim")
                        .WithMany("UserClaim")
                        .HasForeignKey("ClaimId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserClaim_Claim");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                        .WithMany("UserClaim")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserClaim_User");

                    b.Navigation("Claim");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.UserNewsCategory", b =>
                {
                    b.HasOne("PharmaLex.SmartDecisions.Entities.NewsCategory", "NewsCategory")
                        .WithMany("UserNewsCategory")
                        .HasForeignKey("NewsCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserNewsCategory_NewsCategory");

                    b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                        .WithMany("UserNewsCategory")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserNewsCategory_User");

                    b.Navigation("NewsCategory");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.AzureBlob", b =>
                {
                    b.Navigation("NewsArticleContent");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Claim", b =>
                {
                    b.Navigation("UserClaim");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Company", b =>
                {
                    b.Navigation("CompanyContentType");

                    b.Navigation("CompanyNewsCategory");

                    b.Navigation("CompanyUser");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentItem", b =>
                {
                    b.Navigation("FieldValue");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentType", b =>
                {
                    b.Navigation("CompanyContentType");

                    b.Navigation("ContentItem");

                    b.Navigation("ContentTypeDisplay");

                    b.Navigation("Field");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Field", b =>
                {
                    b.Navigation("FieldValue");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Locale", b =>
                {
                    b.Navigation("MultilingualResource");

                    b.Navigation("NewsArticleContent");

                    b.Navigation("Newsletter");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticle", b =>
                {
                    b.Navigation("NewsArticleCategory");

                    b.Navigation("NewsArticleContent");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsArticleContent", b =>
                {
                    b.Navigation("NewsletterNewsArticleContent");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsCategory", b =>
                {
                    b.Navigation("ChildCategory");

                    b.Navigation("CompanyNewsCategory");

                    b.Navigation("NewsArticleCategory");

                    b.Navigation("UserNewsCategory");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Newsletter", b =>
                {
                    b.Navigation("NewsletterNewsArticleContent");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsletterSubscription", b =>
                {
                    b.Navigation("Newsletter");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.NewsSource", b =>
                {
                    b.Navigation("NewsArticle");
                });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.User", b =>
                {
                    b.Navigation("CompanyUser");

                    b.Navigation("NewsArticleContentAuthor");

                    b.Navigation("NewsArticleContentReviewer");

                    b.Navigation("Newsletter");

                    b.Navigation("NewsletterSubscription");

                    b.Navigation("UserClaim");

                    b.Navigation("UserNewsCategory");
                });
#pragma warning restore 612, 618
        }
    }
}
