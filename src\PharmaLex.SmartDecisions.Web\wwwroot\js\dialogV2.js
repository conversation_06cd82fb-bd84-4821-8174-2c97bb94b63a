﻿plx.modal = {
    surface: null,
    container: null,
    opened: false,
    rendered: false,
    init: function (config) {

        plx.modal.render(config);
    },
    render: function (config) {
        if (plx.modal.rendered === false) {

            let maskElement = plx.createElement('div', 'modal-mask', 'modal-mask hidden js-generated');
            let wrapperElement = plx.createElement('div', 'modal-wrapper', 'modal-wrapper');
            let containerElement = plx.createElement('div', config.containerClass ? `${config.containerClass} modal-container` : 'modal-container', 'modal-container');

            let headerElement = plx.createElement('div', 'modal-header', 'modal-header');
            let headerTextElement = plx.createElement('h3', 'modal-header-text', 'modal-header-text', config.headerText || '');
            let closeIconElement = plx.createElement('i', 'dialog-closer', 'm-icon', 'close');
            headerElement.appendChild(headerTextElement);
            headerElement.appendChild(closeIconElement);

            let bodyElement = plx.createElement('div', 'modal-body', 'modal-body');

            containerElement.appendChild(headerElement);
            containerElement.appendChild(bodyElement);

            if (config.showControls) {
                let footerElement = plx.createElement('div', 'modal-footer', 'modal-footer buttons');
                let cancelButtonElement = plx.createElement('button', null, 'secondary', 'Cancel');
                let okButtonElement = plx.createElement('button', null, null, 'OK');
                okButtonElement.addEventListener('click', (e) => {
                    e.preventDefault();
                    config.okAction();
                });

                footerElement.appendChild(cancelButtonElement);
                footerElement.appendChild(okButtonElement);


                containerElement.appendChild(footerElement);
            }

            wrapperElement.appendChild(containerElement);
            maskElement.appendChild(wrapperElement);
            document.body.appendChild(maskElement);

            closeIconElement.addEventListener('click', (e) => {
                plx.modal.close();
            });

            plx.modal.setDomObjects();

            plx.modal.rendered = true;

            if (config.openAfterInit) {
                plx.modal.open(config.width, config.height);
            }
        }
    },
    open: function (width, height) {
        if (plx.modal.opened === false) {
            plx.modal.mask.classList.remove('hidden');

            if (width) {
                plx.modal.wrapper.style.width = width;
            }
            if (height) {
                plx.modal.wrapper.style.height = height;
            }

            document.addEventListener('keypress', plx.modal.keyPressed);
            plx.modal.opened = true;
        }
    },
    close: function () {
        if (plx.modal.opened === true) {
            plx.modal.mask.classList.add('hidden');
            document.removeEventListener('keypress', plx.modal.keyPressed);
            plx.modal.opened = false;
        }
    },
    keyPressed: function (e) {
        if (e.key === 'Escape') {
            plx.modal.close();
        }
    },
    html: function (html) {
        if (typeof html === 'string') {
            plx.modal.body.innerHTML = html;
        } else {
            return plx.modal.body.innerHTML;
        }
    },
    load: function (url, width, height, loader, callback) {
        if (loader) {
            plx.modal.showLoader(loader);
            plx.modal.open(width, height);
        }
        fetch(url, {
            method: 'GET',
            credentials: 'same-origin'
        }).then(response => response.text()).then(response => {
            plx.modal.html(response);
            plx.modal.open(width, height);
            if (typeof callback === 'function') {
                callback();
            }
        });
    },
    showLoader: function (loader) {
        plx.modal.body.innerHTML = '<img src="./images/loaders/double-ring.gif" />';
    },
    body: function () {
        return document.querySelector('modal-body');
    },
    setDomObjects: function () {
        plx.modal.mask = document.querySelector('#modal-mask');
        plx.modal.wrapper = document.querySelector('#modal-wrapper');
        plx.modal.container = document.querySelector('#modal-container');
        plx.modal.body = document.querySelector('#modal-body');
    }

}
