﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.NewsletterApp
{
    public class NewsArticleCategoryModel
    {
        public int NewsCategoryId { get; set; }

        public int NewsArticleId { get; set; }
    }

    public class NewsArticleCategoryMappingProfile : Profile
    {
        public NewsArticleCategoryMappingProfile()
        {
            this.CreateMap<NewsArticleCategory, NewsArticleCategoryModel>();
        }
    }
}
