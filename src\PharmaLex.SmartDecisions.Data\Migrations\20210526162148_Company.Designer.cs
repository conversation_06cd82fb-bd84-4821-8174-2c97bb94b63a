﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    [DbContext(typeof(SmartDecisionsContext))]
    [Migration("20210526162148_Company")]
    partial class Company
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .UseIdentityColumns()
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.2");

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ClaimAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<string>("ClaimType")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar(32)");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .HasMaxLength(1024)
                    .HasColumnType("nvarchar(1024)");

                b.HasKey("AuditId");

                b.ToTable("Claim_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PrimaryContactAddress")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PrimaryContactEmail")
                    .HasMaxLength(128)
                    .HasColumnType("nvarchar(128)");

                b.Property<string>("PrimaryContactName")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PrimaryContactPhone")
                    .HasMaxLength(128)
                    .HasColumnType("nvarchar(128)");

                b.HasKey("AuditId");

                b.ToTable("Company_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyContentTypeAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int>("CompanyId")
                    .HasColumnType("int");

                b.Property<int>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.HasKey("AuditId");

                b.ToTable("CompanyContentType_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.CompanyUserAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<bool?>("Active")
                    .HasColumnType("bit");

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int?>("CompanyId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("UserId")
                    .HasColumnType("int");

                b.HasKey("AuditId");

                b.ToTable("CompanyUser_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ContentItemAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int?>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("Owner")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime?>("VerifiedDate")
                    .HasColumnType("datetime");

                b.HasKey("AuditId");

                b.ToTable("ContentItem_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ContentTypeAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<bool?>("AutoManageName")
                    .HasColumnType("bit");

                b.Property<int?>("ContentTypeCategoryId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("Owner")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PluralName")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("ShortName")
                    .HasMaxLength(31)
                    .HasColumnType("nvarchar(31)");

                b.Property<bool?>("System")
                    .HasColumnType("bit");

                b.HasKey("AuditId");

                b.ToTable("ContentType_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.ContentTypeDisplayAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int?>("ContentTypeDisplayTypeId")
                    .HasColumnType("int");

                b.Property<int?>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("Json")
                    .HasColumnType("nvarchar(max)");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.HasKey("AuditId");

                b.ToTable("ContentTypeDisplay_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.FieldAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int?>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Description")
                    .HasMaxLength(1024)
                    .HasColumnType("nvarchar(1024)");

                b.Property<int?>("FieldTypeId")
                    .HasColumnType("int");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Length")
                    .HasColumnType("int");

                b.Property<string>("Name")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<int?>("RelatedContentTypeId")
                    .HasColumnType("int");

                b.Property<bool?>("Required")
                    .HasColumnType("bit");

                b.Property<bool?>("System")
                    .HasColumnType("bit");

                b.Property<bool?>("Unique")
                    .HasColumnType("bit");

                b.HasKey("AuditId");

                b.ToTable("Field_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.FieldValueAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int?>("ContentItemId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("FieldId")
                    .HasColumnType("int");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("Value")
                    .HasColumnType("nvarchar(max)");

                b.HasKey("AuditId");

                b.ToTable("FieldValue_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.UserAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Email")
                    .HasMaxLength(512)
                    .HasColumnType("nvarchar(512)");

                b.Property<string>("FamilyName")
                    .HasMaxLength(1024)
                    .HasColumnType("nvarchar(1024)");

                b.Property<string>("GivenName")
                    .HasMaxLength(1024)
                    .HasColumnType("nvarchar(1024)");

                b.Property<int?>("Id")
                    .HasColumnType("int");

                b.Property<DateTime?>("LastLoginDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.HasKey("AuditId");

                b.ToTable("User_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Audit.UserClaimAudit", b =>
            {
                b.Property<int>("AuditId")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("AuditAction")
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnType("char(1)")
                    .IsFixedLength(true);

                b.Property<int?>("ClaimId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("UserId")
                    .HasColumnType("int");

                b.HasKey("AuditId");

                b.ToTable("UserClaim_Audit", "Audit");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Claim", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("ClaimType")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar(32)");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(1024)
                    .HasColumnType("nvarchar(1024)");

                b.HasKey("Id");

                b.ToTable("Claim");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Company", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("CreatedBy")
                    .HasColumnType("nvarchar(max)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime2");

                b.Property<string>("LastUpdatedBy")
                    .HasColumnType("nvarchar(max)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime2");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PrimaryContactAddress")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PrimaryContactEmail")
                    .HasMaxLength(128)
                    .HasColumnType("nvarchar(128)");

                b.Property<string>("PrimaryContactName")
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PrimaryContactPhone")
                    .HasMaxLength(128)
                    .HasColumnType("nvarchar(128)");

                b.HasKey("Id");

                b.HasIndex("Name")
                    .IsUnique()
                    .HasDatabaseName("UC_Company_Name");

                b.ToTable("Company");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyContentType", b =>
            {
                b.Property<int>("CompanyId")
                    .HasColumnType("int");

                b.Property<int>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.HasKey("CompanyId", "ContentTypeId");

                b.HasIndex("ContentTypeId");

                b.ToTable("CompanyContentType");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyUser", b =>
            {
                b.Property<int>("CompanyId")
                    .HasColumnType("int");

                b.Property<int>("UserId")
                    .HasColumnType("int");

                b.Property<bool>("Active")
                    .HasColumnType("bit");

                b.Property<string>("CreatedBy")
                    .HasColumnType("nvarchar(max)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime2");

                b.Property<string>("LastUpdatedBy")
                    .HasColumnType("nvarchar(max)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime2");

                b.HasKey("CompanyId", "UserId");

                b.HasIndex("UserId")
                    .IsUnique()
                    .HasDatabaseName("UC_CompanyUser_UserId");

                b.ToTable("CompanyUser");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentItem", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<int>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("Owner")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("VerifiedDate")
                    .HasColumnType("datetime");

                b.HasKey("Id");

                b.HasIndex(new[] { "ContentTypeId", "Name" }, "UC_ContentItem_ContentTypeId_Name")
                    .IsUnique();

                b.ToTable("ContentItem");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentType", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<bool>("AutoManageName")
                    .HasColumnType("bit");

                b.Property<int>("ContentTypeCategoryId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("Owner")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("PluralName")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("ShortName")
                    .IsRequired()
                    .HasMaxLength(31)
                    .HasColumnType("nvarchar(31)");

                b.Property<bool>("System")
                    .HasColumnType("bit");

                b.HasKey("Id");

                b.HasIndex(new[] { "Name" }, "UC_ContentType_Name")
                    .IsUnique();

                b.ToTable("ContentType");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentTypeDisplay", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<int>("ContentTypeDisplayTypeId")
                    .HasColumnType("int");

                b.Property<int>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Json")
                    .IsRequired()
                    .HasColumnType("nvarchar(max)");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.HasKey("Id");

                b.HasIndex("ContentTypeId");

                b.ToTable("ContentTypeDisplay");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Field", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<int>("ContentTypeId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Description")
                    .HasMaxLength(1024)
                    .HasColumnType("nvarchar(1024)");

                b.Property<int>("FieldTypeId")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<int?>("Length")
                    .HasColumnType("int");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<int?>("RelatedContentTypeId")
                    .HasColumnType("int");

                b.Property<bool>("Required")
                    .HasColumnType("bit");

                b.Property<bool>("System")
                    .HasColumnType("bit");

                b.Property<bool>("Unique")
                    .HasColumnType("bit");

                b.HasKey("Id");

                b.HasIndex(new[] { "ContentTypeId", "Name" }, "UC_Field_ContentTypeId_Name")
                    .IsUnique();

                b.ToTable("Field");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.FieldValue", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<int>("ContentItemId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<int>("FieldId")
                    .HasColumnType("int");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Name")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("Value")
                    .IsRequired()
                    .HasColumnType("nvarchar(max)");

                b.HasKey("Id");

                b.HasIndex("ContentItemId");

                b.HasIndex("FieldId");

                b.ToTable("FieldValue");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.User", b =>
            {
                b.Property<int>("Id")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("int")
                    .UseIdentityColumn();

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("Email")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<string>("FamilyName")
                    .HasMaxLength(512)
                    .HasColumnType("nvarchar(512)");

                b.Property<string>("GivenName")
                    .HasMaxLength(512)
                    .HasColumnType("nvarchar(512)");

                b.Property<DateTime?>("LastLoginDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.HasKey("Id");

                b.ToTable("User");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.UserClaim", b =>
            {
                b.Property<int>("UserId")
                    .HasColumnType("int");

                b.Property<int>("ClaimId")
                    .HasColumnType("int");

                b.Property<string>("CreatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("CreatedDate")
                    .HasColumnType("datetime");

                b.Property<string>("LastUpdatedBy")
                    .IsRequired()
                    .HasMaxLength(256)
                    .HasColumnType("nvarchar(256)");

                b.Property<DateTime>("LastUpdatedDate")
                    .HasColumnType("datetime");

                b.HasKey("UserId", "ClaimId");

                b.HasIndex("ClaimId");

                b.ToTable("UserClaim");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyContentType", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.Company", "Company")
                    .WithMany("CompanyContentType")
                    .HasForeignKey("CompanyId")
                    .HasConstraintName("FK_CompanyContentType_Company")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                    .WithMany("CompanyContentType")
                    .HasForeignKey("ContentTypeId")
                    .HasConstraintName("FK_CompanyContentType_ContentType")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.Navigation("Company");

                b.Navigation("ContentType");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.CompanyUser", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.Company", "Company")
                    .WithMany("CompanyUser")
                    .HasForeignKey("CompanyId")
                    .HasConstraintName("FK_CompanyUser_Company")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                    .WithOne("CompanyUser")
                    .HasForeignKey("PharmaLex.SmartDecisions.Entities.CompanyUser", "UserId")
                    .HasConstraintName("FK_CompanyUser_User")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.Navigation("Company");

                b.Navigation("User");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentItem", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                    .WithMany("ContentItem")
                    .HasForeignKey("ContentTypeId")
                    .HasConstraintName("FK_ContentItem_ContentType")
                    .IsRequired();

                b.Navigation("ContentType");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentTypeDisplay", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                    .WithMany("ContentTypeDisplay")
                    .HasForeignKey("ContentTypeId")
                    .HasConstraintName("FK_ContentTypeDisplay_ContentType")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.Navigation("ContentType");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Field", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.ContentType", "ContentType")
                    .WithMany("Field")
                    .HasForeignKey("ContentTypeId")
                    .HasConstraintName("FK_Field_ContentType")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.Navigation("ContentType");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.FieldValue", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.ContentItem", "ContentItem")
                    .WithMany("FieldValue")
                    .HasForeignKey("ContentItemId")
                    .HasConstraintName("FK_FieldValue_ContentItem")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.HasOne("PharmaLex.SmartDecisions.Entities.Field", "Field")
                    .WithMany("FieldValue")
                    .HasForeignKey("FieldId")
                    .HasConstraintName("FK_FieldValue_Field")
                    .IsRequired();

                b.Navigation("ContentItem");

                b.Navigation("Field");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.UserClaim", b =>
            {
                b.HasOne("PharmaLex.SmartDecisions.Entities.Claim", "Claim")
                    .WithMany("UserClaim")
                    .HasForeignKey("ClaimId")
                    .HasConstraintName("FK_UserClaim_Claim")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.HasOne("PharmaLex.SmartDecisions.Entities.User", "User")
                    .WithMany("UserClaim")
                    .HasForeignKey("UserId")
                    .HasConstraintName("FK_UserClaim_User")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.Navigation("Claim");

                b.Navigation("User");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Claim", b =>
            {
                b.Navigation("UserClaim");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Company", b =>
            {
                b.Navigation("CompanyContentType");

                b.Navigation("CompanyUser");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentItem", b =>
            {
                b.Navigation("FieldValue");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.ContentType", b =>
            {
                b.Navigation("CompanyContentType");

                b.Navigation("ContentItem");

                b.Navigation("ContentTypeDisplay");

                b.Navigation("Field");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.Field", b =>
            {
                b.Navigation("FieldValue");
            });

            modelBuilder.Entity("PharmaLex.SmartDecisions.Entities.User", b =>
            {
                b.Navigation("CompanyUser");

                b.Navigation("UserClaim");
            });
#pragma warning restore 612, 618
        }
    }
}
