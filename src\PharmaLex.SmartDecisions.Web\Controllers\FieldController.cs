﻿using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "SystemAdmin")]
    public class FieldController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;

        public FieldController(IDistributedCacheServiceFactory cache, IMapper mapper)
        {
            this.cache = cache;
            this.mapper = mapper;
        }

        [HttpGet("/manage/field/new/{contentTypeId}")]
        public async Task<IActionResult> New(int contentTypeId)
        {
            var ctc = cache.CreateEntity<ContentType>();
            var ct = await ctc.FirstOrDefaultAsync(x => x.Id == contentTypeId);
            return View("EditField", new FieldModel
            {
                ContentTypeId = contentTypeId,
                ContentTypeName = ct.Name
            });
        }

        [HttpPost("/manage/field/new/{contentTypeId}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New([Bind("Name,ContentTypeId,ContentTypeName,FieldTypeId,FieldType,Description,Required,Unique,Length,System,RelatedContentTypeId,Type,MultiSelect")] FieldModel fm, bool add)
        {
            if (this.ModelState.IsValid)
            {
                var fc = cache.CreateTrackedEntity<Field>();
                Field f = mapper.Map<Field>(fm);
                fc.Add(f);
                await fc.SaveChangesAsync();
                return add ? Redirect($"/manage/field/new/{fm.ContentTypeId}") : Redirect($"/manage/content-type/edit/{fm.ContentTypeId}");
            }
            else
            {
                return this.View("EditField", fm);
            }
        }

        [HttpGet("/manage/field/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            var fc = cache.CreateMappedEntity<Field, FieldModel>().Configure(x => x.Include(y => y.ContentType));
            return View("EditField", await fc.FirstOrDefaultAsync(x => x.Id == id));
        }

        [HttpPost("/manage/field/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(FieldModel fm)
        {
            if (this.ModelState.IsValid)
            {
                var fc = cache.CreateTrackedEntity<Field>();
                Field f = await fc.FirstOrDefaultAsync(x => x.Id == fm.Id);
                mapper.Map(fm, f);
                await fc.SaveChangesAsync();
                return Redirect($"/manage/content-type/edit/{fm.ContentTypeId}");
            }
            else
            {
                return this.View("EditField", fm);
            }
        }

        [HttpGet("/manage/field/delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var fc = cache.CreateMappedEntity<Field, FieldModel>().Configure(x => x.Include(y => y.ContentType));
            return View("DeleteField", await fc.FirstOrDefaultAsync(x => x.Id == id));
        }

        [HttpPost("/manage/field/delete/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            var fc = cache.CreateTrackedEntity<Field>();
            Field f = await fc.FirstOrDefaultAsync(x => x.Id == id);
            if (f.System) throw new InvalidOperationException("System managed Fields cannot be deleted");
            fc.Remove(f);
            await fc.SaveChangesAsync();
            return Redirect($"/manage/content-type/edit/{f.ContentTypeId}");
        }
    }
}