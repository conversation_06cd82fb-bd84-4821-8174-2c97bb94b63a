﻿plx.dialog = {
    surface: null,
    container: null,
    opened: false,
    rendered: false,
    init: function (containerClass) {
        plx.dialog.render(containerClass);
        plx.dialog.surface = document.getElementById('dialog-surface');
        plx.dialog.surface.style.display = 'none';
        plx.dialog.container = document.getElementById('dialog');
        plx.dialog.surface.addEventListener('click', plx.dialog.close);
        document.getElementById('dialog-closer').addEventListener('click', plx.dialog.close);
    },
    render: function (containerClass) {
        if (plx.dialog.rendered === false) {
            document.body.appendChild(plx.createElement('div', 'dialog-surface', 'dialog-surface'));
            let dc = containerClass ? `dialog-container ${containerClass}` : 'dialog-container';
            let d = plx.createElement('dialog', 'dialog', dc);
            d.appendChild(plx.createElement('i', 'dialog-closer', 'icon-cancel-circled dialog-closer'));
            d.appendChild(plx.createElement('div', 'dialog-content', 'dialog-content'));
            document.body.appendChild(d);
            plx.dialog.rendered = true;
        }
    },
    open: function (w, h) {
        if (plx.dialog.opened === false) {
            plx.dialog.surface.style.display = 'block';
            plx.dialog.container.style.display = 'block';
            if (w) {
                plx.dialog.container.style.width = w;
            }
            if (h) {
                plx.dialog.container.style.height = h;
            }
            document.addEventListener('keypress', plx.dialog.keyPressed);
            plx.dialog.opened = true;
        }
    },
    close: function () {
        if (plx.dialog.opened === true) {
            plx.dialog.surface.style.display = 'none';
            plx.dialog.container.removeAttribute('style');
            plx.dialog.container.style.display = 'none';
            document.removeEventListener('keypress', plx.dialog.keyPressed);
            plx.dialog.opened = false;
        }
    },
    keyPressed: function (e) {
        if (e.key === 'Escape') {
            plx.dialog.close();
        }
    },
    html: function (html) {
        if (typeof html === 'string') {
            document.getElementById('dialog-content').innerHTML = html;
        } else {
            return document.getElementById('dialog-content').innerHTML;
        }
    },
    load: function (url, w, h, loader, callback) {
        if (loader) {
            plx.dialog.showLoader(loader);
            plx.dialog.open(w, h);
        }
        fetch(url, {
            method: 'GET',
            credentials: 'same-origin'
        }).then(r => r.text()).then(r => {
            plx.dialog.html(r);
            plx.dialog.open(w, h);
            if (typeof callback === 'function') callback();
        });
    },
    showLoader: function (loader) {
        var html = `<div class="dialog-loader"><img src="${loader}" /></div>`;
        document.getElementById('dialog-content').innerHTML = html;
    }
}
