﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class Newsletter : EntityBase
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public DateTime CreatedDateUtc { get; set; }
        public int LocaleId { get; set; }
        public string UniqueKey { get; set; }
        public int? NewsletterSubscriptionId { get; set; }
        public bool IsMonthly { get; set; }

        public string NewsArticleContentIds { get; set; }
        public virtual Locale Locale { get; set; }
        public virtual User User { get; set; }
        public virtual NewsletterSubscription NewsletterSubscription { get; set; }
    }
}
