﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>
  
  <ItemGroup>
    <Content Include="TextTemplates/**/*.pp">
      <Pack>true</Pack>
      <PackagePath>contentFiles\any\any\</PackagePath>
      <CopyToOutputDirectory>true</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.258" />
    <PackageReference Include="System.Runtime.Caching" Version="8.0.1" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

</Project>
