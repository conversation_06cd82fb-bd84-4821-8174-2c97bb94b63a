const path = require("path");
const TerserPlugin = require("terser-webpack-plugin");
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { VueLoaderPlugin } = require('vue-loader');
const RemoveEmptyScriptsPlugin = require('webpack-remove-empty-scripts');
const ESLintPlugin = require('eslint-webpack-plugin');

module.exports = {
    entry: {
        smartDecisionsCss: '/src/scss/index.scss',
        mapsCss: '/src/scss/new/_maps.scss',
        filteredTablesCss: '/src/scss/new/_filtered-tables.scss'
    },
    output: {
        path: path.resolve(__dirname, 'wwwroot/dist'),
        publicPath: '/dist/',
        assetModuleFilename: 'assets/[name][ext][query]',
        filename: 'js/[name].js',
        clean: true,
    },
    plugins: [
        new RemoveEmptyScriptsPlugin(),
        new MiniCssExtractPlugin({
            filename: "css/[name].css",
        }),
        new VueLoaderPlugin(),
        new ESLintPlugin()
    ],
    module: {
        rules: [
            {
                test: /\.js$/, exclude: /(node_modules)/, use: ['babel-loader']
            },
            {
                test: /\.vue$/,
                use: ['vue-loader']
            },
            {
                test: /\.(?:ico|gif|png|jpg|jpeg)$/i,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        maxSize: 5 * 1024
                    }
                }
            },
            {
                test: /\.(woff(2)?|eot|ttf|otf|svg|)$/,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        maxSize: 5 * 1024
                    }
                }
            },
            {
                test: /\.(scss|css)$/,
                use: [
                    "vue-style-loader",
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true,
                        },
                    },
                    {
                        loader: "postcss-loader",
                        options: {
                            postcssOptions: {
                                config: path.resolve(__dirname, 'postcss.config.js'),
                            },
                        },
                    },
                    'sass-loader',
                ],
            },
        ],
    },
    optimization: {
        minimize: true,
        minimizer: [new TerserPlugin({ extractComments: false })],
        splitChunks: {
            chunks: 'all',
            name:'vendors' 
        },
    },
    stats: 'minimal',
    watch: true
};
