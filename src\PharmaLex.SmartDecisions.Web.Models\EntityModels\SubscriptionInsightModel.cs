﻿using AutoMapper;
using System;
using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class SubscriptionInsightModel : EntityModel, IModel
    {
        public string Company { get; set; }
        public string User { get; set; }
        public string Email { get; set; }

        public ICollection<string> Products { get; set; }
        public ICollection<string> Themes { get; set; }
        public ICollection<string> Languages { get; set; }
        public ICollection<string> GeographicalScope { get; set; }
        public ICollection<string> DaysToReceive { get; set; }
        public string SortThemes { get => string.Join(", ", Themes); }
        public string SortProducts { get => string.Join(", ", Products); }
        public string SortLanguages { get => string.Join(", ", Languages); }
        public string SortGeographicalScope { get => string.Join(", ", GeographicalScope); }
        public string SortDaysToReceive { get => string.Join(", ", DaysToReceive); }
        public bool IsMonthlyMiscNewsletter { get; set; }
        public bool IsInfoflash { get; set; }
        public DateTime? LastLoginDate { get; set; }

        public SubscriptionInsightModel()
        {
            Products = new List<string>();
            Themes = new List<string>();
            GeographicalScope = new List<string>();
            DaysToReceive = new List<string>();
            Languages = new List<string>();
        }
    }

    public class SubscriptionInsightModelMappingProfile : Profile
    {
        public SubscriptionInsightModelMappingProfile()
        {
        }
    }
}
