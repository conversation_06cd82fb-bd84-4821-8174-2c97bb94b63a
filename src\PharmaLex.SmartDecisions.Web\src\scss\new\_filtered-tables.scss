﻿/*Legacy support for filtered table*/
@import '_variables';
@import '_mixins';

tr.selectable {
    &:hover {
        td {
            background: $blue-ultra-light;
            cursor: pointer;
        }
    }
}

.table-header {
    display: flex;
    flex: 1 1 auto;
    position: relative;
    color: $grey-1;

    .sorter {
        flex-basis: 1.25rem;
        width: 1.25rem;
        position: relative;
        margin-right: 0.5rem;

        &:before,
        &:after {
            content: '';
            display: block;
            position: absolute;
            width: 5px;
            height: 5px;
            right: 0;
            top: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
        }

        &:before {
            border-bottom-width: 6px;
            border-bottom-style: solid;
            border-bottom-color: $grey-1;
        }

        &:after {
            top: 8px;
            border-top-width: 6px;
            border-top-style: solid;
            border-top-color: $grey-1;
        }

        &.active.sorting_asc {
            &:before {
                border-bottom-color: $blue-dark;
            }
        }

        &.active.sorting_desc {
            &:after {
                border-top-color: $blue-dark;
            }
        }

        &.sorting_desc {
            &:before {
                transform: rotate(0);
            }
        }
    }

    .header-text {
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 0.5rem;
    }

    i {
        flex-shrink: 0;
    }
}

.no-records-container {
    display: flex;
    align-items: center;
    padding: 1rem;
    font-size: .85rem;
}

.pager {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex: 1 1 auto;
    margin: .5rem 0;
    font-size: .85rem;
    background: white;
    border-top: 2px solid $grey-4;

    & > * {
        padding: .5rem 1rem;
    }

    .page-size {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 0 0 14rem;

        span {
            text-align: right;
            padding-right: .5rem;
        }

        select {
            padding: .5rem;
            max-width: 75px;
            background: $white;
            border-color: $grey-2;
        }
    }

    .pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        max-width: 50%;

        &-arrows-start {
            display: inline-block;
        }

        &-arrows-end {
            display: inline-block;
            margin-left: auto;
        }

        &-pages-container {
            margin-bottom: 0.25rem;
        }

        a {
            display: inline-block;
            margin-left: .25rem;
            margin-right: .25rem;

            &:last-of-type {
                margin-right: 0;
            }

            &.first, &.previous, &.next, &.last {
                color: $text;
            }

            &.current {
                font-weight: 700;
                font-size: 1rem;
                margin: 0.5rem;
            }

            &.disabled, &[disabled] {
                opacity: 0.5;
                color: $grey-1;
                pointer-events: none;
            }
        }
    }
}

th .centred-cell {
    text-align: center;
    display: flex;
    justify-content: center;
}

td.centred-cell {
    text-align: center;
}

th .left-cell {
    text-align: left;
    display: flex;
    justify-content: flex-start;
}

td.left-cell {
    text-align: left;
}

th .right-cell {
    text-align: right;
    display: flex;
    justify-content: flex-end;
}

td.right-cell {
    text-align: right;
}


.table-filter {
    flex-basis: 1rem;

    &.search {
        padding-right: .25rem;

        &:before {
            @include base-table-icon;
            content: '\e8b6';
            color: $grey-1;
        }

        &.active {
            &:before {
                color: $brand;
            }
        }
    }
}

table td {
    word-break: break-word;
}

div {
    font-size: .85rem;
}

.table-filter-items {
    position: absolute;
    left: 0;
    top: 1rem;
    z-index: 9;
    width: fit-content;
    padding: .25rem;
    background: $grey-4;
    box-shadow: 0 5px 10px 0 rgba(0,0,0,0.2);
    border-radius: .75rem;
    border: 1px solid $grey-3;

    input {
        background: $white;
        padding: .5rem;
        border-color: $grey-3;

        &[type=search] {
            padding-right: 1rem;
            width: clamp(150px,200px,250px);
        }
    }

    li {
        background: $white;
        white-space: nowrap;
        padding: .5rem;
        border-radius: .5rem;
        margin-bottom: .25rem;
        transition: all .3s;
        font-weight: normal;

        &.is-active {
            background: $brand;
            color: $white;
        }

        &:hover {
            background: $grey-2;
            color: $white;
            transition: all .3s;
            cursor: pointer;
        }
    }

    .m-icon.close {
        position: absolute;
        right: .75rem;
        top: 11px;
        color: $error;
        font-size: 1.25rem !important;
        padding: .5rem;
        background: $white;
    }
}
