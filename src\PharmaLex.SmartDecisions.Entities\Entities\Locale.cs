﻿using PharmaLex.DataAccess;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class Locale : EntityBase, ILocalisableLookup
    {
        public Locale()
        {
            MultilingualResource = new HashSet<MultilingualResource>();
            NewsArticleContent = new HashSet<NewsArticleContent>();
            User = new HashSet<User>();
            Newsletter = new HashSet<Newsletter>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string LocalisedName { get; set; }
        public string IsoLanguageCode { get; set; }
        public string IsoScriptCode { get; set; }
        public string IsoCountryCode { get; set; }
        public bool Active { get; set; }
        public bool Default { get; set; }
        public string LocalisationKey { get; set; }

        public virtual ICollection<MultilingualResource> MultilingualResource { get; set; }
        public virtual ICollection<NewsArticleContent> NewsArticleContent { get; set; }
        public virtual ICollection<User> User { get; set; }
        public virtual ICollection<Newsletter> Newsletter { get; set; }

        [NotMapped]
        public string Display => string.IsNullOrEmpty(this.IsoScriptCode) ? $"{this.IsoLanguageCode}-{this.IsoCountryCode}" : $"{this.IsoLanguageCode}-{this.IsoScriptCode}-{this.IsoCountryCode}";
    }
}
