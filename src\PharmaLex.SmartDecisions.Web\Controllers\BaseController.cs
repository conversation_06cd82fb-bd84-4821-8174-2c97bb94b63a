﻿using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.SmartDecisions.Web.Models;
using PharmaLex.SmartDecisions.Web.Helpers.Extensions;
using PharmaLex.SmartDecisions.Entities.Enums;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class BaseController : Controller
    {
        protected int CurrentUserId => this.User.GetClaimValue<int>("plx:userid");

        protected bool IsSystemAdmin => this.User.HasClaim(x => x.Type == "admin:SystemAdmin" || x.Type == "admin:SuperAdmin");

        protected void AddConfirmationNotification(string html, NotificationOptions options = null)
        {
            TempData.Set("UserNotification", new UserNotificationModel(html, type: UserNotificationType.Confirm, options: options));
        }

        protected void AddNotification(string html, UserNotificationType type, int duration = 2500, UserNotificationPosition position = UserNotificationPosition.TopCenter, NotificationOptions options = null)
        {
            TempData.Set("UserNotification", new UserNotificationModel(html, type: type, duration: duration, position: position, options: options));
        }
    }
}
