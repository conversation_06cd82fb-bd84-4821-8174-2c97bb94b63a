﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class TopicModel : SystemContentTypeModel, IModel
    {
        public string ShortName { get; set; }
    }

    public class TopicModelMappingProfile : Profile
    {
        public TopicModelMappingProfile()
        {
            this.CreateMap<ContentType, TopicModel>();
        }
    }
}
