﻿DECLARE @parentId nvarchar(64)
DECLARE @createdBy nvarchar(100)
SET @createdBy = '<EMAIL>'
INSERT INTO [dbo].[NewsCategory] SELECT N'Newsletter article language', null, 5, 78, N'newsletter-article-language', GETDATE(), @createdBy, GETDATE(), @createdBy
SELECT @parentId = SCOPE_IDENTITY()
INSERT INTO [dbo].[NewsCategory] SELECT N'English', @parentId, 5, 79, N'en', GETDATE(), @createdBy, GETDATE(), @createdBy
INSERT INTO [dbo].[NewsCategory] SELECT N'French', @parentId, 5, 80, N'fr', GETDATE(), @createdBy, GETDATE(), @createdBy

insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.newsletter-article-language', 'Newsletter articles language', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'(news).subscribe.newsletter-article-language', 'Langue des articles de la Newsletter', getdate(), @createdBy, getdate(), @createdBy

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].newsletter-article-language', 'Newsletter Articles Language', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'[NewsCategory].newsletter-article-language', 'Langue des articles de la Newsletter', getdate(), @createdBy, getdate(), @createdBy

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].en', 'English', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'[NewsCategory].en', 'Anglais', getdate(), @createdBy, getdate(), @createdBy

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].fr', 'French', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'[NewsCategory].fr', 'Français', getdate(), @createdBy, getdate(), @createdBy

insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.select-one-article-language', 'Please enter at least one article language', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'(news).subscribe.select-one-article-language', 'Veuillez saisir au moins une langue d''article', getdate(), @createdBy, getdate(), @createdBy

insert into [dbo].[MultilingualResource] select 1, 'drag-drop-article-language-help', 'Articles in the newsletter will appear in the selected languages. For bilingual articles, the language on top in the order below will be the primary language (i.e. only the version in that language will appear in the newsletter). You can change that order by dragging/dropping the language labels.', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'drag-drop-article-language-help', 'Dans la newsletter, les articles paraitront dans les langues sélectionnées. Pour les articles bilingues, la langue en tête dans l''ordre ci-dessous sera la langue principale (i.e.  l''article n''apparaitra que dans cette langue dans la newsletter). Vous pouvez modifier cet ordre en glissant/déposant les libellés des langues.', getdate(), @createdBy, getdate(), @createdBy

insert into [dbo].[MultilingualResource] select 1, 'select-article-language', 'Please select a newsletter article language', getdate(), @createdBy, getdate(), @createdBy
insert into [dbo].[MultilingualResource] select 2, N'select-article-language', 'Veuillez sélectionner une langue pour l''article de la newsletter', getdate(), @createdBy, getdate(), @createdBy