﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class ContentType : EntityBase
    {
        public ContentType()
        {
            ContentItem = new HashSet<ContentItem>();
            ContentTypeDisplay = new HashSet<ContentTypeDisplay>();
            CompanyContentType = new HashSet<CompanyContentType>();
            Field = new HashSet<Field>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string PluralName { get; set; }
        public string ShortName { get; set; }
        public int ContentTypeCategoryId { get; set; }
        public string Owner { get; set; }
        public bool System { get; set; }
        public bool AutoManageName { get; set; }

        public virtual ICollection<ContentItem> ContentItem { get; set; }
        public virtual ICollection<ContentTypeDisplay> ContentTypeDisplay { get; set; }
        public virtual ICollection<CompanyContentType> CompanyContentType { get; set; }
        public virtual ICollection<Field> Field { get; set; }
    }
}
