﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class NewsArticleReadModelMappingProfile : Profile
    {
        public NewsArticleReadModelMappingProfile()
        {
            this.CreateMap<NewsArticleContent, NewsArticleReadModel>()
                .ForMember(d => d.CategoryIds, s => s.MapFrom(x => x.NewsArticle.NewsArticleCategory.Select(y => y.NewsCategoryId).ToList()))
                .ForMember(d => d.ImportanceId, s => s.MapFrom(x => x.NewsArticle.ImportanceId))
                .ForMember(d => d.ContentUrl, o => o.Ignore())
                .ForMember(d => d.Content, o => o.Ignore())
                .ForMember(d => d.ContentBase64, o => o.Ignore())
                .ForMember(d => d.SourcePublicationDate, o => o.Ignore())
                .ForMember(d => d.ProductIds, o => o.Ignore())
                .ForMember(d => d.ThemeIds, o => o.Ignore())
                .ForMember(d => d.NewsSourceId, o => o.Ignore())
                .ForMember(d => d.TypeOfTextIds, o => o.Ignore())
                .ForMember(d => d.PublisherFullName, o => o.Ignore());
        }
    }
}
