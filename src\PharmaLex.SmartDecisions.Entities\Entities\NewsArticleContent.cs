﻿using PharmaLex.DataAccess;
using System;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsArticleContent : EntityBase
    {
        public int Id { get; set; }
        public int NewsArticleId { get; set; }
        public int LocaleId { get; set; }
        public string Title { get; set; }
        public int? AuthorId { get; set; }
        public int? ReviewerId { get; set; }
        public string SourceUrl { get; set; }
        public int PublishingStateId { get; set; }
        public DateTime PublishingStateDateUtc { get; set; }
        public int? AzureBlobId { get; set; }
        public string ImpactAssessmentSummary { get; set; }
        public string FriendlyUrl { get; set; }
        public DateTime? GoLiveDate { get; set; }

        public virtual Locale Locale { get; set; }
        public virtual NewsArticle NewsArticle { get; set; }
        public virtual User Author { get; set; }
        public virtual User Reviewer { get; set; }

        public virtual AzureBlob AzureBlob { get; set; }
    }
}
