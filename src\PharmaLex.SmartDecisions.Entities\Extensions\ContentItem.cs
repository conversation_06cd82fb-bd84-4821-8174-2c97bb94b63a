﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class ContentItem : IEntity
    {
        public string GetFieldValue(string field)
        {
            return this.FieldValue?.FirstOrDefault(x => x.Name.EndsWith($" - {field}"))?.Value;
        }

        public int? GetNullableIntFieldValue(string field)
        {
            return int.TryParse(this.GetFieldValue(field), out int id) ? id : null;
        }

        public IEnumerable<int> GetIntFieldValues(string field)
        {
            return this.GetFieldValue(field).Split('|', StringSplitOptions.None).Select(y => int.Parse(y));
        }
    }
}
