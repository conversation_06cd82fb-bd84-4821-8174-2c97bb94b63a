﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsCategory : EntityBase, ILocalisableLookup
    {
        public NewsCategory()
        {
            ChildCategory = new HashSet<NewsCategory>();
            NewsArticleCategory = new HashSet<NewsArticleCategory>();
            UserNewsCategory = new HashSet<UserNewsCategory>();
            CompanyNewsCategory = new HashSet<CompanyNewsCategory>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int? ParentId { get; set; }
        public int GroupId { get; set; }
        public int SortOrder { get; set; }
        public string LocalisationKey { get; set; }

        public NewsCategory ParentCategory { get; set; }
        public ICollection<NewsCategory> ChildCategory { get; set; }
        public ICollection<NewsArticleCategory> NewsArticleCategory { get; set; }
        public ICollection<UserNewsCategory> UserNewsCategory { get; set; }
        public ICollection<CompanyNewsCategory> CompanyNewsCategory { get; set; }
    }
}
