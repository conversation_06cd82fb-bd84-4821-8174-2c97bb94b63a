﻿@model CompanyModel
@{
    ViewData["Title"] = "Delete company";
}
<div class="sub-header">
    <h2>Delete <em>@Model.Name</em></h2>
    <div class="controls">
        <a class="button secondary" href="/manage/company/edit/@Model.Id">Back</a>
    </div>
</div>

<section>
    <form method="post">
        <div class="flex mb-4">
            <i class="m-icon warning-color">warning</i>
            <p class="lead m-0 p-0 pl-1"><strong>Warning:</strong> Deleting is permanent, and once deleted, the company cannot be restored.</p>
        </div>

        <p class="lead">Are you sure you want to delete company <strong><em>@Model.Name</em></strong>?</p>

        <div class="buttons">
            <a class="button secondary" href="/manage/company/edit/@Model.Id">Cancel</a>
            <button type="submit">Delete</button>
        </div>
    </form>
</section>