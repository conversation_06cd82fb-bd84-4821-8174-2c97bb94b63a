﻿using PharmaLex.SmartDecisions.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using PharmaLex.SmartDecisions.Web.Helpers.Constants;
using PharmaLex.SmartDecisions.Web.Helpers.Extensions;
using PharmaLex.SmartDecisions.Entities.Entities;
using Microsoft.AspNetCore.Localization;
using Azure.Core;
using System.Globalization;

namespace PharmaLex.SmartDecisions.Web.Helpers.Builders
{
    public static class ExpressionBuilder
    {
        private static Dictionary<string, Expression<Func<NewsletterActivity, object>>> sortExpressions =
        new()
        {
                    { TableFilterConstants.Username, x => x.User.GivenName + " " + x.User.FamilyName},
                    { TableFilterConstants.Email, x => x.User.Email },
                    { TableFilterConstants.Company, x => x.User.CompanyUser.Company.Name ?? "Not Assigned" },
                    { TableFilterConstants.Message, x => x.Response + " " + x.Reason },
                    { TableFilterConstants.ReceivedUTCDate, x => x.ReceivedUTCDate },
                    { TableFilterConstants.EventTypeSortKey, x =>  (int)x.EventType }
        };

        public static Expression<Func<NewsletterActivity, bool>>? BuildNewsletterActivity(string[] filters)
        {
            Expression<Func<NewsletterActivity, bool>>? expression = null;

            foreach (var filter in filters)
            {
                var splitFilter = filter.Split("=>");
                var filterName = splitFilter[0];
                var filterValue = splitFilter[^1];

                if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    switch (filterName.ToLower())
                    {
                        case TableFilterConstants.Username:
                            expression = expression!.AndAlso(x => x.User != null && (x.User.GivenName + " " + x.User.FamilyName).Contains(filterValue));
                            break;
                        case TableFilterConstants.Email:
                            expression = expression!.AndAlso(x => x.User != null && x.User.Email.Contains(filterValue));
                            break;
                        case TableFilterConstants.Company:
                            expression = expression!.AndAlso(x => x.User != null && x.User.CompanyUser.Company != null && x.User.CompanyUser.Company.Name.Contains(filterValue));
                            break;
                        case TableFilterConstants.ReceivedUTCDate:
                            if (DateTime.TryParse(filterValue, DateTimeFormatInfo.InvariantInfo, out DateTime searchDate))
                            {
                                expression = expression!.AndAlso(x => x.ReceivedUTCDate.Date == searchDate.Date);
                            }                          
                            break;
                        case TableFilterConstants.Message:
                            expression = expression!.AndAlso(x => x.Reason.Contains(filterValue) || x.Response.Contains(filterValue));
                            break;
                        case TableFilterConstants.EventTypeFilterKey:
                            {
                                var eventTypeIdsArray = filterValue.ToLower().Split(",");
                                var eventTypes = eventTypeIdsArray.Select(x => Enum.Parse<NewsletterActivityEventType>(x, true));
                                expression = expression!.AndAlso(x => eventTypes.Contains(x.EventType));
                                break;
                            }
                    }
                }
            }

            return expression!;
        }

        public static Func<IQueryable<NewsletterActivity>, IOrderedQueryable<NewsletterActivity>> BuildSortExpression(string? sort)
        {
            if (string.IsNullOrWhiteSpace(sort))
            {
                return sub => sub.OrderByDescending(x => x.ReceivedUTCDate);
            }

            var orderSegments = sort.Split("=>");
            var propName = orderSegments[0];
            var order = orderSegments[1];
            var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

            if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out var func))
            {
                return CompareAndOrderBy(func);
            }

            return sub => sub.OrderBy(x => x.ReceivedUTCDate);

            Func<IQueryable<NewsletterActivity>, IOrderedQueryable<NewsletterActivity>> CompareAndOrderBy<TKey>(
                Expression<Func<NewsletterActivity, TKey>> expression)
            {
                return isAsc ?
                    sub => sub.OrderBy(expression) :
                    sub => sub.OrderByDescending(expression);
            }
        }
    }
}
