﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public interface IFieldModel
    {
        public string Name { get; set; }
        int FieldTypeId { get; set; }
        string Type { get; }
        int Id { get; set; }
    }

    public class FieldModel : EntityModel, IFieldModel
    {
        [Required]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Name field.")]
        public new string Name { get; set; }
        public int ContentTypeId { get; set; }
        public string ContentTypeName { get; set; }
        public int FieldTypeId { get; set; }
        public string FieldType { get; set; }
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Description field.")]
        public string Description { get; set; }
        public bool Required { get; set; }
        public bool Unique { get; set; }
        public int? Length { get; set; }
        public bool System { get; set; }
        public int? RelatedContentTypeId { get; set; }
        public string Type { get; set; }
        public bool MultiSelect { get; set; }
    }

    public class FieldColumnModel : IFieldModel, IModel
    {
        public int Id { get; set; }
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Name field.")]
        public string Name { get; set; }
        public int SortDirection { get; set; }
        [JsonIgnore]
        public int FieldTypeId { get; set; }
        public string Type { get; set; }

        public string DataKey { get { return this.Name; } }
        public string SortKey { get { return this.DataKey; } }
    }
}
