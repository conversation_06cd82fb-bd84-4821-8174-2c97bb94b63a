﻿@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@model List<CompanyModel>
@{
    ViewData["Title"] = "Companies";
}

<div id="company-app" v-cloak>
    <div class="sub-header">
        <h2>Companies</h2>
        <div class="controls">
            <a class="button" href="/manage/company/new">Add Company</a>
        </div>
    </div>
    
        <filtered-table styling="" :items="companies" :columns="columns" :filters="filters" :link="link"></filtered-table>
   
</div>

@section Scripts {
    <script type="text/javascript">

        var pageConfig = {
            appElement: '#company-app',
            data: function () {
                return {
                    link: '/manage/company/edit/',
                    companies: @Html.Raw(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'name',
                                sortKey: 'name',
                                sortDirection: 1,
                                type: 'text',
                                header: 'Name',
                                style: 'width: 70%;'
                            },
                            {
                                dataKey: 'primaryContactName',
                                sortKey: 'primaryContactName',
                                header: 'Contact name',
                                type: 'text',
                                style: 'width: 30%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'name',
                            options: [],
                            type: 'search',
                            header: 'Search name',
                            fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'primaryContactName',
                            options: [],
                            type: 'search',
                            header: 'Search contact name',
                            fn: v => p => p.primaryContactName != null ? p.primaryContactName.toLowerCase().includes(v.toLowerCase()) : '',
                            convert: v => v
                        }
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}