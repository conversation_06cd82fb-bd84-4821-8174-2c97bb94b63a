﻿@media screen and (max-width: 1780px) {
    html, body {
        font-size: 90%;
    }
}

@media screen and (max-width: 1500px) {
    html, body {
        font-size: 82%;
    }
}

@media screen and (max-width: 1350px) {
    html, body {
        font-size: 74%;
    }
}


.core-container {
    min-width: 80vw !important;
    max-width: 100vw !important;
}

/*Drag/Drop*/
.draggable-group {
    display: flex;
    flex-direction: column;
    background-color: $grey-5;
    color: $white;
    padding: .75rem;
    border-radius: 0.75rem;
    font-size: .85rem;
    line-height: 1.25rem;
    margin-bottom: 1rem;

    &:first-of-type {
        margin-top: 1.5rem;
    }
    /*Default */
    &.category-default {
        background-color: $brand;
    }
    /*Medicines*/
    &.category-medicines {
        background-color: $medicines;
    }
    /*Biocides */
    &.category-biocides {
        background-color: $biocides !important;
    }
    /*Cosmetics */
    &.category-cosmetics {
        background-color: $cosmetics;
    }
    /*Nutrition */
    &.category-nutrition {
        background-color: $nutrition;
    }
    /*Medical devices */
    &.category-med-devices {
        background-color: $medical-devices;
    }

    h5 {
        margin-bottom: 0;
    }

    &.target-entered {
        background: $grey-3 !important;
        color: $text;
        outline: 2px $text dotted;
        transition: all .3s;
    }
}

.drag-drop-list {
    position: relative;
    /*Medicine*/
    .category-medicines {
        background-color: $medicines-25;
        color: $text;
    }
    /*Biocides*/
    .category-biocides {
        background-color: $biocides-25;
        color: $text;
    }
    /*Cosmetics*/
    .category-cosmetics {
        background-color: $cosmetics-25;
        color: $text;
    }
    /*Nutrition*/
    .category-nutrition {
        background-color: $nutrition-25;
        color: $text;
    }
    /*Devices*/
    .category-med-devices {
        background-color: $medical-devices-25;
        color: $text;
    }
}





.import-summary {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid $grey-3;
    margin-bottom: 1rem;

    i {
        font-size: 2rem;
        color: $warning;
    }

    p {
        margin: 0;
        padding-bottom: 0;
        font-size: 1rem;
        padding-left: 1rem;
    }
}

.import-errors {

    ul {
        li {
            position: relative;
            padding-left: 1.5rem;
            font-size: .85rem;

            &:before {
                position: absolute;
                top: 0;
                left: 0;
                content: '\e002';
                font-family: 'MaterialIcons';
                color: $warning;
                font-size: 1rem;
            }
        }
    }
}




/*Topics*/


.topic-column {
    padding: 0 1.5rem;

    &:first-of-type {
        padding-left: 0;
    }

    &:last-of-type {
        padding-right: 0;
    }
}

.topic-sidebar {
    border-left: 1px solid $grey-5;
}

.topic-value-group {
    margin-bottom: 2rem;


    ul {
        list-style-type: disc;

        li {
            break-inside: avoid;
            margin: .5rem 0 .75rem 1rem;
            padding-left: 0;
            list-style-type: disc;
            font-size: .85rem;
            line-height: 1.25rem;

            strong {
                display: block;
            }
        }
    }
}




/*Jim 16 nov*/


.search-option {
    display: grid;
    grid-template-columns: calc(10% - 2rem) calc(90% - 2rem);
    gap: 0 2rem;
}

.dragdropspacer {
    width: 100%;
    height: 5px;
    background-color: transparent;
}

.dragdropspacermeds {
    height: 15px;
}

.dragdropspacerhover {
    background-color: $dragdrophoverspacer;
    cursor: move;
    margin: .50rem 0 .50rem 0;
    padding: .25rem;
    width: 100%;
    height: 10px;
    border-radius: 0.75rem;
}

.draggable-group {
    margin-bottom: 0.5rem;
}

.vertical-line {
    margin: 0 1rem;
    height: 100%;
    border-left: 1px solid $grey-3;
}

.group-section-container {
    min-height: 30rem;
}

.chip {
    background-color: $grey-4;
    border-radius: 0.8rem;
    color: $black;
    display: inline-block;
    padding: 0.5rem;
    margin: 0.2rem;
    font-size: 0.85rem;
}


.hero-banner-tooltip {
    position: relative;

    & .hero-banner-tooltiptext {
        visibility: hidden;
        background-color: $grey-1;
        color: $white;
        text-align: center;
        border-radius: 0.5rem;
        padding: 0.5rem;
        transition: all 0.4s;
        opacity: 0;
        font-size: 0.85rem;
        position: absolute;
        top: -3.7rem;
        left: 0;
        width: 60rem;
        z-index: 20;
        font-weight: 200;
    }

    &:hover .hero-banner-tooltiptext {
        visibility: visible;
        opacity: 1;
    }
}

.hero-banner-textarea {
    width: 100%;
    resize: vertical;
    max-height: 15rem;
}

.loader {
    position: absolute;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: start;
    justify-content: center;
    opacity: .8;
    z-index: 10000;
    background-color: #fff;
}

.add-view-display {
    width: 23rem !important;
}

.view-topic-dialog {
    .dialog-content {
        padding-top: 0px;
        padding-bottom: 0px;
    }

    #dialog-closer {
        display: none;
    }
}

/* News Search Article*/

#news-wall {
    font-size: 0.85rem;
    padding: 50px;
}

#news-wall a {
    color: #333333;
}

#news-wall article {
    padding: 0 0 10px 0;
}

#news-wall article h1 {
    font-weight: 400;
    font-size: 24px;
    line-height: 29px;
    border-top: 1px solid #E5E5E5;
    margin: 0;
    padding: 10px 0;
}

#news-wall article section {
    padding: 15px 0;
    border-top: 1px solid #E5E5E5;
}

#news-wall article section.meta {
    display: flex;
}

#news-wall article section.meta div {
    display: flex;
    padding: 0 30px;
    overflow: hidden;
    box-sizing: border-box;
}

#news-wall article section.meta div strong {
    padding: 0 5px 0 0;
}

#news-wall article section.meta div span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#news-wall article section.meta div:first-of-type {
    padding-left: 0;
}

#news-wall article section.meta div:last-of-type {
    padding-right: 0;
}

#news-wall article section#pills {
    display: flex;
    justify-content: space-between;
    padding-bottom: 5px;
}

#news-wall article section#pills div {
    position: relative;
}

#news-wall #pills span {
    display: inline-block;
    margin: 0 5px 10px 0;
    padding: 5px 12px;
    border-radius: 12px;
    color: #FFFFFF;
}

#news-wall #pills .legend .legend-help:hover + .legend-container {
    visibility: visible;
}

#news-wall #pills .legend .legend-container {
    visibility: hidden;
}

#news-wall #pills .legend-container .legend-container-items {
    display: flex;
    flex-direction: column;
    border: 1px solid #E5E5E5;
    padding: 0.5rem;
    position: absolute;
    border-radius: 0.4rem;
    top: -1.8rem;
    left: -10.5rem;
    z-index: 10;
    background: white;
    transition: all 0.2s ease;

    & div {
        display: flex;
        align-items: center;
        font-size: 0.75rem;
    }

    & div span {
        width: 20px;
        height: 20px;
        border-radius: 0;
    }
}

#news-wall span.purple {
    background-color: #4A1671;
}

#news-wall span.gray {
    background-color: #557595;
}

#news-wall span.orange {
    background-color: #DF8043;
}

#news-wall span.red {
    background-color: #B10000;
}

#news-wall footer {
    display: flex;
    font-size: 12px;
    padding: 35px 0;
    border-top: 1px solid #E5E5E5;
}

#news-wall footer div {
    display: flex;
}

#news-wall footer span {
    display: block;
    width: 20px;
    height: 20px;
}

#news-wall footer i {
    display: block;
    line-height: 20px;
    padding: 0 13px 0 8px;
    font-style: normal;
}

@media screen and (max-width: 1200px) {

    #news-wall {
        padding: 30px;
    }

    #news-wall article section.meta {
        flex-wrap: wrap;
        padding-bottom: 5px !important;
    }

    #news-wall article section.meta div {
        margin-bottom: 10px;
        padding: 0;
        width: 50%;
    }

    #news-wall article section.meta div:nth-child(odd) {
        padding-right: 5px;
    }

    #news-wall article section.meta div:nth-child(even) {
        padding-left: 5px;
    }

    #news-wall article section.meta div.source {
        width: 100%;
    }
}

@media screen and (max-width: 768px) {

    #news-wall {
        font-size: 12px;
        padding: 20px;
    }

    #news-wall article section.meta div {
        flex-direction: column;
    }
}

@media screen and (max-width: 480px) {

    #news-wall footer {
        flex-wrap: wrap;
    }

    #news-wall footer div {
        width: 50%;
        margin-bottom: 10px;
    }
}

input[type=search] {
    background-color: white;
    border: none;
    border-radius: 0.5rem;
}

.search-download-articles {
    display: flex;
    align-content: center;
    justify-content: space-between;
    background-color: white;
    padding: 1rem;
    margin-bottom: -1.25rem;

    &-selected {
        display: flex;
        align-content: center;
        justify-content: center;
    }
}




.search-icon {
    position: relative;
    font-size: 1.25rem;
    display: flex;
    left:0.25rem;
    align-items: center;
    justify-content: center;
    color: $grey-2;
}

.search-content {
    margin-bottom: 0.75rem;
    justify-content: start;
    align-content: center;
    border-top: 1px solid $grey-4;
    padding-top: 1rem;

    &__input {
        position: relative;
        display: flex;
        align-content: center;
        justify-content: start;
        border: 1px solid $grey-3;
        border-radius: 0.55rem;

        &:hover {
            border-color: $grey-2;
        }
    
        &:has(.search-content-input:focus), &:has(.search-content-input:active) {
            border-color: $brand;;
        }

        & input {
            outline: none;
            width: 53.5rem;

            &::-webkit-search-decoration,
            &::-webkit-search-cancel-button,
            &::-webkit-search-results-button,
            &::-webkit-search-results-decoration {
                -webkit-appearance: none;
            }


            &::placeholder {
                color: $grey-2;
                opacity: 1;
            }
        }
    }

    &__only-title {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 0.5rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
        padding: 0.3rem 0.5rem;

        &:hover {
            background-color: $grey-4;
            cursor: pointer;
        }

        input[type=checkbox] {
            background-color: white;

            &:hover {
                cursor: pointer;
            }
        }

        label {
            margin-bottom: 0 !important;

            &:hover {
                cursor: pointer;
            }
        }
    }
}

.search-filters {

    display: flex;
    background: $grey-4;
    align-content: center;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    flex-direction: row;
    justify-content: space-between;
    flex-direction: column;
    padding: 0.2rem 0;

    @media only screen and (max-width: 1640px) {
        & {
            flex-wrap: wrap;
        }
    }

    &__date-range {
        background-color: white;
        border: 1px solid $white;
        border-radius: $radius;
        display: flex;
        align-items: center;
        position: relative;
        transition: all 0.2s;
        margin-left:0.75rem;
        height: 2rem;

        &:hover {
            border: 1px solid $grey-2;
        }

        & input[type=date] {
            border: none;
            background-color: white;
            transition: all 0.2s;
            position: relative;
            padding-top: 0;
            padding-bottom: 0;

            &::-webkit-calendar-picker-indicator {
                opacity: 0;
                z-index: 10;
                cursor: pointer;
            }

            &:after {
                font-family: 'MaterialIcons';
                content: '\e916';
                position: relative;
                left: -1rem;
                font-size: 1.25rem;
                margin-bottom: 0.1rem;
            }
        }

        & span {
            font-size: 2rem;
        }
    }

    &__fields {
        align-items: center;
        justify-content: center;

        &-field {
            position: relative;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
            transition: all 0.2s;
            padding: 0.3rem 0.5rem;

            &-container {
                padding: 0.3rem 0.5rem;
                transition: all 0.2s;
                border-radius: 0.5rem;
                align-items: center;
                justify-content: center;
                position: relative;
                font-size: 0.85rem;

                &:hover {
                    background-color: $grey-3;
                    cursor: pointer;
                }

                &-selected {
                    display: inline-block;
                    min-width: 1.25rem;
                    position: relative;
                    left: 0.25rem;
                }
            }

            & .m-icon {
                font-size: 1.75rem;
            }

            &-options {
                position: absolute;
                top: 2.5rem;
                left: -1.5rem;
                background-color: white;
                min-height: 4rem;
                min-width: 15rem;
                z-index: 50;
                border: 2px solid $grey-4;
                border-radius: 0.75rem;
                cursor: auto;
                display: block;

                &.active {
                    display: block;
                }

                & ul {
                    min-width: 5rem;
                    padding: 0.5rem;
                    overflow: auto;
                    max-height: 25rem;

                    li {
                        padding: 0.2rem 0.2rem;
                        display: flex;
                        align-items: center;
                        margin-bottom: 0.25rem;

                        input[type=checkbox] {
                            width: 1.05rem;
                            height: 1.05rem;
                            background-color: white;
                            border: 1px solid $grey-3;
                            position: relative;
                            font-size: 0.7rem;

                            &:hover {
                                cursor: pointer;
                            }

                            &:checked:before {
                                position: absolute;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                top: -0;
                                left: 0;
                                width: 1rem;
                                height: 1rem;
                                font-family: 'MaterialIconsOutlined';
                                content: 'check';
                                background: $blue-dark;
                                border-color: $blue-dark;
                                border-radius: 3px;
                                color: #fff;
                            }
                        }

                        label {
                            font-size: 0.75rem;
                            margin-bottom: 0;
                            color: $grey-2;
                            padding-top: 0;

                            &:hover {
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
        }
    }

    &-buttons {
        align-items: center;
        justify-content: center;
    }
}

/*Table*/
.filter-table-image {
    width: 1.5rem;
    height: 1.5rem;
}

.tag-pill {
    font-size: .85em;
    display: inline-block;
    margin: 0 5px 5px 0;
    padding: 3px 7px;
    border-radius: 12px;
    color: #FFFFFF;
    background-color: $blue-600;
}

/* Newsletter resends */
#searchFailures a, button {
    margin-left: 10px;
}

#searchFailures > div > div.flex.justify-end.flex-align-end {
    margin-bottom: 5px;
}

.newsletter-article-page-container {
    max-height: 55rem;
}

.newsletter-article-item {
    font-size: 0.85rem;

    & .highlight-title {
        font-weight: bold;
        padding-top: 0.2rem;
    }

    & a {
        word-break: normal;
    }
}

.m-icon-medium {
    font-size: 2rem !important;
}

.app-title {
    width: 150px;
}

.login-box {
    a {
        width: 100%;
    }
}

.login-box .app-title {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 0;

    .dual-title {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        font-size: 2rem;

        span {
            text-align: left;
        }

        .app-title-regular {
            font-family: $cencora-regular;
        }
    }
}

.app-title {
    h1 {
        font-size: 21px;
        font-weight: 500;
        line-height: 1;

        span {
            display: block;

            &.app-title-bold {
                font-family: $cencora-bold;
            }
        }
    }
}

.app-title-wrapper {
    display: flex;
    flex-direction: column;
}

i.icon-zoom-out {
    display: block;
    width: 25px;
    height: 25px;
    &:before {
        font-family: MaterialIconsOutlined;
        content: "zoom_out";
        font-size: 1.5rem;
    }
}

.page-wrapper {
    padding: 1rem 1.875rem;
}

.sub-header {
    margin-left: -1.875rem;
    margin-right: -1.875rem;
    margin-bottom: 1rem;
    margin-top: -1rem;
}

.sub-header.sticky {
    top: 0;
    z-index: 9999;
}

footer.sticky{
    bottom:0;
}

.buttons {
    width: unset;
}

.home-wrapper {
    margin-left: -1.875rem;
    margin-right: -1.875rem;
    margin-top: -1rem;
}

select {
    &.inline {
        height: 41.6px;
    }
}

.custom-select {
    &.card {
        &:before {
            top: 22px;
            right: 22px
        }
    }
}

.nav-menu .m-icon {
    top: 3px;
}

.account-navigation .nav-menu .m-icon {
    top: 1px;
}

.custom-select:before {
    z-index: 1;
}

.core-container>header.page-header {
    height: 50px;
}

.nav-menu > li.full-menu .flyout
{
    top: 50px;
}

.terms {
    margin-top: 10px;
}
