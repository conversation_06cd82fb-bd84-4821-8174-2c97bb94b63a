﻿@model IEnumerable<UserModel>
@using PharmaLex.SmartDecisions.Entities
@using System.Collections.Generic;
@using PharmaLex.Caching.Data;
@inject IDistributedCacheServiceFactory cache
@{
    ViewData["Title"] = "Manage Users";
    IEnumerable<Claim> _claims = (await cache.CreateEntity<Claim>().WhereAsync(c => c.ClaimType == "admin" || c.ClaimType == "news"))
        .Where(c => this.User.HasClaim(x => x.Type == "news:NewsAuthoringLead" && c.ClaimType == "news") ||
        this.User.HasClaim(x => x.Type == $"{c.ClaimType}:{c.Name}" || x.Type == "admin:SuperAdmin"));
    var claims = _claims.Select(c => new { id = c.Id, name = c.Name });
}

<div id="users">
    <div class="sub-header">
        <h2>Manage Users</h2>
        <div class="controls">
            <a class="button" href="/manage/users/new">Add user</a>
        </div>
    </div>
   
        <filtered-table :items="users" :columns="columns" :filters="filters" :link="link"></filtered-table>
   
   
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#users',
        data: function () {
            return {
                link: '/manage/users/edit/',
                users: @Html.Raw(Model.ToJson()),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'displayFullName',
                            sortKey: 'displayFullName',
                            header: 'Name',
                            type: 'text',
                            header: 'Name',
                            style: 'width: 33%;'
                        },
                        {
                            dataKey: 'email',
                            sortKey: 'email',
                            header: 'Email',
                            type: 'text',
                            header: 'Email',
                            style: 'width: 33%;'
                        },
                        {
                            dataKey: 'displayClaimsText',
                            sortKey: 'displayClaimsText',
                            header: 'Roles',
                            type: 'text',
                            style: 'width: 34%;'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'displayFullName',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.displayFullName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'email',
                        options: [],
                        type: 'search',
                        header: 'Search Email',
                        fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'displayClaimsText',
                        options: @Html.Raw(Json.Serialize(claims)),
                        filterCollection: 'claims',
                        display: 'name',
                        type: 'select',
                        header: 'Filter By Role',
                        fn: v => p => p.claims.includes(v),
                        convert: v => parseInt(v)
                    }
                ]
            };
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}