﻿using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;

namespace PharmaLex.SmartDecisions.Web
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateWebHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateWebHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((context, config) =>
                {
                    var builtConfig = config.Build();
                    var options = new DefaultAzureCredentialOptions() { VisualStudioTenantId = builtConfig["VisualStudioTenantId"] };
                    var secretClient = new SecretClient(
                       new Uri($"https://{builtConfig["KeyVaultName"]}.vault.azure.net/"),
                       new DefaultAzureCredential(options));
                    config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
#if DEBUG
                    config.AddJsonFile("appsettings.json");
#endif
                })
                .ConfigureWebHostDefaults(configure =>
                {
                    configure.UseStartup<Startup>();
                });
    }
}
