﻿<script type="text/x-template" id="location-preferences-template">

    <div class="modal-mask" v-if="showdialog" v-on:click="close">
        <div class="modal-wrapper" v-on:click.stop>
            <div class="modal-container">
                <div class="modal-header">
                    <h3></h3>
                    <i class="m-icon" v-on:click="close">close</i>
                </div>

                <div class="modal-body">
                    <treeview :items="preferencedata"></treeview>
                </div>
                <div class="buttons">
                    <a class="button secondary" v-on:click="close">@ls.Localise("cancel")</a>
                    <button id="icon-button-save" class="button" v-on:click="save">@ls.Localise("ok")</button>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('location-preferences-dialog', {
        template: '#location-preferences-template',
        data: function () {
            return {
            }
        },
        props: {
            showdialog: {
                type: Boolean,
                required: true,
                default: false
            },
            preferencedata: {
                type: Array,
                required: true
            }
        },
        methods: {
            close() {
                this.$emit("location-preferences-closing", false)
            },
            save() {
                if (this.checkValidity()) {
                    this.$emit("locations-updated", { preferencedata: this.preferencedata });
                }
            },
            checkValidity() {
                let valid = true;

                if (this.preferencedata[0].children.filter(x => x.selected).length == 0) {
                    plx.toast.show('@ls.Localise("(news).subscribe.select-one-location")', 5, 'failed', null, 5000, { useIcons: true });
                    valid =  false;
                }

                return valid;
            }
        }
    });
</script>