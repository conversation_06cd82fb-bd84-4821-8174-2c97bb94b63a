﻿using Microsoft.AspNetCore.Http;
using PharmaLex.Authentication.B2C;
using PharmaLex.DataAccess;

namespace Pharmalex.SmartDecisions.Web.Authorization
{
# nullable enable

    public class PlxUserContext : IUserContext
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        public PlxUserContext(IHttpContextAccessor httpContextAccessor)
        {
            this.httpContextAccessor = httpContextAccessor;
        }

        public string? User
        {
            get => httpContextAccessor.HttpContext?.User.GetEmail();
        }
    }
}
