﻿using Microsoft.Extensions.Options;
using Pharmalex.AzureCloudStorage;
using Pharmalex.AzureCloudStorage.Interfaces;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface IDecisionsBlobStorage : IAzureBlobClient { }

    public class DecisionsBlobStorage : AzureBlobClient, IDecisionsBlobStorage
    {
        public DecisionsBlobStorage(IOptions<StorageSettings> settings, string visualStudioTenantId) 
            : base(settings.Value.Account, settings.Value.Container, visualStudioTenantId) { }
    }

    public interface IDecisionsBlobContainer : IAzureBlobContainerClient { }

    public class DecisionsBlobContainer : AzureBlobContainerClient, IDecisionsBlobContainer
    {
        public DecisionsBlobContainer(IDecisionsBlobStorage storage) : base(storage) { }
    }

    public class StorageSettings
    {
        public string Account { get; set; } = string.Empty;
        public string Container { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }
}
