﻿<script type="text/x-template" id="product-preferences-template">

    <div class="modal-mask" v-if="showdialog" v-on:click="close">
        <div class="modal-wrapper" v-on:click.stop>

            <div class="modal-container">
                <div class="modal-header">
                    <h3></h3>
                    <i class="m-icon" v-on:click="close">close</i>
                </div>
                <div class="modal-body">
                    <div class="flex flex-nowrap">
                        <div class="flex-item flex-x1">
                            <treeview :items="preferencedata"></treeview>
                        </div>
                    </div>
                    
                </div>

                <div class="buttons">
                    <a class="button secondary" v-on:click="close">@ls.Localise("cancel")</a>
                    <a id="icon-button-save" class="button" v-on:click="save">@ls.Localise("ok")</a>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">

    function preferencesNotNested(prefs) {
        let flatPreferences = [];
        for (const pref of flattenPreferences(prefs.children)) {
            flatPreferences.push(pref);
        };
        return flatPreferences;
    }

    function* flattenPreferences(prefs) {
        for (const pref of prefs) {
            yield pref;
            yield* flattenPreferences(pref.children);
        }
    };

    vueApp.component('product-preferences-dialog', {
        template: '#product-preferences-template',
        data: function () {
            return {
            }
        },
        props: {
            showdialog: {
                type: Boolean,
                required: true,
                default: false
            },
            preferencedata: {
                type: Array,
                required: true
            }
        },
        methods: {
            close() {
                this.$emit("product-preferences-closing", false)
            },
            save() {
                if (this.checkValidity()) {
                    this.$emit("products-updated", { preferencedata: this.preferencedata });
                }
            },
            checkValidity() {
                let valid = true;

                let flatPreferences = preferencesNotNested(this.preferencedata[0]);

                if (flatPreferences.filter(x => x.selected).length == 0) {
                    plx.toast.show('@ls.Localise("(news).subscribe.select-one-product")', 5, 'failed', null, 5000, { useIcons: true });
                    valid = false;
                }

                return valid;
            }
        }
    });
</script>