﻿# PowerShell Commands
Scaffold-DbContext "Server=(local);Database=SmartDecisions;Trusted_Connection=True;" Microsoft.EntityFrameworkCore.SqlServer -force -schemas "dbo" -outputDir Entities -noOnConfiguring -NoPluralize -namespace PharmaLex.SmartDecisions.Entities
Scaffold-DbContext "Server=(local);Database=SmartDecisions;Trusted_Connection=True;" Microsoft.EntityFrameworkCore.SqlServer -force -schemas "Audit" -outputDir Audit -noOnConfiguring -NoPluralize

# EF Commands
dotnet ef database update --project src\PharmaLex.SmartDecisions.Data --startup-project src\PharmaLex.SmartDecisions.Web
dotnet ef migrations add {MigrationName} --project src\PharmaLex.SmartDecisions.Data --startup-project src\PharmaLex.SmartDecisions.Web
dotnet ef migrations remove {MigrationName} --project src\PharmaLex.SmartDecisions.Data --startup-project src\PharmaLex.SmartDecisions.Web
dotnet ef migrations script --project src\PharmaLex.SmartDecisions.Data --startup-project src\PharmaLex.SmartDecisions.Web