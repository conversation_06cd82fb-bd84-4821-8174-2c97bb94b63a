﻿using PharmaLex.DataAccess;
using System;
using System.Linq;
using System.Linq.Expressions;

namespace PharmaLex.SmartDecisions.Web.Helpers.Extensions
{
    public static class QueryableExtension
    {
        public static IQueryable<TEntity> FilterItems<TEntity>(this IQueryable<TEntity> entities,
           Expression<Func<TEntity, bool>>? expression,
           Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? orderBy,
           int skip = 0,
           int take = 0) where TEntity : EntityBase
        {
            if (expression != null)
            {
                entities = entities.Where(expression);
            }

            if (orderBy != null)
            {
                entities = orderBy(entities);
            }

            if (skip > 0)
            {
                entities = entities.Skip(skip);
            }

            if (take > 0)
            {
                entities = entities.Take(take);
            }

            return entities;
        }
    }
}
