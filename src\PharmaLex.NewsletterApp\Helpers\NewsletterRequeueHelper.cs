﻿using Microsoft.Azure.Functions.Worker.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp.Helpers
{
    public interface INewsletterRequeueHelper
    {
        Task<List<int>> ExtractSubscriptionsFromRequest(HttpRequestData request);
    }

    public class NewsletterRequeueHelper : INewsletterRequeueHelper
    {
        public async Task<List<int>> ExtractSubscriptionsFromRequest(HttpRequestData request)
        {
            var processList = new List<int>();

            string requestBody = await new StreamReader(request.Body).ReadToEndAsync();
            dynamic data = JsonConvert.DeserializeObject(requestBody);
            
            if (data != null)
            {
                var subscriptions = data?.subscriptions;

                foreach (var subscription in subscriptions)
                {
                    processList.Add((int)subscription);
                }
            }

            return processList;
        }
    }
}
