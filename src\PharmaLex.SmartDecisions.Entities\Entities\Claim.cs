﻿using PharmaLex.DataAccess;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class Claim : EntityBase
    {
        public Claim()
        {
            UserClaim = new HashSet<UserClaim>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string ClaimType { get; set; }

        public virtual ICollection<UserClaim> UserClaim { get; set; }

        [NotMapped]
        public string Key => $"{this.ClaimType}:{this.Name}";
    }
}
