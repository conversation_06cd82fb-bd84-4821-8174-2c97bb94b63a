﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class NewsCategoryAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public int? ParentId { get; set; }
        public int? GroupId { get; set; }
        public int? SortOrder { get; set; }
        public string LocalisationKey { get; set; }
    }
}
