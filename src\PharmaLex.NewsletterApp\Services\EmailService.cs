﻿using Microsoft.Extensions.Configuration;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public interface IEmailService
    {
        Task<(bool, HttpStatusCode)> Send(UserModel subscriber, NewsletterModel model);
        Task<(bool, HttpStatusCode)> Send(string subject, string message, string[] receivers);
    }
    public class EmailService : IEmailService
    {
        private readonly string sendGridApiKey;
        private readonly string senderEmail;

        private readonly ILocalisationService localisationService;
        private readonly IConfiguration configuration;

        public EmailService(IConfiguration configuration, ILocalisationService localisationService)
        {
            this.localisationService = localisationService;
            this.configuration = configuration;
            this.sendGridApiKey = configuration.GetConnectionString("SendGrid");
            this.senderEmail = configuration.GetValue<string>("senderEmail");
        }

        public async Task<(bool, HttpStatusCode)> Send(UserModel subscriber, NewsletterModel model)
        {
            var client = new SendGridClient(new SendGridClientOptions { ApiKey = this.sendGridApiKey, HttpErrorAsException = true });

            string sender = this.localisationService.LocaliseText(model.LocaleId, "(news).newsletter.from");
            var from = new EmailAddress(this.senderEmail, sender);

            if (model.IsInfoflash)
            {
                var articleTitle = model.ProductGroupArticles?.FirstOrDefault().Articles?.FirstOrDefault().Title ?? "";
                model.Subject = $"{this.localisationService.LocaliseText(model.LocaleId, $"(news).infoflash-email-title")}: {articleTitle}";
            }
            else
            {
                string monthly = model.IsMonthly ? "-monthly" : string.Empty;
                model.Subject = this.localisationService.LocaliseText(model.LocaleId, $"(news).newsletter.subject{monthly}");
            }

            var newsletterTemplateId = configuration.GetValue<string>($"{(model.IsInfoflash ? "infoflash" : "newsletter")}-{model.IsoLanguageCode.ToLower()}");

            var to = new EmailAddress(subscriber.Email, subscriber.DisplayFullName);

            SendGridMessage msg = new SendGridMessage();
            msg.SetTemplateId(newsletterTemplateId);
            msg.SetTemplateData(model);
            msg.SetFrom(from);
            msg.SetSubject(model.Subject);
            msg.AddTo(to);

            Response response = await client.SendEmailAsync(msg).ConfigureAwait(false);
            return (response.IsSuccessStatusCode, response.StatusCode);
        }

        public async Task<(bool, HttpStatusCode)> Send(string subject, string message, string[] receivers)
        {
            var client = new SendGridClient(new SendGridClientOptions { ApiKey = this.sendGridApiKey, HttpErrorAsException = true });

            var msg = new SendGridMessage();
            msg.SetFrom(new EmailAddress(this.senderEmail));
            msg.SetSubject(subject);
            msg.PlainTextContent = message;
            Array.ForEach(receivers, x => msg.AddTo(x));

            Response response = await client.SendEmailAsync(msg).ConfigureAwait(false);
            return (response.IsSuccessStatusCode, response.StatusCode);
        }
    }
}
