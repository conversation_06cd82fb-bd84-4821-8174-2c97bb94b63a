﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SendGrid.Helpers.EventWebhook;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class SendGridWebhooksHttpTrigger
    {
        private readonly IConfiguration configuration;
        private readonly string[] emailTemplateIds;

        public SendGridWebhooksHttpTrigger(IConfiguration configuration)
        {
            this.configuration = configuration;
            this.emailTemplateIds = new string[] {
                configuration.GetValue<string>("newsletter-en"),
                configuration.GetValue<string>("newsletter-fr"),
                configuration.GetValue<string>("infoflash-en"),
                configuration.GetValue<string>("infoflash-fr"),
            };
        }

        [Function("startsendgridwebhook")]
        [QueueOutput("%qn-webhooks%")]
        public async Task<SendGridWebhook> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route ="log")] HttpRequestData req, FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("RetrieveSendgridWebhooks");

            var sendGridPublicKey = configuration.GetValue<string>("Sendgrid:Webhook:Key");
            var signature = req.Headers.GetValues(RequestValidator.SIGNATURE_HEADER).FirstOrDefault();
            var timestamp = req.Headers.GetValues(RequestValidator.TIMESTAMP_HEADER).FirstOrDefault();

            if (string.IsNullOrEmpty(sendGridPublicKey)) 
            {
                logger.LogError($"SendgridWebhookCallback: Missing SendGrid public key.");
                return null;
            }

            if (string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(timestamp))
            {
                logger.LogError($"SendgridWebhookCallback: Signature Headers are empty.");
                return null;
            }

            string rawRequestBody = await new StreamReader(req.Body).ReadToEndAsync();

            if (!string.IsNullOrEmpty(rawRequestBody))
            {
                var validator = new RequestValidator();
                var ecPublicKey = validator.ConvertPublicKeyToECDSA(sendGridPublicKey);
                var isValidSendGridRequest = validator.VerifySignature(ecPublicKey, rawRequestBody, signature, timestamp);

                if (!isValidSendGridRequest)
                {
                    logger.LogError($"SendgridWebhookCallback: SendGrid VerifySignature failed.");
                    return null;
                }

                var model = JsonConvert.DeserializeObject<SendGridWebhookModel[]>(rawRequestBody.ToString());
                var modelToReturn = model.Where(x => emailTemplateIds.Contains(x.TemplateId)).ToArray();
                return modelToReturn.Any() ? new SendGridWebhook { Logs = modelToReturn } : null;
            }
            else
            {
                logger.LogError($"SendgridWebhookCallback: Request Body is empty.");
            }

            return null;
        }
    }
}
