﻿@model CompanyUserViewModel
@using Microsoft.AspNetCore.Authorization
@inject IAuthorizationService AuthorizationService
@{
    ViewData["Title"] = "Remove company user";
    bool isPharmaLexUser = (await AuthorizationService.AuthorizeAsync(User, "Admin")).Succeeded;
}

<div class="sub-header">
    <h2>@ls.Localise("[[table]].remove") <em>@Model.CompanyUser.DisplayFullName</em></h2>
</div>

<section>
    <form method="post">

        <div class="flex mb-2">
            <i class="m-icon warning-color">warning</i>
            <p class="lead m-0 p-0 pl-1"><strong>@ls.Localise("(news).users.warning"):</strong> @ls.LocaliseInterpolate("(news).users.remove-user", Model.CompanyUser.DisplayFullName.ToString(), Model.Company.Name.ToString())?</p>
        </div>

        <div class="buttons">
             @if (isPharmaLexUser)
            {
                  <a class="button secondary" href="/manage/company/edit/@Model.Company.Id">@ls.Localise("cancel")</a>
            }
            else
            {
                 <a class="button secondary" href="/users/edit/@Model.CompanyUser.Id">@ls.Localise("cancel")</a>
            }        
            <button type="submit">@ls.Localise("[[table]].remove")</button>
        </div>
    </form>
</section>