﻿@model ContentTypeDisplayModel
@{
    ViewData["Title"] = "Delete Content Type Display";
}

<div>
    <div class="sub-header">
        <h2>Delete <em>@Model.Name</em> Display from <em>@Model.ContentTypeName</em> Content Type</h2>
        <div class="controls">
            <a class="button secondary" href="/manage/content-type/edit/@Model.ContentTypeId">@Model.ContentTypeName Content Type</a>
        </div>
    </div>

    <section>
        <form method="post">
            <div class="flex mb-4">
                <i class="m-icon warning-color">warning</i>
                <p class="lead m-0 p-0 pl-1"><strong>Warning:</strong> Deleting is permanent and once deleted the display cannot be restored.</p>
            </div>

            <p class="lead">Are you sure you want to delete the display <strong><em>@Model.Name</em></strong> from the content type <strong><em>@Model.ContentTypeName</em></strong>?</p>
            <div class="buttons">
                <a class="button secondary" href="/manage/content-type/edit/@Model.ContentTypeId">Cancel</a>
                <button type="submit">Delete</button>
            </div>
        </form>
    </section>
    
</div>