﻿--alter table [dbo].[ContentTypeDisplay]
--with check add constraint [FK_ContentTypeDisplay_ContentType] foreign key([ContentTypeId]) 
--references [dbo].[ContentType] ([Id]) on delete cascade
--go

create trigger [dbo].[ContentTypeDisplay_Insert] on [dbo].[ContentTypeDisplay]
for insert as
insert into [Audit].[ContentTypeDisplay_Audit]
select 'I', [Id], [Name], [ContentTypeId], [ContentTypeDisplayTypeId], [Json], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[ContentTypeDisplay_Update] on [dbo].[ContentTypeDisplay]
for update as
insert into [Audit].[ContentTypeDisplay_Audit]
select 'U', [Id], [Name], [ContentTypeId], [ContentTypeDisplayTypeId], [<PERSON><PERSON>], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[ContentTypeDisplay_Delete] on [dbo].[ContentTypeDisplay]
for delete as
insert into [Audit].[ContentTypeDisplay_Audit]
select 'D', [Id], [Name], [ContentTypeId], [ContentTypeDisplayTypeId], [Json], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go