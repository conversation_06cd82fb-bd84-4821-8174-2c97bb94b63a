﻿using PharmaLex.DataAccess;
using System;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsletterSubscriptionAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public int? UserId { get; set; }
        public string Timezone { get; set; }
        public int? TimezoneOffset { get; set; }
        public int? DeliveryLocalDay { get; set; }
        public int? DeliveryUtcDay { get; set; }
        public int? DeliveryLocalHour { get; set; }
        public int? DeliveryUtcHour { get; set; }
        public DateTime? CreatedDateUtc { get; set; }
        public bool? IsMonthly { get; set; }
        public bool? IsInfoflash { get; set; }
        public bool? Active { get; set; }
    }
}
