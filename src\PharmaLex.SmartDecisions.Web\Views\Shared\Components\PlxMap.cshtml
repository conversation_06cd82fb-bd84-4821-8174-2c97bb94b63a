﻿<environment include="Development">
    <link rel="stylesheet" href="~/dist/css/mapsCss.css" asp-append-version="true" />
</environment>

<environment exclude="Development">
    <link rel="stylesheet" href="@VersionCdn.GetUrl("/dist/css/mapsCss.css")" asp-append-version="true" />
</environment>

<script type="text/x-template" id="plx-map-template">
    <div class="vue-template-map">
        <select id="countrySelect" v-show="showList" v-bind:disabled="!editable" multiple v-model="selectedCountries" v-on:change="onCountryListSelectionChanged" :class="['company-countries-select']">
            <option v-for="country in countries" :value="country.id">{{country.name}}</option>
        </select>
        <div v-show="!showList" id="map-container" :class="['company-countries-map']"></div>
        <div class="map-controls" style="display:flex; justify-content:space-between; align-items: center;">
            <a class="action" @@click="showList=!showList">{{toggleText}}</a>
            <select class="on-map" v-if="regions.length && editable" v-model="copyFromRegion" v-on:change="onCopyFromRegionChange">
                <option value="-1">Select region</option>
                <option v-for="(r, i) in regions" :value="r.id">{{r.name}}</option>
            </select>
            <a v-if="editable" class="action" @@click="onSelectAllCountries">Select all</a>
            <a v-if="editable" class="action" @@click="onClear">Clear all</a>
        </div>
    </div>
</script>


<script src="/data/countries.topo.js"></script>
<script src="/lib/d3/d3.v4.js"></script>
<script src="/lib/d3/topojson.js"></script>
<script src="/js/map.js" asp-append-version="true"></script>