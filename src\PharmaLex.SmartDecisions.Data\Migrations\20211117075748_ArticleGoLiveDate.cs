﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.SmartDecisions.Data.Extensions;

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class ArticleGoLiveDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "GoLiveDate",
                schema: "Audit",
                table: "NewsArticleContent_Audit",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "GoLiveDate",
                table: "NewsArticleContent",
                type: "datetime2",
                nullable: true);

            migrationBuilder.SqlFileExec("05-ArticleGoLiveDate-01-AlterNewsArticleContentTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GoLiveDate",
                schema: "Audit",
                table: "NewsArticleContent_Audit");

            migrationBuilder.DropColumn(
                name: "GoLiveDate",
                table: "NewsArticleContent");
        }
    }
}
