﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Helpers.Authorization;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    public class CompanyUserAdminController : BaseController
    {

        private readonly IMapper mapper;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IAzureAdB2CGraphService graphService;
        private readonly ICompanyUserManagementService userService;

        public CompanyUserAdminController(IDistributedCacheServiceFactory cache,
            IMapper mapper,
            IAzureAdB2CGraphService graphService,
            ICompanyUserManagementService userService)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.graphService = graphService;
            this.userService = userService;
        }

        [HttpGet("company/user/find"), Authorize("CompanyAdmin")]
        public async Task<IActionResult> AdminFind(string term, int companyId)
        {
            IList<User> dbUsers = (await this.cache.CreateEntity<User>("CompanyUser")
                .WhereAsync(x => x.CompanyUser != null && x.CompanyUser.CompanyId == companyId));

            List<Microsoft.Graph.Models.User> graphUsers = await this.graphService.FindUsers(term);
            List<UserFindResultModel> users = mapper.Map<List<UserFindResultModel>>(graphUsers);
            foreach (var um in users)
            {
                var dbUser = dbUsers.FirstOrDefault(x => x.Email.ToLower() == um.Email.ToLower());
                if (dbUser != null)
                {
                    var m = mapper.Map<UserFindResultModel>(dbUser);
                    um.Name = m.Name;
                    um.Id = dbUser.Id;
                    um.GivenName = dbUser.GivenName;
                    um.FamilyName = dbUser.FamilyName;
                    um.Active = dbUser.CompanyUser?.Active ?? um.Active;
                }
            }

            return Json(users.OrderBy(x => x.Name));
        }

        [HttpGet("/company/user/new/{companyId}"), Authorize("CompanyAdmin")]
        public async Task<IActionResult> AdminNew(int companyId)
        {
            CompanyModel company = await cache.CreateMappedEntity<Company, CompanyModel>()
                .Configure(o => o.Include(x => x.CompanyUser))
                .FirstOrDefaultAsync(x => x.Id == companyId);
            return View("../CompanyUser/Edit", new CompanyUserViewModel(company));
        }

        [HttpPost("/company/user/new/{companyId}"), Authorize("CompanyAdmin"), ValidateAntiForgeryToken]
        public async Task<IActionResult> AdminNew(int companyId, CompanyUserModel companyUser, int[] sites)
        {
            var company = await cache.CreateMappedEntity<Company, CompanyModel>("CompanyUser").FirstOrDefaultAsync(x => x.Id == companyId);

            var model = new CompanyUserViewModel(company) { CompanyUser = companyUser };
            if (this.ModelState.IsValid && this.userService.ValidateNewModelState(model, companyUser))
            {
                bool success = await this.userService.ProcessCompanyUser(companyUser, company, sites);
                if (!success)
                    return this.BadRequest();

                return this.Redirect($"/manage/company/edit/{companyId}");
            }

            return this.View("../CompanyUser/Edit", model);
        }

        [HttpGet("/company/user/edit/{id}"), Authorize("CompanyAdmin")]
        public async Task<IActionResult> AdminEdit(int id)
        {
            var user = await cache.CreateEntity<User>().Configure(o => o
                    .Include(x => x.UserClaim)
                        .ThenInclude(x => x.Claim)
                    .Include(x => x.CompanyUser)
                        .ThenInclude(x => x.Company)
                ).FirstOrDefaultAsync(x => x.Id == id && x.CompanyUser != null);

            if (user == null)
            {
                return this.NotFound();
            }

            var company = await cache.CreateMappedEntity<Company, CompanyModel>()
                .Configure(o => o.Include(x => x.CompanyUser))
                .FirstOrDefaultAsync(x => x.Id == user.CompanyUser.CompanyId);

            var model = new CompanyUserViewModel(company)
            {
                CompanyUser = mapper.Map<CompanyUserModel>(user)
            };

            return View("../CompanyUser/Edit", model);
        }

        [HttpPost("/company/user/edit/{id}"), Authorize("CompanyAdmin"), ValidateAntiForgeryToken]
        public async Task<IActionResult> AdminEdit(int id, CompanyUserModel companyUser, int[] sites)
        {
            var user = await cache.CreateEntity<User>("CompanyUser")
                .FirstOrDefaultAsync(x => x.Id == id && x.CompanyUser != null);

            if (user == null)
            {
                return this.NotFound();
            }

            var company = await cache.CreateMappedEntity<Company, CompanyModel>()
                .Configure(o => o.Include(x => x.CompanyUser))
                .FirstOrDefaultAsync(x => x.Id == user.CompanyUser.CompanyId);

            var model = new CompanyUserViewModel(company) { CompanyUser = companyUser };
            if (this.ModelState.IsValid && this.userService.ValidateEditModelState(model, user))
            {
                bool success = await this.userService.ProcessCompanyUser(companyUser, company, sites);
                if (!success)
                    return this.BadRequest();

                return this.Redirect($"/manage/company/edit/{company.Id}");
            }

            return this.View("../CompanyUser/Edit", model);
        }

        [HttpGet("/company/user/delete/{id}"), AuthorizeMultiple("CompanyAdmin;CompanyManager", false)]
        public async Task<IActionResult> AdminDelete(int id)
        {
            var companyUserCache = cache.CreateMappedEntity<CompanyUser, CompanyUserModel>().Configure(o => o.Include(x => x.User));
            var companyUser = await companyUserCache.FirstOrDefaultAsync(x => x.UserId == id);

            var company = await cache.CreateMappedEntity<Company, CompanyModel>()
               .Configure(o => o.Include(x => x.CompanyUser))
               .FirstOrDefaultAsync(x => x.CompanyUser.Select(x => x.UserId).Contains(companyUser.Id));

            var model = new CompanyUserViewModel(company) { CompanyUser = companyUser };
            bool isPlxUser = this.User.IsPharmaLexUser();

            if (companyUser != null)
            {
                return View("../CompanyUser/Delete", model);
            }
            else if (isPlxUser)
            {
                return Redirect($"/manage/company/edit/{company.Id}");
            }
            {
                return Redirect($"/users/edit/{model.CompanyUser.Id}");
            }
        }

        [HttpPost("/company/user/delete/{id}"), AuthorizeMultiple("CompanyAdmin;CompanyManager", false), ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            var companyUserCache = cache.CreateTrackedEntity<CompanyUser>().Configure(o => o.Include(x => x.User));
            var companyUser = await companyUserCache.FirstOrDefaultAsync(x => x.UserId == id);
            if (companyUser != null)
            {
                companyUserCache.Remove(companyUser);
                await companyUserCache.SaveChangesAsync();
                var escapedName = HttpUtility.HtmlEncode($"{companyUser.User.GivenName} {companyUser.User.FamilyName}");
                this.AddConfirmationNotification($"<em>{escapedName}</em> removed", new NotificationOptions() { UseIcons = true });
            }

            var newsletterSubscriptionCache = cache.CreateTrackedEntity<NewsletterSubscription>();
            var newsletterSubscriptions = await newsletterSubscriptionCache.WhereAsync(x => x.UserId == companyUser.UserId);
            foreach (var subscription in newsletterSubscriptions)
            {
                subscription.Active = false;
            }

            await newsletterSubscriptionCache.SaveChangesAsync();

            bool isPlxUser = this.User.IsPharmaLexUser();
            if (isPlxUser)
            {
                return Redirect($"/manage/company/edit/{companyUser.CompanyId}");
            }
            else
            {
                return Redirect($"/users");

            }

        }
    }
}
