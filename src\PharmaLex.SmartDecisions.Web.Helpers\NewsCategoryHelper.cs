﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface INewsCategoryHelper
    {
        Task<Dictionary<string, int>> GetNewsCategoryLocalisationIds();
        Task<Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>>> InitSubscriberPreferenceGroups(int userId);

        Task<Dictionary<NewsCategoryGroup, List<NewsCategory>>> InitNewsCategoryGroups(int localeId);
    }

    public class NewsCategoryHelper : INewsCategoryHelper
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IRepositoryFactory repoFactory;
        private readonly IMapper mapper;
        private readonly INewsCategoryLicenseHelper license;
        private readonly ILocalisationService localisationService;

        public NewsCategoryHelper(IDistributedCacheServiceFactory cache,            
            IRepositoryFactory repoFactory,
            IMapper mapper,
            INewsCategoryLicenseHelper license,
            ILocalisationService localisationService)
        {
            this.cache = cache;
            this.repoFactory = repoFactory;
            this.mapper = mapper;
            this.license = license;
            this.localisationService = localisationService;
        }

        public async Task<Dictionary<string, int>> GetNewsCategoryLocalisationIds()
        {
            var categories = cache.CreateMappedEntity<NewsCategory, NewsCategoryModel>();
            return (await categories.AllAsync()).ToDictionary(x => x.LocalisationKey, x => x.Id);
        }

        public async Task<Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>>> InitSubscriberPreferenceGroups(int userId)
        {
            var preferencesRepo = repoFactory.Create<UserNewsCategory>().Configure(o => o.Include(x => x.NewsCategory));
            List<UserNewsCategoryModel> preferences = mapper.ProjectTo<UserNewsCategoryModel>(preferencesRepo.Where(x => x.UserId == userId)).OrderBy(x => x.Order).ToList();

            var subscriberPreferenceGroups = new Dictionary<NewsCategoryGroup, List<UserNewsCategoryModel>>();
            foreach (NewsCategoryGroup group in Enum.GetValues(typeof(NewsCategoryGroup)))
            {
                var license = await this.license.GetUserNewsCategoryGroupLicense(userId, group, true);
                subscriberPreferenceGroups.Add(group, preferences
                    .Where(x => license.Contains(x.NewsCategoryId)).ToList());
            }

            return subscriberPreferenceGroups;
        }

        public async Task<Dictionary<NewsCategoryGroup, List<NewsCategory>>> InitNewsCategoryGroups(int localeId)
        {
            var localisedNewsCategoryGroups = (await this.localisationService.LocaliseNewsCategories(localeId)).GroupBy(x => x.GroupId);

            var newsCategoryGroups = new Dictionary<NewsCategoryGroup, List<NewsCategory>>();
            foreach (NewsCategoryGroup group in Enum.GetValues(typeof(NewsCategoryGroup)))
            {
                newsCategoryGroups.Add(group, localisedNewsCategoryGroups.First(x => x.Key == (int)group).OrderBy(x => x.Name).ToList());
            }

            return newsCategoryGroups;
        }
    }
}
