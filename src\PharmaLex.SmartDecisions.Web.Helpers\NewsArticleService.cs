﻿using AutoMapper;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers.Exceptions;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface INewsArticleService
    {
        Task<(NewsArticle, NewsArticleContent)> CreateOrUpdateNewsArticle(NewsArticleModel model);
    }

    public class NewsArticleService : INewsArticleService
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly IMapper mapper;
        private readonly IConfiguration configuration;
        private readonly IDecisionsBlobContainer blobContainer;

        public NewsArticleService(IDistributedCacheServiceFactory cacheFactory,
            IMapper mapper,
            IConfiguration configuration,
            IDecisionsBlobContainer blobContainer)
        {
            this.cacheFactory = cacheFactory;
            this.mapper = mapper;
            this.configuration = configuration;
            this.blobContainer = blobContainer;
        }

        public async Task<(NewsArticle, NewsArticleContent)> CreateOrUpdateNewsArticle(NewsArticleModel model)
        {
            var nonIndexedBlobPath = configuration.GetValue<string>("AppSettings:ImpactAssessmentNotIndexedBlobPath");
            var indexedBlobPath = configuration.GetValue<string>("AppSettings:ImpactAssessmentBlobPath");

            var articlesCache = this.cacheFactory.CreateTrackedEntity<NewsArticle>()
                .Configure(o => o
                    .Include(x => x.NewsSource)
                    .Include(x => x.NewsArticleContent)
                        .ThenInclude(x => x.AzureBlob)
                    .Include(x => x.NewsArticleCategory));

            var article = await articlesCache.FirstOrDefaultAsync(x => x.Id == model.Id);
            if (article == null)
            {
                article = this.mapper.Map<NewsArticle>(model);
            }
            else
            {
                this.mapper.Map<NewsArticleModel, NewsArticle>(model, article);
            }

            var updatedLocale = model.NewsArticleContent[0];
            var updatedContent = article.NewsArticleContent.FirstOrDefault(x => x.Id == updatedLocale.Id);
            var canUpdateFriendlyUrl = true;
            if (updatedContent != null)
            {
                canUpdateFriendlyUrl = updatedContent.PublishingStateId != (int)NewsArticlePublishingState.Approved;
                if (updatedContent.LocaleId != updatedLocale.LocaleId)
                {
                    throw new MismatchingContentLocaleException("Provided content locale does not match the expected value.");
                }

                this.mapper.Map(updatedLocale, updatedContent);
            }
            else
            {
                updatedContent = this.mapper.Map<NewsArticleContent>(updatedLocale);
                article.NewsArticleContent.Add(updatedContent);
            }

            if (canUpdateFriendlyUrl)
            {
                updatedContent.FriendlyUrl = BuildFriendlyUrl(updatedLocale.Title);
            }
            if (updatedContent.Id <= 0)
            {
                if (article.Id == 0)
                {
                    articlesCache.Add(article);
                }
                await articlesCache.SaveChangesAsync();
            }

            var azureBlobName = article.NewsArticleContent.FirstOrDefault()?.AzureBlob?.Name;
            var isOldPdfArticle = azureBlobName != null && azureBlobName.EndsWith(".pdf");

            string locale = (await this.cacheFactory.CreateEntity<Locale>().FirstOrDefaultAsync(x => x.Id == updatedLocale.LocaleId)).IsoLanguageCode;

            var metadata = new Dictionary<string, string>
                {
                    { "id", updatedContent.Id.ToString() },
                    { "locale", locale},
                    { "localeId", updatedContent.LocaleId.ToString() },
                    { "newsArticleId", article.Id.ToString() },
                    { "base64Title", Convert.ToBase64String(Encoding.UTF8.GetBytes(model.NewsArticleContent.FirstOrDefault()?.Title.Trim())) },
                    { "friendlyUrl", updatedContent.FriendlyUrl },
                    { "publishingStateId", model.NewsArticleContent.FirstOrDefault()?.PublishingStateId.ToString() },
                    { "newsSourceId", model.NewsSourceId.Value.ToString() },
                    { "sourceUrl", Uri.EscapeDataString(model.NewsArticleContent.FirstOrDefault()?.SourceUrl.Trim()) },
                    { "sourcePublicationDate", model.SourcePublicationDate},
                    { "categories", JsonConvert.SerializeObject(model.NewsCategoryIds) },
                    { "importanceId", model.ImportanceId.Value.ToString()}
                };

            if (!string.IsNullOrEmpty(model.NewsArticleContent.FirstOrDefault().GoLiveDate)) //Convert string for EDM.DateTime format)
            {
                metadata.Add("golivedate", model.NewsArticleContent.FirstOrDefault().GoLiveDate);
            }

            if (isOldPdfArticle)
            {
                var blob = await this.blobContainer.GetBlobClientAsync(azureBlobName);
                await blob.SetMetadataAsync(metadata);
            }
            else if (!string.IsNullOrEmpty(updatedLocale.Body))
            {
                string blobName = updatedContent.AzureBlob?.Name ??
                    $"{nonIndexedBlobPath}/{locale}/{updatedContent.Id}-{updatedContent.FriendlyUrl.Substring(updatedContent.FriendlyUrl.IndexOf('-') + 1)}.html";

                using (var stream = new MemoryStream(Encoding.Default.GetBytes(updatedLocale.Body)))
                {
                    var blobHttpHeader = new BlobHttpHeaders { ContentType = "text/html" };

                    var cb = await this.blobContainer
                    .UploadBlobAsync(stream, blobName, httpHeaders: blobHttpHeader, metadata: metadata);

                    if (updatedContent.AzureBlob == null)
                    {
                        updatedContent.AzureBlob = this.mapper.Map<AzureBlob>(cb);
                    }
                    else
                    {
                        this.mapper.Map(cb, updatedContent.AzureBlob);
                    }
                }

                // save text version of blob that can be indexed
                var indexableBlobName = BuildFileNameOfIndexableFile(updatedContent.AzureBlob.Name, nonIndexedBlobPath, indexedBlobPath);

                if(!string.IsNullOrEmpty(model.NewsArticleContent[0].ImpactAssessmentText))
                {
                    using (var stream = new MemoryStream(Encoding.Default.GetBytes(model.NewsArticleContent[0].ImpactAssessmentText)))
                    {
                        var blobHttpHeader = new BlobHttpHeaders { ContentType = "text/plain" };

                        metadata = new Dictionary<string, string>{
                                { "id", updatedContent.Id.ToString() },
                                { "locale", locale},
                                { "localeId", updatedContent.LocaleId.ToString() },
                                { "newsArticleId", article.Id.ToString() },
                                { "base64Title", Convert.ToBase64String(Encoding.UTF8.GetBytes(model.NewsArticleContent.FirstOrDefault()?.Title.Trim())) },
                                { "friendlyUrl", updatedContent.FriendlyUrl },
                                { "publishingStateId", model.NewsArticleContent.FirstOrDefault()?.PublishingStateId.ToString() },
                                { "newsSourceId", model.NewsSourceId.Value.ToString() }, //check for null
                                { "sourceUrl", Uri.EscapeDataString(model.NewsArticleContent.FirstOrDefault()?.SourceUrl.Trim()) },
                                { "sourcePublicationDate", model.SourcePublicationDate},
                                { "categories", JsonConvert.SerializeObject(model.NewsCategoryIds) },
                                { "importanceId", model.ImportanceId.Value.ToString()},//check for null  
                        };

                        if (!string.IsNullOrEmpty(model.NewsArticleContent.FirstOrDefault().GoLiveDate)) //Convert string for EDM.DateTime format)
                        {
                            metadata.Add("golivedate", model.NewsArticleContent.FirstOrDefault().GoLiveDate);
                        }


                        var cb = await this.blobContainer
                            .UploadBlobAsync(stream, indexableBlobName, httpHeaders: blobHttpHeader, metadata: metadata);
                    }
                }                
            }
            else if (updatedContent.AzureBlob != null)
            {
                if (!model.IsMigrated)
                {
                    await this.blobContainer.DeleteBlobAsync(updatedContent.AzureBlob.Name);
                    await this.blobContainer.DeleteBlobAsync(BuildFileNameOfIndexableFile(updatedContent.AzureBlob.Name, nonIndexedBlobPath, indexedBlobPath));

                    var blobCache = this.cacheFactory.CreateTrackedEntity<AzureBlob>();
                    blobCache.Remove(updatedContent.AzureBlob);
                    await blobCache.SaveChangesAsync();
                    updatedContent.AzureBlob = null;
                }
            }

            await articlesCache.SaveChangesAsync();

            return (article, updatedContent);
        }       

        private static string BuildFriendlyUrl(string url)
        {
            return $"{Guid.NewGuid():N}-{FriendlyUrlGenerator.URLFriendly(url)}";
        }

        private static string BuildFileNameOfIndexableFile(string blobName, string nonIndexedBlobPath, string indexedBlobPath)
        {
            var indexableBlobName = blobName.Replace(nonIndexedBlobPath, indexedBlobPath);
            indexableBlobName = indexableBlobName.Replace(Path.GetFileName(blobName), $"{Path.GetFileNameWithoutExtension(blobName)}.txt");
            return indexableBlobName;
        }
    }
}
