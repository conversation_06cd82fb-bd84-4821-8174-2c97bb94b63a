﻿ALTER TRIGGER [dbo].[Company_Insert] ON [dbo].[Company]
FOR INSERT AS
INSERT INTO [Audit].[Company_Audit] (
	[AuditAction]
	, [Id]
	, [Name]
	, [PrimaryContactName]
	, [PrimaryContactEmail]
	, [PrimaryContactAddress]
	, [PrimaryContactPhone]
	, [CreatedDate]
	, [CreatedBy]
	, [LastUpdatedDate]
	, [LastUpdatedBy]
	, [MaximumUsersCount]
	, [MaximumActiveUsersCount]
)
SELECT 'I'
	, [Id]
	, [Name]
	, [PrimaryContactName]
	, [PrimaryContactEmail]
	, [PrimaryContactAddress]
	, [PrimaryContactPhone]
	, [CreatedDate]
	, [CreatedBy]
	, [LastUpdatedDate]
	, [LastUpdatedBy]
	, [MaximumUsersCount]
	, [MaximumActiveUsersCount]
FROM [Inserted]
GO

ALTER TRIGGER [dbo].[Company_Update] ON [dbo].[Company]
FOR UPDATE AS
INSERT INTO [Audit].[Company_Audit] (
	[AuditAction]
	, [Id]
	, [Name]
	, [PrimaryContactName]
	, [PrimaryContactEmail]
	, [PrimaryContactAddress]
	, [PrimaryContactPhone]
	, [CreatedDate]
	, [CreatedBy]
	, [LastUpdatedDate]
	, [LastUpdatedBy]
	, [MaximumUsersCount]
	, [MaximumActiveUsersCount]
)
SELECT 'U'
	, [Id]
	, [Name]
	, [PrimaryContactName]
	, [PrimaryContactEmail]
	, [PrimaryContactAddress]
	, [PrimaryContactPhone]
	, [CreatedDate]
	, [CreatedBy]
	, [LastUpdatedDate]
	, [LastUpdatedBy]
	, [MaximumUsersCount]
	, [MaximumActiveUsersCount] 
FROM [Inserted]
GO

ALTER TRIGGER [dbo].[Company_Delete] ON [dbo].[Company]
FOR DELETE AS
INSERT INTO [Audit].[Company_Audit] (
	[AuditAction]
	, [Id]
	, [Name]
	, [PrimaryContactName]
	, [PrimaryContactEmail]
	, [PrimaryContactAddress]
	, [PrimaryContactPhone]
	, [CreatedDate]
	, [CreatedBy]
	, [LastUpdatedDate]
	, [LastUpdatedBy]
	, [MaximumUsersCount]
	, [MaximumActiveUsersCount]
)
SELECT 'D'
	, [Id]
	, [Name]
	, [PrimaryContactName]
	, [PrimaryContactEmail]
	, [PrimaryContactAddress]
	, [PrimaryContactPhone]
	, [CreatedDate]
	, [CreatedBy]
	, GETDATE()
	, COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME())
	, [MaximumUsersCount]
	, [MaximumActiveUsersCount] 
FROM [Deleted]
GO