﻿using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using Microsoft.EntityFrameworkCore.Migrations.Operations.Builders;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text.RegularExpressions;

namespace PharmaLex.SmartDecisions.Data.Extensions
{
    public static class MigrationBuilderExtensions
    {
        public static OperationBuilder<SqlOperation> SqlFile(this MigrationBuilder migrationBuilder, string sqlFileName)
        {
            return migrationBuilder.Sql(ReadFile(sqlFileName));
        }

        public static OperationBuilder<SqlOperation> SqlFileExec(this MigrationBuilder migrationBuilder, string sqlFileName)
        {
            string sqlScript = ReadFile(sqlFileName);

            Regex r = new Regex("\\s*\\n+\\s*GO\\s*\\n*\\s*\\z*", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(500));

            return migrationBuilder.Sql(string.Join("\nGO\n", Process(r.Split(sqlScript))));
        }

        private static string ReadFile(string sqlFileName)
        {
            Assembly assembly = typeof(MigrationBuilderExtensions).Assembly;

            using (Stream stream = assembly.GetManifestResourceStream(assembly.GetName().Name + ".Scripts." + sqlFileName))
            using (StreamReader reader = new StreamReader(stream))
            {
                return reader.ReadToEnd();
            }
        }

        private static IEnumerable<string> Process(string[] operations)
        {
            foreach (string op in operations)
            {
                if (!string.IsNullOrWhiteSpace(op))
                    yield return $"EXEC('\n{op.Replace("'", "''")}\n');";
            }
        }
    }
}
