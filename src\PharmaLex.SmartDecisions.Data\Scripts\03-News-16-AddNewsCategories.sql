﻿DECLARE @parentId nvarchar(64)
DECLARE @parentId2 nvarchar(64)
INSERT INTO [dbo].[NewsCategory] SELECT N'Geographical Scope', null, 1, 1, N'geographical-scope', GETDATE(), 'update script', GETDATE(), 'update script'
SELECT @parentId = SCOPE_IDENTITY()
INSERT INTO [dbo].[NewsCategory] SELECT N'European Union', @parentId, 1, 2, N'european-union', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'France', @parentId, 1, 3, N'france', GETDATE(), 'update script', GETDATE(), 'update script'

INSERT INTO [dbo].[NewsCategory] SELECT N'Products', null, 2, 4, N'products', GETDATE(), 'update script', GETDATE(), 'update script'
SELECT @parentId = SCOPE_IDENTITY()
INSERT INTO [dbo].[NewsCategory] SELECT N'Medicines', @parentId, 2, 5, N'medicines', GETDATE(), 'update script', GETDATE(), 'update script'
SELECT @parentId2 = SCOPE_IDENTITY()
INSERT INTO [dbo].[NewsCategory] SELECT N'Active substances/excipients, API', @parentId2, 2, 6, N'active-substances', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Chemicals', @parentId2, 2, 7, N'chemicals', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Orphans', @parentId2, 2, 8, N'orphan', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Paediatrics', @parentId2, 2, 9, N'paediatric', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Generics', @parentId2, 2, 10, N'generics', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Hybrids', @parentId2, 2, 11, N'hybrids', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Biologicals: ATMPs/biosimilars/biotechnologies/blood products', @parentId2, 2, 12, N'biologicals', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Radiopharmaceuticals', @parentId2, 2, 13, N'radoipharmaceuticals', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Herbal medicines/phytotherapy/homeopathy', @parentId2, 2, 14, N'herbal-medicines', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Poisonous substances/drugs/psychotropics', @parentId2, 2, 15, N'poisonous-substances', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'MITM (drugs of major therapeutic interest)', @parentId2, 2, 16, 'mitm', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Medical devices / In vitro diagnostic medical devices', @parentId, 2, 17, N'medical-devices', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Cosmetics', @parentId, 2, 18, N'cosmetics', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Biocides', @parentId, 2, 19, N'biocides', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Nutrition', @parentId, 2, 20, N'nutrition', GETDATE(), 'update script', GETDATE(), 'update script'

INSERT INTO [dbo].[NewsCategory] SELECT N'Themes', null, 3, 31, N'themes', GETDATE(), 'update script', GETDATE(), 'update script'
SELECT @parentId = SCOPE_IDENTITY()

INSERT INTO [dbo].[NewsCategory] SELECT N'Legislation/Regulation', @parentId, 3, 32, N'legislation', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Pharmacovigilance and risk management', @parentId, 3, 33, N'pharmacovigilance', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Clinical trials (and GCP)', @parentId, 3, 34, N'clinical-trials', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Pharmaceutical establishments and good practice / inspections', @parentId, 3, 35, N'pharmaceutical-establishments', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Early access / compassionate use', @parentId, 3, 36, N'early-access', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Marketing authorisation - Registration', @parentId, 3, 37, N'marketing-authorisation', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Advertising', @parentId, 3, 38, N'advertising', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Product information / Labelling', @parentId, 3, 39, N'product-information', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Disease specific information', @parentId, 3, 40, N'disease-specific-information', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Import/export', @parentId, 3, 41, N'import-export', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Environment', @parentId, 3, 42, N'environment', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Intellectual property (data protection, patent, GDPR ...)', @parentId, 3, 43, N'intelectual-property', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Anti-gift Law & Transparency of links of interest', @parentId, 3, 44, N'anti-gift-law', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Reimbursement and pricing of health products', @parentId, 3, 45, N'reimbursement-and-pricing', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Fees and charges', @parentId, 3, 46, N'fees-and-charges', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Organisation of the health authority / institution', @parentId, 3, 47, N'organisation-health-authority', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Health facilities and pharmacies', @parentId, 3, 48, N'health-facilities', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Serialization', @parentId, 3, 49, N'serialization', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Quality', @parentId, 3, 50, N'quality', GETDATE(), 'update script', GETDATE(), 'update script'

INSERT INTO [dbo].[NewsCategory] SELECT N'Covid-19', @parentId, 3, 51, N'covid', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Brexit', @parentId, 3, 52, N'brexit', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Nitrosamines', @parentId, 3, 53, N'nitrosamine', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Legal precedent / Case law', @parentId, 3, 54, N'legal-precedent', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Product specific information', @parentId, 3, 55, N'product-specific-information', GETDATE(), 'update script', GETDATE(), 'update script'

INSERT INTO [dbo].[NewsCategory] SELECT N'Type of text', null, 4, 60, N'type-of-text', GETDATE(), 'update script', GETDATE(), 'update script'
SELECT @parentId = SCOPE_IDENTITY()

INSERT INTO [dbo].[NewsCategory] SELECT N'Minutes Committee / Meeting', @parentId, 4, 61, N'minutes-committee', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Guideline', @parentId, 4, 62, N'guideline', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Decision - Recommendation - Opinion from a health authority', @parentId, 4, 63, N'decision-recommendation-opinion', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Regulatory/legislative text', @parentId, 4, 64, N'regulatory-text', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Press release', @parentId, 4, 65, N'press-release', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[NewsCategory] SELECT N'Agreement', @parentId, 4, 66, N'agreement', GETDATE(), 'update script', GETDATE(), 'update script'

