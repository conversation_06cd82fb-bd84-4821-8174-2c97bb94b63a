﻿<script type="text/x-template" id="treeview-node-template">
    <div class="node">
        <div :class="['node-row', {}]">
            <div>
                <i v-if="nodeItem.children.length" :class="[{ 'icon-right-dir': !isOpen, 'icon-down-dir': isOpen }]" v-on:click.stop="isOpen=!isOpen"></i>
            </div>
            <i v-if="!nodeItem.parentId" :class="[{ 'icon-folder': !isOpen, 'icon-folder-open': isOpen }]"></i>
            <input v-else type="checkbox" :id="itemId" v-model="nodeItem.selected" v-on:change="onCheck"/>
            <div>
                <label :for="itemId">{{nodeItem.name}}</label>
            </div>
        </div>
        <div class="node-children" v-if="isOpen">
            <treeview-node @@nodeSelected="nodeSelected" v-for="(child, i) in nodeItem.children" v-model:node-item="child" :key="nodeItem.id + '.' + child.id + '.' + i"></treeview-node>
        </div>
    </div>
</script>
<script type="text/x-template" id="treeview-template">
    <div class="treeview">
        <treeview-node @@nodeSelected="processParent"  v-for="(item, ix) in items" v-model:node-item="item" :key="'M' + ix"></treeview-node>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('treeview-node', {
        template: '#treeview-node-template',
        data() {
            return {
                isOpen: !!this.nodeItem.children.length,
                itemId: `id-${this.nodeItem.id}`
            };
        },
        props: {
            nodeItem: {
                required: true,
                type: Object
            }
        },
        methods: {
            nodeSelected(data) {
                this.$emit('nodeSelected', data);
            },

            onCheck() {
                this.$emit('nodeSelected', this.nodeItem.parentId);

                if (this.nodeItem.children.length > 0) {
                    this.selectNestedChildren(this.nodeItem.children, this.nodeItem.selected);
                }
                this.$emit('change', this.nodeItem);
            },
            selectNestedChildren(array, selected) {
                if (array) {
                    array.forEach(child => {
                        this.selectNestedChildren(array.children, selected);
                        child.selected = selected;
                    });
                }
            }
        }
    });

    vueApp.component('treeview', {
        template: '#treeview-template',
        props: {
            items: {
                required: true,
                type: Array
            }
        },
        methods: {
            processParent(id) {
                this.setParentCheckValue(id);
            },
            setParentCheckValue(id) {
                if (this.items[0].children) {
                    let parentNode = this.items[0].children.find(x => x.id == id);
                    if (parentNode) {
                        if (parentNode.children.length > 0) {
                            parentNode.selected = (parentNode.children.find(x => x.selected == false)) ? false : true;
                        }
                    }
                }
            }
        },
        mounted() {
            this.items[0].children.forEach(child =>
                this.setParentCheckValue(child.id)
            );
        }      
    });
</script>