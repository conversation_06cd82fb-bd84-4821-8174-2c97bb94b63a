﻿using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using PharmaLex.Caching.Data;
using PharmaLex.Office;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface IContentItemExportWriter : IExcelWriter
    {
        byte[] Write(ContentType ct, IEnumerable<ContentItem> items);
    }

    public class ContentItemExportWriter : ExcelWriter, IContentItemExportWriter
    {
        private IDistributedCacheServiceFactory cache;

        public ContentItemExportWriter(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public byte[] Write(ContentType ct, IEnumerable<ContentItem> items)
        {
            var wb = this.CreateWorkbook();
            wb.Styles.Add("bluegrey2-header", CreateHeaderStyle(wb.Workbook, new XSSFColor(IndexedColors.White), new XSSFColor(new byte[] { (byte)109, (byte)167, (byte)213 })));
            Dictionary<string, XSSFCellStyle> styles = wb.Styles;
            var sheet = wb.Workbook.CreateSheet(ct.ShortName);
            int rowIndex = 0, cellIndex;

            IEnumerable<PicklistModel> picklists = ct.Field.Where(x => x.FieldType == FieldType.Picklist || x.FieldType == FieldType.Relationship).Select(x => new PicklistModel(x.RelatedContentTypeId.Value, cache.CreateMappedEntity<ContentItem, PicklistItemModel>().Where(y => y.ContentTypeId == x.RelatedContentTypeId)));

            IRow header = sheet.CreateRow(rowIndex++);
            header.CreateCells(0, styles["bluegrey2-header"], "Id", "Name*", "Owner*", "Verified on*");
            cellIndex = 4;
            foreach (var f in ct.Field)
            {
                header.CreateCell(cellIndex++, this.GetHeaderValue(f), styles["bluegrey2-header"]);
            }

            foreach (var ci in items)
            {
                IRow row = sheet.CreateRow(rowIndex++);
                row.CreateCells(0, styles["wrapped"], ci.Id.ToString(), ci.Name, ci.Owner, ci.VerifiedDate.ToString("yyyy-MM-dd"));
                cellIndex = 4;
                foreach (var f in ct.Field)
                {
                    row.CreateCell(cellIndex++, this.GetCellValue(f, ci.FieldValue.FirstOrDefault(x => x.FieldId == f.Id)?.Value, picklists), styles["wrapped"]);
                }
            }

            sheet.CreateFreezePane(0, 1);
            sheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, ct.Field.Count + 3));
            sheet.AutoSizeColumns(0, ct.Field.Count + 4);
            return wb.Workbook.ToByteArray();
        }

        private string GetHeaderValue(Field f)
        {
            string n = f.Name;
            if(f.Required)
            {
                n += "*";
            }
            return n;
        }

        private string GetCellValue(Field f, string v, IEnumerable<PicklistModel> picklists)
        {
            if (String.IsNullOrWhiteSpace(v))
            {
                return v;
            }
            switch (f.FieldType)
            {
                case FieldType.Picklist:
                    var pl = picklists.First(x => x.ContentTypeId == f.RelatedContentTypeId);
                    if (f.MultiSelect)
                    {
                        return String.Join(" | ", pl.Items?.Where(x => v.Split('|').Contains(x.Id.ToString()))?.Select(x => x.Name) ?? new string[0]);
                    }
                    return pl.Items.First(x => x.Id == Int32.Parse(v)).Name;
                case FieldType.Relationship: goto case FieldType.Picklist;
                default: return v;
            }            
        }
    }
}
