﻿using AutoMapper;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface INewsCategoryLicenseHelper
    {
        Task<List<LicenseNewsCategoryModel>> GetNewsCategoryGroupLicense(NewsCategoryGroup group);
        Task<List<int>> GetUserNewsCategoryGroupLicense(int userId, NewsCategoryGroup group, bool includeSubcategories = false);
        Task<List<int>> GetUserNewsCategoryLicense(int userId, bool includeSubcategories = false);
    }
    public class NewsCategoryLicenseHelper : INewsCategoryLicenseHelper
    {
        private readonly IMapper mapper;
        private readonly IDbAccessProvider dbProvider;
        IRepositoryFactory repositoryFactory;

        public NewsCategoryLicenseHelper(
            IDbAccessProvider provider,
            IMapper mapper,
            IRepositoryFactory repositoryFactory)
        {
            this.mapper = mapper;
            this.dbProvider = provider;
            this.repositoryFactory = repositoryFactory;
        }

        public async Task<List<LicenseNewsCategoryModel>> GetNewsCategoryGroupLicense(NewsCategoryGroup group)
        {
            return (await this.GetLicense())
                .Where(x => x.GroupId == (int)group)
                .ToList();
        }

        public async Task<List<int>> GetUserNewsCategoryGroupLicense(int userId, NewsCategoryGroup group, bool includeSubcategories = false)
        {
            return (await this.GetLicense(userId, includeSubcategories))
                .Where(x => x.GroupId == (int)group)
                .Select(x => x.Id).ToList();
        }

        public async Task<List<int>> GetUserNewsCategoryLicense(int userId, bool includeSubcategories = false)
        {
            return (await this.GetLicense(userId, includeSubcategories))
                .Select(x => x.Id).ToList();
        }

        private async Task<List<LicenseNewsCategoryModel>> GetLicense(int? userId = null, bool includeSubcategories = false)
        {
            var allCategories = this.mapper.Map<List<LicenseNewsCategoryModel>>(await this.dbProvider.All<NewsCategory>());
            var geographicalRootId = allCategories.First(x =>
                x.GroupId == (int)NewsCategoryGroup.GeographicalScope && !x.ParentId.HasValue).Id;
            var productRootId = allCategories.First(x =>
                x.GroupId == (int)NewsCategoryGroup.Products && !x.ParentId.HasValue).Id;
            var themesRootId = allCategories.First(x =>
                x.GroupId == (int)NewsCategoryGroup.Themes && !x.ParentId.HasValue).Id;
            var typeOfTextRootId = allCategories.First(x =>
                x.GroupId == (int)NewsCategoryGroup.TypeOfText && !x.ParentId.HasValue).Id;
            var articleLanguageRootId = allCategories.First(x =>
                x.GroupId == (int)NewsCategoryGroup.NewsletterArticleLanguage && !x.ParentId.HasValue).Id;

            CompanyUser companyUser = null;
            if (userId.HasValue)
            {
                companyUser = repositoryFactory.Create<CompanyUser>().Configure(o => o).FirstOrDefault(x => x.UserId == userId);
            }

            List<LicenseNewsCategoryModel> result;
            if (companyUser != null)
            {
                var companyNewsCategories = repositoryFactory.Create<CompanyNewsCategory>()
                    .Configure(o => o.Include(x => x.NewsCategory))
                    .Where( x => x.CompanyId == companyUser.CompanyId);
                result = this.mapper.Map<List<LicenseNewsCategoryModel>>(companyNewsCategories.Select(x => x.NewsCategory));
            }
            else
            {
                result = allCategories.Where(x => x.ParentId == geographicalRootId || x.ParentId == productRootId).ToList();
            }

            result.AddRange(allCategories.Where(x => x.ParentId == themesRootId));
            result.AddRange(allCategories.Where(x => x.ParentId == typeOfTextRootId));
            result.AddRange(allCategories.Where(x => x.ParentId == articleLanguageRootId));

            if (includeSubcategories)
            {
                int[] parentsList = result.Select(x => x.Id).ToArray();
                result.AddRange(allCategories.Where(x => x.ParentId.HasValue && parentsList.Contains(x.ParentId.Value)));
            }

            return result;
        }
    }
}
