﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using PharmaLex.NewsletterApp.Helpers;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class NewsletterRequeue
    {
        INewsletterRequeueHelper helper;

        public NewsletterRequeue(INewsletterRequeueHelper helper)
        {
            this.helper = helper;
        }

        [Function("retrydailynewsletters")]
        [QueueOutput("%qn%")]
        public async Task<NewsletterQueueMessage[]> Run([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequestData req, FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("RetryDailyNewsletters");

            var processList = await helper.ExtractSubscriptionsFromRequest(req);

            return processList.Select(x => new NewsletterQueueMessage { Id = x }).ToArray();
        }
    }
}
