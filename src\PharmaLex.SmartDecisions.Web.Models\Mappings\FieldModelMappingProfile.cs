﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class FieldModelMappingProfile : Profile
    {
        public FieldModelMappingProfile()
        {
            this.CreateMap<Field, FieldModel>()
                .ForMember(d => d.Type, s => s.MapFrom(x => x.FieldType.ToString().ToLowerInvariant()))
                .ForMember(d => d.MultiSelect, s => s.MapFrom(x => (x.FieldType == FieldType.Picklist || x.FieldType == FieldType.Relationship) && x.Length != 1));

            this.CreateMap<FieldModel, Field>()
                .ForMember(d => d.ContentType, s => s.Ignore())
                .ForMember(d => d.FieldType, s => s.Ignore())
                .ForMember(d => d.Length, s => s.MapFrom(x => x.FieldTypeId != (int)FieldType.Picklist && x.FieldTypeId != (int)FieldType.Relationship ? x.Length : x.MultiSelect ? 2 : 1))
                .ForMember(d => d.FieldValue, s => s.Ignore())
                .ForMember(d => d.CreatedDate, o => o.Ignore())
                .ForMember(d => d.CreatedBy, o => o.Ignore())
                .ForMember(d => d.LastUpdatedDate, o => o.Ignore())
                .ForMember(d => d.LastUpdatedBy, o => o.Ignore());

            this.CreateMap<FieldModel, FieldColumnModel>()
                .ForMember(d => d.SortDirection, s => s.Ignore());
        }
    }
}
