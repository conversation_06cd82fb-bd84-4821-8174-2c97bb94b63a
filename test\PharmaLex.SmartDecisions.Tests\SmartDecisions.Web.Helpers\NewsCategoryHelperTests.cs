﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using MockQueryable.NSubstitute;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using Shouldly;
using Xunit;

namespace PharmaLex.SmartDecisions.Tests.SmartDecisions.Web.Helpers
{
    public class NewsCategoryHelperTests
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IRepositoryFactory repoFactory;
        private readonly IMapper mapper;
        private readonly INewsCategoryLicenseHelper license;
        private readonly ILocalisationService localisationService;
        private readonly INewsCategoryHelper _sut;

        public NewsCategoryHelperTests()
        {
            cache = Substitute.For<IDistributedCacheServiceFactory>();
            repoFactory = Substitute.For<IRepositoryFactory>();
            mapper = Substitute.For<IMapper>();
            license = Substitute.For<INewsCategoryLicenseHelper>();
            localisationService = Substitute.For<ILocalisationService>();
            _sut = new NewsCategoryHelper(cache, repoFactory, mapper, license, localisationService);
        }

        [Fact]
        public async void InitSubscriberPreferenceGroups_LicenseForUserForGeographicalScope_ReturnsValueForGeographicalScope()
        {
            // Arrange
            var userNewsCategoryList = new List<UserNewsCategory>
            {
                new()
                {
                    UserId = 1,
                    NewsCategoryId = 1,
                    Order = 1,
                    NewsCategory = new NewsCategory
                    {
                        Id = 1,
                        LocalisationKey = "test",
                        ChildCategory = new List<NewsCategory> {new(){Id = 1, Name = "test category", ParentId = 1, SortOrder = 1, LocalisationKey = "test"}},

                    }
                }
            };
            var userNewsCategoryModelList = new List<UserNewsCategoryModel>
            {
                new()
                {
                    UserId = 1,
                    NewsCategoryId = 1,
                    Order = 1,
                }
            }.AsQueryable();

            var mock = userNewsCategoryList.BuildMock();

            var userNewsCategoryRepository = Substitute.For<IRepository<UserNewsCategory>>();
            userNewsCategoryRepository.Configure(Arg.Any<Func<IIncludable<UserNewsCategory>, IIncludable<UserNewsCategory>>>()).Returns(mock);
            repoFactory.Create<UserNewsCategory>().Returns(userNewsCategoryRepository);

            mapper.ProjectTo<UserNewsCategoryModel>(Arg.Any<IQueryable<UserNewsCategory>>()).Returns(userNewsCategoryModelList);

            license.GetUserNewsCategoryGroupLicense(1, NewsCategoryGroup.GeographicalScope, true).Returns(new List<int> { 1 });
            license.GetUserNewsCategoryGroupLicense(1, Arg.Any<NewsCategoryGroup>(), true).
                ReturnsForAnyArgs(x => x.ArgAt<NewsCategoryGroup>(1) == NewsCategoryGroup.GeographicalScope ? new List<int> { 1 } : new List<int>());

            // Act
            var result = await _sut.InitSubscriberPreferenceGroups(1);

            // Assert
            result.ShouldSatisfyAllConditions(
                () => result.Count.ShouldBe(5),
                () => result[NewsCategoryGroup.GeographicalScope].Count.ShouldBe(1),
                () => result[NewsCategoryGroup.Products].Count.ShouldBe(0),
                () => result[NewsCategoryGroup.Themes].Count.ShouldBe(0),
                () => result[NewsCategoryGroup.TypeOfText].Count.ShouldBe(0),
                () => result[NewsCategoryGroup.NewsletterArticleLanguage].Count.ShouldBe(0)
            );
        }
    }
}
