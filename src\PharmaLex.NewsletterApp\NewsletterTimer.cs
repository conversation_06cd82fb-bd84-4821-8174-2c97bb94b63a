using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;

namespace PharmaLex.NewsletterApp
{
    public class NewsletterTimer
    {
        private readonly IRepositoryFactory repoFactory;
        public NewsletterTimer(IRepositoryFactory repoFactory)
        {
            this.repoFactory = repoFactory;
        }

        [Function("NewsletterTimer")]
        [QueueOutput("%qn%")]
        public async Task<NewsletterQueueMessage[]> Run([TimerTrigger("0 0 0-23 * * *"/*, RunOnStartup = true*/)] MyInfo timer, FunctionContext context)
        {
            var logger = context.GetLogger("NewsletterTickFunction");
            logger.LogInformation($"Timer trigger function executed at: {DateTime.UtcNow}, Execution context: {context.InvocationId}");

            int day = (int)DateTime.UtcNow.DayOfWeek;

            var allSubscriptions = await this.repoFactory.Create<NewsletterSubscription>()
                .Configure(o => o
                    .Include(x => x.User)
                        .ThenInclude(x => x.CompanyUser))
                .Where(x => !x.IsMonthly && !x.IsInfoflash && x.Active &&
                    (x.User.CompanyUser == null || x.User.CompanyUser.Active) &&
                    day == x.DeliveryLocalDay)
                .ToListAsync();

            var eligibleSubscriptions = allSubscriptions.Where(x =>
                    x.DeliveryLocalHour == TimeZoneInfo
                    .ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById(x.Timezone))
                    .Hour);

            return eligibleSubscriptions.Select(x => new NewsletterQueueMessage { Id = x.Id }).ToArray();
        }
    }

    public class NewsletterQueueMessage
    {
        public int Id { get; set; }
    }

    public class MyInfo
    {
        public MyScheduleStatus ScheduleStatus { get; set; }

        public bool IsPastDue { get; set; }
    }

    public class MyScheduleStatus
    {
        public DateTime Last { get; set; }

        public DateTime Next { get; set; }

        public DateTime LastUpdated { get; set; }
    }
}
