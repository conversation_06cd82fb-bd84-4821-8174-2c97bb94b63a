﻿insert into [dbo].[MultilingualResource] select 1, '[Locale].en', 'English', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[Locale].fr', 'French', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, 'information', 'Information', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'news-source', 'News Source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'languages', 'Languages', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'language', 'Language', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'title', 'Title', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'source-url', 'Source URL', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'source-publication-date', 'Source Publication Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'date', 'Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'state', 'State', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'author', 'Author', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'reviewer', 'Reviewer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'categories', 'Categories', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'cancel', 'Cancel', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'save', 'Save', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'ok', 'Ok', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'max-field-length', 'The maximum length of this field is', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, 'select-news-source', 'Select News Source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'select-author', 'Select Author', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'select-reviewer', 'Select Reviewer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'enter-title', 'Enter Title', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'enter-source-url', 'Enter Source URL', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'publication-date', 'Publication Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-in-title', 'Search in Title', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-in-type-of-text', 'Search in type of text', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-in-themes', 'Search in themes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-in-source-url', 'Search in source url', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'no-article-found', 'No article found', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'start-date', 'Start Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'end-date', 'End Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search', 'Search', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-database', 'Search database', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'close-window', 'Close', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'product', 'Product', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'select-location', 'Please select a location', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'select-one-product', 'Please select at least one product', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-in-source', 'Search in source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'drag-drop-product-help', 'Please select the products that you would like included in your newsletter. You can arrange the order they will appear in the newsletter by dragging and dropping them into your preferred order.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'drag-drop-theme-help', 'Please select the themes that you would like included in your newsletter. You can arrange the order they will appear in the newsletter by dragging and dropping them into your preferred order.</br>If no themes are selected, you will receive all items in random order.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'update-search', 'Update Search', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'hide-search', 'Hide Search', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'search-text', 'Search Text', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'preview', 'Preview', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'confirm', 'Confirm', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


insert into [dbo].[MultilingualResource] select 1, 'terms-and-conditions', 'Terms and Conditions', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'edit-news-article', 'Edit News Article', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'created-by', 'Created by', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'publishing-state', 'Publishing State', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'details', 'Details', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'impact-assessment', 'Impact Assessment', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'clear-selection', 'Clear Selection', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'open', 'Open', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'warn-article-changed', 'Changes to the current article are not saved and may be lost. Are you sure?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'news-articles', 'News Articles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'send-review', 'Send for Review', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'approve', 'Approve', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, 'reject', 'Reject', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.newsletter-subscription', 'Newsletter Subscription', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.week-days', 'Days and Time to Receive', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.click-to-format', 'Click to ', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.remove', 'remove', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.add', 'add', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.delivery-time', 'Delivery Time', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.select-time-format', 'Select {0} time', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.newsletter-subscription-updated', 'Newsletter subscription updated', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.geographical-scope', 'Geographical Scope', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.choose', 'Select', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.products', 'Products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.themes', 'Themes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.select-one-date-time', 'Please select at least one date and time', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.fill-in-times-for-days', 'Please fill in times for all selected days', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.one-product-or-theme', 'Please select at least one product or theme', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.save-successful', 'Saved successfully.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.problem-saving-preferences', 'A problem occurred whilst saving your preferences.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).headline', 'Headline', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.select-one-product', 'Please enter at least one product', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.select-one-theme', 'Please enter at least one theme', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.select-one-location', 'Please enter at least one location', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.monthly-newsletter', 'Monthly miscellaneous newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.first-monday-16', 'First Monday @16:00', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.first-monday', 'First Monday', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.newsletters-not-sent', 'Regular newsletters will not be sent', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).subscribe.infoflash', 'Early notification for major breaking news', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).none-selected', 'None selected', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).miscellaneous', 'Miscellaneous', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).yes', 'Yes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).no', 'No', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.manage', 'Manage users', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.add', 'Add user', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.limit-reached', 'Attention! You have reached some of your license limits.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.maximum-users-limit-format', 'Maximum number of {} users has been reached. You cannot add more users.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.maximum-active-users-limit-format', 'Maximum number of {} active users has been reached. You cannot enable more users.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).infoflash', 'INFOFLASH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).infoflash-email-title', 'Knowledge Accelerated INFOFLASH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.failed-newsletters', 'Failed Newsletters', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.newsletter-type', 'Newsletter type', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.date-of-run', 'Run date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.all', 'All', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.weekly', 'Weekly', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.monthly', 'Monthly', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.status', 'Status', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.processed', 'Processed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.unprocessed', 'Unprocessed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.resubmit', 'Resubmit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.search-in-email', 'Email search', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.failures-submitted-successfully', 'Failed records resubmitted successfully', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.failures-submission-failure', 'Resubmission of failed records was not successful', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.no-failures-to-submit', 'There are no unprocessed records to resubmit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.please-select-date', 'Please select a date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).support.select-newsletter-type', 'Please select either weekly or monthly newsletter type.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).users.column-name', 'Name', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.column-active', 'Active', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.manager', 'Manager', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.license', 'License', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.given-name', 'Given name', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.family-name', 'Family name', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.resend', 'Resend', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.resend-email', 'Resend Invitation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.copy-invitation-link', 'Copy Invitation Link', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.ask-resend-email', 'Do you wish to resend the invitation email to this user?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.remove-user', 'Are you sure you want to remove user {} from company {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).users.warning', 'Warning', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).open.download', 'Download', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).edit.go-live-date', 'Date to go live', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).newsletter.from', 'Your personalized Pharmalex watch service – KNOWLEDGE. ACCELERATED. Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).newsletter.subject', 'Regulatory news not to be missed !', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).newsletter.subject-monthly', 'Don&apos;t miss the miscellaneous news of the month!', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].geographical-scope', 'Geographical Scope', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].european-union', 'European Union', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].france', 'France', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].products', 'Products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].medicines', 'Medicines', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].active-substances', 'Active substances / excipients, API', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].chemicals', 'Chemicals', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].orphan', 'Orphans', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].paediatric', 'Paediatrics', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].generics', 'Generics', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].hybrids', 'Hybrids', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].biologicals', 'Biologicals: ATMPs / biosimilars / biotechnologies / blood products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].radoipharmaceuticals', 'Radiopharmaceuticals', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].herbal-medicines', 'Herbal medicines / phytotherapy / homeopathy', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].poisonous-substances', 'Poisonous substances / drugs / psychotropics', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].mitm', 'MITM (drugs of major therapeutic interest)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].medical-devices', 'MD & IVDMD', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].cosmetics', 'Cosmetics', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].biocides', 'Biocides', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].nutrition', 'Nutrition', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].themes', 'Themes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].legislation', 'Legislation / Regulation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].pharmacovigilance', 'Pharmacovigilance and risk management', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].clinical-trials', 'Clinical trials (and GCP)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].pharmaceutical-establishments', 'Pharmaceutical establishments and good practice / inspections', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].early-access', 'Early access / compassionate use', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].marketing-authorisation', 'Marketing authorisation - Registration', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].advertising', 'Advertising', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].product-information', 'Product information / Labelling', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].disease-specific-information', 'Disease specific information', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].import-export', 'Import / export', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].environment', 'Environment', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].intelectual-property', 'Intellectual property (data protection, patent, GDPR ...)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].anti-gift-law', 'Anti-gift Law & Transparency of links of interest', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].reimbursement-and-pricing', 'Reimbursement and pricing of health products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].fees-and-charges', 'Fees and charges', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].organisation-health-authority', 'Organisation of the health authority / institution', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].health-facilities', 'Health facilities and pharmacies', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].serialization', 'Serialization', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].quality', 'Quality', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].covid', 'Covid-19', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].brexit', 'Brexit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].nitrosamine', 'Nitrosamines', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].legal-precedent', 'Legal precedent / Case law', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].product-specific-information', 'Product specific information', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].type-of-text', 'Type of text', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].minutes-committee', 'Minutes Committee / Meeting', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].guideline', 'Guideline', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].decision-recommendation-opinion', 'Decision - Recommendation - Opinion from a health authority', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].regulatory-text', 'Regulatory / legislative text', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].press-release', 'Press release', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].agreement', 'Agreement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsArticlePublishingState].draft', 'Draft', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsArticlePublishingState].pendingreview', 'Pending review', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsArticlePublishingState].approved', 'Approved', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsArticlePublishingState].rejected', 'Rejected', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsArticlePublishingState].published', 'Published', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ministry-health', 'Ministry of Health', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ceps', 'CEPS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ansm', 'ANSM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cmdh', 'CMDh', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ema', 'EMA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ema-esubmission', 'eSubmission (EMA)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ich', 'ICH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].european-instances-court', 'European Court of Justice', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].european-instances-comission', 'European Commission', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].european-instances-parliament', 'European Parliament', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].european-instances-council', 'European Council', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].european-instances-council-eu', 'Council of the EU', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].has', 'HAS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].health-insurance', 'Health Insurance', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].anses', 'Anses', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].french-parliament', 'French Parliament', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].french-parliament-assembly', 'National Assembly', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].french-parliament-senate', 'Senate', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].council-state', 'Council of State', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].snitem', 'SNITEM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cnil', 'CNIL', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cnop', 'CNOP (Order of Pharmacists)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cnom', 'CNOM (Order of Physicians)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].dgccrf', 'DGCCRF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].echa', 'ECHA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].efsa', 'EFSA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].edqm', 'EDQM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].emvo', 'EMVO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].france-mvo', 'France MVO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].encepp', 'ENCePP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].eunethta', 'EUnetHTA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].jorf', 'JORF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ojeu', 'OJEU', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].leem', 'Leem', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].gmed', 'GMED', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].synadiet', 'Synadiet', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].urssaf', 'URSSAF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsSource].national-academy-pharmacy', 'National Academy of Pharmacy', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].anfor', 'AFNOR', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].agency-biomedicine', 'Agency of Biomedicine', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ars', 'ARS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].competition-authority', 'Competition Authority', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].citeo', 'CITEO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].hma', 'HMA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].constitutional-council', 'Constitutional Council', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].council-europe', 'Council of Europe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].court-auditors', 'Court of Auditors', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].cyclamed', 'Cyclamed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].fda', 'FDA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ecdc', 'ECDC', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].efpia', 'EFPIA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].febea', 'FEBEA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].hcsp', 'HCSP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ico', 'ISO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].meddra', 'MedDRA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].mhra', 'MHRA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].ministry-ecological-transition', 'Ministry of Ecological Transition', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].france-public-health', 'France public health', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].chamber-commerce-industry', 'CCI (Chamber of Commerce and Industry)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].inpi', 'INPI', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].icmra', 'ICMRA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].inca', 'INCa', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[NewsSource].oms', 'OMS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsSource].others', 'Other', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).preferences.selected-products', 'Selected Products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.selected-themes', 'Selected Themes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.drag-drop-info', 'Drag and drop to order your selected products', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.up-to-date', 'Are your news preferences up to date?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.select-preferences', 'Select Preferences', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.none-selected', 'None selected', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.selected-preferences', 'Selected Preferences', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).preferences.dates', 'Dates', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


insert into [dbo].[MultilingualResource] select 1, '(news).search-title', 'Search in Title', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).search-source-url', 'Search in Source URL', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).search-publication-date', 'Search in Pub. Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).filter-news-source', 'Filter by News Source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).filter-publishing-state', 'Filter by Pub. State', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).filter-author', 'Filter by Author', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).filter-reviewer', 'Filter by Reviewer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).(articles).confirm-removal', 'Are you sure you want to remove', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).confirm-removal-title', 'Confirm Removal', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).removal-failed', 'The article was not removed due to an error. Please refresh the page!', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).creation-failed', 'The article was not created due to an error. Please refresh the page!', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).created', 'News article created', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).updated', 'News article updated', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).removed', 'News article removed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).confirm-approve-infoflash-title', 'Confirm Approval', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).confirm-approve-infoflash-text', 'This will send an email to all users that are subscribed to the INFOFLASH service. Do you wish to continue?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).confirm-preview-infoflash-text', 'An email will be sent to your email account. Do you wish to proceed?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).preview-email-sent', 'An Infoflash preview email has been sent to your email address. Please check your inbox', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(articles).preview-email-error', 'There was an error sending an Infoflash preview email to your email address.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).(menu).news', 'News', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(menu).news-list', 'News articles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(menu).subscription', 'Subscription', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(menu).search-articles', 'Search articles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(menu).failed-newsletters', 'Failed newsletters', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).(account).view-account', 'View account', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(account).sign-out', 'Sign out', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(account).sign-in', 'Login', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(account).reset-password', 'Reset password', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(account).login-account-format', 'Login using your {} account', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '(news).(account).login-account-unauthorized-format', 'Your account does not have access to the {} system. If you think this is an error and you should have access please contact the system administrator.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


insert into [dbo].[MultilingualResource] select 1, '[[table]].no-results-message', 'No matching records found', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].sort-by-format', 'Sort by {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].filter-by-format', 'Filter by {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].search-in-format', 'Search in {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].clear-filters', 'Clear filters', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].add-item', 'Add item', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].edit', 'Edit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].remove', 'Remove', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].no-value', '-- no value', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].new-article', 'New Article', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.first', 'First', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.previous', 'Previous', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.next', 'Next', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.last', 'Last', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.showing-format', 'Showing {} to {} of {} entries', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.showing-filtered-format', '(filtered from {})', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 1, '[[table]].pager.page-size', 'Page size', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

go
insert into [dbo].[MultilingualResource] select 2, '[Locale].en', 'Anglaise', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[Locale].fr', 'Française', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[Locale].bg', 'Bulgare', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, 'information', 'Informations', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'news-source', 'Source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'languages', 'Langues', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'language', 'Langue', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'title', 'Titre', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'source-url', 'Source de l’URL ', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'source-publication-date', 'Date de Parution Source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'date', 'Date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'state', 'Etat', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'author', 'Auteur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'reviewer', 'Relecteur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'categories', 'Catégories', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'cancel', 'Annuler', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'save', 'Enregistrer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'ok', 'Valider', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'max-field-length', 'La longueur maximale de ce champ est de', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, 'select-news-source', 'Sélectionnez une source d''actualités', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'select-author', 'Sélectionnez l''auteur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'select-reviewer', 'Sélectionner un Relecteur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'enter-title', 'Entrez le titre', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'enter-source-url', 'Saisissez l''URL source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'publication-date', 'Date de publication', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-in-title', 'Rechercher dans le titre', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-in-type-of-text', 'Recherche par type de texte', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-in-themes', 'Rechercher dans les thèmes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-in-source-url', 'Rechercher dans l''url source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'no-article-found', 'Aucun article trouvé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'start-date', 'Date de début', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'end-date', 'Date de fin', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search', 'Rechercher', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-database', 'Rechercher', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'close-window', 'Fermer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'product', 'Produit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'select-location', 'Veuillez sélectionner un emplacement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'select-one-product', 'Veuillez sélectionner au moins un produit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-in-source', 'Rechercher dans la source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'drag-drop-product-help', 'Veuillez sélectionner les produits que vous souhaitez voir figurer dans votre newsletter. Vous pouvez organiser l''ordre dans lequel ils apparaitront dans la newsletter en les faisant glisser pour les classer selon vos préférences.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'drag-drop-theme-help', 'Veuillez sélectionner les thèmes que vous souhaitez voir figurer dans votre newsletter. Vous pouvez organiser l''ordre dans lequel ils apparaitront dans la newsletter en les faisant glisser pour les classer selon vos préférences.</br>Sans sélection de votre part, vous recevrez tous les articles dans un ordre aléatoire.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'update-search', 'Modifier la recherche', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'hide-search', 'Masquer la recherche', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'search-text', 'Recherche de texte', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'preview', 'Aperçu', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'confirm', 'Confirmer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, 'terms-and-conditions', 'Termes et Conditions', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'edit-news-article', 'Modifier l''article d''actualité', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'created-by', 'Créé par', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'publishing-state', 'État de publication', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'details', 'Des détails', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'impact-assessment', 'Évaluation de l''impact', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'clear-selection', 'Effacer la sélection', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'open', 'Ouvrir', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'warn-article-changed', 'Les modifications apportées à l''article actuel ne sont pas enregistrées et peuvent être perdues. Êtes-vous sûr?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'news-articles', 'Articles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'send-review', 'Envoyer pour examen', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'approve', 'Approuver', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, 'reject', 'Rejeter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.newsletter-subscription', 'Inscription à la Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.week-days', 'Envoi le', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.click-to-format', 'Cliquez pour ', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.remove', 'supprimer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.add', 'ajouter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.delivery-time', 'Heure de livraison', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.select-time-format', 'Sélectionner l''heure dans le {0}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.newsletter-subscription-updated', 'Abonnement à la newsletter mis à jour', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).headline', 'A la une', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).yes', 'Oui', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).no', 'Non', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.manage', 'Gérer les utilisateurs', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.add', 'Ajouter un utilisateur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.limit-reached', 'Attention! Vous avez atteint certaines limites de votre licence.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.maximum-users-limit-format', 'Le nombre maximum de {} utilisateurs a été atteint. Vous ne pouvez pas ajouter d''autres utilisateurs.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.maximum-active-users-limit-format', 'Le nombre maximum de {} utilisateurs actifs a été atteint. Vous ne pouvez pas activer plus d''utilisateurs.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).infoflash', 'INFOFLASH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).infoflash-email-title', 'Knowledge Accelerated INFOFLASH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.failed-newsletters', 'Échec de la newsletters', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.newsletter-type', 'Type de newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.date-of-run', 'Date d''exécution', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.all', 'Toute', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.weekly', 'Hebdomadaire', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.monthly', 'Mensuelle', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.status', 'Statut', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.processed', 'Traité', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.unprocessed', 'Non transformé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.resubmit', 'Resoumettre', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.search-in-email', 'Recherche par e-mail', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.failures-submitted-successfully', 'Les enregistrements ayant échoué ont été soumis à nouveau avec succès', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.failures-submission-failure', 'La resoumission des enregistrements ayant échoué n''a pas abouti', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.no-failures-to-submit', 'Il n''y a pas d''enregistrements non traités à soumettre à nouveau', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.please-select-date', 'Veuillez sélectionner une date', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).support.select-newsletter-type', 'Veuillez sélectionner le type de newsletter hebdomadaire ou mensuelle.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).users.column-name', 'Nom', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.column-active', 'Actif', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.manager', 'Directeur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.license', 'Licence', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.given-name', 'Prénom', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.family-name', 'Nom de famille', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.resend', 'Renvoyer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.resend-email', 'Renvoyer l''invitation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.copy-invitation-link', 'Copier le lien d''invitation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.ask-resend-email', 'Souhaitez-vous renvoyer l''e-mail d''invitation à cet utilisateur ?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.remove-user', 'Êtes-vous sûr de vouloir supprimer l''utilisateur {} de l''entreprise {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).users.warning', 'Avertissement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).open.download', 'Télécharger', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).edit.go-live-date', 'Date de mise en ligne', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).newsletter.from', 'Votre veille personnalisée Pharmalex – KNOWLEDGE. ACCELERATED. Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).newsletter.subject', 'Les actus règlementaires à ne pas manquer !', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).newsletter.subject-monthly', 'Ne manquez pas les Faits du mois !', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].geographical-scope', 'Périmètre Géographique', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].european-union', 'Union européenne', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].france', 'France', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].products', 'Produits de santé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].medicines', 'Médicaments', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].active-substances', 'Substances actives / excipients / MPUP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].chemicals', 'Chimiques', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].orphan', 'Orphelins', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].paediatric', 'Pédiatriques', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].generics', 'Génériques', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].hybrids', 'Hybrides', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].biologicals', 'Biologiques : MTI / biosimilaires / biotechnologies / produits dérivés du sang', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].radoipharmaceuticals', 'Radiopharmaceutiques', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].herbal-medicines', 'Médicaments à base de plantes / phytothérapie / homéopathie', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].poisonous-substances', 'Substances vénéneuses / psychotropes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].mitm', 'MITM (médicaments d’intérêt thérapeutique majeur)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].medical-devices', 'DM & DMDIV', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].cosmetics', 'Cosmétiques', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].biocides', 'Biocides', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].nutrition', 'Nutrition', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].themes', 'Thèmes / sujets', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].legislation', 'Législation / règlementation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].pharmacovigilance', 'Pharmacovigilance et gestion des risques', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].clinical-trials', 'Essais cliniques et BPC', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].pharmaceutical-establishments', 'Établissements pharmaceutiques et bonnes pratiques / inspections', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].early-access', 'Accès précoce / accès compassionnel', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].marketing-authorisation', 'AMM et enregistrement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].advertising', 'Publicité', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].product-information', 'Information produit / étiquetage', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].disease-specific-information', 'Informations spécifiques sur une maladie', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].import-export', 'Importation / exportation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].environment', 'Environnement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].intelectual-property', 'Propriété intellectuelle (protection des données, brevet, RGPD)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].anti-gift-law', 'Loi anti-cadeaux et transparence des liens', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].reimbursement-and-pricing', 'Prix et prise en charge des produits de santé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].fees-and-charges', 'Redevances et taxes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].organisation-health-authority', 'Organisation des agences de santé / institutions', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].health-facilities', 'Pharmacies et établissements de santé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].serialization', 'Sérialisation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].quality', 'Qualité', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].covid', 'Covid-19', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].brexit', 'Brexit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].nitrosamine', 'Nitrosamines', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].legal-precedent', 'Jurisprudence', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].product-specific-information', 'Informations spécifiques sur un produit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].type-of-text', 'Format', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].minutes-committee', 'Compte rendu Comité / Réunion', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].guideline', 'Ligne directrice', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].decision-recommendation-opinion', 'Décision – Recommandation – Avis émanant d’une autorité sanitaire', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].regulatory-text', 'Texte règlementaire / législatif', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].press-release', 'Communiqué de presse', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].agreement', 'Accord', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticlePublishingState].draft', 'Brouillon', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticlePublishingState].pendingreview', 'Révision en attente', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticlePublishingState].approved', 'Approuvé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticlePublishingState].rejected', 'Rejeté', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticlePublishingState].published', 'Publié', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ministry-health', 'Ministère de la Santé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ceps', 'CEPS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ansm', 'ANSM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cmdh', 'CMDh', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ema', 'EMA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ema-esubmission', 'eSubmission (EMA)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ich', 'ICH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].european-instances-court', 'CJUE', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].european-instances-comission', 'Commission européenne', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].european-instances-parliament', 'Parlement européen', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].european-instances-council', 'Conseil européen', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].european-instances-council-eu', 'Conseil de l’Union européenne', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].has', 'HAS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].health-insurance', 'Assurance maladie', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].anses', 'Anses', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].french-parliament', 'Parlement français', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].french-parliament-assembly', 'Assemblée nationale', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].french-parliament-senate', 'Sénat', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].council-state', 'Conseil d’Etat', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].snitem', 'SNITEM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cnil', 'CNIL', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cnop', 'CNOP (Ordre des pharmaciens)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cnom', 'CNOM (Ordre des médecins)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].dgccrf', 'DGCCRF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].echa', 'ECHA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].efsa', 'EFSA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].edqm', 'EDQM', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].emvo', 'EMVO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].france-mvo', 'France MVO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].encepp', 'ENCePP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].eunethta', 'EUnetHTA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].jorf', 'JORF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ojeu', 'JOUE', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].leem', 'Leem', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].gmed', 'GMED', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].synadiet', 'Synadiet', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].urssaf', 'URSSAF', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsSource].national-academy-pharmacy', 'Académie nationale de pharmacie', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].anfor', 'AFNOR', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].agency-biomedicine', 'Agence de la biomédecine', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ars', 'ARS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].competition-authority', 'Autorité de la concurrence', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].citeo', 'CITEO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].hma', 'HMA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].constitutional-council', 'Conseil constitutionnel', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].council-europe', 'Conseil de l’Europe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].court-auditors', 'Cour des comptes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].cyclamed', 'Cyclamed', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].fda', 'FDA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ecdc', 'ECDC', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].efpia', 'EFPIA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].febea', 'FEBEA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].hcsp', 'HCSP', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ico', 'ISO', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].meddra', 'MedDRA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].mhra', 'MHRA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].ministry-ecological-transition', 'Ministère de la transition écologique', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].france-public-health', 'Santé publique France', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].chamber-commerce-industry', 'CCI (Chambre de commerce et d’industrie)', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].inpi', 'INPI', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].icmra', 'ICMRA', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].inca', 'INCa', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsSource].oms', 'OMS', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '[NewsSource].others', 'Autre', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.geographical-scope', 'Périmètre géographique', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.choose', 'Sélectionner', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.products', 'Produits de santé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.themes', 'Thèmes', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.select-one-date-time', 'Veuillez sélectionner au moins une date et heure', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.fill-in-times-for-days', 'Veuillez remplir les heures pour tous les jours sélectionnés', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.one-product-or-theme', 'Veuillez sélectionner au moins un produit ou thème', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.save-successful', 'Enregistré avec succès.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.problem-saving-preferences', 'Un problème est survenu lors de l''enregistrement de vos préférences.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.select-one-product', 'Veuillez saisir au moins un produit', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.select-one-theme', 'Veuillez saisir au moins un thème', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.select-one-location', 'Veuillez saisir au moins un emplacement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.monthly-newsletter', 'Bulletin mensuel divers', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.first-monday-16', 'Envoile premier lundi du mois@16:00', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.first-monday', 'Envoile premier lundi du mois', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.newsletters-not-sent', 'Les newsletters régulières ne seront pas envoyées', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).subscribe.infoflash', 'Notification relative aux informations particulièrement attendues', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).none-selected', 'Aucune sélection', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).miscellaneous', 'Divers', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).preferences.selected-products', 'Produits sélectionnés', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.selected-themes', 'Thèmes sélectionnés', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.drag-drop-info', 'Glissez-déposez pour commander vos produits sélectionnés', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.up-to-date', 'Vos préférences en matière d'' sont-elles à jour?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.select-preferences', 'Sélectionnez Préférences', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.none-selected', 'Aucune sélection', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.selected-preferences', 'Préférences sélectionnées', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).preferences.dates', 'Dates', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).search-title', 'Rechercher dans le titre', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).search-source-url', 'Rechercher dans l''URL source', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).search-publication-date', 'Rechercher dans la date de pub.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).filter-news-source', 'Filtrer par source d''actualités', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).filter-publishing-state', 'Filtrer par état de publication', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).filter-author', 'Filtrer par Auteur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).filter-reviewer', 'Filtrer par Relecteur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).(articles).confirm-removal', 'Êtes-vous sûr de vouloir supprimer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).confirm-removal-title', 'Confirmer la suppression', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).removal-failed', 'L''article n''a pas été supprimé en raison d''une erreur. Veuillez actualiser la page!', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).creation-failed', 'L''article n''a pas été créé en raison d''une erreur. Veuillez actualiser la page !', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).created', 'Article de presse créé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).updated', 'Article de presse mis à jour', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).removed', 'Article de presse supprimé', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).confirm-approve-infoflash-title', 'Confirmer l''approbation', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).confirm-approve-infoflash-text', 'Cela enverra un e-mail à tous les utilisateurs abonnés au service INFOFLASH. Souhaitez-vous continuer?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).confirm-preview-infoflash-text', 'Un e-mail sera envoyé à votre compte de messagerie. Voulez-vous continuer?', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).preview-email-sent', 'Un e-mail de prévisualisation Infoflash a été envoyé à votre adresse e-mail. Veuillez vérifier votre boîte de réception', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(articles).preview-email-error', 'Une erreur s''est produite lors de l''envoi d''un e-mail d''aperçu Infoflash à votre adresse e-mail.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 2, '(news).(menu).news', 'Nouvelles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(menu).news-list', 'Articles', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(menu).subscription', 'Abonnement', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(menu).search-articles', 'Base de données', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(menu).failed-newsletters', 'Échec de la newsletters', getdate(), '<EMAIL>', getdate(), '<EMAIL>'	

insert into [dbo].[MultilingualResource] select 2, '(news).(account).view-account', 'Voir le compte', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(account).sign-out', 'Déconnexion', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(account).sign-in', 'Connexion', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(account).reset-password', 'Changer le mot de passe', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(account).login-account-format', 'Connectez-vous avec votre compte {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).(account).login-account-unauthorized-format', 'Votre compte n''a pas accès au système {}. Si vous pensez qu''il s''agit d''une erreur et que vous devriez y avoir accès, veuillez contacter l''administrateur système.', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


insert into [dbo].[MultilingualResource] select 2, '[[table]].no-results-message', 'Aucun enregistrements correspondants trouvés', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].sort-by-format', 'Trier par {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].filter-by-format', 'Filtrer par {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].search-in-format', 'Recherche dans la {}', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].clear-filters', 'Effacer les filtres', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].add-item', 'Ajouter un item', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].edit', 'Éditer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].remove', 'Supprimer', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].no-value', '-- aucune valeur', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].new-article', 'Nouvel article', getdate(), '<EMAIL>', getdate(), '<EMAIL>'


insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.first', 'Premier', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.previous', 'Précédent', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.next', 'Suivant', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.last', 'Dernier', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.showing-format', 'Affichage de {} à {} sur {} entrées', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.showing-filtered-format', '(filtré à partir de {})', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[[table]].pager.page-size', 'Taille de la page', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

go
