﻿@model DisplayTopicViewModel
@using PharmaLex.Caching.Data
@using Microsoft.AspNetCore.Authorization
@inject IAuthorizationService AuthorizationService
@inject IDistributedCacheServiceFactory cache
@{
    Layout = null;
    var ci = Model.SelectedItem;
    //Fix for EU - should not be part of the records for display record type, only countries should be?
    var authority = Model.RegulatoryAuthorities.First(x => x.Id.ToString() == ci.GetFieldValue("Regulatory Authority"));

    bool canEdit = (await AuthorizationService.AuthorizeAsync(User, "SystemAdmin")).Succeeded || ci.Owner.ToLowerInvariant() == this.User.Identity.Name.ToLowerInvariant();
}

<div class="sub-header sticky">
    <img src="@VersionCdn.Host/images/flags/iso/flat/48/@(Model.Country.TwoLetterCode).png" width="64" onerror="this.style.display='none'" class="p-0 pr-2" />
    <h3>@Model.Country.Name - @authority.Name.Substring(5)</h3>

    <div class="controls">
        @if (canEdit)
        {
            <a class="m-icon" href="/data/edit/@ci.Id">edit</a>
        }
        <i class="m-icon" onclick="plx.dialog.close()">close</i>
    </div>
</div>
<section class="grey-5-background">

    <div class="flex p-2 mb-2 flex-justify-center flex-align-center grey-2-background white-color">
        @if (!String.IsNullOrEmpty(Model.Display.HeroTemplate))
        {
            <h3 class="topic-hero-text m-0 regular">
                @Html.Raw(Model.Display.HeroHtml)
            </h3>
        }
    </div>

    <div class="flex flex-nowrap relative">
        <div class="flex-item width-100 tile">

            <div class="flex flex-nowrap">

                @foreach (var c in Model.Display.Columns)
                {
                    var columnClass = $"flex-x{Model.Display.Columns.Count}";
                    <div class="flex-item topic-column @columnClass mb-4">
                        @foreach (var s in c.Sections.Where(x => x.HasContent))
                        {
                            <div class="topic-value-group">
                                <h3 class="brand-color mb-1">@s.Title</h3>
                                @foreach (var g in s.Groups.Where(x => x.HasContent))
                                {
                                    if (g.Fields.Count > 1 || g.IsList)
                                    {
                                        <ul>
                                            @foreach (var f in g.Fields)
                                            {
                                                <li>@f.Value.Name <strong>@Html.Raw(f.Value.Html)</strong></li>
                                            }
                                        </ul>
                                    }
                                    else
                                    {
                                        <p class="mt-2">@Html.Raw(g.Fields.First().Value.Html)</p>
                                    }
                                }
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
        <div class="flex-item flex-30-percent topic-sidebar px-2">
            <div class="flex flex-cols">
                <img src="@VersionCdn.Host/images/flags/iso/flat/32/@(Model.Markets.First(x => x.Id == authority.Market).TwoLetterCode).png" width="48" />
                <h3 class="text-color m-0">@authority.Name.Substring(5)
                @if (!String.IsNullOrEmpty(authority.Acronym))
                {
                
                @($"({authority.Acronym})")
                }</h3>
            </div>
                
            @if (!String.IsNullOrEmpty(authority.Url))
            {
                <p class="mt-1 mb-4"><a href="@authority.Url" target="_blank">@authority.Url</a></p>
            }

            @foreach (var g in Model.Display.Aside.Groups.Where(x => x.HasContent))
            {
                foreach (var f in g.Fields)
                {
                    <div class="topic-value mb-2">
                        <h4 class="mb-0">@f.Value.Name</h4>
                        <p>@Html.Raw(f.Value.Html)</p>
                    </div>
                }
            }

        </div>

        @if (Model.Items.Count() > 1)
        {
            <div class="topic-cycle-button floating-back-button" data-item-id="@Model.PreviousItemId">&lt;</div>
            <div class="topic-cycle-button floating-forward-button" data-item-id="@Model.NextItemId">&gt;</div>
        }
    </div>


    <footer class="mt-2 grey-5-background sticky py-1">
        <div class="flex justify-space-between flex-align-center">
            <small>Showing @(Model.SelectedItemIndex + 1) of @Model.Items.Count() @Model.ContentType.PluralName</small>
            <small><strong>Data owner:</strong> @ci.Owner</small>

                @if (DateTime.Now - ci.VerifiedDate < new TimeSpan(180, 0, 0, 0))
                {
                    <i class="m-icon success-color">check</i>
                }
                else if (DateTime.Now - ci.VerifiedDate < new TimeSpan(360, 0, 0, 0))
                {
                    <i class="m-icon">info</i>
                }
                else
                {
                    <div class="m-icon-group align-right">
                        <i class="m-icon warning-color small">warning</i>
                            <small>Last verified: @ci.VerifiedDate.ToString("dd MMM yyyy")</small>
                    </div>
                }
        </div>

    </footer>

</section>