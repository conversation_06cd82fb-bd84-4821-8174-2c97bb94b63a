﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities.Entities;
using System;

namespace PharmaLex.SmartDecisions.Web.Models.EntityModels
{
    public class NewsletterActivityModel : IModel
    {
        public int Id { get; set; }
        public string Company { get; set; }
        public string UserFullName { get; set; }
        public string Email { get; set; }
        public string Message { get; set; }
        public string EventType { get; set; }
        public int EventTypeId { get; set; }
        public DateTime ReceivedUTCDate { get; set; }
    }

    public class NewsletterActivityModelMappingProfile : Profile
    {
        public NewsletterActivityModelMappingProfile()
        {
            this.CreateMap<NewsletterActivity, NewsletterActivityModel>()
                .ForMember(d => d.UserFullName, s => s.MapFrom(src => src.User != null ? src.User.FullName : string.Empty))
                .ForMember(d => d.Email, s => s.MapFrom(src => src.User != null ? src.User.Email : string.Empty))
                .ForMember(d => d.Company, s => s.MapFrom(src => src.User.CompanyUser != null ? src.User.CompanyUser.Company.Name : "Not Assigned"))
                .ForMember(d => d.Message, s => s.MapFrom(src => (!string.IsNullOrEmpty(src.Response) || !string.IsNullOrEmpty(src.Reason)) ? $"{src.Response} {src.Reason}" : "None"))
                .ForMember(d => d.EventType, s => s.MapFrom(src => src.EventType.ToString()))
                .ForMember(d => d.EventTypeId, s => s.MapFrom(src => (int)src.EventType));
        }
    }
}
