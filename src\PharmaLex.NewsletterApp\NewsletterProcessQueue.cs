using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class NewsletterProcessQueue
    {
        private readonly INewsletterService newsletterService;
        public NewsletterProcessQueue(
            INewsletterService newsletterService
            )
        {
            this.newsletterService = newsletterService;
        }

        [Function("NewsletterProcessQueue")]
        public async Task Run([QueueTrigger("%qn%")] string subscriptionItem, FunctionContext context)
        {
            ILogger logger = context.GetLogger("NewsletterProcessQueue");
            logger.LogInformation($"Start processing queue message: {subscriptionItem}.");

            int subscriptionId = JsonConvert.DeserializeObject<NewsletterQueueMessage>(subscriptionItem).Id;
            logger.LogInformation($"Subscription id is: {subscriptionId}");

            if (await this.newsletterService.TryInit(subscriptionId, logger))
            {
                if (await this.newsletterService.TryBuildNewsletterModel())
                {
                    await this.newsletterService.SendNewsletter();
                }
            }
        }
    }
}
