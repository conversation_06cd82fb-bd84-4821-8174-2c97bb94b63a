﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class RegulatoryAuthorityModel : NamedContentTypeModel, IModel
    {
        public IEnumerable<int> Countries { get; set; }
        public int Market { get; set; }
        public string Acronym { get; set; }
        public string Url { get; set; }

        public RegulatoryAuthorityModel()
        {
            this.ContentTypeName = "Regulatory Authority";
        }
    }

    public class RegulatoryAuthorityModelMappingProfile : Profile
    {
        public RegulatoryAuthorityModelMappingProfile()
        {
            this.CreateMap<ContentItem, RegulatoryAuthorityModel>()
                .ForMember(d => d.Acronym, s => s.MapFrom(x => x.GetFieldValue("Acronym")))
                .ForMember(d => d.Url, s => s.MapFrom(x => x.GetFieldValue("Url")))
                .ForMember(d => d.Market, s => s.MapFrom(x => x.GetNullableIntFieldValue("Market")))
                .ForMember(d => d.Countries, s => s.MapFrom(x => x.GetIntFieldValues("Countries")));
        }
    }
}
