﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class NewsSource : EntityBase, ILocalisableLookup
    {
        public NewsSource()
        {
            NewsArticle = new HashSet<NewsArticle>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
        public int SortOrder { get; set; }
        public string LocalisationKey { get; set; }

        public virtual ICollection<NewsArticle> NewsArticle { get; set; }
    }
}
