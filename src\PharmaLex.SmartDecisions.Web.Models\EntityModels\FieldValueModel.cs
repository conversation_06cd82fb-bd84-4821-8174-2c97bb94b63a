﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class FieldValueModel : EntityModel, IModel
    {
        public int ContentItemId { get; set; }
        public int FieldId { get; set; }
        public string Value { get; set; }
        public string DisplayValue
        {
            get { return this.Value.Replace("\n", "<br>", StringComparison.InvariantCulture); }
        }
    }

    public class FieldValueModelMappingProfile : Profile
    {
        public FieldValueModelMappingProfile()
        {
            this.CreateMap<FieldValue, FieldValueModel>().ReverseMap();
        }
    }
}