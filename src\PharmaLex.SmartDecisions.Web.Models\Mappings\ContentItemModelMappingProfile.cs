﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class ContentItemModelMappingProfile : Profile
    {
        public ContentItemModelMappingProfile()
        {
            this.CreateMap<ContentItem, ContentItemModel>()
                .ForMember(d => d.Values, s => s.MapFrom(x => x.FieldValue));

            this.CreateMap<ContentItemModel, ContentItem>()
                .ForMember(d => d.FieldValue, s => s.MapFrom(x => x.Values))
                .ForMember(d => d.CreatedDate, o => o.Ignore())
                .ForMember(d => d.CreatedBy, o => o.Ignore())
                .ForMember(d => d.LastUpdatedDate, o => o.Ignore())
                .ForMember(d => d.LastUpdatedBy, o => o.Ignore());

            this.CreateMap<ContentItemModel, ContentItemModel>()
                .ForMember(d => d.VerifiedDate, s => s.Ignore())
                .ForMember(d => d.Owner, s => s.Ignore())
                .ForAllMembers(o => o.Condition((source, destination, member) => member != null));
        }
    }
}
