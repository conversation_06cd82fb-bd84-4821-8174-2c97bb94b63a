﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.SmartDecisions.Data.Extensions;

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class AddUserEmailLink : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InvitationEmailLink",
                schema: "Audit",
                table: "User_Audit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InvitationEmailLink",
                table: "User",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.SqlFileExec("06-AddUserEmailLink-01-AlterUserTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InvitationEmailLink",
                schema: "Audit",
                table: "User_Audit");

            migrationBuilder.DropColumn(
                name: "InvitationEmailLink",
                table: "User");
        }
    }
}
