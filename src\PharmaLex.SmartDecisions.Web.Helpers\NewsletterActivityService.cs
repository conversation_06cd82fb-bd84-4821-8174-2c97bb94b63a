﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.SmartDecisions.Web.Helpers.Builders;
using PharmaLex.SmartDecisions.Web.Helpers.Extensions;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using PharmaLex.SmartDecisions.Web.Models;
using PharmaLex.SmartDecisions.Web.Models.EntityModels;
using PharmaLex.SmartDecisions.Web.Models.ViewModels;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface INewsletterActivityService
    {
        Task<ApiPagedListResult<NewsletterActivityModel>> GetPagedNewsletterActivitiesAsync(int skip, int take, string[] filters, string? sort);
    }

    public class NewsletterActivityService : INewsletterActivityService
    {
        private readonly INewsletterActivityRepository newsletterActivityRepository;

        public NewsletterActivityService(INewsletterActivityRepository newsletterActivityRepository)
        {
            this.newsletterActivityRepository = newsletterActivityRepository;
        }

        public async Task<ApiPagedListResult<NewsletterActivityModel>> GetPagedNewsletterActivitiesAsync(int skip, int take, string[] filters, string? sort)
        {
            var expression = filters.Length > 0 ? ExpressionBuilder.BuildNewsletterActivity(filters) : null;
            var predicate = ExpressionBuilder.BuildSortExpression(sort);
            var query = newsletterActivityRepository.GetQueryableItems(x => x.Include(u => u.User).ThenInclude(u => u.CompanyUser));
            var entities = await query
              .FilterItems(expression, predicate, skip, take)
              .Select(x => new NewsletterActivityModel()
              {
                  Id = x.Id,
                  UserFullName = x.User.FullName,
                  Email = x.User.Email,
                  Company = x.User.CompanyUser != null ? x.User.CompanyUser.Company.Name : "Not Assigned",
                  Message = (!string.IsNullOrEmpty(x.Response) || !string.IsNullOrEmpty(x.Reason)) ? $"{x.Response} {x.Reason}" : "None",
                  ReceivedUTCDate = x.ReceivedUTCDate,
                  EventTypeId = (int)x.EventType,
                  EventType = x.EventType.ToString()
              }).ToListAsync();

            var newsletterActivities = new ApiPagedListResult<NewsletterActivityModel>(
                entities,
                new()
                {
                    TotalItemCount = query.Count(),
                    FilteredCount = expression == null ? query.Count() : query.Count(expression),
                    Offset = skip,
                    Limit = take,
                });

            return newsletterActivities;
        }
    }
}
