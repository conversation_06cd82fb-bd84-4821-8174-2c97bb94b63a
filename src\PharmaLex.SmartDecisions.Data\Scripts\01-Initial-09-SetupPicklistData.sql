﻿declare @ctid int
select @ctid = [Id] from [ContentType] where [Name] = 'Medicinal Product Domain'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Human Use',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Veterinary Use',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Human and Veterinary Use',  @ctid,  '<EMAIL>',  'Mar 23 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Procedure Type'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'National Procedure',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Centralised Procedure',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Medicinal Product Type'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Investigational',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Authorised',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Product Category'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Medicinal Product',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Vaccine',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Pesticide',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Biocide',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Feed Additive',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Care Product',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Food Stuff',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Company Responsible for Submitting the MAH Transfer'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Current MAH',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Proposed MAH',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Current and Proposed MAH',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Submission Type'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Variation',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Notification',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Transfer',  @ctid,  '<EMAIL>',  'Mar 23 2020  1:00PM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Applicable',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Yes or No'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'No',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Yes',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Applicable',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Recommended',  @ctid,  '<EMAIL>',  'Mar 27 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Should be Avoided',  @ctid,  '<EMAIL>',  'Mar 27 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Timeline Definition'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'No Official Timeline Published',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'PharmaLex Experience',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Published by Health Authority ',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Dossier Format'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'ASEAN',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'eCTD',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'NeeS',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Other',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Paper',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'VNeeS',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Sample Type'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Active Ingredient',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Inactive Ingredient (Excipient)',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Drug Product',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Applicable',  @ctid,  '<EMAIL>',  'Mar 26 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Labelling Document Details'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Annex II',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Combined',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Immediate Packaging',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Intermediate Packaging',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Other Product Information',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Outer Packaging',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Package Leaflet',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Summary of Product Characteristics',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Legalisation Requirements'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Apostile',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Legalisation',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Applicable',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Notarisation',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Translation',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
select @ctid = [Id] from [ContentType] where [Name] = 'Consequential Variation(s)'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'DDPS Variation',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Batch Control Site',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Batch Release Site',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - MAH',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Manufacturing Site',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Packager',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Distributor',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Denominator of the Medicinal Product',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - Local Representative',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Name Change - QPPV',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'New PSMF',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'New PSMF Summary',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'No Consequential Variations Required',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Unknown',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Not Specified',  @ctid,  '<EMAIL>',  'Mar 25 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select    'Change of PSMF Summary',  @ctid,  '<EMAIL>',  'Mar 27 2020 12:00AM',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'