﻿
DECLARE @parentId int
SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Geographical Scope')

INSERT INTO [dbo].[NewsCategory] SELECT N'Switzerland', @parentId, 1, 68, N'switzerland', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].switzerland', 'Switzerland', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].switzerland', 'Suisse', getdate(), '<EMAIL>', getdate(), '<EMAIL>'