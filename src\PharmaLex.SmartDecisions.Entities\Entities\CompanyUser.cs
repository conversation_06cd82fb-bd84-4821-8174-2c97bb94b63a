﻿using System;
using System.Collections.Generic;
using PharmaLex.DataAccess;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class CompanyUser : EntityBase
    {
        public CompanyUser()
        {
        }

        public int CompanyId { get; set; }
        public int UserId { get; set; }
        public bool Active { get; set; }

        public virtual Company Company { get; set; }
        public virtual User User { get; set; }
    }
}
