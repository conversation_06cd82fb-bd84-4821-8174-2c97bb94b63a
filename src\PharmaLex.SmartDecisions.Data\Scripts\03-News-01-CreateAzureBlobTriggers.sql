﻿
create trigger [dbo].[AzureBlob_Insert] on [dbo].[AzureBlob]
for insert as
insert into [Audit].[AzureBlob_Audit]
select 'I', [Id], [Name], [Uri], [ContentType], [Length], [Container], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[AzureBlob_Update] on [dbo].[AzureBlob]
for update as
insert into [Audit].[AzureBlob_Audit]
select 'U', [Id], [Name], [Uri], [ContentType], [Length], [Container], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[AzureBlob_Delete] on [dbo].[AzureBlob]
for delete as
insert into [Audit].[AzureBlob_Audit]
select 'D', [Id], [Name], [Uri], [ContentType], [Length], [Container], [CreatedDate], [CreatedBy], getdate(), coalesce(rtrim(convert(varchar(128), context_info())), suser_Name()) from [Deleted]
go