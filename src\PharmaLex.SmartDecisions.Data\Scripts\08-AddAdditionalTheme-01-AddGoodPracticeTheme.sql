﻿
DECLARE @parentId int
SET @parentId = (SELECT TOP 1 [Id] from [dbo].[NewsCategory] WHERE [Name] = 'Themes')

INSERT INTO [dbo].[NewsCategory] SELECT N'Good Laboratory Practices', @parentId, 3, 67, N'good-laboratory-practices', GETDATE(), 'update script', GETDATE(), 'update script'

insert into [dbo].[MultilingualResource] select 1, '[NewsCategory].good-laboratory-practices', 'Good Laboratory Practices', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsCategory].good-laboratory-practices', 'Bonnes pratiques de Laboratoire', getdate(), '<EMAIL>', getdate(), '<EMAIL>'