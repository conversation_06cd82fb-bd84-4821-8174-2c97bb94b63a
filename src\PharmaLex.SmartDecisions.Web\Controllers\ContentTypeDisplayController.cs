﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Models;
using PharmaLex.SmartDecisions.Web.Models.ViewModels;

namespace PharmaLex.SmartDecisions.Web.Controllers
{
    [Authorize(Policy = "SystemAdmin")]
    public class ContentTypeDisplayController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cacheFactory;
        private readonly IMapper mapper;
        private readonly IContentService contentService;

        public ContentTypeDisplayController(IDistributedCacheServiceFactory cacheFactory, IContentService contentService, IMapper mapper)
        {
            this.cacheFactory = cacheFactory;
            this.contentService = contentService;
            this.mapper = mapper;
        }

        [HttpGet("/manage/content-type-display/new/{contentTypeId}")]
        public async Task<IActionResult> New(int contentTypeId, bool ownedRecordsOnly)
        {
            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field).Include(y => y.ContentTypeDisplay));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == contentTypeId);
            var fields = ctm.Field.Select(f => new { id = f.Id, name = f.Name });

            var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue));

            var items = (await cic.WhereAsync(x => x.ContentTypeId == ctm.Id && (!ownedRecordsOnly || x.Owner.ToLower() == this.User.Identity.Name.ToLower()))).Select(x =>
            {
                x.ContentType = ctm;
                x.Values = x.Values.Where(y => fields.Select(f => f.id).Contains(y.FieldId));
                return x;
            });

            var contentTypeRecords = items.Select(x =>
            {
                var pl = x.ContentType.Field.FirstOrDefault(y => y.Name == "Regulatory Authority");
                var fvm = x.Values.FirstOrDefault(y => y.FieldId == pl.Id);

                var plc = cacheFactory.CreateMappedEntity<ContentItem, PicklistItemModel>();

                fvm.Value = plc.FirstOrDefault(y => y.ContentTypeId == pl.RelatedContentTypeId && y.Id == Int32.Parse(fvm.Value))?.Name;

                return new ContentTypeViewRecordModel() { Id = fvm.ContentItemId, Value = fvm.Value + " (" + x.Name.Substring(x.Name.LastIndexOf('-') + 1, x.Name.Length - x.Name.LastIndexOf('-') - 1) + ")" };
            }).ToList();

            var ctr = new ContentTypeDisplayModel
            {
                ContentTypeViewRecordModels = contentTypeRecords,

                ContentTypeViewFieldModels = ctm.Field.Select(f => new ContentTypeViewFieldModel
                {
                    Id = f.Id,
                    Name = f.Name,
                    IsSystem = f.System,
                    IsPicklist = f.FieldTypeId == (int)FieldType.Picklist
                }).ToList(),

                Name = ctm.Name,
                ContentTypeId = contentTypeId,
                IsNew = true
            };

            return View("EditContentTypeDisplay", ctr);
        }

        [HttpPost("/manage/content-type-display/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New([FromBody][Bind("ContentTypeId,ContentTypeName,ContentTypeDisplayTypeId,DisplayType,Json,IsNew")] ContentTypeDisplayModel dm)
        {
            var ctdc = cacheFactory.CreateTrackedEntity<ContentTypeDisplay>();
            ContentTypeDisplay ctd = mapper.Map<ContentTypeDisplay>(dm);
            ctdc.Add(ctd);
            await ctdc.SaveChangesAsync();

            this.AddConfirmationNotification($"Saved successfully", new NotificationOptions() { UseIcons = true });

            return Json(mapper.Map<ContentTypeDisplayModel>(ctd));
        }

        [HttpGet("/manage/content-type-display/edit/{id}")]
        public async Task<IActionResult> Edit(int id, bool ownedRecordsOnly = false)
        {
            var ctdc = cacheFactory.CreateMappedEntity<ContentTypeDisplay, ContentTypeDisplayModel>().Configure(x => x.Include(y => y.ContentType));
            var ctr = await ctdc.FirstOrDefaultAsync(x => x.Id == id);
            var ctc = cacheFactory.CreateMappedEntity<ContentType, ContentTypeModel>().Configure(x => x.Include(y => y.Field).Include(y => y.ContentTypeDisplay));
            var ctm = await ctc.FirstOrDefaultAsync(x => x.Id == ctr.ContentTypeId);
            var fields = ctm.Field.Select(f => new { id = f.Id, name = f.Name });
            var cic = cacheFactory.CreateMappedEntity<ContentItem, ContentItemModel>().Configure(x => x.Include(y => y.FieldValue));

            var items = (await cic.WhereAsync(x => x.ContentTypeId == ctm.Id && (!ownedRecordsOnly || x.Owner.ToLower() == this.User.Identity.Name.ToLower()))).Select(x =>
            {
                x.ContentType = ctm;
                x.Values = x.Values.Where(y => fields.Select(f => f.id).Contains(y.FieldId));
                return x;
            });

           

            var contentTypeRecords = items.Select(x =>
            {
                var pl = x.ContentType.Field.FirstOrDefault(y => y.Name == "Regulatory Authority");
                var fvm = x.Values.FirstOrDefault(y => y.FieldId == pl.Id);

                var plc = cacheFactory.CreateMappedEntity<ContentItem, PicklistItemModel>();

                fvm.Value = plc.FirstOrDefault(y => y.ContentTypeId == pl.RelatedContentTypeId && y.Id == Int32.Parse(fvm.Value))?.Name;

                return new ContentTypeViewRecordModel() { Id = fvm.ContentItemId, Value = fvm.Value + " (" + x.Name.Substring(x.Name.LastIndexOf('-') + 1, x.Name.Length - x.Name.LastIndexOf('-') - 1) + ")" };
            }).ToList();

            contentTypeRecords = contentTypeRecords.Where(x =>
            {
                var plc = cacheFactory.CreateMappedEntity<ContentItem, PicklistItemModel>();
                var twoLetterCountryCode = x.Value.Substring(0, 2);

                var country = contentService.ListItemsByContentType<CountryModel>(x => x.TwoLetterCode == twoLetterCountryCode).Result.FirstOrDefault();
                if (country == null)
                {
                    return false;
                }
                var authorities = contentService.ListItemsByContentType<RegulatoryAuthorityModel>(x => x.Countries.Contains(country.Id)).Result;

                return authorities.Any();
            }).ToList();

            ctr.ContentTypeViewRecordModels = contentTypeRecords;

            ctr.ContentTypeViewFieldModels = ctm.Field.Select(f => new ContentTypeViewFieldModel
            {
                Id = f.Id,
                Name = f.Name,
                IsSystem = f.System,
                IsPicklist = f.FieldTypeId == (int)FieldType.Picklist
            }).ToList();

            return View("EditContentTypeDisplay", ctr);
        }

        [HttpPost("/manage/content-type-display/edit")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit([FromBody] ContentTypeDisplayModel dm)
        {
            var ctdc = cacheFactory.CreateTrackedEntity<ContentTypeDisplay>();
            ContentTypeDisplay ctd = await ctdc.FirstOrDefaultAsync(x => x.Id == dm.Id);
            mapper.Map(dm, ctd);
            await ctdc.SaveChangesAsync();
            return Redirect($"/manage/content-type/edit/{dm.ContentTypeId}");
        }

        [HttpGet("/manage/content-type-display/delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var ctdc = cacheFactory.CreateMappedEntity<ContentTypeDisplay, ContentTypeDisplayModel>().Configure(x => x.Include(y => y.ContentType));
            return View("DeleteContentTypeDisplay", await ctdc.FirstOrDefaultAsync(x => x.Id == id));
        }

        [HttpPost("/manage/content-type-display/delete/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            var ctdc = cacheFactory.CreateTrackedEntity<ContentTypeDisplay>();
            ContentTypeDisplay ctd = await ctdc.FirstOrDefaultAsync(x => x.Id == id);
            ctdc.Remove(ctd);
            await ctdc.SaveChangesAsync();
            this.AddConfirmationNotification($"Deleted successfully", new NotificationOptions() { UseIcons = true });
            return Redirect($"/manage/content-type/edit/{ctd.ContentTypeId}");
        }
    }
}