﻿<script type="text/x-template" id="autocomplete-template">
    <div class="autocomplete"
         @@keydown.enter.prevent="onEnter">
        <input type="text" :placeholder="config.placeholder"
               @@input="onChange"
               v-model="search"
               ref="write"
               @@keydown.down.prevent="onArrowDown"
               @@keydown.up.prevent="onArrowUp" />
        <ul v-show="isOpen && search"
            class="autocomplete-items">
            <li v-for="(fi, i) in filteredItems" :title="fi.name"
                :key="i"
                @@click="onClick(i)"
                :class="['paragraph',{ 'is-active': i === arrowCounter }, 'autocomplete-item']">
                {{ fi.name }}
            </li>
        </ul>
    </div>
</script>

<script type="text/x-template" id="autocomplete-list-template">
    <div>
        <ul :class="['autocomplete-list', {'selectable':config.selectable}]" v-if="assignedItems.length">
            <li v-for="(p, i) in assignedItems" v-on:click="clicked(p)" :class="['flex justify-space-between flex-align-center',{'selected': p === selectedItem}]">
                <div class="paragraph" :title="p.name">{{p.name}}</div>
                <i v-if="active" class="m-icon ml-auto"
                   v-on:click.stop="deleteItem(i)"
                   title="Remove">delete</i>
            </li>
        </ul>
        <div v-else :class="['autocomplete-list empty-msg', {'validation-error': !valid}]">
            {{emptyMessage}}
        </div>
        <autocomplete v-if="active" :items="unassignedItems" :config="autocompleteConfig" v-on:selected="addItem"></autocomplete>
        <template v-for="(item, index) in assignedItems">
            <input :name="modelProperty + '[' + index + '].Id'" type="hidden" :value="item.id" />
            <input :name="modelProperty + '[' + index + '].Name'" type="hidden" :value="item.name" />
        </template>
    </div>
</script>
<script src="/js/vue/autocomplete-list.js" asp-append-version="true"></script>
