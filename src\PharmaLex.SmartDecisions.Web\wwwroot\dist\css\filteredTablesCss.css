/*Legacy support for filtered table*/
/*Units*/
/*Colours*/
/*Fonts*/
/*Space*/
tr.selectable:hover td {
  background: #E7F7FF;
  cursor: pointer; }

.table-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  position: relative;
  color: #3B3B3B; }
  .table-header .sorter {
    -ms-flex-preferred-size: 1.25rem;
        flex-basis: 1.25rem;
    width: 1.25rem;
    position: relative;
    margin-right: 0.5rem; }
    .table-header .sorter:before, .table-header .sorter:after {
      content: '';
      display: block;
      position: absolute;
      width: 5px;
      height: 5px;
      right: 0;
      top: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent; }
    .table-header .sorter:before {
      border-bottom-width: 6px;
      border-bottom-style: solid;
      border-bottom-color: #3B3B3B; }
    .table-header .sorter:after {
      top: 8px;
      border-top-width: 6px;
      border-top-style: solid;
      border-top-color: #3B3B3B; }
    .table-header .sorter.active.sorting_asc:before {
      border-bottom-color: #0073BE; }
    .table-header .sorter.active.sorting_desc:after {
      border-top-color: #0073BE; }
    .table-header .sorter.sorting_desc:before {
      -webkit-transform: rotate(0);
              transform: rotate(0); }
  .table-header .header-text {
    text-overflow: ellipsis;
    overflow: hidden;
    margin-right: 0.5rem; }
  .table-header i {
    -ms-flex-negative: 0;
        flex-shrink: 0; }

.no-records-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 1rem;
  font-size: .85rem; }

.pager {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 1;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  margin: .5rem 0;
  font-size: .85rem;
  background: white;
  border-top: 2px solid #E8E8E8; }
  .pager > * {
    padding: .5rem 1rem; }
  .pager .page-size {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 14rem;
            flex: 0 0 14rem; }
    .pager .page-size span {
      text-align: right;
      padding-right: .5rem; }
    .pager .page-size select {
      padding: .5rem;
      max-width: 75px;
      background: #fff;
      border-color: #8A8A8A; }
  .pager .pagination {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    max-width: 50%; }
    .pager .pagination-arrows-start {
      display: inline-block; }
    .pager .pagination-arrows-end {
      display: inline-block;
      margin-left: auto; }
    .pager .pagination-pages-container {
      margin-bottom: 0.25rem; }
    .pager .pagination a {
      display: inline-block;
      margin-left: .25rem;
      margin-right: .25rem; }
      .pager .pagination a:last-of-type {
        margin-right: 0; }
      .pager .pagination a.first, .pager .pagination a.previous, .pager .pagination a.next, .pager .pagination a.last {
        color: #1E1E1E; }
      .pager .pagination a.current {
        font-weight: 700;
        font-size: 1rem;
        margin: 0.5rem; }
      .pager .pagination a.disabled, .pager .pagination a[disabled] {
        opacity: 0.5;
        color: #3B3B3B;
        pointer-events: none; }

th .centred-cell {
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center; }

td.centred-cell {
  text-align: center; }

th .left-cell {
  text-align: left;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start; }

td.left-cell {
  text-align: left; }

th .right-cell {
  text-align: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end; }

td.right-cell {
  text-align: right; }

.table-filter {
  -ms-flex-preferred-size: 1rem;
      flex-basis: 1rem; }
  .table-filter.search {
    padding-right: .25rem; }
    .table-filter.search:before {
      position: relative;
      top: -1px;
      left: -1px;
      content: '\e8b6';
      color: #3B3B3B; }
    .table-filter.search.active:before {
      color: #461E96; }

table td {
  word-break: break-word; }

div {
  font-size: .85rem; }

.table-filter-items {
  position: absolute;
  left: 0;
  top: 1rem;
  z-index: 9;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  padding: .25rem;
  background: #E8E8E8;
  -webkit-box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.2);
  border-radius: .75rem;
  border: 1px solid #CACACA; }
  .table-filter-items input {
    background: #fff;
    padding: .5rem;
    border-color: #CACACA; }
    .table-filter-items input[type=search] {
      padding-right: 1rem;
      width: max(150px, min(200px, 250px)); }
  .table-filter-items li {
    background: #fff;
    white-space: nowrap;
    padding: .5rem;
    border-radius: .5rem;
    margin-bottom: .25rem;
    -webkit-transition: all .3s;
    transition: all .3s;
    font-weight: normal; }
    .table-filter-items li.is-active {
      background: #461E96;
      color: #fff; }
    .table-filter-items li:hover {
      background: #8A8A8A;
      color: #fff;
      -webkit-transition: all .3s;
      transition: all .3s;
      cursor: pointer; }
  .table-filter-items .m-icon.close {
    position: absolute;
    right: .75rem;
    top: 11px;
    color: #E22F00;
    font-size: 1.25rem !important;
    padding: .5rem;
    background: #fff; }


/*# sourceMappingURL=filteredTablesCss.css.map*/