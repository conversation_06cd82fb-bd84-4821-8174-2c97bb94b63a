﻿@import 'variables';

table {
    border-collapse: separate;

    thead {
        background: white;
    }

    th, td {
        font-size: .75rem;        
    }

    tbody tr:nth-child(odd) {
        background: $grey-5;
    }

    &.bordered {
        border: 1px solid $grey-2;

        th, td {
            border-collapse: collapse;
            border: 1px solid $grey-2;
        }
    }

    th.actions {

        & > div {
            i.m-icon {
                &.clear {
                    &.active {
						font-weight: inherit;
                    }
                }
            }
        }
    }
}
.table-header .sorter:before, .table-header .sorter.sorting_asc:before {
    transform: none;
}
