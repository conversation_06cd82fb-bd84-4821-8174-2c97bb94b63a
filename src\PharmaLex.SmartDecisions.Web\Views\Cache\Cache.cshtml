﻿@model IEnumerable<CacheViewModel>
@{
    ViewData["Title"] = "Cache";
}

<div id="cache">
    <div class="sub-header">
        <h2>Cache</h2>
        <div class="controls">
            <form id="deleteForm" method="post" action="/cache/flush">
                <input type="submit" value="Flush All Cached Data" class="button" />
            </form>
        </div>
    </div>
    <filtered-table :items="cacheItems"
                    :columns="columns"
                    :filters="filters"
                    :link="link"
                    :resources="resources">
    </filtered-table>
</div>
@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#cache',
            data: function () {
                return {
                    link: '/cache/edit/',
                    cacheItems: @Html.Raw(Json.Serialize(Model)),
                    resources: {
                        noRecordsMessage: '@ls.LocaliseSafe("[[table]].no-results-message")',
                        sortByFormat: '@ls.LocaliseSafe("[[table]].sort-by-format")',
                        filterByFormat: '@ls.LocaliseSafe("[[table]].filter-by-format")',
                        searchInFormat: '@ls.LocaliseSafe("[[table]].search-in-format")',
                        clearFilters: '@ls.LocaliseSafe("[[table]].clear-filters")',
                        addItem: '@ls.LocaliseSafe("[[table]].add-item")',
                        noValue: '@ls.LocaliseSafe("[[table]].no-value")',
                        pager: {
                            showingFormat: '@ls.LocaliseSafe("[[table]].pager.showing-format")',
                            showingFilteredFormat: '@ls.LocaliseSafe("[[table]].pager.showing-filtered-format")',
                            pageSize: '@ls.LocaliseSafe("[[table]].pager.page-size")',
                            first: '@ls.LocaliseSafe("[[table]].pager.first")',
                            previous: '@ls.LocaliseSafe("[[table]].pager.previous")',
                            next: '@ls.LocaliseSafe("[[table]].pager.next")',
                            last: '@ls.LocaliseSafe("[[table]].pager.last")',
                        }
                    },
                    columns: {
                        idKey: 'value',
                        config: [
                            {
                                dataKey: 'value',
                                sortKey: 'value',
                                header: 'Dependency',
                                type: 'text',
                            },
                            {
                                dataKey: 'key',
                                sortKey: 'key',
                                header: 'Key',
                                type: 'text',
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'value',
                            options: [],
                            type: 'search',
                            header: 'Search Key',
                            fn: v => p => p.value.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV2" />
}