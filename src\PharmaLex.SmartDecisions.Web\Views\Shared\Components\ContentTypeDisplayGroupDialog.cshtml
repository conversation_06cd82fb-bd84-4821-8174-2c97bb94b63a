﻿<script type="text/x-template" id="content-type-display-group-template">
    <div class="modal-mask" v-on:click="close">
        <div class="modal-wrapper" v-on:click.stop>
            <div class="modal-container flex columns justify-space-between group-section-container">
                <div class="p-1 m-1">{{ dialogData.isAddNew ? 'Create New' : 'Edit'}}</div>
                <div class="white-background pb-4">
                    <div class="form-group p-2" v-if="!dialogData.isAside">
                        <label for="SectionTitle1">Title</label>
                        <input name="SectionTitle1" type="text" autofocus="autofocus" id="SectionTitle1" v-model="dialogData.title" class="white-background"/>
                    </div>
                    <div>
                        <label for="SectionTitle1" class="ml-2">Groups</label>
                        <div>
                            <div v-for="(group, groupIndex) in dialogData.groups" :key="'g_' + groupIndex" class="flex columns blue-background p-1 pt-2 m-1 bordered rounded-large">
                                <div class="flex justify-end">
                                    <i v-show="dialogData.groups.length !== 1" class="m-icon pb-1 pr-1" v-on:click="removeGroup(groupIndex)">close</i>
                                </div>
                                <div v-for="(field, fieldindex) in group.fields" :key="fieldindex" class="quality-background bordered rounded-large lozenge p-1 mr-1 ml-1 justify-space-between mb-1">
                                    <span class="pt-1 pb-1">{{fields.find(x => x.id === field).name}}</span>
                                    <div><i class="m-icon" v-on:click="removeFilter(groupIndex, fieldindex, field)">close</i></div>
                                </div>
                                <select
                                    name="DisplayType" type="dropdown"
                                    autofocus="autofocus"
                                    id="DisplayType"
                                    data-val="true"
                                    data-val-required="Display type is required"
                                    v-if="group.isAdding"
                                    @@change="onAddGroupSectionItem(groupIndex)"
                                    class="white-background"
                                    v-model="selected">
                                    <option>Select an item</option>
                                    <option v-for="field in displayFields" :value="field.id">{{field.name}}</option>
                                </select>
                                <div class="flex justify-space-between flex-align-center">
                                    <div>
                                        <div v-if="group.fields && group.fields.length === 1">
                                            <input type="checkbox" v-model="group.isList" :id="'idg_' + groupIndex"/>
                                            <label :for="'idg_' + groupIndex">Display group as a list</label>
                                        </div>
                                    </div>
                                    <div class="lozenge-list">
                                        <button type="button" v-on:click="openGroupItemSelect(groupIndex)" class="quality-background mb-1 mr-1">+</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="lozenge-list" v-if="!dialogData.isAside">
                            <button type="button" v-on:click="addGroup">+</button>
                        </div>
                    </div>
                </div>

                <div class="controls">
                    <button type="button" class="button" v-on:click="add" v-if="dialogData.isAddNew" :disabled="disableAddSaveButton()">Add</button>
                    <button type="button" class="button" v-on:click="save" :disabled="disableAddSaveButton()" v-else>Save</button>
                    <button type="button" class="button secondary" v-on:click="close">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">

    vueApp.component('content-type-display-group-dialog', {
        template: '#content-type-display-group-template',
        data: function() {

            let dialogData = {};
            let displayFields;

            if(this.sectiondata.isAside) {
                displayFields = [...this.uniquesystemfields];

                if(!this.sectiondata.groups.length) {
                    dialogData.groups = [{isAdding: true}];
                }
            } else {
                displayFields = [...this.uniquefields];
            }

            if(this.sectiondata.isAddNew) {
                dialogData.title = '';
                dialogData.groups = [{isAdding: true}];
            }
            else {
                dialogData.title = this.sectiondata.title;
                dialogData.groups = this.sectiondata.groups.map(g => ({fields: [...g.fields], isList: g.isList}));

                if(!this.sectiondata.groups.length) {
                    dialogData.groups = [{isAdding: true}];
                }
            }

            dialogData.isAside = this.sectiondata.isAside;
            dialogData.isAddNew = this.sectiondata.isAddNew;
            dialogData.columnIndex = this.sectiondata.columnIndex;
            dialogData.rowIndex = this.sectiondata.rowIndex;

            return {
                selected: 'Select an item',
                dialogData: dialogData,
                displayFields: displayFields,
                modifiedFields: []
            }
        },
        props: {
            sectiondata: {
                type: Object,
                required: true
            },
            uniquefields: {
                type: Array,
                required: false
            },
            fields: {
                type: Array,
                required: false
            },
            systemfields: {
                type: Array,
                required: false
            },
            uniquesystemfields: {
                type: Array,
                required: false
            }
        },
        methods: {
            close(){
                this.$emit("content-type-display-group-close", false);
            },
            add() {

                if(!this.dialogData.groups.filter(g => g.fields && g.fields.length).length) {
                    this.$emit("content-type-display-group-close", false);
                    return;
                }

                let sectionGroup = {};
                sectionGroup.columnIndex = this.sectiondata.columnIndex;
                sectionGroup.groups = this.dialogData.groups.filter(g => g.fields && g.fields.length).map(({isAdding, ...item}) => item);
                sectionGroup.title = this.dialogData.title;

                for(let f of this.modifiedFields) {
                    this.$emit('on-add-field', f, false);
                }

                this.$emit("add-section-group", sectionGroup);
                this.$emit("content-type-display-group-close", false);
            },
            removeFilter(groupIndex, fieldindex, fieldId) {
                this.dialogData.groups[groupIndex].fields.splice(fieldindex, 1);
                this.dialogData.groups[groupIndex].isAdding = true;
                
                this.displayFields.push(this.fields.find(x => x.id === fieldId));
                this.displayFields.sort((a, b) => a.name.localeCompare(b.name));

                this.modifiedFields.push({id: fieldId, isRemoved: true});

            },
            removeGroup(groupIndex) {

                for(let fieldId of this.dialogData.groups[groupIndex].fields || []) {
                    this.displayFields.push(this.fields.find(x => x.id === fieldId));
                    this.displayFields.sort((a, b) => a.name.localeCompare(b.name));
                    
                    this.modifiedFields.push({id: fieldId, isRemoved: true});
                }

                this.dialogData.groups.splice(groupIndex, 1);

                if(!this.dialogData.groups.length) {
                    this.dialogData.groups.push({isAdding : true});
                }
            },
            save() {
                let data = {groups: this.dialogData.groups.filter(g => g.fields && g.fields.length).map(({isAdding, ...item}) => item), title: this.dialogData.title};
                data.rowIndex = this.sectiondata.rowIndex;
                data.columnIndex = this.sectiondata.columnIndex;
                data.isAside = this.sectiondata.isAside;

                for(let f of this.modifiedFields) {
                    this.$emit('on-add-field', f, data.isAside);
                }

                this.$emit("save-section-group", data);
                this.$emit("content-type-display-group-close", false);
            },
            onAddGroupSectionItem(groupIndex) {
                if(!this.dialogData.groups[groupIndex].fields) {
                    this.dialogData.groups[groupIndex].fields = [];
                }

                this.dialogData.groups[groupIndex].isAdding = false;
                this.dialogData.groups[groupIndex].fields.push(this.fields.find(x => x.id === this.selected).id);
                this.displayFields.splice(this.displayFields.findIndex(x => x.id === this.selected), 1);
                
                this.modifiedFields.push({id: this.selected, isRemoved: false});

                this.selected = 'Select an item';
            },
            openGroupItemSelect(groupIndex) {
                let newGroup = this.dialogData.groups[groupIndex];
                newGroup.isAdding = true;
                this.dialogData.groups.splice(groupIndex, 1, newGroup);
            },
            addGroup() {
                this.dialogData.groups.push({isAdding: true});
            },
            disableAddSaveButton() {
                if (this.dialogData.isAside){
                    return !this.dialogData.groups.filter(g => g.fields && g.fields.length).length;
                }

                return !this.dialogData.groups.filter(g => g.fields && g.fields.length).length || !this.dialogData.title.trim().length;
            }
        }
    });
</script>