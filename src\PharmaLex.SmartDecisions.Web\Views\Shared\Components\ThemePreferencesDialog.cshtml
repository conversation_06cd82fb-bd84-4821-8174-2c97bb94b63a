﻿<script type="text/x-template" id="theme-preferences-template">

    <div class="modal-mask" v-if="showdialog" v-on:click="close">
        <div class="modal-wrapper" v-on:click.stop>
            <div class="modal-container">
                <div class="modal-header">
                    <h3></h3>
                    <i class="m-icon" v-on:click="close">close</i>
                </div>
                <div class="modal-body">
                    <treeview :items="preferencedata" class="select-preferences-treeview"></treeview>
                </div>

                <div class="buttons">
                    <a class="button secondary" v-on:click="close">@ls.Localise("cancel")</a>
                    <a id="icon-button-save" class="button" v-on:click="save">@ls.Localise("ok")</a>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('theme-preferences-dialog', {
        template: '#theme-preferences-template',
        data: function () {
            return {
            }
        },
        props: {
            showdialog: {
                type: Boolean,
                required: true,
                default: false
            },
            preferencedata: {
                type: Array,
                required: true
            }
        },
        methods: {
            close() {
                this.$emit("theme-preferences-closing", false)
            },
            save() {
                this.$emit("themes-updated", { preferencedata: this.preferencedata });               
            },
        }
    });
</script>