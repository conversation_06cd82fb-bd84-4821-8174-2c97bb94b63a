﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.SmartDecisions.Entities;

namespace PharmaLex.SmartDecisions.Web.Models.Mappings
{
    public class PicklistModelMappingProfile : Profile
    {
        public PicklistModelMappingProfile()
        {
            this.CreateMap<FieldModel, PicklistModel>()
                .ForMember(d => d.ContentTypeId, s => s.MapFrom(x => x.RelatedContentTypeId))
                .ForMember(d => d.Items, o => o.Ignore());

            this.CreateMap<ContentType, PicklistItemModel>();
            this.CreateMap<ContentItem, PicklistItemModel>();

            this.CreateMap<ContentTypeCategory, PicklistItemModel>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));

            this.CreateMap<FieldType, PicklistItemModel>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));

            this.CreateMap<ContentTypeDisplayType, PicklistItemModel>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }
}
