/**
 * vuex v3.1.1
 * (c) 2019 Evan You
 * @license MIT
 */
(function(n,t){typeof exports=="object"&&typeof module!="undefined"?module.exports=t():typeof define=="function"&&define.amd?define(t):(n=n||self,n.Vuex=t())})(this,function(){"use strict";function et(n){function t(){var n=this.$options;n.store?this.$store=typeof n.store=="function"?n.store():n.store:n.parent&&n.parent.$store&&(this.$store=n.parent.$store)}var r=Number(n.version.split(".")[0]),i;r>=2?n.mixin({beforeCreate:t}):(i=n.prototype._init,n.prototype._init=function(n){n===void 0&&(n={});n.init=n.init?[t].concat(n.init):t;i.call(this,n)})}function st(n){if(e){n._devtoolHook=e;e.emit("vuex:init",n);e.on("vuex:travel-to-state",function(t){n.replaceState(t)});n.subscribe(function(n,t){e.emit("vuex:mutation",n,t)})}}function u(n,t){Object.keys(n).forEach(function(i){return t(n[i],i)})}function ht(n){return n!==null&&typeof n=="object"}function ct(n){return n&&typeof n.then=="function"}function i(n,t){if(!n)throw new Error("[vuex] "+t);}function lt(n,t){return function(){return n(t)}}function w(n,t,i){if(d(n,i),t.update(i),i.modules)for(var r in i.modules){if(!t.getChild(r)){console.warn("[vuex] trying to add a new module '"+r+"' on hot reloading, manual reload is needed");return}w(n.concat(r),t.getChild(r),i.modules[r])}}function d(n,t){Object.keys(k).forEach(function(r){if(t[r]){var f=k[r];u(t[r],function(t,u){i(f.assert(t),vt(n,r,u,t,f.expected))})}})}function vt(n,t,i,r,u){var f=t+" should be "+u+' but "'+t+"."+i+'"';return n.length>0&&(f+=' in module "'+n.join(".")+'"'),f+(" is "+JSON.stringify(r)+".")}function g(n,t){return t.indexOf(n)<0&&t.push(n),function(){var i=t.indexOf(n);i>-1&&t.splice(i,1)}}function nt(n,t){n._actions=Object.create(null);n._mutations=Object.create(null);n._wrappedGetters=Object.create(null);n._modulesNamespaceMap=Object.create(null);var i=n.state;s(n,i,[],n._modules.root,!0);v(n,i,t)}function v(t,i,r){var f=t._vm,o,e,s;t.getters={};o=t._wrappedGetters;e={};u(o,function(n,i){e[i]=lt(n,t);Object.defineProperty(t.getters,i,{get:function(){return t._vm[i]},enumerable:!0})});s=n.config.silent;n.config.silent=!0;t._vm=new n({data:{$$state:i},computed:e});n.config.silent=s;t.strict&&dt(t);f&&(r&&t._withCommit(function(){f._data.$$state=null}),n.nextTick(function(){return f.$destroy()}))}function s(t,i,r,u,f){var l=!r.length,e=t._modules.getNamespace(r),h,c,o;u.namespaced&&(t._modulesNamespaceMap[e]=u);l||f||(h=y(i,r.slice(0,-1)),c=r[r.length-1],t._withCommit(function(){n.set(h,c,u.state)}));o=u.context=yt(t,e,r);u.forEachMutation(function(n,i){var r=e+i;wt(t,r,n,o)});u.forEachAction(function(n,i){var r=n.root?i:e+i,u=n.handler||n;bt(t,r,u,o)});u.forEachGetter(function(n,i){var r=e+i;kt(t,r,n,o)});u.forEachChild(function(n,u){s(t,i,r.concat(u),n,f)})}function yt(n,t,i){var r=t==="",u={dispatch:r?n.dispatch:function(i,r,u){var e=h(i,r,u),s=e.payload,o=e.options,f=e.type;if((!o||!o.root)&&(f=t+f,!n._actions[f])){console.error("[vuex] unknown local action type: "+e.type+", global type: "+f);return}return n.dispatch(f,s)},commit:r?n.commit:function(i,r,u){var e=h(i,r,u),s=e.payload,o=e.options,f=e.type;if((!o||!o.root)&&(f=t+f,!n._mutations[f])){console.error("[vuex] unknown local mutation type: "+e.type+", global type: "+f);return}n.commit(f,s,o)}};return Object.defineProperties(u,{getters:{get:r?function(){return n.getters}:function(){return pt(n,t)}},state:{get:function(){return y(n.state,i)}}}),u}function pt(n,t){var i={},r=t.length;return Object.keys(n.getters).forEach(function(u){if(u.slice(0,r)===t){var f=u.slice(r);Object.defineProperty(i,f,{get:function(){return n.getters[u]},enumerable:!0})}}),i}function wt(n,t,i,r){var u=n._mutations[t]||(n._mutations[t]=[]);u.push(function(t){i.call(n,r.state,t)})}function bt(n,t,i,r){var u=n._actions[t]||(n._actions[t]=[]);u.push(function(t,u){var f=i.call(n,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:n.getters,rootState:n.state},t,u);return ct(f)||(f=Promise.resolve(f)),n._devtoolHook?f.catch(function(t){n._devtoolHook.emit("vuex:error",t);throw t;}):f})}function kt(n,t,i,r){if(n._wrappedGetters[t]){console.error("[vuex] duplicate getter key: "+t);return}n._wrappedGetters[t]=function(n){return i(r.state,r.getters,n.state,n.getters)}}function dt(n){n._vm.$watch(function(){return this._data.$$state},function(){i(n._committing,"do not mutate vuex store state outside mutation handlers.")},{deep:!0,sync:!0})}function y(n,t){return t.length?t.reduce(function(n,t){return n[t]},n):n}function h(n,t,r){return ht(n)&&n.type&&(r=t,t=n,n=n.type),i(typeof n=="string","expects string as the type, but found "+typeof n+"."),{type:n,payload:t,options:r}}function tt(t){if(n&&t===n){console.error("[vuex] already installed. Vue.use(Vuex) should be called only once.");return}n=t;et(n)}function c(n){return Array.isArray(n)?n.map(function(n){return{key:n,val:n}}):Object.keys(n).map(function(t){return{key:t,val:n[t]}})}function l(n){return function(t,i){return typeof t!="string"?(i=t,t=""):t.charAt(t.length-1)!=="/"&&(t+="/"),n(t,i)}}function a(n,t,i){var r=n._modulesNamespaceMap[i];return r||console.error("[vuex] module namespace not found in "+t+"(): "+i),r}var ot=typeof window!="undefined"?window:typeof global!="undefined"?global:{},e=ot.__VUE_DEVTOOLS_GLOBAL_HOOK__,r=function(n,t){this.runtime=t;this._children=Object.create(null);this._rawModule=n;var i=n.state;this.state=(typeof i=="function"?i():i)||{}},p={namespaced:{configurable:!0}},f,n,t,o;p.namespaced.get=function(){return!!this._rawModule.namespaced};r.prototype.addChild=function(n,t){this._children[n]=t};r.prototype.removeChild=function(n){delete this._children[n]};r.prototype.getChild=function(n){return this._children[n]};r.prototype.update=function(n){this._rawModule.namespaced=n.namespaced;n.actions&&(this._rawModule.actions=n.actions);n.mutations&&(this._rawModule.mutations=n.mutations);n.getters&&(this._rawModule.getters=n.getters)};r.prototype.forEachChild=function(n){u(this._children,n)};r.prototype.forEachGetter=function(n){this._rawModule.getters&&u(this._rawModule.getters,n)};r.prototype.forEachAction=function(n){this._rawModule.actions&&u(this._rawModule.actions,n)};r.prototype.forEachMutation=function(n){this._rawModule.mutations&&u(this._rawModule.mutations,n)};Object.defineProperties(r.prototype,p);f=function(n){this.register([],n,!1)};f.prototype.get=function(n){return n.reduce(function(n,t){return n.getChild(t)},this.root)};f.prototype.getNamespace=function(n){var t=this.root;return n.reduce(function(n,i){return t=t.getChild(i),n+(t.namespaced?i+"/":"")},"")};f.prototype.update=function(n){w([],this.root,n)};f.prototype.register=function(n,t,i){var o=this,f,e;i===void 0&&(i=!0);d(n,t);f=new r(t,i);n.length===0?this.root=f:(e=this.get(n.slice(0,-1)),e.addChild(n[n.length-1],f));t.modules&&u(t.modules,function(t,r){o.register(n.concat(r),t,i)})};f.prototype.unregister=function(n){var t=this.get(n.slice(0,-1)),i=n[n.length-1];t.getChild(i).runtime&&t.removeChild(i)};var b={assert:function(n){return typeof n=="function"},expected:"function"},at={assert:function(n){return typeof n=="function"||typeof n=="object"&&typeof n.handler=="function"},expected:'function or object with "handler" function'},k={getters:b,mutations:b,actions:at};t=function t(r){var a=this,u,e,o,l;r===void 0&&(r={});!n&&typeof window!="undefined"&&window.Vue&&tt(window.Vue);i(n,"must call Vue.use(Vuex) before creating a store instance.");i(typeof Promise!="undefined","vuex requires a Promise polyfill in this browser.");i(this instanceof t,"store must be called with the new operator.");u=r.plugins;u===void 0&&(u=[]);e=r.strict;e===void 0&&(e=!1);this._committing=!1;this._actions=Object.create(null);this._actionSubscribers=[];this._mutations=Object.create(null);this._wrappedGetters=Object.create(null);this._modules=new f(r);this._modulesNamespaceMap=Object.create(null);this._subscribers=[];this._watcherVM=new n;var h=this,c=this,y=c.dispatch,p=c.commit;this.dispatch=function(n,t){return y.call(h,n,t)};this.commit=function(n,t,i){return p.call(h,n,t,i)};this.strict=e;o=this._modules.root.state;s(this,o,[],this._modules.root);v(this,o);u.forEach(function(n){return n(a)});l=r.devtools!==undefined?r.devtools:n.config.devtools;l&&st(this)};o={state:{configurable:!0}};o.state.get=function(){return this._vm._data.$$state};o.state.set=function(){i(!1,"use store.replaceState() to explicit replace store state.")};t.prototype.commit=function(n,t,i){var s=this,u=h(n,t,i),r=u.type,f=u.payload,e=u.options,c={type:r,payload:f},o=this._mutations[r];if(!o){console.error("[vuex] unknown mutation type: "+r);return}this._withCommit(function(){o.forEach(function(n){n(f)})});this._subscribers.forEach(function(n){return n(c,s.state)});e&&e.silent&&console.warn("[vuex] mutation type: "+r+". Silent option has been removed. Use the filter functionality in the vue-devtools")};t.prototype.dispatch=function(n,t){var r=this,e=h(n,t),u=e.type,f=e.payload,o={type:u,payload:f},i=this._actions[u],s;if(!i){console.error("[vuex] unknown action type: "+u);return}try{this._actionSubscribers.filter(function(n){return n.before}).forEach(function(n){return n.before(o,r.state)})}catch(c){console.warn("[vuex] error in before action subscribers: ");console.error(c)}return s=i.length>1?Promise.all(i.map(function(n){return n(f)})):i[0](f),s.then(function(n){try{r._actionSubscribers.filter(function(n){return n.after}).forEach(function(n){return n.after(o,r.state)})}catch(t){console.warn("[vuex] error in after action subscribers: ");console.error(t)}return n})};t.prototype.subscribe=function(n){return g(n,this._subscribers)};t.prototype.subscribeAction=function(n){var t=typeof n=="function"?{before:n}:n;return g(t,this._actionSubscribers)};t.prototype.watch=function(n,t,r){var u=this;return i(typeof n=="function","store.watch only accepts a function."),this._watcherVM.$watch(function(){return n(u.state,u.getters)},t,r)};t.prototype.replaceState=function(n){var t=this;this._withCommit(function(){t._vm._data.$$state=n})};t.prototype.registerModule=function(n,t,r){r===void 0&&(r={});typeof n=="string"&&(n=[n]);i(Array.isArray(n),"module path must be a string or an Array.");i(n.length>0,"cannot register the root module by using registerModule.");this._modules.register(n,t);s(this,this.state,n,this._modules.get(n),r.preserveState);v(this,this.state)};t.prototype.unregisterModule=function(t){var r=this;typeof t=="string"&&(t=[t]);i(Array.isArray(t),"module path must be a string or an Array.");this._modules.unregister(t);this._withCommit(function(){var i=y(r.state,t.slice(0,-1));n.delete(i,t[t.length-1])});nt(this)};t.prototype.hotUpdate=function(n){this._modules.update(n);nt(this,!0)};t.prototype._withCommit=function(n){var t=this._committing;this._committing=!0;n();this._committing=t};Object.defineProperties(t.prototype,o);var it=l(function(n,t){var i={};return c(t).forEach(function(t){var u=t.key,r=t.val;i[u]=function(){var i=this.$store.state,u=this.$store.getters,t;if(n){if(t=a(this.$store,"mapState",n),!t)return;i=t.context.state;u=t.context.getters}return typeof r=="function"?r.call(this,i,u):i[r]};i[u].vuex=!0}),i}),rt=l(function(n,t){var i={};return c(t).forEach(function(t){var u=t.key,r=t.val;i[u]=function(){for(var i=[],u=arguments.length,t,f;u--;)i[u]=arguments[u];if(t=this.$store.commit,n){if(f=a(this.$store,"mapMutations",n),!f)return;t=f.context.commit}return typeof r=="function"?r.apply(this,[t].concat(i)):t.apply(this.$store,[r].concat(i))}}),i}),ut=l(function(n,t){var i={};return c(t).forEach(function(t){var u=t.key,r=t.val;r=n+r;i[u]=function(){if(!n||a(this.$store,"mapGetters",n)){if(!(r in this.$store.getters)){console.error("[vuex] unknown getter: "+r);return}return this.$store.getters[r]}};i[u].vuex=!0}),i}),ft=l(function(n,t){var i={};return c(t).forEach(function(t){var u=t.key,r=t.val;i[u]=function(){for(var i=[],u=arguments.length,t,f;u--;)i[u]=arguments[u];if(t=this.$store.dispatch,n){if(f=a(this.$store,"mapActions",n),!f)return;t=f.context.dispatch}return typeof r=="function"?r.apply(this,[t].concat(i)):t.apply(this.$store,[r].concat(i))}}),i}),gt=function(n){return{mapState:it.bind(null,n),mapGetters:ut.bind(null,n),mapMutations:rt.bind(null,n),mapActions:ft.bind(null,n)}};return{Store:t,install:tt,version:"3.1.1",mapState:it,mapMutations:rt,mapGetters:ut,mapActions:ft,createNamespacedHelpers:gt}});