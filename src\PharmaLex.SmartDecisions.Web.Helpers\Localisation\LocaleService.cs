﻿using Microsoft.AspNetCore.Http;
using PharmaLex.Caching.Data;
using PharmaLex.SmartDecisions.Entities;
using System.Threading;
using PharmaLex.Authentication.B2C;
using PharmaLex.DataAccess;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public interface ILocaleService
    {
        Locale Default { get; }
        Locale Current { get; }
    }
    public class LocaleService : ILocaleService
    {
        private readonly IDistributedCacheServiceFactory cacheService;
        private readonly IHttpContextAccessor httpContextAccessor;
        public LocaleService(IDistributedCacheServiceFactory cacheService,
            IHttpContextAccessor httpContextAccessor)
        {
            this.cacheService = cacheService;
            this.httpContextAccessor = httpContextAccessor;
        }

        public Locale Default
        {
            get
            {
                return this.cacheService.CreateEntity<Locale>().FirstOrDefault(x => x.Default);
            }
        }

        public Locale Current
        {
            get
            {
                int userId = this.httpContextAccessor.HttpContext.User.GetClaimValue<int>("plx:userid");

                string twoLetterIsoLanguageName = Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName;
                Locale currentLocale = this.cacheService.CreateEntity<Locale>()
                    .Configure(o => o.Include(x => x.User))
                    .FirstOrDefault(x => x.IsoLanguageCode == twoLetterIsoLanguageName);

                if (userId > 0 && !currentLocale.User.Any(x => x.Id == userId && x.LocaleId == currentLocale.Id))
                {
                    var cache = this.cacheService.CreateTrackedEntity<User>();
                    User user = cache.FirstOrDefault(x => x.Id == userId);
                    user.LocaleId = currentLocale.Id;
                    cache.SaveChanges();
                }

                return currentLocale;
            }
        }
    }
}
