create table #statements(Id int identity(1,1), [Sql] nvarchar(max));
insert into #statements select 'declare @ctid int'

declare @ctid int
declare @ctname nvarchar(128)

declare ct_cursor cursor for select Id, [Name] from ContentType where ContentTypeCategoryId = 1
open ct_cursor
fetch next from ct_cursor into @ctid, @ctname
while @@fetch_status = 0 begin
	insert into #statements select 'select @ctid = [Id] from [ContentType] where [Name] = ''' + @ctname + ''''
	insert into #statements select 'insert into [ContentItem] ([Name], [ContentTypeId], [Owner], [VerifiedDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select  
''' + replace([Name], '''', '''''') + ''',
@ctid,
''' + replace([Owner], '''', '''''') + ''',
''' + cast([VerifiedDate] as varchar) + ''',
getdate(), ''<EMAIL>'', getdate(), ''<EMAIL>'''
	from [ContentItem]
	where ContentTypeId = @ctid
	
	fetch next from ct_cursor into @ctid, @ctname
end
close ct_cursor
deallocate ct_cursor

select * from #statements order by id

drop table #statements