﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class RemoveNeweletterNewsArticleContentTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NewsletterNewsArticleContent");

            migrationBuilder.DropTable(
                name: "NewsletterNewsArticleContent_Audit",
                schema: "Audit");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "NewsletterNewsArticleContent",
                columns: table => new
                {
                    NewsletterId = table.Column<int>(type: "int", nullable: false),
                    NewsArticleContentId = table.Column<int>(type: "int", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    TimeOpenUtc = table.Column<DateTime>(type: "datetime", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterNewsArticleContent", x => new { x.NewsletterId, x.NewsArticleContentId });
                    table.ForeignKey(
                        name: "FK_NewsletterNewsArticleContent_NewsArticleContent",
                        column: x => x.NewsArticleContentId,
                        principalTable: "NewsArticleContent",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NewsletterNewsArticleContent_Newsletter",
                        column: x => x.NewsletterId,
                        principalTable: "Newsletter",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NewsletterNewsArticleContent_Audit",
                schema: "Audit",
                columns: table => new
                {
                    AuditId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AuditAction = table.Column<string>(type: "char(1)", unicode: false, fixedLength: true, maxLength: 1, nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    NewsArticleContentId = table.Column<int>(type: "int", nullable: true),
                    NewsletterId = table.Column<int>(type: "int", nullable: true),
                    TimeOpenUtc = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NewsletterNewsArticleContent_Audit", x => x.AuditId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NewsletterNewsArticleContent_NewsArticleContentId",
                table: "NewsletterNewsArticleContent",
                column: "NewsArticleContentId");
        }
    }
}
