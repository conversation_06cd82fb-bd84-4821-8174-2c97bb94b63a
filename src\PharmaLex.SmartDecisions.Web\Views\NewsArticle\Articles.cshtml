﻿@model List<NewsArticleContentListModel>
@using PharmaLex.SmartDecisions.Entities
@using System.Linq
@using Newtonsoft.Json
@using PharmaLex.DataAccess
@using Microsoft.AspNetCore.Localization
@inject PharmaLex.Caching.Data.IDistributedCacheServiceFactory Cache
@{
    var newsletterTypesScopes = (await ls.LocaliseEnum<NewsArticleImportance>());
    var newsSources = (await ls.LocaliseList<NewsSource>()).OrderBy(x => x.Name);
    var publishingStates = await ls.LocaliseEnum<NewsArticlePublishingState>();

    var newsAuthors = Cache.CreateEntity<User>()
        .Configure(o => o
            .Include(x => x.UserClaim)
            .ThenInclude(x => x.Claim))
        .Where(x => x.UserClaim.Any(y => y.Claim.ClaimType == "news"))
        .Select(x => new
        {
            id = x.Id,
            name = x.FullName
        })
        .OrderBy(x => x.name);

    var requestCulture = this.Context.Features.Get<IRequestCultureFeature>().RequestCulture.UICulture.Name;
    ViewData["Title"] = ls.Localise("news-articles").Value;

    var newsletterTypeScope = @ls.LocaliseSafe("(news).newsletter-type-heading").Value;
}

<div id="news-articles" v-cloak>
    <div class="sub-header">
        <h2>@ls.Localise("news-articles")</h2>
        <div class="controls">
            <a class="button" href="/article/new">@ls.LocaliseSafe("[[table]].new-article")</a>
        </div>
    </div>

    <filtered-table :items="articles"
                    :columns="columns"
                    :filters="filters"
                    :link="link"
                    :resources="resources">
    </filtered-table>

    <modal-dialog v-if="confirmResolve"
                  :title="'@ls.LocaliseSafe("(news).(articles).confirm-removal-title")'"
                  v-on:close="confirmReject"
                  v-on:confirm="confirmResolve">
        <p>{{confirmText}}</p>
    </modal-dialog>
</div>

@section Scripts {
    <script type="text/javascript">
    let articles = @Html.Raw(Model.ToJson());

    const convertDate = date => {
        return Intl.DateTimeFormat('@requestCulture', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(new Date(date));
    };
    const convertNullableDate = date => {
        if (date) return convertDate(date);
        return date;
    };
    const date = new Date();

    articles.forEach(x => {
        setImportanceImage(x);
    });

    const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

    var pageConfig = {
        appElement: '#news-articles',
        data: function () {
            return {
                confirmResolve: null,
                confirmReject: null,
                confirmText: null,
                link: '/article/edit/{path}',
                articles,
                resources: {
                    noRecordsMessage: '@ls.LocaliseSafe("[[table]].no-results-message")',
                    sortByFormat: '@ls.LocaliseSafe("[[table]].sort-by-format")',
                    filterByFormat: '@ls.LocaliseSafe("[[table]].filter-by-format")',
                    searchInFormat: '@ls.LocaliseSafe("[[table]].search-in-format")',
                    clearFilters: '@ls.LocaliseSafe("[[table]].clear-filters")',
                    addItem: '@ls.LocaliseSafe("[[table]].add-item")',
                    edit: '@ls.LocaliseSafe("[[table]].edit")',
                    remove: '@ls.LocaliseSafe("[[table]].remove")',
                    save: '@ls.LocaliseSafe("save")',
                    cancel: '@ls.LocaliseSafe("cancel")',
                    noValue: '@ls.LocaliseSafe("[[table]].no-value")',
                    pager: {
                        showingFormat: '@ls.LocaliseSafe("[[table]].pager.showing-format")',
                        showingFilteredFormat: '@ls.LocaliseSafe("[[table]].pager.showing-filtered-format")',
                        pageSize: '@ls.LocaliseSafe("[[table]].pager.page-size")',
                        first: '@ls.LocaliseSafe("[[table]].pager.first")',
                        previous: '@ls.LocaliseSafe("[[table]].pager.previous")',
                        next: '@ls.LocaliseSafe("[[table]].pager.next")',
                        last: '@ls.LocaliseSafe("[[table]].pager.last")',
                    }
                },
                columns: {
                    idKey: 'id',
                    remove: {
                        //canRemoveProp: /*column-name*/,
                        enabled: true,
                        claim: (item) => {
                            return new Promise((claimResolve, claimReject) => {
                                var confirm = new Promise((resolve, reject) => {
                                    this.confirmText = `${'@ls.LocaliseSafe("(news).(articles).confirm-removal")'} '${item.title}'?`;
                                    this.confirmResolve = resolve;
                                    this.confirmReject = reject;
                                });

                                confirm.then(() => {
                                    fetch(`/article/edit/content/${item.id}`, {
                                        method: 'DELETE',
                                        credentials: 'same-origin',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'RequestVerificationToken': token
                                        }
                                    }).then(result => {
                                        if (result.ok) {
                                                plx.toast.show('@ls.LocaliseSafe("(news).(articles).removed")', 2, 'confirm', null, 2500, { useIcons: true });
                                            claimResolve();
                                        }
                                        else {
                                                plx.toast.show('@ls.LocaliseSafe("(news).(articles).removal-failed")', 2, 'failed', null, 5000, { useIcons: true });
                                            claimReject();
                                        }
                                    });
                                }, claimReject)
                                    .finally(() => {
                                        this.confirmResolve = this.confirmReject = null;
                                        this.confirmTitle = null;
                                    });
                            });
                        }
                    },
                    config: [
                        {
                            dataKey: 'title',
                            sortKey: 'title',
                            header: '@ls.LocaliseSafe("title")',
                            sortDirection: 1,
                            type: 'text',
                            edit: {
                                type: 'plain',
                                required: true,
                                pattern: '.{1,256}',
                                customErrorMessage: '@ls.LocaliseSafe("max-field-length") 256'
                            },
                            style: 'width: 25%;'
                        },
                        {
                            dataKey: 'newsSource',
                            sortKey: 'newsSource',
                            header: '@ls.LocaliseSafe("news-source")',
                            type: 'text',
                            edit: {
                                type: 'select',
                                required: true
                            },
                            style: 'width: 15%;'
                        },
                        {
                            dataKey: 'sourcePublicationDate',
                            sortKey: 'sourcePublicationDate',
                            sortComparer: 'date',
                            header: '@ls.LocaliseSafe("source-publication-date")',
                            type: 'date',
                            edit: {
                                type: 'plain',
                                required: true,
                                convert: convertDate,
                                value: `${ date.getFullYear() }-${(date.getMonth() + 1).toString().padStart(2, '0')}-${ date.getDate().toString().padStart(2, '0') }`,
                                max: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
                            },
                            style: 'width: 10%;'
                        },
                        {
                            dataKey: 'goLiveDate',
                            sortKey: 'goLiveDate',
                            sortComparer: 'date',
                            header: '@ls.LocaliseSafe("newsletter-publication-date")',
                            type: 'date',
                            edit: {
                                type: 'plain',
                                required: false,
                                convert: convertNullableDate,
                                value: '',
                                min: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
                            },
                            style: 'width: 10%;'
                        },
                        {
                            dataKey: 'sourceUrl',
                            sortKey: 'sourceUrl',
                            header:  '@ls.LocaliseSafe("source-url")',
                            type: 'link',
                            edit: {
                                type: 'plain',
                                required: true,
                                pattern: "https?://.*"
                            },
                            style: 'width: 10%;'
                        },
                        {
                            dataKey: 'publishingState',
                            sortKey: 'publishingState',
                            header: '@ls.LocaliseSafe("state")',
                            type: 'text',
                            style: 'width: 5%;'
                        },
                        {
                            dataKey: 'author',
                            sortKey: 'author',
                            header: '@ls.LocaliseSafe("author")',
                            type: 'text',
                            edit: {
                                type: 'select',
                                required: false
                            },
                            style: 'width: 10%;'
                        },
                        {
                            dataKey: 'reviewer',
                            sortKey: 'reviewer',
                            header: '@ls.LocaliseSafe("reviewer")',
                            type: 'text',
                            edit: {
                                type: 'select',
                                required: false
                            },
                            style: 'width: 10%;'
                        },
                        {
                            dataKey: 'importanceType',
                            sortKey: 'importanceName',
                            header: '@newsletterTypeScope',
                            type: 'image',
                            style: 'width: 3%;'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'title',
                        options: [],
                        type: 'search',
                        header: '@ls.LocaliseSafe("(news).search-title")',
                        fn: v => p => plx.escapeAccent(p.title.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                        convert: v => v
                    },
                    {
                        key: 'sourceUrl',
                        options: [],
                        type: 'search',
                        header: '@ls.LocaliseSafe("(news).search-source-url")',
                        fn: v => p => p.sourceUrl.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'sourcePublicationDate',
                        options: [],
                        type: 'search',
                        header: '@ls.LocaliseSafe("(news).search-publication-date")',
                        fn: v => p => convertDate(p.sourcePublicationDate).toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'goLiveDate',
                        options: [],
                        type: 'search',
                        header: '@ls.LocaliseSafe("(news).search-newsletter-date")',
                        fn: v => p => {
                            const convP = (convertNullableDate(p.goLiveDate) || 'null').toLowerCase();
                            const convV = v.toLowerCase();
                            return convP !== 'null'
                                     ? convP.includes(convV)
                                     : convP === convV;
                        },
                        convert: v => v || 'null'
                    },
                    {
                        key: 'newsSource',
                        options: @Html.Raw(JsonConvert.SerializeObject(newsSources)),
                        filterCollection: 'newsSourceId',
                        display: 'name',
                        type: 'select-multiple',
                        header: '@ls.LocaliseSafe("(news).filter-news-source")',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.newsSource),
                        convert: v => v
                    },
                    {
                        key: 'publishingState',
                        options: @Html.Raw(publishingStates.ToJson()),
                        filterCollection: 'publishingStateId',
                        display: 'name',
                        type: 'select-multiple',
                        header: '@ls.LocaliseSafe("(news).filter-publishing-state")',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.publishingState),
                        convert: v => v
                    },
                    {
                        key: 'author',
                        options: @Html.Raw(JsonConvert.SerializeObject(newsAuthors)),
                        filterCollection: 'authorId',
                        display: 'name',
                        type: 'select-multiple',
                        header: '@ls.LocaliseSafe("(news).filter-author")',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.author),
                        convert: v => v
                    },
                    {
                        key: 'reviewer',
                        options: @Html.Raw(JsonConvert.SerializeObject(newsAuthors)),
                        filterCollection: 'reviewerId',
                        display: 'name',
                        type: 'select-multiple',
                        header: '@ls.LocaliseSafe("(news).filter-reviewer")',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.reviewer),
                        convert: v => v
                    },
                    {
                        key: 'importanceType',
                        options: @Html.Raw(JsonConvert.SerializeObject(newsletterTypesScopes)),
                        filterCollection: 'importanceId',
                        type: 'select-multiple',
                        header: '@ls.LocaliseSafe("(news).newsletter-type-heading")',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.importanceName),
                        convert: v => v
                    }
                ]
            };
        }
    };

    </script>
}

@section VueComponentScripts {
    <partial name="Components/ImageCell" />
    <partial name="Components/FilteredTableV2" />
    <partial name="Components/ModalDialog" />
}