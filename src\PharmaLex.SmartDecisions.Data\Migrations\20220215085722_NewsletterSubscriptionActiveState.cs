﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.SmartDecisions.Data.Extensions;

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class NewsletterSubscriptionActiveState : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Active",
                schema: "Audit",
                table: "NewsletterSubscription_Audit",
                type: "bit",
                nullable: true,
                defaultValue: true);

            migrationBuilder.AddColumn<bool>(
                name: "Active",
                table: "NewsletterSubscription",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.SqlFileExec("07-NewsletterSubscriptionActiveState-01-UpdateNewsletterSubscriptionTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Active",
                schema: "Audit",
                table: "NewsletterSubscription_Audit");

            migrationBuilder.DropColumn(
                name: "Active",
                table: "NewsletterSubscription");
        }
    }
}
