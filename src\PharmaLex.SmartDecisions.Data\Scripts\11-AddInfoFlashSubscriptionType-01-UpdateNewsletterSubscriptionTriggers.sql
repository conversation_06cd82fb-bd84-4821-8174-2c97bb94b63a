﻿ALTER TRIGGER [dbo].[NewsletterSubscription_Insert] ON [dbo].[NewsletterSubscription]
FOR INSERT AS
INSERT INTO [Audit].[NewsletterSubscription_Audit]
(AuditAction,Id, [UserId], [Timezone], [TimezoneOffset], [DeliveryLocalDay], [DeliveryUtcDay], [DeliveryLocalHour], [DeliveryUtcHour], [CreatedDateUtc], [IsMonthly], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [Active], [IsInfoflash])
SELECT 'I' ,Id, [UserId], [Timezone], [TimezoneOffset], [DeliveryLocalDay], [DeliveryUtcDay], [DeliveryLocalHour], [DeliveryUtcHour], [CreatedDateUtc], [IsMonthly], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [Active], [IsInfoflash] FROM [Inserted]
GO
 
ALTER TRIGGER [dbo].[NewsletterSubscription_Update] ON [dbo].[NewsletterSubscription]
FOR UPDATE AS
INSERT INTO [Audit].[NewsletterSubscription_Audit]
(AuditAction,Id, [UserId], [Timezone], [TimezoneOffset], [DeliveryLocalDay], [DeliveryUtcDay], [DeliveryLocalHour], [DeliveryUtcHour], [CreatedDateUtc], [IsMonthly], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [Active], [IsInfoflash])
SELECT 'U' ,Id, [UserId], [Timezone], [TimezoneOffset], [DeliveryLocalDay], [DeliveryUtcDay], [DeliveryLocalHour], [DeliveryUtcHour], [CreatedDateUtc], [IsMonthly], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [Active], [IsInfoflash] FROM [Inserted]
GO
 
ALTER TRIGGER [dbo].[NewsletterSubscription_Delete] ON [dbo].[NewsletterSubscription]
FOR DELETE AS
INSERT INTO [Audit].[NewsletterSubscription_Audit]
(AuditAction,Id, [UserId], [Timezone], [TimezoneOffset], [DeliveryLocalDay], [DeliveryUtcDay], [DeliveryLocalHour], [DeliveryUtcHour], [CreatedDateUtc], [IsMonthly], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [Active], [IsInfoflash])
SELECT 'D' ,Id, [UserId], [Timezone], [TimezoneOffset], [DeliveryLocalDay], [DeliveryUtcDay], [DeliveryLocalHour], [DeliveryUtcHour], [CreatedDateUtc], [IsMonthly], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [Active], [IsInfoflash] FROM [Deleted]
GO
 