﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.SmartDecisions.Data.Extensions;

#nullable disable

namespace PharmaLex.SmartDecisions.Data.Migrations
{
    public partial class AddInfoflash : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsInfoflash",
                schema: "Audit",
                table: "NewsletterSubscription_Audit",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsInfoflash",
                table: "NewsletterSubscription",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.SqlFileExec("11-AddInfoFlashSubscriptionType-01-UpdateNewsletterSubscriptionTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsInfoflash",
                schema: "Audit",
                table: "NewsletterSubscription_Audit");

            migrationBuilder.DropColumn(
                name: "IsInfoflash",
                table: "NewsletterSubscription");
        }
    }
}
