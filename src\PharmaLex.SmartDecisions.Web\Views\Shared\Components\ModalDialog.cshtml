﻿<script type="text/x-template" id="modal-dialog-template">
    <div class="modal-mask" v-on:click="$emit('close')">
        <div class="modal-wrapper" v-on:click.stop :style="'width: '+width+'; height: '+ height+';'">
            <div class="modal-container">
                <div class="modal-header">
                    <h3>{{title}}</h3>
                    <i id="dialog-closer" class="m-icon" v-on:click="$emit('close')">close</i>
                </div>
                
                <div class="dialog-custom-content" style="display: flex; flex-direction: column">
                    <div>
                        <slot></slot>
                    </div>
                    <div>
                        <a class="button icon-button-tick" v-on:click="$emit('confirm')">@ls.Localise("(news).yes")</a>
                        <a class="button secondary icon-button-cancel" v-on:click="$emit('close')">@ls.Localise("cancel")</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('modal-dialog', {
        template: '#modal-dialog-template',
        props: {
            title: {
                type: String,
            },
            width: {
                type: String,
                default: '50vw'
            },
            height: {
                type: String,
                default: 'auto'
            }
        }
    });
</script>
