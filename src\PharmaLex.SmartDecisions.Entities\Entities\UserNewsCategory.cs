﻿using PharmaLex.DataAccess;
using System;

namespace PharmaLex.SmartDecisions.Entities
{
    public class UserNewsCategory : EntityBase
    {
        public int Id { get; set; }
        public int Order { get; set; }

        public int UserId { get; set; }
        public int NewsCategoryId { get; set; }

        public DateTime CreatedDateUtc { get; set; }

        public virtual User User { get; set; }
        public virtual NewsCategory NewsCategory { get; set; }
    }
}
