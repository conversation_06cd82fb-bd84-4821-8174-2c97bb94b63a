﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class NewsArticleSearchResultsModel : IModel
    {
        public int Id { get; set; }
        public string FriendlyUrl { get; set; }
        public string Title { get; set; }
        public List<int> Categories { get; set; }
        public string SourceUrl { get; set; }
        public int NewsSourceId { get; set; }
        public DateTime SourcePublicationDate { get; set; }
        public int ImportanceId { get; set; }
        public int NewsArticleId { get; set; }
        public int LocaleId { get; set; }
    }

    public class NewsArticleSearchModelMappingProfile : Profile
    {
        public NewsArticleSearchModelMappingProfile()
        {
            this.CreateMap<NewsArticleContent, NewsArticleSearchResultsModel>()
                .ForMember(d => d.SourcePublicationDate, s => s.MapFrom(x => x.NewsArticle.SourcePublicationDate))
                .ForMember(d => d.ImportanceId, s => s.MapFrom(x => x.NewsArticle.ImportanceId))
                .ForMember(d => d.Categories, s => s.MapFrom(x => x.NewsArticle.NewsArticleCategory.Select(y => y.NewsCategoryId)))
                .ForMember(d => d.NewsSourceId, s => s.MapFrom(x => x.NewsArticle.NewsSourceId));
        }
    }
}
