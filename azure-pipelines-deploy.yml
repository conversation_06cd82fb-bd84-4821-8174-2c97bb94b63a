parameters:
  - name: decisionPackageLocation
    type: string
    default: 'drop/PharmaLex.SmartDecisions.Web.zip'
  - name: newsletterPackageLocation
    type: string
    default: 'newsletter/PharmaLex.NewsletterApp.zip'
  - name: environments
    type: object
    default:
      - name: 'dev'
        prefix: 'dev'
        azureSubscription: 'SmartDecisions Development'
        CompanyUserLoginEmailTemplateId: "d-546190ec301d4d19812f6aa489b96126"
        CompanyUserSignUpEmailTemplateId: "d-693bf4227879492da3482d6ff2edf4dd"
        RecordExpirationNotificationTemplateId: "d-817d5c32c09642258da3c0fe39806db2"
      - name: 'stg'
        prefix: 'stg'
        azureSubscription: 'SmartDecisions Development'
        CompanyUserLoginEmailTemplateId: "d-f2f3b84e7a5b43cd89be8a7ea3c2f7ac"
        CompanyUserSignUpEmailTemplateId: "d-bcaab9a6215e40398c5ad1dd3c0ee469"
        RecordExpirationNotificationTemplateId: "d-c0a495dc8d9e40b68ffd32265ff446ca"
        dependsOn:
          - dev
      - name: 'prodeu'
        prefix: 'prod'
        azureSubscription: 'SmartDecisions Production'
        CompanyUserLoginEmailTemplateId: "d-656559a1bc724c5faf0c84be9f7c2097"
        CompanyUserSignUpEmailTemplateId: "d-31ca42cee7514e268090b2a8e5e5fe2a"
        RecordExpirationNotificationTemplateId: "d-33e786a47e87441d808467d29a610bd4"
        dependsOn:
          - stg

pool: 'pv-pool'

trigger: none
pr: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: SmartDecisions.Build
      project: SmartDecisions
      trigger:
        branches:
          include:
            - develop

variables:
  platform: 'sdc'
  region: 'eun'

stages:
  - ${{ each env in parameters.Environments }}:
      - stage: '${{ env.name }}'
        displayName: '${{ env.name }} Deploy'
        dependsOn: '${{ env.dependsOn }}'
        variables:
          KeyVaultName: '${{ variables.platform }}-${{ env.prefix }}-kv-${{ variables.region }}'
          emailsSendingEnabled: 'true'
          AzureStorage.Account: '${{ variables.platform }}${{ env.prefix }}shared${{ variables.region }}'
          AzureStorage.Container: 'smartdecisions' #Hardcoded to the current container used in PLX PROD
          AzureSearch.IsEnabled: 'true'
          AzureSearch.IndexName: 'sdc-${{ env.name }}-index'
          AzureSearch.ConnectionString.default: ''
          AzureSearch.IndexerName: 'sdc-${{ env.name }}-indexer'
          AzureSearch.DataSourceName: 'sdc-${{ env.name }}'
          ${{ if eq(env.prefix, 'prod') }}: #Using central search service
            AzureSearch.ServiceName: 'ss-prod-ss-eun'
            HtmlToPdfConverter.Url: 'https://htmltopdfconverter.smartphlex.com'
            AzureWebJobs.DeleteNewsletterInsightsDataTimer.Disabled: 'false'
          ${{ else }}:
            AzureSearch.ServiceName: 'ss-nonprod-ss-eun'
            HtmlToPdfConverter.Url: 'https://htmltopdfconverter-${{ env.prefix }}.smartphlex.com'
            AzureWebJobs.DeleteNewsletterInsightsDataTimer.Disabled: 'false'
          AppSettings.BuildInfo: 'Build: $(resources.pipeline.build-pipeline.runName)'
          AppSettings.BuildNumber: $(resources.pipeline.build-pipeline.runName)
          AppSettings.CompanyUserLoginEmailTemplateId: ${{ env.CompanyUserLoginEmailTemplateId }}
          AppSettings.CompanyUserSignUpEmailTemplateId: ${{ env.CompanyUserSignUpEmailTemplateId }}
          AppSettings.RecordExpirationNotificationTemplateId: ${{ env.RecordExpirationNotificationTemplateId }}
          Static.Env: '${{ env.name }}'
        jobs:
          - deployment: DeploySQL
            displayName: 'Deploy SQL dacpac ${{ env.name }}'
            environment: ${{ env.name }}
            pool: 'pv-windows-pool'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - task: DownloadPipelineArtifact@2
                      inputs:
                        source: 'specific'
                        project: $(resources.pipeline.build-pipeline.projectID)
                        pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                        runVersion: 'specific'
                        runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                        runId: $(resources.pipeline.build-pipeline.runID)
                        path: '$(System.DefaultWorkingDirectory)'
                    - task: SqlAzureDacpacDeployment@1
                      displayName: 'Azure SQL SqlTask'
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        AuthenticationType: servicePrincipal
                        ServerName: '${{ variables.platform }}-${{ env.prefix }}-sqlserver-${{ variables.region }}.database.windows.net'
                        DatabaseName: '${{ variables.platform }}-${{ env.prefix }}-default-${{ variables.region }}'
                        deployType: SqlTask
                        SqlFile: '$(System.DefaultWorkingDirectory)/drop/Migrations/migration.sql'
                        IpDetectionMethod: 'AutoDetect'
                        DeleteFirewallRule: true
          - deployment: Deploy
            displayName: 'Deploy Env: ${{ env.name }}'
            environment: ${{ env.name }}
            strategy:
              runOnce:
                deploy:
                  steps:
                    - task: DownloadPipelineArtifact@2
                      inputs:
                        source: 'specific'
                        project: $(resources.pipeline.build-pipeline.projectID)
                        pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                        runVersion: 'specific'
                        runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                        runId: $(resources.pipeline.build-pipeline.runID)
                        path: '$(System.DefaultWorkingDirectory)'

                    - task: AzureKeyVault@2
                      displayName: 'Azure Key Vault: ${{ variables.platform }}-${{ env.prefix }}-kv-${{ variables.region }}'
                      inputs:
                        azureSubscription: ${{ env.azureSubscription }}
                        KeyVaultName: '${{ variables.platform }}-${{ env.prefix }}-kv-${{ variables.region }}'
                        RunAsPreJob: true

                    - task: FileTransform@1
                      displayName: 'File Transform: newsletter'
                      inputs:
                        folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.newsletterPackageLocation }}'
                        fileType: json
                        targetFiles: '**/appSettings.json'

                    - task: AzureFunctionApp@2
                      displayName: 'Azure Function App Deploy: smartdecisions-newsletter'
                      inputs:
                        connectedServiceNameARM: ${{ env.azureSubscription }}
                        appType: functionApp
                        appName: '${{ variables.platform }}-${{ env.prefix }}-fa-${{ variables.region }}'
                        package: '$(System.DefaultWorkingDirectory)/${{ parameters.newsletterPackageLocation }}'

                    - task: FileTransform@1
                      displayName: 'File Transform: '
                      inputs:
                        folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.decisionPackageLocation }}'
                        fileType: json
                        targetFiles: '**/appsettings.json'

                    - task: AzureRmWebAppDeployment@4
                      displayName: Azure App Service Deploy
                      inputs:
                        ConnectionType: 'AzureRM'
                        azureSubscription: ${{ env.azureSubscription }}
                        ResourceGroupName: 'rg-${{ variables.platform }}-${{ env.prefix }}-${{ variables.region }}'
                        appType: 'webApp'
                        WebAppName: '${{ variables.platform }}-${{ env.prefix }}-as-${{ variables.region }}'
                        packageForLinux: '$(System.DefaultWorkingDirectory)/${{ parameters.decisionPackageLocation }}'
