﻿update [dbo].[MultilingualResource] set Content = 'filtered from a total of' where [Key] = '[[table]].pager.showing-filtered-format' and LocaleId = 1
update [dbo].[MultilingualResource] set Content = 'filtrés à partir d''un total de' where [Key] = '[[table]].pager.showing-filtered-format' and LocaleId = 2

update [dbo].[MultilingualResource] set Content = 'Rows per page' where [Key] =  '[[table]].pager.page-size' and LocaleId = 1
update [dbo].[MultilingualResource] set Content = 'Lignes par page' where [Key] = '[[table]].pager.page-size' and LocaleId = 2