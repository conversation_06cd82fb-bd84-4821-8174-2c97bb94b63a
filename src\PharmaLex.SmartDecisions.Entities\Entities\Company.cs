﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class Company : EntityBase
    {
        public Company()
        {
            CompanyUser = new HashSet<CompanyUser>();
            CompanyContentType = new HashSet<CompanyContentType>();
            CompanyNewsCategory = new HashSet<CompanyNewsCategory>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string PrimaryContactName { get; set; }
        public string PrimaryContactEmail { get; set; }
        public string PrimaryContactAddress { get; set; }
        public string PrimaryContactPhone { get; set; }
        public int MaximumUsersCount { get; set; }
        public int MaximumActiveUsersCount { get; set; }

        public virtual ICollection<CompanyUser> CompanyUser { get; set; }
        public virtual ICollection<CompanyContentType> CompanyContentType { get; set; }
        public virtual ICollection<CompanyNewsCategory> CompanyNewsCategory { get; set; }
    }
}
