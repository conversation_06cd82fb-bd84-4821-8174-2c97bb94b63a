﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public interface IContentItemModel : IModel
    {
        int Id { get; set; }
        string Name { get; set; }
        IEnumerable<FieldValueModel> Values { get; set; }
    }

    public static class IContentItemModelExtensions
    {
        public static string GetFieldValue(this IContentItemModel m, int id)
        {
            return m.Values?.FirstOrDefault(x => x.Id == id)?.Value;
        }

        public static string GetFieldValue(this IContentItemModel m, string field)
        {
            return m.Values?.FirstOrDefault(x => x.Name.EndsWith($" - {field}", StringComparison.InvariantCulture))?.Value;
        }

        public static string GetFieldDisplayHtml(this IContentItemModel m, FieldModel field, IEnumerable<PicklistModel> picklists)
        {
            string v = m.Values?.FirstOrDefault(x => x.FieldId == field?.Id)?.Value;
            if (!String.IsNullOrEmpty(v))
            {
                if (field.FieldTypeId == (int)FieldType.Multiline)
                {
                    return v.Replace("\n", "<br>", StringComparison.InvariantCulture);
                }
                if (field.FieldTypeId == (int)FieldType.Bool)
                {
                    return $"<i class=\"icon-{(v == "true" ? "tick" : "cross")}\"></i>";
                }
                if (field.FieldTypeId == (int)FieldType.Url)
                {
                    return $"<a href=\"{v}\" target=\"_blank\">{v}</a>";
                }
                if (field.FieldTypeId == (int)FieldType.Email)
                {
                    return $"<a href=\"mailto:{v}\">{v}</a>";
                }
                if (field.FieldTypeId == (int)FieldType.Date)
                {
                    return DateTime.TryParse(v, out DateTime dt) ? dt.ToString("dd MMM yyyy") : "";
                }
                if (field.FieldTypeId == (int)FieldType.Picklist || field.FieldTypeId == (int)FieldType.Relationship)
                {
                    if (field.MultiSelect)
                    {
                        string[] vs = v.Split('|');
                        return String.Join(", ", picklists.FirstOrDefault(x => x.ContentTypeId == field.RelatedContentTypeId)?.Items.Where(x => vs.Contains(x.Id.ToString())).Select(x => System.Net.WebUtility.HtmlEncode(x.Name)));
                    }
                    else
                    {
                        return System.Net.WebUtility.HtmlEncode(picklists.FirstOrDefault(x => x.ContentTypeId == field.RelatedContentTypeId)?.Items.FirstOrDefault(x => x.Id.ToString() == v)?.Name);
                    }
                }
            }
            return v;
        }
    }
}
