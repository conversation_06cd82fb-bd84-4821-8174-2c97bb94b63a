﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class ContentTypeDisplayAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public string Name { get; set; }
        public int? ContentTypeId { get; set; }
        public int? ContentTypeDisplayTypeId { get; set; }
        public string Json { get; set; }
    }
}
