﻿using PharmaLex.DataAccess;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class Field : IEntity
    {
        [NotMapped]
        public FieldType FieldType
        {
            get { return (FieldType)Enum.Parse(typeof(FieldType), this.FieldTypeId.ToString()); }
            set { this.FieldTypeId = (int)value; }
        }

        [NotMapped]
        public bool MultiSelect
        {
            get { return (this.FieldType == FieldType.Picklist || this.FieldType == FieldType.Relationship) && this.Length > 1; }
        }
    }
}
