﻿const gulp = require('gulp');
const concat = require('gulp-concat');
const cleanCss = require('gulp-clean-css');

gulp.task('min:css', function () {
	return gulp.src(['wwwroot/css/reset.css',
		'wwwroot/css/fonts.css',
		'wwwroot/css/screen.css',
		'wwwroot/css/decisions.css'])
		.pipe(concat('site.min.css'))
		.pipe(cleanCss())
		.pipe(gulp.dest('wwwroot/css/'));
});

gulp.task('map-bundle.min:js', function () {
	return gulp.src(['wwwroot/data/countries.topo.js',
		'wwwroot/lib/d3/d3.v4.js',
		'wwwroot/lib/d3/topojson.js',
		'wwwroot/js/map.js'])
		.pipe(concat('map-bundle.min.js'))
		.pipe(gulp.dest('wwwroot/js/'));
});

gulp.task('jquery.min:js', function () {
	return gulp.src(['wwwroot/lib/jquery/dist/jquery.js'])
		.pipe(concat('jquery.min.js'))
		.pipe(gulp.dest('wwwroot/lib/jquery/dist/'));
});