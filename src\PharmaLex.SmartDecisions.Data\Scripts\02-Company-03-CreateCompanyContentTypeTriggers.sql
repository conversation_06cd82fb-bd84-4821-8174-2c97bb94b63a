﻿CREATE TRIGGER [dbo].[CompanyContentType_Insert] ON [dbo].[CompanyContentType]
FOR INSERT AS
INSERT INTO [Audit].[CompanyContentType_Audit] ([AuditAction], [CompanyId], [ContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'I', [CompanyId], [ContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[CompanyContentType_Update] ON [dbo].[CompanyContentType]
FOR UPDATE AS
INSERT INTO [Audit].[CompanyContentType_Audit] ([AuditAction], [CompanyId], [ContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'U', [CompanyId], [ContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[CompanyContentType_Delete] ON [dbo].[CompanyContentType]
FOR DELETE AS
INSERT INTO [Audit].[CompanyContentType_Audit] ([AuditAction], [CompanyId], [ContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
SELECT 'D', [CompanyId], [ContentTypeId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO