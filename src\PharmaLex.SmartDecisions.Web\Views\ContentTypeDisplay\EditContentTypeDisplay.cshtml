﻿@model ContentTypeDisplayModel
@using PharmaLex.SmartDecisions.Entities
@inject AutoMapper.IMapper mapper
@{
    ViewData["Title"] = "Edit Content Type Display";

    var types = mapper.Map<IEnumerable<PicklistItemModel>>(Enum.GetValues(typeof(ContentTypeDisplayType))).ToSelectList();
    var displayJson = string.IsNullOrEmpty(Model.Json) ? "\"\"" : Model.Json;
}

<div id="app" v-cloak>
    @Html.AntiForgeryToken()
    <div class="sub-header">
        <h2 v-if="displayJson.id > 0">@Model.Name</h2>
        <h2 v-else>Add Content Type Display</h2>
        <div class="controls">
            <button class="button" v-on:click="preview" :disabled="isNew">Preview</button>
            <div class="vertical-line"></div>
            <a class="button" v-on:click="saveDisplayType">Save</a>
            <a class="button secondary" href="/manage/content-type/edit/@Model.ContentTypeId">Cancel</a>
            <div class="vertical-line" v-if="!isNew"></div>
            <a href="/manage/content-type-display/delete/@Model.Id" v-if="!isNew" class="button">Delete</a>
        </div>
    </div>

    <content-type-display-group-dialog :sectiondata="sectionData"
                                       :uniquefields="uniqueFields"
                                       :fields="fields"
                                       :systemfields="systemFields"
                                       :uniquesystemfields="uniqueSystemFields"
                                       v-if="showContentTypeDisplayGroupDialog"
                                       @@save-section-group="saveSectionGroup"
                                       @@add-section-group="addSectionGroup"
                                       @@content-type-display-group-close="showContentTypeDisplayGroupDialog=false"
                                       @@on-add-field="onAddField"></content-type-display-group-dialog>


    <div class="modal-mask" v-on:click="closePreview" v-if="showPreviewRecords">
        <div class="modal-wrapper" v-on:click.stop>

            <div class="modal-container flex columns justify-content">
                <div class="p-1">Record</div>
                <div class="p-1">
                    <select name="Records" type="dropdown"
                            autofocus="autofocus"
                            id="Records"
                            class="white-background"
                            v-model="recordSelected">
                        <option>Choose a record</option>
                        <option v-for="record in records" :value="record.id">{{record.value}}</option>
                    </select>
                </div>
                <div class="flex p-1">
                    <button type="button" class="button mr-2" v-on:click="previewRecord" :disabled="recordSelected === 'Choose a record'">Preview</button>
                    <button type="button" class="button secondary" v-on:click="showPreviewRecords=false">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-nowrap">
        <div class="flex-item flex-x1">
            <div class="flex columns pl-3 pt-1 gapped-1">
                <h5>Display View Record</h5>
                <div class="flex columns">
                    <div class="tile">
                        <div class="flex justify-space-between pb-1 flex-align-center">
                            <h5 class="flex-item justify-center mb-1 flex justify-content flex-align-center"><span class="pr-1">Hero Banner </span><span class="hero-banner-tooltip"><i class="m-icon pr-2">help</i><span class="hero-banner-tooltiptext">Create a short statement to be used as the title of the Reg Intel Record. This will be a combination of free text and specific data fields from the topic's data records.</span></span></h5>
                            <button class="button" v-on:click="toggleShortCodes">View shortcodes</button>
                        </div>
                        <div class="bordered">
                            <textarea v-model="displayJson.heroTemplate" class="shadowed hero-banner-textarea"></textarea>
                        </div>
                    </div>
                    <div class="flex gapped-2 pt-2">
                        <div class="flex-item flex-x3 tile group-section-container" v-for="(column, columnIndex) in displayJson.columns" :key="columnIndex">
                            <h5>Section {{columnIndex === 0 ? 'One' : 'Two'}}</h5>
                            <content-type-display-group-section v-for="(data, rowIndex) in column.sections"
                                                                :key="data.title + rowIndex"
                                                                :data="data"
                                                                :fields="fields"
                                                                :columnindex="columnIndex"
                                                                :rowindex="rowIndex"
                                                                :isaside="false"
                                                                @@update-group-section="updateSectionDialog"
                                                                @@remove-group-section="removeSectionGroup"></content-type-display-group-section>
                            <div class="lozenge-list">
                                <button type="button" v-on:click="openSectionItemDialog(columnIndex)">+</button>
                            </div>
                        </div>
                        <div class="flex-item flex-x3 tile group-section-container">
                            <h5>Sidebar</h5>
                            <content-type-display-group-section :data="displayJson.aside"
                                                                :fields="fields"
                                                                :columnindex="2"
                                                                :isaside="true"
                                                                @@update-group-section="updateSectionDialog"></content-type-display-group-section>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex-item flex-x4 flex columns mt-minus-2" v-if="isShorCodesDisplayed">
            <div class="tile ml-2 flex-x1">
                <div class="flex justify-space-between pb-1 flex-align-center">
                    <span>Shortcodes</span>
                    <i class="m-icon pt-1 pr-1" v-on:click="toggleShortCodes">close</i>
                </div>
                <div>
                    <div v-for="p in picklistFields" class="chip">{{ p.name }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<environment include="Development">
    <script src="~/js/dialog.js"></script>
</environment>
<environment exclude="Development">
    <script src="@VersionCdn.GetUrl("js/dialog.js")"></script>
</environment>
<script type="text/javascript">
    const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

    var pageConfig = {
        appElement: '#app',
        data() {

            let displayJson;

            let fields = @Html.Raw(Json.Serialize(Model.ContentTypeViewFieldModels));
            fields.sort((a, b) => a.name.localeCompare(b.name));
            let uniqueFields = [...fields];

            let systemFields = @Html.Raw(Json.Serialize(Model.ContentTypeViewFieldModels.Where(f => f.IsSystem).ToList()));
            systemFields.sort((a, b) => a.name.localeCompare(b.name));

            let uniqueSystemFields = [...systemFields];

            let isNew = @Html.Raw(Model.IsNew.ToString().ToLower());

            if(isNew) {
                displayJson = {};
                displayJson.columns = [{sections: []}, {sections: []}];
                displayJson.aside = {};
                displayJson.aside.groups = [];
            }
            else {
                displayJson = @Html.Raw(displayJson);

                for(let c of displayJson.columns) {
                    for(let s of c.sections) {
                        for(let g of s.groups) {
                            for(let field of g.fields) {
                                uniqueFields.splice(uniqueFields.findIndex(f => f.id === field), 1);
                            }
                        }
                    }
                }

                if(displayJson.aside.groups.length) {
                    for(let af of displayJson.aside.groups[0].fields) {
                        uniqueSystemFields.splice(uniqueSystemFields.findIndex(f => f.id == af), 1);
                    }
                }
            }

            if(!displayJson.aside)
            {
                displayJson.aside = {};
                displayJson.aside.groups = [];
            }

            let contentTypeDisplayTypeId = @Html.Raw(Model.ContentTypeDisplayTypeId) === 0 ? 1 : @Html.Raw(Model.ContentTypeDisplayTypeId);

            return {
                displayTypes: [@Html.Raw(String.Join(',', types.Select(x => $"{{text:\"{x.Text}\",value:\"{x.Value}\"}}")))],
                showContentTypeDisplayGroupDialog: false,
                displayJson: displayJson,
                fields: fields,
                uniqueFields: uniqueFields,
                systemFields: systemFields,
                uniqueSystemFields: uniqueSystemFields,
                picklistFields: @Html.Raw(Json.Serialize(Model.ContentTypeViewFieldModels.Where(f => f.IsPicklist).ToList())),
                isShorCodesDisplayed: false,
                name: '@Html.Raw(Model.Name)',
                contentTypeDisplayTypeId: contentTypeDisplayTypeId,
                contentTypeName: '@Html.Raw(Model.ContentTypeName)',
                showPreviewRecords: false,
                showPreviewData: false,
                recordSelected: 'Choose a record',
                contentTypeId: @Model.ContentTypeId,
                records: @Html.Raw(Json.Serialize(Model.ContentTypeViewRecordModels)),
                previewDisplay: null,
                isNew: isNew,
                contentTypeDisplayId: @Model.Id
            }
        },
        methods: {
            updateSectionDialog(data) {

                let sectionData = {};

                if(!data){
                    sectionData.isAddNew = true;
                    sectionData.isAside = data.isAside;
                } else{
                    sectionData = {...data};
                    sectionData.isAddNew = false;
                }

                this.sectionData = sectionData;
                this.showContentTypeDisplayGroupDialog = true;
            },
            openSectionItemDialog(columnIndex) {

                let sectionData = {};

                sectionData.columnIndex = columnIndex;
                sectionData.isAddNew = true;
                sectionData.isAside = false;

                this.sectionData = sectionData;
                this.showContentTypeDisplayGroupDialog = true;
            },
            addSectionGroup(sectionGroup) {
                let sectionData = {};
                sectionData.groups = sectionGroup.groups;
                sectionData.title = sectionGroup.title;

                this.displayJson.columns[sectionGroup.columnIndex].sections.push(sectionData);
            },
            saveSectionGroup(group) {

                if(group.isAside) {
                    this.displayJson.aside.groups.splice(0, group.groups.length, ...group.groups);
                } else {
                    let groupCount = this.displayJson.columns[group.columnIndex].sections[group.rowIndex].groups.length;
                    this.displayJson.columns[group.columnIndex].sections[group.rowIndex].groups.splice(0, groupCount, ...group.groups);
                    this.displayJson.columns[group.columnIndex].sections[group.rowIndex].title = group.title;
                }
            },
            removeSectionGroup(groupSection){

                var removeGroupSection = this.displayJson.columns[groupSection.columnIndex].sections[groupSection.rowIndex];

                for(let g of removeGroupSection.groups) {
                    for(let f of g.fields) {
                        this.onRemoveField(f, false);
                    }
                }

                this.displayJson.columns[groupSection.columnIndex].sections.splice(groupSection.rowIndex, 1);
            },
            saveDisplayType() {
                const requestOptions = {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                            ContentTypeId: @Html.Raw(Model.ContentTypeId),
                            ContentTypeName: this.contentTypeName,
                            ContentTypeDisplayTypeId: this.contentTypeDisplayTypeId,
                            DisplayType: '@Model.DisplayType',
                            Json: JSON.stringify(this.displayJson),
                            Id: this.contentTypeDisplayId,
                            Name: '@Model.Name'
                    })
                };

                let url;

                if(this.isNew) {
                    url = "/manage/content-type-display/new";

                    fetch(url, requestOptions)
                    .then(res => res.json())
                    .then(response =>
                    {
                        window.location.href = '/manage/content-type-display/edit/' + response.id;
                    });
                }
                else {
                    url = "/manage/content-type-display/edit";
                    fetch(url, requestOptions)
                    .then(res => {
                        plx.toast.show('Updated successfully', 2, 'confirm', null, 2500, { useIcons: true });
                    });
                }
            },
            onRemoveField(fieldId, isAside) {
                if(isAside) {
                    this.uniqueSystemFields.push(this.fields.find(f => f.id === fieldId));
                    this.uniqueSystemFields.sort((a, b) => a.name.localeCompare(b.name));
                } else {
                    this.uniqueFields.push(this.fields.find(f => f.id === fieldId));
                    this.uniqueFields.sort((a, b) => a.name.localeCompare(b.name));
                }

                this.$forceUpdate();
            },
            onAddField(field, isAside) {
                if(isAside) {
                    if(field.isRemoved) {
                        this.uniqueSystemFields.push(this.fields.find(f => f.id === field.id));
                        this.uniqueSystemFields.sort((a, b) => a.name.localeCompare(b.name));
                    } else {
                        this.uniqueSystemFields.splice(this.uniqueSystemFields.findIndex(f => f.id === field.id), 1);
                        this.uniqueSystemFields.sort((a, b) => a.name.localeCompare(b.name));
                    }
                } else {
                    if(field.isRemoved) {
                        this.uniqueFields.push(this.fields.find(f => f.id === field.id));
                        this.uniqueFields.sort((a, b) => a.name.localeCompare(b.name));
                    } else {
                        this.uniqueFields.splice(this.uniqueFields.findIndex(f => f.id === field.id), 1);
                        this.uniqueFields.sort((a, b) => a.name.localeCompare(b.name));
                    }
                }
                this.$forceUpdate();
            },
            toggleShortCodes() {
                this.isShorCodesDisplayed = !this.isShorCodesDisplayed;
            },
            preview() {
                this.showPreviewRecords = true;
            },
            closePreview() {
                this.showPreviewRecords = false;
            },
            previewRecord() {

                this.showPreviewRecords = false;

                const requestOptions = {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                            ContentTypeId: @Html.Raw(Model.ContentTypeId),
                            ContentTypeName: this.contentTypeName,
                            ContentTypeDisplayTypeId: this.contentTypeDisplayTypeId,
                            DisplayType: '@Model.DisplayType',
                            Json: JSON.stringify(this.displayJson),
                            Id: this.contentTypeDisplayId,
                            Name: '@Model.Name'
                    })
                };

                fetch("/manage/content-type-display/edit", requestOptions)
                .then(response =>
                {
                    plx.dialog.load('/topic/data/' + this.contentTypeId + "/" + this.recordSelected + "/" + this.contentTypeDisplayId, '90%', '90vh', '/images/loaders/spinner-75.svg');
                    this.recordSelected = 'Choose a record';
                });
            }
        },
        beforeCreate: function() {
            plx.dialog.init('view-topic-dialog');
        }
    };
</script>
}

@section VueComponentScripts {
    <partial name="Components/ContentTypeDisplayGroupDialog" />
    <partial name="Components/ContentTypeDisplayGroupSection" />
}
