﻿using PharmaLex.DataAccess;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.SmartDecisions.Entities
{
    public partial class ContentType : IEntity
    {
        [NotMapped]
        public ContentTypeCategory Category
        {
            get { return (ContentTypeCategory)this.ContentTypeCategoryId; }
            set { this.ContentTypeCategoryId = (int)value; }
        }
    }
}
