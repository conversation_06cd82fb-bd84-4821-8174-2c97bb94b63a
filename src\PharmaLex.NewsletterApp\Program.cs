using System;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.NewsletterApp.Helpers;
using PharmaLex.SmartDecisions.Data.Persistance.Repository;
using PharmaLex.SmartDecisions.Entities;
using PharmaLex.SmartDecisions.Web.Helpers;
using PharmaLex.SmartDecisions.Web.Helpers.Interfaces;
using PharmaLex.SmartDecisions.Web.Models;
using System.Reflection;
using System.Threading.Tasks;

namespace PharmaLex.NewsletterApp
{
    public class Program
    {
        public static async Task Main()
        {
            var host = new HostBuilder()
                .ConfigureFunctionsWebApplication()
                .ConfigureAppConfiguration((_, config) =>
                {
                    config.AddJsonFile("appSettings.json");
                    var builtConfig = config.Build();
                    var options = new DefaultAzureCredentialOptions() { VisualStudioTenantId = builtConfig["VisualStudioTenantId"] };
                    var secretClient = new SecretClient(
                        new Uri($"https://{builtConfig["KeyVaultName"]}.vault.azure.net/"),
                        new DefaultAzureCredential(options));
                    config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
#if DEBUG
                    config.AddJsonFile("appSettings.json");
#endif
                })
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddStackExchangeRedisCache(options =>
                        {
                            options.Configuration = hostContext.Configuration.GetConnectionString("RedisCache");
                        });

                    services.AddAutoMapper(typeof(LicenseNewsCategoryModel).Assembly);

                    var executingAssembly = Assembly.GetExecutingAssembly();
                    services.AddAutoMapper(executingAssembly);
                    services.AddSingleton<IDistributedCacheService, DistributedCacheService>();
                    services.AddScoped<IDbAccessProvider, DbAccessProvider>();
                    services.AddScoped<INewsCategoryLicenseHelper, NewsCategoryLicenseHelper>();
                    services.AddScoped<IDistributedCacheServiceFactory, DistributedCacheServiceFactory>();

                    services.AddScoped<IEmailService, EmailService>();
                    services.AddScoped<INewsletterService, NewsletterService>();
                    services.AddScoped<ILocalisationService, LocalisationService>();
                    services.AddScoped<IUserContext, NewsletterUserContext>();
                    services.AddScoped<IDecisionsBlobStorage, DecisionsBlobStorage>(x =>
                        new DecisionsBlobStorage(x.GetRequiredService<IOptions<StorageSettings>>(),
                            hostContext.Configuration["VisualStudioTenantId"] ??
                            throw new InvalidOperationException()));
                    services.AddScoped<IDecisionsBlobContainer, DecisionsBlobContainer>()
                            .Configure<StorageSettings>(hostContext.Configuration.GetSection("AzureStorage"));
                    services.AddScoped<IAzureBlobHelper, AzureBlobHelper>();
                    services.AddScoped<INewsletterRequeueHelper, NewsletterRequeueHelper>();

                    services.RegisterDbContext<SmartDecisionsContext>();
                    services.AddScoped<PlxDbContext, SmartDecisionsContext>();
                    services.AddScoped<INewsletterActivityRepository, NewsletterActivityRepository>();                  

                    JsonConvert.DefaultSettings = () => new JsonSerializerSettings
                    {
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    };
                })
                .Build();

            await host.RunAsync();
        }
    }
}