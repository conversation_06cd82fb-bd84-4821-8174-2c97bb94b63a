﻿using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public class FilterModel : IModel
    {        
        public string Key { get; private set; }
        public string DataKey { get { return this.Key; } }

        public IEnumerable<string> Options { get; private set; }

        public FilterModel(string key, IEnumerable<string> options)
        {
            this.Key = key;
            this.Options = options;
        }

        public string Type { get { return this.Key == "Name" ? "search" : "select"; } }

        public string Header { get { return this.Type == "search" ? $"Search {this.Key}" : $"Filter by {this.Key}"; } }

        public string Display { get { return this.Key; } }

        public string FilterCollection { get { return this.Key; } }
    }
}
