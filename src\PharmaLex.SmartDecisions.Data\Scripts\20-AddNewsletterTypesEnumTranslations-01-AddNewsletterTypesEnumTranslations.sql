﻿insert into [dbo].[MultilingualResource] select 1, '[NewsArticleImportance].newsletter', N'Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticleImportance].newsletter', N'Newsletter', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsArticleImportance].headline', N'Headline', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticleImportance].headline', N'A la une', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsArticleImportance].miscellaneous', N'Miscellaneous', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticleImportance].miscellaneous', N'Divers', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '[NewsArticleImportance].infoflash', N'INFOFLASH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '[NewsArticleImportance].infoflash', N'INFOFLASH', getdate(), '<EMAIL>', getdate(), '<EMAIL>'

insert into [dbo].[MultilingualResource] select 1, '(news).newsletter-type-heading', N'Classification', getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [dbo].[MultilingualResource] select 2, '(news).newsletter-type-heading', N'Catégorie', getdate(), '<EMAIL>', getdate(), '<EMAIL>'