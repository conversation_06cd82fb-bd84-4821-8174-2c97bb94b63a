BEGIN TRAN

DECLARE @createdBy nvarchar(100)
SET @createdBy = '<EMAIL>'

DECLARE @NewsCategoryIdFr int
DECLARE @NewsCategoryIdEn int
SELECT @NewsCategoryIdFr = Id from NewsCategory WHERE LocalisationKey = 'fr';
SELECT @NewsCategoryIdEn = Id from NewsCategory WHERE LocalisationKey = 'en';

WITH CTE AS (
	SELECT DISTINCT ns.UserId from [dbo].[NewsletterSubscription] ns
	LEFT JOIN [dbo].[UserNewsCategory] unc ON unc.UserId = ns.UserId AND unc.NewsCategoryId in (@NewsCategoryIdFr, @NewsCategoryIdEn)
	WHERE ns.Active = 1 and unc.UserId IS NULL
), PREFERENCE_ORDER AS (
	SELECT unc.UserId, MAX([Order]) as [Max_Order] FROM [dbo].[UserNewsCategory] unc
	INNER JOIN CTE on CTE.UserId = unc.UserId
	GROUP BY unc.UserId
)
INSERT INTO [dbo].[UserNewsCategory] 
SELECT ISNULL(po.Max_Order, 0) + 1, CTE.UserId, @NewsCategoryIdFr, GETUTCDATE(), getdate(), @createdBy, getdate(), @createdBy
FROM CTE LEFT JOIN PREFERENCE_ORDER po on po.UserId = CTE.UserId

COMMIT