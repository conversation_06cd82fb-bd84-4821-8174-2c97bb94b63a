﻿--alter table [dbo].[Field]
--add constraint [UC_Field_ContentTypeId_Name] unique ([ContentTypeId], [Name])
--go

--alter table [dbo].[Field]
--with check add constraint [FK_Field_ContentType] foreign key([ContentTypeId]) 
--references [dbo].[ContentType] ([Id]) on delete cascade
--go

create trigger [dbo].[Field_Insert] on [dbo].[Field]
for insert as
insert into [Audit].[Field_Audit]
select 'I', [Id], [Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[Field_Update] on [dbo].[Field]
for update as
insert into [Audit].[Field_Audit]
select 'U', [Id], [Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] from [Inserted]
go

create trigger [dbo].[Field_Delete] on [dbo].[Field]
for delete as
insert into [Audit].[Field_Audit]
select 'D', [Id], [Name], [ContentTypeId], [FieldTypeId], [Length], [Required], [Unique], [Description], [System], [RelatedContentTypeId], [CreatedDate], [CreatedBy], getdate(),coalesce(rtrim(convert(varchar(128), context_info())), suser_name()) from [Deleted]
go
