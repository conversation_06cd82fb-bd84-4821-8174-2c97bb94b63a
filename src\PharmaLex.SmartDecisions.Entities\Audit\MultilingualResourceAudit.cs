﻿using PharmaLex.DataAccess;

#nullable disable

namespace PharmaLex.SmartDecisions.Entities.Audit
{
    public partial class MultilingualResourceAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int? Id { get; set; }
        public int? LocaleId { get; set; }
        public string Key { get; set; }
        public string Content { get; set; }
    }
}
