﻿using System.Collections.Generic;

namespace PharmaLex.SmartDecisions.Web.Models
{
    public abstract class ContentItemViewModels
    {
        public ContentTypeModel ContentType { get; set; }
    }

    public class ListContentItemViewModel : ContentItemViewModels
    {
        public IEnumerable<ContentItemModel> Items { get; set; }

        public static FieldColumnModel NameColumn
        {
            get
            {
                return new FieldColumnModel
                {
                    Name = "Name",
                    SortDirection = 1,
                    FieldTypeId = 1,
                    Type = "text"
                };
            }
        }

        public List<FieldColumnModel> Columns { get; set; }

        public static FilterModel NameFilter
        {
            get
            {
                return new FilterModel("Name", new List<string>());
            }
        }

        public List<FilterModel> Filters { get; set; }
    }

    public class EditContentItemViewModel : ContentItemViewModels
    {
        public ContentItemModel Item { get; set; }
    }

    public class ViewContentItemsViewModel : ContentItemViewModels
    {
        public IEnumerable<ContentItemModel> Items { get; set; }
    }
}
