﻿using AutoMapper;
using PharmaLex.SmartDecisions.Entities;
using System;
using System.Linq;

namespace PharmaLex.NewsletterApp
{
    public class NewsArticleContentModel
    {
        public int Id { get; set; }

        public int NewsArticleId { get; set; } 

        public LocaleModel Locale { get; set; }

        public int ImportanceId { get; set; }

        public int NewsSourceId { get; set; }

        public DateTime SourcePublicationDate { get; set; }

        public string Title { get; set; }

        public string SourceUrl { get; set; }

        public string FriendlyUrl { get; set; }

        public string AzureBlobName { get; set; }

        public string ImpactAssessmentSummary { get; set; }

        public int[] NewsArticleCategoryIds { get; set; }
    }

    public class NewsArticleContentModelMappingProfile : Profile
    {
        public NewsArticleContentModelMappingProfile()
        {
            this.CreateMap<NewsArticleContent, NewsArticleContentModel>()
                .ForMember(d => d.ImportanceId, s => s.MapFrom(x => x.NewsArticle.ImportanceId))
                .ForMember(d => d.NewsSourceId, s => s.MapFrom(x => x.NewsArticle.NewsSourceId))
                .ForMember(d => d.SourcePublicationDate, s => s.MapFrom(x => x.NewsArticle.SourcePublicationDate))
                .ForMember(d => d.NewsArticleCategoryIds, s => s.MapFrom(x => x.NewsArticle.NewsArticleCategory.Select(y => y.NewsCategoryId)));
        }
    }
}
