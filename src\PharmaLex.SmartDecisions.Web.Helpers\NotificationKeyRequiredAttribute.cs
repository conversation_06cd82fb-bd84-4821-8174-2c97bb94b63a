﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;

namespace PharmaLex.SmartDecisions.Web.Helpers
{
    public class NotificationKeyRequiredAttribute : TypeFilterAttribute
    {
        public NotificationKeyRequiredAttribute() : base(typeof(NotificationKeyRequiredFilter)) { }
    }

    public class NotificationKeyRequiredFilter : IAuthorizationFilter
    {
        private readonly IConfiguration configuration;
        
        public NotificationKeyRequiredFilter(IConfiguration configuration) 
        {
            this.configuration = configuration;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            string key = context.HttpContext.GetRouteData().Values["key"]?.ToString();
            if (key != configuration.GetValue<string>("AppSettings:NotificationKey"))
            {
                context.Result = new ForbidResult();
            }
        }
    }
}
