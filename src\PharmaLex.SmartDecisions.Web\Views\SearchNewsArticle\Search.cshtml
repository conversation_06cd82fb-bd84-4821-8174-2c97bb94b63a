@model NewsArticleSearchModel
@using PharmaLex.SmartDecisions.Entities
@using Newtonsoft.Json
@using PharmaLex.Authentication.B2C
@inject INewsCategoryLicenseHelper categoryHelper
@using Microsoft.AspNetCore.Localization

@{
    ViewData["Title"] = ls.Localise("search").Value;
    var newsCategories = (await ls.LocaliseList<NewsCategory>()).OrderBy(x => x.Name).ToList();
    var newsSources = (await ls.LocaliseList<NewsSource>()).OrderBy(x => x.Name).ToList();
    var languages = (await ls.LocaliseList<Locale>()).OrderByDescending(x => x.Default).ToList();

    int userId = this.User.GetClaimValue<int>("plx:userid");
    var licensedGeographicalScopeCategories = await categoryHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.GeographicalScope, true);
    var licensedProductCategories = await categoryHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.Products, true);
    var licensedThemeCategories = await categoryHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.Themes, true);
    var licensedTypeOfTextCategories = await categoryHelper.GetUserNewsCategoryGroupLicense(userId, NewsCategoryGroup.TypeOfText, true);
    var requestCulture = this.Context.Features.Get<IRequestCultureFeature>().RequestCulture.UICulture.Name;

    var newsletterTypesScopes = (await ls.LocaliseEnum<NewsArticleImportance>()).Where(x => x.Name != "INFOFLASH");
    var geographicalScopes = newsCategories.Where(x => licensedGeographicalScopeCategories.Contains(x.Id));
    var geographicalScope = @ls.Localise("[NewsCategory].geographical-scope").Value;
    var productScope = @ls.Localise("[NewsCategory].products").Value;
    var themesScope = @ls.Localise("[NewsCategory].themes").Value;
    var typeOfTextScope = @ls.Localise("[NewsCategory].type-of-text").Value;
    var newsSourceScope = @ls.Localise("news-source").Value;
    var newsletterTypeScope = @ls.LocaliseSafe("(news).newsletter-type-heading").Value;
    var language = @ls.Localise("language").Value;
}

<div id="filter-articles" v-cloak>
    <div class="page-loading" v-if="searching">
        <div class="page-loader"></div>
    </div>
    <div class="sub-header">
        <h2>@ls.Localise("search")</h2>
    </div>

    <div class="search-download-articles">
        <div :class="['search-download-articles-selected', {'warning-color': downloadArticlesArray.length > 25}]">{{downloadArticlesArray.length}} @ls.Localise("selected-of") 25 &nbsp;<span v-if="downloadArticlesArray.length > 25" class="warning-color">@ls.Localise("exceed-maximum-number-message")</span></div>
        <button class="button" v-on:click="downloadArticles" :disabled="!downloadArticlesArray.length || downloadArticlesArray.length > 25">@ls.Localise("download-articles")</button>
    </div>

    <form method="post" id="searchArticles" v-on:submit="searching=true">
        @Html.AntiForgeryToken()
        <section>
            <div class="flex flex-cols">
                <div class="flex-container search-content">
                    <div class="search-content__input">
                        <span class="m-icon search-icon">search</span>
                        <input class="search-content-input" type="search" id="SearchText" name="SearchText" v-model="searchText" minlength="3" placeholder="@ls.Localise("search")" tabindex="1" ref="searchInput" />
                    </div>
                    <div class="flex search-content__only-title">
                        <input type="checkbox" id="IsOnlyTitleSearch" name="IsOnlyTitleSearch" :value="isOnlyTitleSearch" :checked="isOnlyTitleSearch" v-on:change="onlyTitleSearch" tabindex="2" />
                        <label for="IsOnlyTitleSearch">@ls.Localise("search-only-in-title")</label>
                    </div>
                </div>
                <div class="flex search-filters">
                    <div class="flex justify-space-between flex-align-center">
                        <div class="flex search-filters__date-range">
                            <input type="date" id="StartDate" :max="todayDate" name="StartDate" v-model="startDate" tabindex="3" style="display:flex" />
                            <span>-</span>
                            <input type="date" id="EndDate" :min="startDate" :max="todayDate" name="EndDate" v-model="endDate" tabindex="4" style="display:flex" />
                        </div>
                        <div class="flex">
                            <div class="flex search-filters__fields ml-2 mr-2">
                                <multi-select-dropdown :title="language" :text="selectAllText" :options="dropDownLanguages" v-model:preselected="searchLanguages"></multi-select-dropdown>
                                <input v-for="item in searchLanguages" type="hidden" id="Languages" name="Languages" :value="item" />

                                <multi-select-dropdown :title="geographicalScope" :text="selectAllText" :options="dropDownGeographicalScopeCategories" v-model:preselected="searchGeographicalScopes"></multi-select-dropdown>
                                <input v-for="item in searchGeographicalScopes" type="hidden" id="GeographicalScopes" name="GeographicalScopes" :value="item" />

                                <multi-select-dropdown :title="productScope" :text="selectAllText" :options="dropDownProductCategories" v-model:preselected="searchProducts"></multi-select-dropdown>
                                <input v-for="item in searchProducts" type="hidden" id="Products" name="Products" :value="item" />

                                <multi-select-dropdown :title="themesScope" :text="selectAllText" :options="dropDownThemeCategories" v-model:preselected="searchThemes"></multi-select-dropdown>
                                <input v-for="item in searchThemes" type="hidden" id="Themes" name="Themes" :value="item" />

                                <multi-select-dropdown :title="typeOfTextScope" :text="selectAllText" :options="dropDownTypeOfTextCategories" v-model:preselected="searchTypeOfText"></multi-select-dropdown>
                                <input v-for="item in searchTypeOfText" type="hidden" id="TypesOfText" name="TypesOfText" :value="item" />

                                <multi-select-dropdown :title="newsSourceScope" :text="selectAllText" :options="dropDownNewsSourceCategories" v-model:preselected="searchNewsSource"></multi-select-dropdown>
                                <input v-for="item in searchNewsSource" type="hidden" id="NewsSources" name="NewsSources" :value="item" />

                                <multi-select-dropdown :title="newsletterTypeScope" :text="selectAllText" :options="dropDownNewsletterTypes" v-model:preselected="searchNewsletterTypes"></multi-select-dropdown>
                                <input v-for="item in searchNewsletterTypes" type="hidden" id="NewsletterTypes" name="NewsletterTypes" :value="item" />
                            </div>
                            <div class="flex ml-2 mr-2 search-filters-buttons">
                                <button class="button ml-1 mr-1" :disabled="!canSearch">@ls.Localise("apply")</button>
                                <button class="ml-1 mr-1 button secondary" @@click="clearClick">@ls.Localise("clear")</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </form>
    
    <filtered-table :items="articles"
                    :columns="columns"
                    :filters="filters"
                    :link="link"
                    behavior="newtab"
                    :resources="resources"
                    :is-selectable="isSelectable"
                    v-on:on-page-index-change="onPageIndexChange"
                    v-on:on-page-size-change="onPageSizeChange"
                    v-on:on-selection-change="onSelectionChange">
    </filtered-table>
</div>

<script type="text/javascript">
    function preferencesNotNested(prefs) {
        let flatPreferences = [];

        for (const pref of flattenPreferences(prefs.children)) {
            flatPreferences.push(pref);
        };
        return flatPreferences;
    }

    function* flattenPreferences(prefs) {
        for (const pref of prefs) {
            yield pref;
            yield* flattenPreferences(pref.children);
        }
    };
</script>

@section Scripts {
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;

        const convertDate = date => {
            return Intl.DateTimeFormat('@requestCulture', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            }).format(new Date(date));
        }
        const date = new Date();

        var pageConfig = {
            appElement: '#filter-articles',
            data() {

                const buildCategories = (categories, current) => {
                    current = current || categories.filter(x => !x.parentId);
                    current.forEach(c => {
                        c.children = categories.filter(x => x.parentId == c.id);
                        c.prefOrder = 1;
                        buildCategories(categories, c.children);
                    });

                    return current;
                };

                const newsCategories = buildCategories(@Html.Raw(JsonConvert.SerializeObject(newsCategories))).filter(x => !x.parentId);
                const hierarchicalGeographicalScopes = newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.GeographicalScope) );
                const hierarchicalProducts = newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.Products) );
                const hierarchicalThemes = newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.Themes) );
                const hierarchicalTypesOfText = newsCategories.filter(x => x.groupId === @((int)NewsCategoryGroup.TypeOfText) );

                const licensedGeographicalScopeCategories = @Html.Raw(JsonConvert.SerializeObject(licensedGeographicalScopeCategories));
                const licenseProductCategories = @Html.Raw(JsonConvert.SerializeObject(licensedProductCategories));
                const licensedThemeCategories = @Html.Raw(JsonConvert.SerializeObject(licensedThemeCategories));
                const licensedTypeOfTextCategories = @Html.Raw(JsonConvert.SerializeObject(licensedTypeOfTextCategories));

                let dropDownGeographicalScopeCategories = preferencesNotNested(hierarchicalGeographicalScopes[0]).filter(x => licensedGeographicalScopeCategories.indexOf(x.id) >= 0);
                let dropDownProductCategories = preferencesNotNested(hierarchicalProducts[0]).filter(x => licenseProductCategories.indexOf(x.id) >= 0);
                let dropDownThemeCategories = preferencesNotNested(hierarchicalThemes[0]).filter(x => licensedThemeCategories.indexOf(x.id) >= 0);
                let dropDownTypeOfTextCategories = preferencesNotNested(hierarchicalTypesOfText[0]).filter(x => licensedTypeOfTextCategories.indexOf(x.id) >= 0);
                let dropDownNewsSourceCategories = @Html.Raw(JsonConvert.SerializeObject(newsSources));
                let dropDownNewsletterTypes = @Html.Raw(JsonConvert.SerializeObject(newsletterTypesScopes));
                let dropDownLanguages = @Html.Raw(JsonConvert.SerializeObject(languages));

                const geographicalScope = @Html.Raw(JsonConvert.SerializeObject(geographicalScope));
                const productScope = @Html.Raw(JsonConvert.SerializeObject(productScope));
                const themesScope = @Html.Raw(JsonConvert.SerializeObject(themesScope));
                const typeOfTextScope = @Html.Raw(JsonConvert.SerializeObject(typeOfTextScope));
                const newsSourceScope = @Html.Raw(JsonConvert.SerializeObject(newsSourceScope));
                const newsletterTypeScope = @Html.Raw(JsonConvert.SerializeObject(newsletterTypeScope));
                const language = @Html.Raw(JsonConvert.SerializeObject(language));

                let pageSize = 0;
                let pageIndex = 0;

                const modelData = @Html.Raw(Model.ToJson());
                searchGeographicalScopes = modelData.geographicalScopes;
                searchNewsletterTypes = modelData.newsletterTypes;
                searchProducts = modelData.products;
                searchThemes = modelData.themes;
                searchTypeOfText = modelData.typesOfText;
                searchNewsSource = modelData.newsSources;
                searchLanguages = modelData.languages;
                searchText = modelData.searchText;
                isOnlyTitleSearch = modelData.isOnlyTitleSearch;

                const articles = @Html.Raw(Model.Results.ToJson());
                articles.forEach(x => {
                    x.sourcePublicationDate = x.sourcePublicationDate.split('T')[0];
                    setImportanceImage(x);
                });

                return {
                    link: '/article/{friendlyUrl}',
                    articles,
                    searchText: "",
                    searching: false,
                    isOnlyTitleSearch: false,
                    selectAllText: '@ls.LocaliseSafe("select-all")',
                    resources: {
                        noRecordsMessage: '@ls.LocaliseSafe("[[table]].no-results-message")',
                        sortByFormat: '@ls.LocaliseSafe("[[table]].sort-by-format")',
                        filterByFormat: '@ls.LocaliseSafe("[[table]].filter-by-format")',
                        searchInFormat: '@ls.LocaliseSafe("[[table]].search-in-format")',
                        clearFilters: '@ls.LocaliseSafe("[[table]].clear-filters")',
                        noValue: '@ls.LocaliseSafe("[[table]].no-value")',
                        pager: {
                            showingFormat: '@ls.LocaliseSafe("[[table]].pager.showing-format")',
                            showingFilteredFormat: '@ls.LocaliseSafe("[[table]].pager.showing-filtered-format")',
                            pageSize: '@ls.LocaliseSafe("[[table]].pager.page-size")',
                            first: '@ls.LocaliseSafe("[[table]].pager.first")',
                            previous: '@ls.LocaliseSafe("[[table]].pager.previous")',
                            next: '@ls.LocaliseSafe("[[table]].pager.next")',
                            last: '@ls.LocaliseSafe("[[table]].pager.last")',
                        }
                    },
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'title',
                                sortKey: 'title',
                                header: '@ls.LocaliseSafe("title")',
                                type: 'text',
                                style: 'min-width: 15rem;',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'themes',
                                sortKey: 'sortThemes',
                                header: '@ls.LocaliseSafe("(news).subscribe.themes")',
                                type: 'chips',
                                headerStyle: 'resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'geographicalScope',
                                sortKey: 'geographicalScope',
                                header: '@ls.LocaliseSafe("(news).subscribe.geographical-scope")',
                                type: 'text'
                            },
                            {
                                dataKey: 'source',
                                sortKey: 'source',
                                header: '@ls.LocaliseSafe("news-source")',
                                type: 'text'
                            },
                            {
                                dataKey: 'typeOfText',
                                sortKey: 'typeOfText',
                                header: '@ls.LocaliseSafe("[NewsCategory].type-of-text")',
                                type: 'text'
                            },
                            {
                                dataKey: 'sourceUrl',
                                sortKey: 'sourceUrl',
                                header: "@ls.LocaliseSafe("source-url")",
                                type: 'link',
                                style: 'min-width: 15rem;',
                                headerStyle: ' resize: horizontal; overflow-x: auto;'
                            },
                            {
                                dataKey: 'sourcePublicationDate',
                                sortKey: 'sourcePublicationDate',
                                sortComparer: 'date',
                                header: '@ls.LocaliseSafe("source-publication-date")',
                                sortDirection: -1,
                                type: 'date',
                                edit: {
                                    type: 'plain',
                                    required: true,
                                    convert: convertDate,
                                    value: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
                                }
                            },
                            {
                                dataKey: 'goLiveDate',
                                sortKey: 'goLiveDate',
                                sortComparer: 'date',
                                header: '@ls.LocaliseSafe("newsletter-publication-date")',
                                sortDirection: -1,
                                type: 'date',
                                edit: {
                                    type: 'plain',
                                    required: true,
                                    convert: convertDate,
                                    value: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
                                }
                            },
                            {
                                dataKey: 'importanceType',
                                sortKey: 'importanceName',
                                header: '@newsletterTypeScope',
                                type: 'image'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'title',
                            options: [],
                            type: 'search',
                            header: '@ls.LocaliseSafe("search-in-title")',
                            fn: v => p => plx.escapeAccent(p.title.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'themes',
                            options: [],
                            type: 'search',
                            header: '@ls.LocaliseSafe("search-in-themes")',
                            fn: v => p => plx.escapeAccent(p.themes.join(' ').toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'geographicalScope',
                            options: @Html.Raw(JsonConvert.SerializeObject(geographicalScopes)),
                            filterCollection: 'geographicalScopeId',
                            type: 'select-multiple',
                            fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.geographicalScope),
                            convert: v => v
                        },
                        {
                            key: 'source',
                            options: @Html.Raw(JsonConvert.SerializeObject(newsSources)),
                            filterCollection: 'newsSourceId',
                            type: 'select-multiple',
                            header: '@ls.LocaliseSafe("search-in-source")',
                            fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.source),
                            convert: v => v
                        },
                        {
                            key: 'typeOfText',
                            options: [],
                            type: 'search',
                            header: '@ls.LocaliseSafe("search-in-type-of-text")',
                            fn: v => p => plx.escapeAccent(p.typeOfText.toLowerCase()).includes(plx.escapeAccent(v.toLowerCase())),
                            convert: v => v
                        },
                        {
                            key: 'sourceUrl',
                            options: [],
                            type: 'search',
                            header: "@ls.LocaliseSafe("search-in-source-url")",
                            fn: v => p => p.sourceUrl.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'sourcePublicationDate',
                            options: [],
                            type: 'search',
                            header: '@ls.LocaliseSafe("(news).search-publication-date")',
                            fn: v => p => convertDate(p.sourcePublicationDate).toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'goLiveDate',
                            options: [],
                            type: 'search',
                            header: '@ls.LocaliseSafe("(news).search-newsletter-date")',
                            fn: v => p => convertDate(p.goLiveDate).toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'importanceType',
                            options: @Html.Raw(JsonConvert.SerializeObject(newsletterTypesScopes)),
                            filterCollection: 'importanceId',
                            type: 'select-multiple',
                            header: '@ls.LocaliseSafe("(news).newsletter-type-heading")',
                            fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.importanceName),
                            convert: v => v
                        }
                    ],
                    todayDate: this.getDate(new Date()),
                    startDate: modelData.startDate ? this.getDate(modelData.startDate) : '',
                    endDate: modelData.endDate ? this.getDate(modelData.endDate) : '',

                    dropDownGeographicalScopeCategories,
                    dropDownProductCategories,
                    dropDownThemeCategories,
                    dropDownTypeOfTextCategories,
                    dropDownNewsSourceCategories,
                    dropDownNewsletterTypes,
                    dropDownLanguages,

                    searchGeographicalScopes,
                    searchNewsletterTypes,
                    searchProducts,
                    searchThemes,
                    searchTypeOfText,
                    searchNewsSource,
                    searchLanguages,
                    searchText,
                    isOnlyTitleSearch,
                    isSelectable: true,

                    geographicalScope,
                    productScope,
                    themesScope,
                    typeOfTextScope,
                    newsSourceScope,
                    newsletterTypeScope,
                    language,
                    pageSize,
                    pageIndex,
                    downloadArticlesArray: [],
                    productsConfig: {
                        resources: {
                            placeholderText: '@ls.LocaliseSafe("(news).subscribe.select-one-product")',
                            clearSelectionText: '@ls.LocaliseSafe("clear-selection")'
                        },
                        required: false
                    },
                };
            },
            methods: {
                getDate(d) {
                    const date = typeof d === 'object' ? d : new Date(d);
                    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
                },
                clearClick(e) {
                    this.searchGeographicalScopes = Vue.ref([]);
                    this.searchProducts = Vue.ref([]);
                    this.searchThemes = Vue.ref([]);
                    this.searchTypeOfText = Vue.ref([]);
                    this.searchNewsSource = Vue.ref([]);
                    this.searchNewsletterTypes = Vue.ref([]);
                    this.searchText = '';
                    this.startDate = '';
                    this.endDate = '';
                    this.isOnlyTitleSearch = false;
                    this.searchLanguages = Vue.ref([]);

                    e.preventDefault();
                },
                onlyTitleSearch() {
                    this.isOnlyTitleSearch = !this.isOnlyTitleSearch;
                },
                onSelectionChange(toggledItems) {
                    this.downloadArticlesArray = this.articles.filter(x => toggledItems.includes(x.id)).map(x => x.friendlyUrl);
                },
                downloadArticles() {
                    this.searching = true;

                    var newsArticlesModel = {};
                    newsArticlesModel.articleUrls = this.downloadArticlesArray.slice(0, 25);

                    fetch('/article/download/articles', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: JSON.stringify(newsArticlesModel)
                    })
                        .then((response) => response.blob())
                        .then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.style.display = 'none';
                            a.href = url;
                            a.download = `${this.searchText ? this.searchText + '-' : ''}results-${new Date().toLocaleDateString()}`;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            this.searching = false;
                        });
                },
                onPageIndexChange(pageIndex) {
                    this.pageIndex = pageIndex;
                },
                onPageSizeChange(pageSize) {
                    this.pageSize = pageSize;
                }
            },
            computed: {
                canSearch() {
                    return this.searchText?.length > 2
                        || this.searchGeographicalScopes.length > 0
                        || this.searchProducts.length > 0
                        || this.searchThemes.length > 0
                        || this.searchTypeOfText.length > 0
                        || this.searchNewsSource.length > 0
                        || this.searchNewsletterTypes.length > 0
                        || this.searchLanguages.length > 0;
                }
            },
            mounted() {
                this.$refs.searchInput.focus();
            }
        };

    </script>
}

@section VueComponentScripts {
    <partial name="Components/ImageCell" />
    <partial name="Components/FilteredTableV2" />
    <partial name="Components/SearchList" />
    <partial name="Components/MultiSelectDropdown" />
}