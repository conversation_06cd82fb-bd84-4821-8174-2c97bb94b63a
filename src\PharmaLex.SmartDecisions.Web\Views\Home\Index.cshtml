﻿@using PharmaLex.Caching.Data
@using PharmaLex.SmartDecisions.Entities
@using PharmaLex.SmartDecisions.Web.Helpers
@using System.Linq
@{
    ViewData["Title"] = "";
}
<div class="home-wrapper">
    <div class="map-surface">
        <div id="map-container" class="map-container">
            <div id="reset-zoom-button" class="map-button reset-zoom-button"><i class="icon-zoom-out"></i><span>Reset</span></div>
        </div>
    </div>
    <div id="toolbox" class="toolbox" v-cloak>
        <h3>Topics</h3>
        <div class="toolbox-item select-wrapper form-group">
            <label for="topic">Topic</label>
            <div class="custom-select">        
                <select name="topic" v-model="topicId" v-on:change="topicChanged">
                    <option v-for="t in topics" :value="t.id">{{t.shortName}}</option>
                </select>
            </div>
        </div>
        <div class="toolbox-item select-wrapper form-group">
            <label for="topic">Country</label>
            <div class="custom-select">
                <select name="country" v-model="selectedCountryId" v-on:change="countryChanged">
                    <option value="0">Select country</option>
                    <option v-for="c in countries" :value="c.mapId">{{c.name}}</option>
                </select>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="@VersionCdn.Host/lib/d3/d3.v4.min.js"></script>
    <script src="@VersionCdn.Host/lib/d3/topojson.min.js"></script>
    <script src="@VersionCdn.GetUrl("data/countries.topo.js")"></script>
    <script src="@VersionCdn.GetUrl("js/map.js")"></script>
    <script src="@VersionCdn.GetUrl("js/dialog.js")"></script>
   <script type="text/javascript">
    plx.main = {
        init: function () {
            plx.main.setMapContainer();
            plx.map.init('map-container', plx.main.onMapInitialised, plx.main.onMapRendered, plx.main.onMapZoomed, plx.main.onCountrySelected, plx.main.onLoadTooltipContent);
            plx.map.render();
            window.addEventListener('resize', plx.main.resize);
        },
        setMapContainer: function () {
            let b = d3.select('body').node().getBoundingClientRect();
            let c = d3.select('#map-container');
            let cb = c.node().getBoundingClientRect();
            const topOffset = 4;
            c.style('height', `${b.height - cb.top - topOffset}px`);
        },
        resize: function () {
            clearTimeout(plx.main.resizeTimer);
            plx.main.resizeTimer = setTimeout(() => {
                plx.main.setMapContainer();
                plx.map.render();
                vueRootComponent.highlightTopicCountries();
            });
        },
        onMapInitialised: function () {
            plx.dialog.init('view-topic-dialog');
        },
        onMapRendered: function () {
        },
        onMapZoomed: function () {
            if (plx.map.resetting) {
                 vueRootComponent.selectedCountryId = 0;
            }
        },
        onCountrySelected: function (id) {
            if (id <= 0) {
                plx.map.resetZoom();
                plx.dialog.close();
            }
                else if (vueRootComponent.topicCountries.findIndex(x => x.mapId == id) > -1) {
                    vueRootComponent.selectedCountryId = id;
                    plx.dialog.load(`/topic/${vueRootComponent.topicId}/${id}`, '90%', '90vh', '/images/loaders/spinner-75.svg', vueRootComponent.addTopicDialogEventListeners);
            }
        },
        onLoadTooltipContent: function (id) {
            let html = '';
            return html;
        }
    };
    var pageConfig = {
        appElement: '#toolbox',
        data() {
            return {
                countries: [],
                authorities: [],
                topics: [],
                topicId: -1,
                content: [],
                topicCountries: [],
                selectedCountryId: 0
            }
        },
        computed: {
            topic() {
                return this.topics.find(x => x.id == this.topicId);
            }
        },
        beforeCreate() {
            plx.main.init();
        },
        created() {
            Promise.all([
                fetch(`/content/countries`).then(r => r.json()), // Could filter these by authority
                fetch('/content/regulatory-authorities').then(r => r.json()), // Could filter these by topic
                fetch('/topics').then(r => r.json())
            ]).then(([c, a, t]) => {
                this.countries = c;
                this.authorities = a;
                this.topics = t;
                if (this.topics.length > 0) {
                    this.topicId = t[0].id;
                    this.topicChanged();
                }
            });
        },
        methods: {
            topicChanged() {
                let c = this.content.find(x => x.topicId == this.topicId);
                this.selectedCountryId = 0;
                if (c) {
                    return this.highlightTopicCountries(c);
                }
                fetch(`/topic/content/${this.topicId}`, {
                    method: 'GET',
                    credentials: 'same-origin'
                }).then(r => r.json()).then(r => {
                    let c = { topicId: this.topicId, items: r };
                    this.content.push(c);
                    this.highlightTopicCountries(c);
                });
            },
            highlightTopicCountries(c) {
                if (!c) {
                    c = this.content.find(x => x.topicId == this.topicId);
                }
                plx.map.resetCountryFill();
                let ra = [...new Set(c.items.map(x => { return this.authorities.find(y => y.id == x.authorityId) }))];
                this.topicCountries = [...new Set(ra.map(x => this.countries.filter(y => x.countries.indexOf(y.id) > -1)))].flat();
                [].forEach.call(this.topicCountries, o => {
                    plx.map.fillCountry(o.mapId, 'rgb(109,167,213)')
                });
                plx.map.resetZoom();
            },
            addTopicDialogEventListeners() {
                document.querySelectorAll('.topic-cycle-button').forEach(x => x.addEventListener('click', (e) => {
                        plx.dialog.load(`/topic/${this.topicId}/${this.selectedCountryId}?itemId=${e.target.getAttribute('data-item-id')}`, '85%', '85%', '/images/loaders/spinner-75.svg', this.addTopicDialogEventListeners);
                }));
            },
            countryChanged() {
                let c = plx.map.countries.find(x => x.id == this.selectedCountryId);
                plx.map.countrySelected(c);
                if (this.topicCountries.findIndex(x => x.mapId == c.id) > -1) {
                    plx.dialog.load(`/topic/${this.topicId}/${c.id}`, '85%', '85%', '/images/loaders/spinner-75.svg', this.addTopicDialogEventListeners);
                }
            }
        }
    };
</script>

}
