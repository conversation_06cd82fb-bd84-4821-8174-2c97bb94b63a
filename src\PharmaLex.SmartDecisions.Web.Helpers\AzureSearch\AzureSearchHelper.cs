﻿using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Threading.Tasks;

namespace PharmaLex.SmartDecisions.Web.Helpers.AzureSearch
{
    public interface IAzureSearchHelper
    {
        Task<SearchResults<T>> TextSearchAsync<T>(string searchString, bool isSearchOnlyInTitle = false, string[]? selectFields = null, string filter = "");
        Task CreateIndexAsync<T>();
        Task RunIndexer();
        Task CreateIndexer();
        Task CreateDataSource();
        Task<SearchIndexerStatus> GetIndexerStatusAsync(string indexerName);
        Task RunIndexerOnSuccess();
        Task DeleteIndexer();

        Task DeleteIndex();

        Task DeleteDataSource();
    }

    public class AzureSearchHelper : IAzureSearchHelper
    {
        private readonly SearchClient searchClient;
        private readonly SearchIndexClient searchIndexClient;
        private readonly SearchIndexerClient searchIndexerClient;
        private readonly SearchSettings searchSettings;

        public AzureSearchHelper(
            SearchClient searchClient,
            SearchIndexClient searchIndexClient,
            SearchIndexerClient searchIndexerClient,
            IOptionsMonitor<SearchSettings> searchOptions)
        {
            this.searchSettings = searchOptions.CurrentValue;
            this.searchClient = searchClient;
            this.searchIndexClient = searchIndexClient;
            this.searchIndexerClient = searchIndexerClient;
        }

        public async Task<SearchResults<T>> TextSearchAsync<T>(string searchText, bool isSearchOnlyInTitle = false, string[]? selectFields = null, string filter = "")
        {
            var options = new SearchOptions()
            {
                Filter = filter,
                SearchMode = SearchMode.All,
                QueryType = SearchQueryType.Simple
            };

            if (isSearchOnlyInTitle)
            {
                options.SearchFields.Add("searchableTitle");
            }

            if (selectFields != null)
            {
                foreach (var field in selectFields)
                {
                    options.Select.Add(field);
                }
            }

            return await searchClient.SearchAsync<T>(searchText, options);
        }

        public async Task CreateIndexAsync<T>()
        {
            var analyzer = new CustomAnalyzer("custom-analyzer", LexicalTokenizerName.MicrosoftLanguageStemmingTokenizer)
            {
                TokenFilters = { TokenFilterName.AsciiFolding, TokenFilterName.Lowercase, TokenFilterName.Classic, TokenFilterName.Elision }
            };

            var options = new SearchClientOptions()
            {
                Serializer = new JsonObjectSerializer(
                    new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    })
            };

            var fieldBuilder = new FieldBuilder
            {
                Serializer = options.Serializer
            };

            var index = new SearchIndex(searchSettings.IndexName)
            {
                Fields = fieldBuilder.Build(typeof(T)),
            };

            index.Analyzers.Add(analyzer);

            await searchIndexClient.CreateIndexAsync(index);
        }

        public async Task CreateIndexer()
        {
            var mappingFunction = new FieldMappingFunction("base64Decode");
            mappingFunction.Parameters.Add("useHttpServerUtilityUrlTokenDecode", false);

            var searchIndexer = new SearchIndexer(searchSettings.IndexerName, searchSettings.DataSourceName, searchSettings.IndexName)
            {
                Description = "Smart Decisions Indexer",
                Schedule = null,
                FieldMappings =
                {
                    new FieldMapping("base64Title")
                    { 
                        TargetFieldName = "searchableTitle",
                        MappingFunction = mappingFunction
                    },
                }
            };
            await searchIndexerClient.CreateIndexerAsync(searchIndexer);
        }

        public async Task RunIndexer()
        {
            await searchIndexerClient.RunIndexerAsync(searchSettings.IndexerName);
        }

        public async Task CreateDataSource()
        {
            await searchIndexerClient.CreateDataSourceConnectionAsync(
                new SearchIndexerDataSourceConnection(
                    searchSettings.DataSourceName,
                    SearchIndexerDataSourceType.AzureBlob,
                    searchSettings.ConnectionString,
                    new SearchIndexerDataContainer(searchSettings.BlobContainer)
                    { 
                        Query = searchSettings.BlobFolder
                    } ));

        }

        public async Task<SearchIndexerStatus> GetIndexerStatusAsync(string indexerName)
        {
            return await searchIndexerClient.GetIndexerStatusAsync(indexerName);
        }

        public async Task DeleteIndexer()
        {
            try
            {
                if (await searchIndexerClient.GetIndexerAsync(searchSettings.IndexerName) is not null)
                {
                    await searchIndexerClient.DeleteIndexerAsync(searchSettings.IndexerName);
                }
            }
            catch (RequestFailedException e) when (e.Status == 404)
            {
                // log
            }
        }

        public async Task DeleteIndex()
        {
            try
            {
                if (await searchIndexClient.GetIndexAsync(searchSettings.IndexName) is not null)
                {
                    await searchIndexClient.DeleteIndexAsync(searchSettings.IndexName);
                }
            }
            catch (RequestFailedException e) when (e.Status == 404)
            {
                // log
            }
        }

        public async Task DeleteDataSource()
        {
            try
            {
                var existingDataSource = await searchIndexerClient.GetDataSourceConnectionAsync(searchSettings.DataSourceName);
                if (existingDataSource is not null)
                {
                    await searchIndexerClient.DeleteDataSourceConnectionAsync(existingDataSource);
                }
            }
            catch (RequestFailedException e) when (e.Status == 404)
            {
                // log 
            }
        }

        public async Task RunIndexerOnSuccess()
        {
            while (true)
            {
                var lastIndexerStatus = (await this.GetIndexerStatusAsync(searchSettings.IndexerName)).LastResult.Status;
                if (lastIndexerStatus is IndexerExecutionStatus.InProgress)
                {
                    await Task.Delay(2000);
                    continue;
                }

                await this.RunIndexer();
                break;
            }
        }
    }
}
