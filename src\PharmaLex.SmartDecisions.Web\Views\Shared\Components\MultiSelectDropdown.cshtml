﻿
<script type="text/x-template" id="multi-select-dropdown-template">
    <div class="flex search-filters__fields-field">
       <div class="flex search-filters__fields-field-container" v-on:click="onClick()">
          <span>{{title}} </span>
          <div class="flex search-filters__fields-field-container-selected">
              {{selected.length > 0 ? '(' + selected.length + ')' : ''}}
          </div>
          <span class="m-icon">keyboard_arrow_down</span>
       </div>
       <div class="search-filters__fields-field-options" v-if="isOpen">
          <div class="multiselect">
             <ul>
                <li>
                    <div class="checkbox-group">
                      <input type="checkbox" :id="selectAllId" @@change="selectAll" :value="isAllSelected" :checked="selected.length === options.length">
                      <label :for="selectAllId">{{text}}</label>
                   </div>
                </li>
                <li v-for="option in options" :key="option.id">
                   <div class="checkbox-group">
                      <input type="checkbox" :id="option.id + option.name" :value="option.id" :checked="selected.includes(option.id)" @@change="onChange(option.id)">
                      <label :for="option.id + option.name">{{ option.name }}</label>
                   </div>
                </li>
             </ul>
          </div>
       </div>
    </div>
</script>

<script type="text/javascript">

    vueApp.component('multi-select-dropdown', {
        template: '#multi-select-dropdown-template',
        props: {
            options: {
                type: Array,
                default: () => []
            },
            preselected: {
                type: Array,
                default: () => []
            },
            title: {
                type: String,
                default: ''
            },
            text: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                selected: [...this.preselected],
                isOpen: false,
                selectAllId: 0,
                isAllSelected: false
            };
        },
        methods: {
            onChange(id) {
                const index = this.selected.indexOf(id);

                if (index < 0) {
                    this.selected.push(id);
                } else {
                    this.selected.splice(index, 1);
                }
                this.$emit('update:preselected', this.selected);
            },
            selectAll() {
                if(this.selected.length === this.options.length) {
                    this.selected.splice(0, this.selected.length);
                    this.isAllSelected = false;
                } else {
                    this.isAllSelected = true;
                    for(const o of this.options) {
                        if(!this.selected.includes(o.id)){
                            this.selected.push(o.id);
                        }
                    }
                }
                this.$emit('update:preselected', this.selected);

            },
            onClick() {
                this.isOpen = !this.isOpen;
            },
            handleClickOutside(evt) {
                if (!this.$el.contains(evt.target)) {
                    this.isOpen = false;
                }
            }
        },
        watch: {
            preselected(val) {
                this.selected = [...this.preselected];
            }
        },
        mounted() {
            document.addEventListener('click', this.handleClickOutside);
            this.selectAllId = this._uid
        },
        destroyed() {
            document.removeEventListener('click', this.handleClickOutside);
        }
    });
</script>